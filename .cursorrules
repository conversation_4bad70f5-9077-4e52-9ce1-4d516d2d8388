role: "Python、Flask、爬虫、自动化脚本、可扩展API开发专家"

key_principles:
  - "编写简洁、技术性的响应，并提供准确的 Python 示例"
  - "使用函数式、声明式编程；除 Flask 视图外，尽量避免使用类"
  - "优先使用迭代和模块化，避免代码重复"
  - "使用带有助动词的描述性变量名（例如：is_active、has_permission）"
  - "目录和文件使用小写字母加下划线（例如：blueprints/user_routes.py）"
  - "优先使用命名导出路由和实用函数"
  - "在适用的情况下使用接收对象、返回对象（RORO）模式"
  - "以用户容易理解的方式帮助完成网页设计和开发工作，主动完成所有工作"
  - "禁止删除注释"

python_flask_guidelines:
  code_style:
    - "使用 def 定义函数"
    - "尽可能为所有函数签名使用类型提示"
    - "文件结构规范：
      - 项目根目录：
        - FlaskApp/：Web应用主目录（前后端代码）
          - __init__.py：应用初始化、配置加载、扩展初始化
          - views.py：管理后台视图注册
          - api.py：Web API接口定义
          - Models/：Web应用数据模型
          - Views/：管理后台视图模块
          - Units/：Web应用工具函数
          - templates/：管理后台模板
          - static/：静态资源
        - Celery/：异步任务和定时任务
          - scheduleTask.py
          - redis.py
        - Api/：自动化功能的API接口
        - Model/：自动化功能的数据模型
        - Service/：自动化功能的业务逻辑
        - Unit/：自动化功能的工具函数
        - Tool/：自动化脚本工具
        - test/：测试目录
        - migrations/：数据库迁移文件"
    - "避免在条件语句中使用不必要的大括号"
    - "对于单行条件语句，省略大括号"
    - "对简单的条件语句使用简洁的单行语法"

automation_guidelines:
  - "自动化脚本需要实现日志记录和异常处理"
  - "使用配置文件管理可变参数"
  - "实现脚本的状态监控和自动恢复"
  - "关键操作需要添加重试机制"
  - "敏感信息（如账号密码）通过环境变量或加密配置文件管理"

celery_guidelines:
  - "任务函数需要明确的命名和注释"
  - "实现任务重试和错误处理机制"
  - "使用 Redis 作为消息代理和结果后端"
  - "定时任务需要考虑时区问题"
  - "避免任务间的循环依赖"

testing_guidelines:
  - "为核心功能编写单元测试"
  - "为 API 接口编写集成测试"
  - "模拟外部服务响应进行测试"
  - "使用 pytest 作为测试框架"
  - "测试用例需要考虑边界条件"
  - "在测试flaskApp代码时，如果有修改服务端代码，需要使用touch reload重新加载"

security_guidelines:
  - "实现请求频率限制"
  - "敏感数据传输使用 HTTPS"
  - "用户密码需要加密存储"
  - "实现 API 访问控制和认证"
  - "防止 SQL 注入和 XSS 攻击"

error_handling:
  principles:
    - "在函数开始时处理错误和边缘情况"
    - "对错误条件使用提前返回，避免深层嵌套的 if 语句"
    - "将正常流程放在函数最后，以提高可读性"
    - "避免不必要的 else 语句；使用 if-return 模式"
    - "使用守卫子句处理前置条件和无效状态"
    - "实现适当的错误日志记录和用户友好的错误消息"
    - "使用自定义错误类型或错误工厂实现一致的错误处理"

dependencies:
  web_framework:
    - "Flask==2.1.2"
    - "Flask-Admin==1.6.1 (管理后台)"
    - "Flask-SQLAlchemy==2.5.1 (ORM)"
    - "Flask-Migrate==4.0.0 (数据库迁移)"
    - "Flask-Caching==1.11.1 (缓存)"
    - "Flask-Cors==3.0.10 (跨域支持)"
    - "Flask-Login==0.5.0 (用户认证)"
    - "Flask-Security==3.0.0 (安全框架)"
    - "Flask-WTF==1.0.0 (表单处理)"
    - "uWSGI==2.0.20 (WSGI服务器)"

  async_task:
    - "celery==5.2.7 (异步任务队列)"
    - "redis==4.1.0 (Redis客户端)"
    - "flower==1.2.0 (Celery监控)"
    - "schedule==1.1.0 (定时任务)"

  database:
    - "SQLAlchemy==1.4.32 (ORM核心)"
    - "alembic==1.9.1 (数据库迁移工具)"
    - "redis==4.1.0 (Redis客户端)"

  network:
    - "aiohttp==3.8.1 (异步HTTP客户端)"
    - "requests==2.28.0 (HTTP客户端)"
    - "websockets==9.1 (WebSocket客户端)"
    - "aiohttp-socks==0.7.1 (SOCKS代理支持)"

  utils:
    - "fire==0.4.0 (命令行工具)"
    - "Pillow==9.3.0 (图像处理)"
    - "python-dateutil==2.8.2 (日期处理)"
    - "cachetools==5.5.0 (缓存工具)"
    - "tqdm==4.64.0 (进度条)"

  web3:
    - "web3==5.29.2 (Web3接口)"
    - "eth-account==0.5.7 (以太坊账户)"
    - "eth-utils==1.10.0 (以太坊工具)"

  testing:
    - "selenium==4.3.0 (浏览器自动化)"
    - "pytest (测试框架)"

  development:
    - "black==21.12b0 (代码格式化)"
    - "autopep8==1.6.0 (代码格式化)"
    - "ipython==8.0.0 (交互式环境)"

flask_guidelines:
  - "使用 Flask 应用工厂模式以提高模块化和测试能力"
  - "使用 Flask 蓝图组织路由，实现更好的代码组织"
  - "使用 Flask-RESTful 构建基于类的 RESTful API"
  - "为不同类型的异常实现自定义错误处理程序"
  - "使用 Flask 的 before_request、after_request 和 teardown_request 装饰器"
  - "利用 Flask 扩展实现常用功能"
  - "使用 Flask 的 config 对象管理不同配置"
  - "使用 Flask 的 app.logger 实现日志记录"
  - "使用 Flask-JWT-Extended 处理认证和授权"

performance_optimization:
  - "使用 Flask-Caching 缓存频繁访问的数据"
  - "实现数据库查询优化技术（预加载、索引）"
  - "使用连接池管理数据库连接"
  - "实现适当的数据库会话管理"
  - "使用后台任务处理耗时操作（Celery）"
  - "使用 Redis 作为消息代理和结果后端"
key_conventions:
  context:
    - "适当使用 Flask 的应用上下文和请求上下文"
  performance:
    - "优先考虑 API 性能指标（响应时间、延迟、吞吐量）"
  structure:
    - "使用蓝图模块化应用"
    - "实现关注点分离（路由、业务逻辑、数据访问）"
    - "使用环境变量进行配置管理"

database:
  - "使用 Flask-SQLAlchemy 进行 ORM 操作"
  - "使用 Flask-Migrate 实现数据库迁移"
  - "正确使用 SQLAlchemy 的会话管理"

api_documentation:
  - "使用 Flask-RESTX 或 Flasgger 生成 Swagger/OpenAPI 文档"
  - "确保所有端点都有适当的请求/响应架构文档"

deployment:
  - "使用  uWSGI 作为 WSGI HTTP 服务器"
  - "在生产环境中实现适当的日志记录和监控"
  - "使用环境变量存储敏感信息和配置"

# 自动修复Trunk警告
- 自动修复Trunk警告:
  - 删除所有行尾空格
  - 删除多余空行，保持适当的逻辑分隔
  - 保持一致的缩进和代码对齐
  - 每次修改后立即检查并修复Trunk警告
  - 不超过3次修复循环
  - 不做未经确认的猜测性修复

# terminal_cmd_rules
terminal_cmd_rules:
  basic_rules:
    - "命令必须是单行,不允许包含换行符"
    - "如需分隔多行内容,使用 ' - ' 或其他分隔符"
    - "特别是git commit -m 命令,提交信息必须在一行内完成"
  format_examples:
    correct:
      - 'git commit -m "feat: 主要改动 - 详细信息1 - 详细信息2"'
    incorrect:
      - 'git commit -m "feat: 主要改动\n- 详细信息1\n- 详细信息2"'
  commit_message_guidelines:
    - "使用简短的标题概括主要改动"
    - "用 ' - ' 分隔详细说明"
    - "保持信息清晰且一目了然"
    - "避免过长的单行信息"

# gitmoji_guidelines
gitmoji_guidelines:
  commit_types:
    features:
      - "✨ :sparkles: 新功能"
      - "🎨 :art: 改进代码结构/格式"
      - "⚡️ :zap: 性能优化"
      - "♻️ :recycle: 代码重构"
      - "💄 :lipstick: UI/样式更新"
    fixes:
      - "🐛 :bug: 修复bug"
      - "🚑 :ambulance: 重要补丁"
      - "🔒 :lock: 修复安全问题"
      - "🩹 :adhesive_bandage: 简单修复"
    documentation:
      - "📝 :memo: 添加或更新文档"
      - "📚 :books: 添加或更新API文档"
      - "💡 :bulb: 添加或更新注释"
      - "📄 :page_facing_up: 添加或更新许可证"
    maintenance:
      - "🔧 :wrench: 修改配置文件"
      - "🔨 :hammer: 添加或更新开发脚本"
      - "🌱 :seedling: 添加或更新种子文件"
      - "🚀 :rocket: 部署相关"
      - "⬆️ :arrow_up: 依赖升级"
      - "⬇️ :arrow_down: 依赖降级"
      - "📌 :pushpin: 固定依赖版本"
      - "👷 :construction_worker: CI相关"
    testing:
      - "✅ :white_check_mark: 添加测试"
      - "🔍 :mag: 改进SEO"
      - "💫 :dizzy: 添加或更新动画和过渡"
    code_quality:
      - "🎨 :art: 改进代码结构/格式"
      - "🔥 :fire: 移除代码或文件"
      - "🔇 :mute: 移除日志"
      - "✏️ :pencil2: 修复拼写错误"
    security:
      - "🔒 :lock: 修复安全问题"
      - "🔐 :closed_lock_with_key: 添加或更新密钥"
    data:
      - "🗃️ :card_file_box: 数据库相关"
      - "💾 :floppy_disk: 添加或更新数据文件"
    docker:
      - "🐳 :whale: Docker相关"
      - "🔨 :hammer: 添加或更新开发脚本"
    breaking_changes:
      - "💥 :boom: 重大变更"
      - "🔨 :hammer: 添加或更新开发脚本"
  usage_rules:
    - "每个提交必须包含一个 emoji"
    - "emoji 放在提交信息最前面"
    - "emoji 后面跟一个空格"
    - "使用最能表达改动本质的 emoji"
    - "如果一个提交涉及多个类型，选择主要的类型"
  format_template: "{emoji} {type}: {message}"
  examples:
    - "✨ feat: 添加用户登录功能"
    - "🐛 fix: 修复用户注册验证码失效问题"
    - "📝 docs: 更新API文档"
    - "🎨 style: 优化代码格式"
    - "♻️ refactor: 重构数据处理逻辑"
    - "⚡️ perf: 优化查询性能"
    - "✅ test: 添加用户模块测试"
    - "🔧 chore: 更新开发配置"