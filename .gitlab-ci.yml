# This file is a template, and might need editing before it works on your project.
# Official language image. Look for the different tagged releases at:
# https://hub.docker.com/r/library/python/tags/
image: python:3.8.4

# Change pip's cache directory to be inside the project directory since we can
# only cache local items.
variables:
  PIP_CACHE_DIR: "$CI_PROJECT_DIR/.cache/pip"

# <PERSON><PERSON>'s cache doesn't store the python packages
# https://pip.pypa.io/en/stable/reference/pip_install/#caching
#
# If you want to also cache the installed packages, you have to install
# them in a virtualenv and cache it as well.
cache:
  paths:
    - .cache/pip
    - venv/
    - 

before_script:
  - python -V  # Print out python version for debugging
  - git clone --depth 1 --shallow-submodules http://darkedge.i234.me:44444/python/league-work.git
  - pip install -r requirements.txt
  # - pip install virtualenv
  # - virtualenv venv
  # - source venv/bin/activate


run:
  script:
    # - python setup.py bdist_wheel
    # an alternative approach is to install and run:
    # - pip install -r re
    # run the command here
    echo "hello"
  artifacts:
    paths:
      - dist/*.whl

