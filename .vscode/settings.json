{"python.defaultInterpreterPath": "/Users/<USER>/.pyenv/versions/league/bin/python", "python.testing.unittestArgs": ["-v", "-s", "./test", "-p", "test_*.py"], "python.testing.pytestEnabled": false, "python.testing.unittestEnabled": true, "files.associations": {"*.html": "jinja-py"}, "python.testing.promptToConfigure": true, "trunk.addToolsToPath": true, "trunk.numJobs": 4, "trunk.autoInit": false, "python-envs.pythonProjects": []}