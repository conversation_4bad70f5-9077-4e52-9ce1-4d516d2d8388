#!/usr/bin/python3
# -*- coding: utf-8 -*-

import json

import requests

from Unit.Logger import logger

"""
功能描述：后台推送api
编写人：darkedge
编写日期：2022年02月24日

"""


def barkSendMsg(title, body, deviceKey, groupName=None):
    try:
        url = "https://api.day.app/push"
        headers = {
            "Content-Type": "application/json",
        }
        data = {"title": title, "body": body, "device_key": deviceKey}

        if groupName:
            data["group"] = groupName

        # trunk-ignore(bandit/B113)
        response = requests.post(url, headers=headers, data=json.dumps(data))
        logger.debug(response.text)
    except Exception as e:
        logger.debug(e)
