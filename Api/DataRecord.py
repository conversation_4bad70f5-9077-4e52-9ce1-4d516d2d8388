# /bin/env python3
# -*- coding: UTF-8 -*- 
# trunk-ignore(ruff/F401)
from Unit.Redis import redisHelper,redisMgr,todayStr,tzDate,currentHour,isUTCMonday

import json


'''
功能描述：实现redis数据查询同步
编写人：darkedge
编写日期：2021年12月18日

步骤分析：
   
'''

def getUserInfo(email,keyPrefix="main",date=None):
    if date is None:
        date = todayStr()

    key = '%s_%s_%s' % (keyPrefix,date,email)
    info = redisHelper.getJson(key)
    if info:
        return info
    return {}

def updateUserInfo(email,keyPrefix="main",date=None,info=None):
    if date is None:
        date = todayStr()
    if info is None:
        info = {}
    key = '%s_%s_%s' % (keyPrefix,date,email)
    oldInfo = getUserInfo(email,keyPrefix,date)
    oldInfo.update(info)
    redisHelper.setJson(key,oldInfo)

def tabPrint(data,size=15):
    return print("\t".join(data))
    # l = ""
    # f = "%-" + ("%ds" % size)
    # for key in data:
    #     l += f % key
    # print(l)
class DataRecord:
    def __init__(self,email,keyPrefix="main",date=None,isEnable = True):
        if date is None:
            date = todayStr()
        self.userinfo = getUserInfo(email,keyPrefix,date)
        self.userinfo["email"] = email
        self.email = email
        self.keyPrefix = keyPrefix
        self.date = date
        self.isEnable = isEnable

    def get(self,key):
        return self.userinfo.get(key)

    def checkLoc(self,loc):
        if self.userinfo.get("loc"):
            if self.userinfo.get("loc") != json.dumps(loc):
                return True

        return False

    def commit(self):
        if not self.isEnable:
            return self
        updateUserInfo(self.email,self.keyPrefix,self.date,self.userinfo)
        return self
    
    def updateBan(self,ban):
        self.userinfo["ban"] = ban
        return self

    def updatePwd(self,pwd):
        self.userinfo["pwd"] = pwd
        return self

    def updateLoc(self,loc:list):
        self.userinfo["loc"] = json.dumps(loc)
        if self.isEnable:
            redisHelper.addMainLoc(loc)
        return self

    def updateLevel(self,level):
        self.userinfo["level"] = level
        return self
    
    def updateResources(self,resources):
        self.userinfo["resources"] = json.dumps(resources)
        return self

    def updateCrystal(self,crystal):
        self.userinfo["crystal"] = crystal
        return self

    def updateMintCount(self,count = 1):
        mintCount = self.get("mintCount")
        if mintCount:
            self.userinfo["mintCount"] += count
        else:
            self.userinfo["mintCount"] = count

        return self

    def updateSumTroopsCount(self,count):
        self.userinfo["sumTroopsCount"] = count
        return self

    def updateAttackCount(self,count = 1):
        attackCount = self.get("attackCount")
        if attackCount:
            self.userinfo["attackCount"] += count
        else:
            self.userinfo["attackCount"] = count
        return self

    def updateCaptchaCount(self,count = 1):
        captchaCount = self.get("captchaCount")
        if captchaCount:
            self.userinfo["captchaCount"] += count
        else:
            self.userinfo["captchaCount"] = count
        return self
    

def dataDisplay(keyPrefix="main",date=None):
    if date:
        return
    allRepairLocs = redisHelper.allRepairLocs()
    allMainLocs = redisHelper.allMainLocs()

    keys = redisMgr.keys('%s_*' % keyPrefix)
    infos = {}
    sumMints = {}

    for key in keys:
        key = key.decode('utf-8')
        info = redisHelper.getJson(key)
        key_parts = key.split('_')  # Rename the variable 'l' to 'key_parts'
        infoDate = key_parts[1]  # Update the variable name to 'key_parts'
        email = key_parts[2]  # Update the variable name to 'key_parts'
        if not infos.get(infoDate):
            infos[infoDate] = {}
        infos[infoDate][email] = info
        mintCount = info.get("mintCount")
        if not mintCount:
            mintCount = 0
        if sumMints.get(email):
            sumMints[email] += mintCount
        else:
            sumMints[email] = mintCount
    
    print("今日汇总：")
    titles = ["日期","封禁","邮箱","肉鸡","坐标","等级","粮食","木头","石头","金币","钻石","兵力","攻击次数","验证码次数","打包次数","打包总数","总资源"]
    descs = ["ban","email","loc","level","resources","crystal","sumTroopsCount","attackCount","captchaCount","mintCount"]

    # title = "\t".join(titles)
    tabPrint(titles)
    date = todayStr()
    disInfos = infos.get(date)
    effectiveQuantity = 0
    effectiveLocSum = 0

    sum = 0
    banSum = 0
    logInfos = []
    canPackageSum = 0
    captchaCountSum = 0

    if disInfos:
        effectiveQuantity = len(disInfos)
        for email in disInfos:
            resourceSum = 0
            canPackage = False
            info = disInfos[email]
            desc = []
            desc.append(date)
            # resourceSum = 0
            for key in descs:
                if key == "resources":
                    if info.get(key):
                        resources = info["resources"]
                        resources = json.loads(resources)
                        for resource in resources:
                            fr = resource / 1000 / 1000
                            desc.append("%0.2f" % fr)
                            resourceSum += resource
                            if fr > 14:
                                canPackage = True
                        # resourceSum += int(resource)
                elif key == "loc":
                    value = info.get(key)
                    nearLocSum = 0
                    for repairLoc in allRepairLocs:
                        if value[0] == 0:
                            break
                        elif int(repairLoc[0]) == value[0]:
                            if abs(int(repairLoc[1]) - int(value[1])) <= 3 and abs(int(repairLoc[2]) - int(value[2])) <= 3:
                                nearLocSum += 1
                                effectiveLocSum += 1

                    desc.append(str(nearLocSum))
                    desc.append(",".join([str(v) for v in value[1:]]))
                else:
                    value = info.get(key)
                    if not value:
                        value = 0
                    if key == "mintCount":
                        sum += value
                    elif key == "ban":
                        banSum += value
                    elif key == "captchaCount":
                        captchaCountSum += value
                    desc.append(str(value))
            desc.append(str(sumMints[email]))
            desc.append(str("%0.2f" % (resourceSum / 1000 / 1000)))
            
            tabPrint(desc)
            logInfos.append(desc)
            if canPackage:
                canPackageSum += 1
            
    # sum sumMints
    # sum = 0
    # for email in sumMints:
    #     sum += sumMints[email]
    locs = redisMgr.keys('loc_*')
    repairs = redisMgr.keys('repair_*')
    teleports = redisMgr.keys('teleport_*')

    t = tzDate().strftime("%m-%d_%H:%M")
    print(t)
    sumStr = "今日打包总计 %d 列表号数:%d 已Ban:%d 空缺位置:%ld 主号坐标:%ld 修复中:%ld 修复表:%ld 等飞:%ld\n" % (sum,effectiveQuantity,banSum,len(locs),len(allMainLocs),effectiveLocSum,len(repairs),len(teleports))
    sumStr += "可打包:%ld 打码次数 %d" % (canPackageSum,captchaCountSum)
    print(sumStr)

    fileName = "./log/%s.log" % t
    with open(fileName,'w+') as f:
        for log in logInfos:
            f.write("|".join(log) + "\n")
        f.write(sumStr + "\n")
    