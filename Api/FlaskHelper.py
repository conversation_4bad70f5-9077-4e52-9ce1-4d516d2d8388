#!/usr/bin/python3
# -*- coding: utf-8 -*-

import asyncio
import json
import os

import httpx
import requests

from Unit.Logger import logger


"""
功能描述：洗脚城api
编写人：darkedge
编写日期：2022年05月01日

"""
mainUrl = "https://league.dachangge.com/"
envUrl = os.getenv("FLASK_HELPER_URL")

def setEnvUrl(url):
    global mainUrl
    mainUrl = url
    logger.info(f'使用环境变量URL:{mainUrl}')

if envUrl:
    setEnvUrl(envUrl)

async def asyncSleep(seconds):
    """异步睡眠"""
    await asyncio.sleep(seconds)

def updateToken(token):
    """更新token"""
    try:
        res = requests.post(mainUrl + "api/mining/updatetoken", json={"token": token})
        if res.status_code == 200:
            logger.debug(f"更新token成功:{res.json()}")
            return True
        else:
            return False
    except Exception as e:
        logger.error(f"updateToken 错误:{e}", exc_info=True)
        return False

def requestSuperGo(token, kingdomId, moId, count=25):
    """
    洗脚城撤回指令
    """
    data = {
        "token": token,
        "kingdomId": kingdomId,
        "moId": moId,
        "count": str(count),
    }
    # logger.info(f'requestSuperGo: {data}')
    resCount = None
    try:
        res = requests.post(mainUrl + "normal/supergo", json=data, timeout=20)
        if res.status_code == 200:
            resJson = res.json()
            code = resJson.get("code")
            if code == 200:
                resCount = resJson["count"]
            else:
                msg = resJson.get("msg")
                logger.info(f"洗脚城撤回指令失败，msg:{msg}")
        else:
            logger.error(f"requestSuperGo 状态码错误:{res.status_code} {res.text}")
    except Exception as e:
        logger.error(f"requestSuperGo异常:{e}")

    return resCount


def requestToken():
    """
    洗脚城获取token
    """

    token = None
    try:
        res = requests.post(mainUrl + "normal/gettoken", timeout=20)
        if res.status_code == 200:
            resJson = res.json()
            code = resJson.get("code")
            if code == 200:
                token = resJson["token"]
                logger.info("洗脚城获取token成功")
            else:
                msg = resJson.get("msg")
                logger.info(f"洗脚城获取token失败，msg:{msg}")
        else:
            logger.error(f"requestToken 状态码错误:{res.status_code} {res.text}")
    except Exception as e:
        logger.error(f"requestToken:{e}")

    return token


def requestTokens(count, isGoogle=False, relay=0):
    """
    洗脚城获取token
    """

    tokens = None
    try:
        res = requests.post(
            mainUrl + "normal/gettokens", json={"count": count, "isGoogle": int(isGoogle)}, timeout=120
        )
        if res.status_code == 200:
            resJson = res.json()
            code = resJson.get("code")
            if code == 200:
                tokens = resJson["tokens"]
                logger.info(f"洗脚城获取tokens成功，token:{len(tokens)}")
            else:
                msg = resJson.get("msg")
                logger.info(f"洗脚城获取tokens失败，msg:{msg}")
        else:
            logger.error(f"requestTokens 状态码错误:{res.status_code} {res.text}")
    except Exception as e:
        logger.error(f"requestTokens:{e}")
        if relay < 3:
            relay += 1
            tokens = requestTokens(count, isGoogle=isGoogle, relay=relay)

    return tokens


async def requestTokenA():
    """
    洗脚城获取token 异步版本
    """

    token = None
    client = httpx.AsyncClient(timeout=20)
    try:
        response = await client.post(mainUrl + "normal/gettoken")
        res: httpx.Response = response
        if res.status_code == 200:
            resJson = res.json()
            code = resJson.get("code")
            if code == 200:
                token = resJson["token"]
            else:
                msg = resJson.get("msg")
                logger.info(f"洗脚城获取token失败，msg:{msg}")
    except Exception:
        pass

    await asyncSleep(0.01)
    await client.aclose()
    return token


def requestLogWrite(title, msg, type=1, relay=0):
    """
    日志写入接口
    :param title: 日志标题
    :param msg: 日志内容
    :param type: 日志类型，参考LogRecordModel.typeList:
        0: 默认
        1: 系统
        2: 告警
        3: 资源
    :param relay: 重试次数
    """
    try:
        res = requests.post(
            mainUrl + "api/log/write",
            json={"name": title, "msg": msg, "type": type},
            timeout=20,
        )
        if res.status_code == 200:
            resJson = res.json()
            code = resJson.get("code")
            if code == 200:
                logger.info("日志上报成功")
            else:

                logger.info(f"日志上报失败{code}")
        else:
            logger.error(f"requestLogWrite 状态码错误:{res.status_code} {res.text}")
    except Exception as e:
        logger.error(f"requestLogWrite:{e}")
        if relay < 3:
            relay += 1
            requestLogWrite(title=title, msg=msg, type=type, relay=relay)


async def requestRentClaimA(address):
    """
    租聘收集 异步版本
    """

    client = httpx.AsyncClient(timeout=20)
    result = False
    try:
        response = await client.post(
            "https://api-lok-beta.leagueofkingdoms.com/api/drago/rent/claim",
            data={"address": address},
        )
        res: httpx.Response = response
        if res.status_code == 200:
            resJson = res.json()
            result = resJson.get("result")
    except Exception:
        pass

    await asyncSleep(0.01)
    await client.aclose()
    return result


def requestDragoInventory(address, includeRent=True):
    try:
        response = requests.post(
            url="https://lok-nft.leagueofkingdoms.com/api/drago/inventory",
            headers={
                "Content-Type": "application/json; charset=utf-8",
            },
            data=json.dumps({"address": address, "includeRent": includeRent}),
        )
        jsonValue = response.json()
        myDragos = jsonValue.get("myDragos")
        return myDragos
        # if myDragos:
        #     for v in myDragos:
        #         filter = v['filter']
        #         parts = filter['parts']
        #         dark = parts['dark']
        #         if dark == 7:
        #             tokenId = v['tokenId']
        #             level = v['level']
        #             if level > 4:
        #                 print(f'编号:{tokenId} 等级:{level}\n')
    except requests.exceptions.RequestException:
        print("HTTP Request failed")


def requestSaveUser(appleSubId, deviceInfo, fakeIP=None, relay=0, socks5=None):
    """
    保存用户
    """

    try:
        data = {
            "appleSubId": appleSubId,
            "deviceInfo": deviceInfo,
        }
        if fakeIP:
            data["fakeIP"] = fakeIP
        proxies = None
        if socks5:
            proxies = {"https": f"socks5://{socks5}"}
        res = requests.post(
            mainUrl + "api/user/save", json=data, timeout=30, proxies=proxies
        )
        if res.status_code == 200:
            resJson = res.json()
            code = resJson.get("code")
            if code == 200:
                return True
            else:
                if code == 201:
                    logger.error("requestSaveUser 参数异常")
        else:
            logger.error(f"requestTokens 状态码错误:{res.status_code} {res.text}")
    except Exception as e:
        logger.error(f"requestTokens:{e}")
        if relay < 3:
            relay += 1
            return requestSaveUser(
                appleSubId, deviceInfo, fakeIP, relay=relay, socks5=socks5
            )
    return False


def getTofunftSellOrderData(token_id):
    base_url = "https://tofunft.com/_next/data/OHa85d5qMR5H7ovb1Im8m/en/nft/polygon/0x9E8Ea82e76262E957D4cC24e04857A34B0D8f062/"

    url = f"{base_url}{token_id}.json?network=polygon&tokenContract=0x9E8Ea82e76262E957D4cC24e04857A34B0D8f062&tokenId={token_id}"

    headers = {
        "authority": "tofunft.com",
        "accept": "*/*",
        "accept-language": "zh-CN,zh;q=0.9",
        "cookie": "_ga_QD91RPQ1M1=GS1.1.1656041749.1.0.1656041754.0; _ga=GA1.1.857985613.1651112380; _ga_ERYKG9YKMT=GS1.1.1701411490.83.1.1701413378.0.0.0",
        "referer": f"https://tofunft.com/nft/polygon/0x9E8Ea82e76262E957D4cC24e04857A34B0D8f062/{token_id}",
        "sec-ch-ua": '"Google Chrome";v="119", "Chromium";v="119", "Not?A_Brand";v="24"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"macOS"',
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
    }

    response = requests.get(url, headers=headers)
    json_data = response.json()

    # Extracting sellOrder from pageProps.data
    sell_order_data = (
        json_data.get("pageProps", {}).get("data", {}).get("sellOrder", {})
    )

    return sell_order_data


def requestMakeGlobalCrystalInfo(worldId):
    """
    生成全局水晶信息
    Args:
        worldId (int): 世界ID
    Returns:
        bool: 是否成功
    """
    try:
        data = {}
        if worldId:
            data["worldId"] = worldId

        res = requests.post(
            mainUrl + "api/zero/makeGlobalCrystalInfo",
            json=data,
            timeout=20
        )
        if res.status_code == 200:
            resJson = res.json()
            code = resJson.get("code")
            if code == 200:
                logger.info("生成全局水晶信息成功")
                return True
            else:
                logger.info(f"生成全局水晶信息失败，code:{code}")
        else:
            logger.error(f"requestMakeGlobalCrystalInfo 状态码错误:{res.status_code} {res.text}")
    except Exception as e:
        logger.error(f"requestMakeGlobalCrystalInfo异常:{e}")
    return False
