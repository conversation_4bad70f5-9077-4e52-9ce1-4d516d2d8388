# /bin/env python3
# -*- coding: UTF-8 -*-
"""
功能描述：谷歌验证识别
编写人：darkedge
编写日期：2021年12月25日
   
"""
import threading
import time

import requests

from Unit.Logger import logger

domain = "https://api.yescaptcha.com"
# domain = "http://127.0.0.1:8181"

# clientKey：在个人中心获取
clientKey = "0384121b1affc3294c664c373af36df91192a9231454"
# 目标参数：
websiteKey = "6Lf_r3IaAAAAAA2mnFVyE0U0ftYsmFEsEpDC9gcv"
# 目标参数：
websiteURL = "https://play.leagueofkingdoms.com/"
# 验证码类型：
task_type = "NoCaptchaTaskProxyless"

localGoogleCaptchaService = None


class GoogleCaptcha(object):
    """
    验证码并发类
    """

    def __init__(self, descAccounts, maxCount=5):
        self.captchaTokens = []
        self.useLock = threading.Lock()
        self.maxCount = maxCount
        self.descAccounts = descAccounts
        self.run()

    def setGlobal(self):
        global localGoogleCaptchaService
        localGoogleCaptchaService = self

    def getOneToken(self):
        """
        获取一个token
        """
        self.useLock.acquire()
        while len(self.captchaTokens) == 0:
            time.sleep(1)
        token = self.captchaTokens.pop()
        self.useLock.release()
        return token

    def run(self):
        """
        启动线程
        """
        threading.Thread(target=self.__run, daemon=True).start()

    def __run(self):
        """
        线程执行
        """
        while True:
            selfCount = len(self.captchaTokens)
            if selfCount < self.maxCount and len(self.descAccounts) + 1 > selfCount:
                try:
                    logger.info("后台获取验证码")
                    currentTime = time.time()
                    taskId = create_task()
                    if not taskId:
                        continue
                    recaptchaRes = get_response(taskId)
                    if recaptchaRes:
                        self.captchaTokens.append(recaptchaRes)
                        logger.info(
                            f"获取验证码完成 耗时{time.time() - currentTime} 当前可用{len(self.captchaTokens)}"
                        )
                except Exception as e:
                    logger.error(e)
            time.sleep(1)


def getBalance():
    url = f"{domain}/getBalance"
    data = {"clientKey": clientKey}
    result = requests.post(url, json=data).json()
    balance = result.get("balance")
    return balance


def create_task(taskParams=None) -> str:
    """
    第一步，创建验证码任务
    :param
    :return taskId : string 创建成功的任务ID
    """
    url = f"{domain}/createTask"
    data = {
        "clientKey": clientKey,
        "softID": 1447,
        "task": {
            "websiteURL": websiteURL,
            "websiteKey": websiteKey,
            "type": task_type,
            # "proxyType":"SOCKS5",
            # "proxyAddress":"***************",
            # "proxyPort":33024,
            # "proxyLogin":"jhc",
            # "proxyPassword":"123456",
            "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_8) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36",
        },
    }
    if taskParams:
        data["task"] = taskParams
    s = requests.session()
    s.keep_alive = False
    try:
        # 发送JSON格式的数据
        result = s.post(url, json=data, timeout=30).json()
        taskId = result.get("taskId")
        if taskId is not None:
            return taskId
        errorId = result.get("errorId")
        if errorId and errorId == -1:
            errorCode = result.get("errorCode")
            errorDescription = result.get("errorDescription")
            if errorDescription:
                logger.error(errorDescription)
            if errorCode == "ERROR_ZERO_BALANCE":
                time.sleep(99999999)
            elif errorCode == "ERROR_IP_BLOCKED_1MIN":
                time.sleep(60)

        logger.warning(result)

    except Exception as e:
        logger.error(e)
    finally:
        s.close()


def get_response(taskID: str):
    """
    第二步：使用taskId获取response
    :param taskID: string
    :return response: string 识别结果
    """

    # 循环请求识别结果，3秒请求一次
    times = 0
    s = requests.session()
    while times < 120:
        try:
            url = f"{domain}/getTaskResult"
            data = {"clientKey": clientKey, "taskId": taskID}
            result = requests.post(url, json=data, timeout=60).json()
            solution = result.get("solution", {})
            if solution:
                response = solution.get("gRecaptchaResponse")
                if response:
                    return response
            status = result.get("status", "")
            if status == "processing":
                logger.debug("处理中....")
            else:
                logger.error(result)
        except Exception as e:
            logger.error(e)
        finally:
            s.close()

        times += 3
        time.sleep(3)


def verify_website(response):
    """
    第三步：提交给网站进行验证
    :param response: string
    :return html: string
    """
    url = "https://www.google.com/recaptcha/api2/demo"
    data = {"g-recaptcha-response": response}
    r = requests.post(url, data=data)
    if r.status_code == 200:
        return r.text


if __name__ == "__main__":
    logger.info(getBalance())
