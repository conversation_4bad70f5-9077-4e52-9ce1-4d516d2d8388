#!/usr/bin/python3
# -*- coding: utf-8 -*-

import requests
from Unit.Logger import logger

localIps = []

def allocateIP():
    """获取国外动态ip"""
    if len(localIps) > 0:
        return localIps.pop()

    s = requests.session()
    s.keep_alive = False
    try:
        response = s.get(
            url="http://tiqu.ipidea.io:81/abroad",
            timeout=20,
            params={
                "num": "1",
                "type": "2",
                "lb": "1",
                "sb": "0",
                "flow": "1",
                "regions": "",
                "port": "2",
            },

        )
        jsonRes = response.json()
        if jsonRes:
            code = jsonRes.get("code")
            if code == 0:
                data = jsonRes.get("data")
                for item in data:
                    localIps.append(f'{item.get("ip")}:{item.get("port")}')
                return allocateIP()

            elif code == 113:
                logger.error("白名单检测")
                exit(0)
            else:
                logger.error(jsonRes)
                return None


    except requests.exceptions.RequestException:
        logger.error('HTTP Request failed')
    finally:
        s.close()

    return None

def myBalance():
    try:
        response = requests.get(
            url="https://api.ipidea.net/index/index/get_my_balance?neek=330827&appkey=b4f5b79fcc4e9b67aed61ae4a47c0036",
        )
        jsonRes = response.json()
        if jsonRes:
            code = jsonRes.get("code")
            if code == 0:
                return jsonRes.get("data")
    except Exception as e:
        logger.error(e)
    return None