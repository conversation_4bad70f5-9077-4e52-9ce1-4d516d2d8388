#!/usr/bin/python3
# -*- coding: utf-8 -*-
'''
功能描述：邮箱激活
编写人：darkedge
编写日期：2022年01月18日
   
'''

import re,random
from Api.UserInfo import UserInfo
from Unit.FileTool import loadS5List
from imbox import Imbox

class ImapService(object):
    def __init__(self):
        self.imbox = Imbox('mail.tg.mk','<EMAIL>','347506')
        self.s5List = loadS5List()
    
    def start(self):
        pass

    def returnSomeUserInfo(self,socks5=None,descEmail=None,autoActive=True):
        imbox = self.imbox
        unread_inbox_messages = imbox.messages(unread=True)

        for uid, message in unread_inbox_messages:
        # Every message is an object with the following keys
            # self.imbox.mark_seen(uid)
            sent_from = message.sent_from
            sendEmail = sent_from[0].get('email')
            if sendEmail == '<EMAIL>':
                sent_to = message.sent_to
                email = sent_to[0].get("email")
                if descEmail:
                    if descEmail != email:
                        continue
                body = message.body
                plain = body.get("plain")
                if plain:
                    text = plain[0]
                    res = re.search("https://api-lok", text)
                    if res:
                        startLoc = res.regs[0][0]
                        text = text[startLoc:]
                        sl = text.split('"')
                        url = sl[0]
                        if not autoActive:
                            return url,uid
                        if socks5 is None:
                            socks5 = random.choice(self.s5List)
                        user = UserInfo(email,"111111",socks5=socks5)
                        res = user.requestGet(url)
                        if res.status_code == 200:
                            self.makeSeemAndDelete(uid)
                            if res.text == "Your email is verified successfully.":                                
                                user.log("验证成功")
                                return user,uid
                                
                            else:
                                user.errorLog(f"验证失败 {res.text}")
                        else:
                            user.errorLog(f"验证失败 {res.status_code}")
            else:
                self.makeSeemAndDelete(uid)
            
        return None,None

    def makeSeemAndDelete(self,uid):
        self.imbox.mark_seen(uid)
        self.imbox.delete(uid)

    def checkResIsSucceed(self,res):
        return res == "Your email is verified successfully."

imapService = None#ImapService()