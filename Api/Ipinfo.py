#!/usr/bin/python3
# -*- coding: utf-8 -*-

import requests
import random

from Unit.Logger import logger
import time


def getIP(socks5: str = None):
    """
    获取IP https://ip.cn/api/index?ip=&type=0
    """
    try:
        proxies = None
        if socks5:
            proxies = {
                "http": "socks5://" + socks5,
                "https": "socks5://" + socks5,
            }

        r = requests.session()
        r.keep_alive = False
        res = r.get("https://ip.cn/api/index?type=0", proxies=proxies, timeout=15)
        r.close()
        return res.json().get("ip")
    except Exception:
        logger.error(f"socket:{socks5} getIP获取异常")
        # logger.error(f"socks5:{socks5} {e}", exc_info=True)
        return getIP2(socks5)
    return None


def getIP2(socks5: str = None):
    """
    获取IP
    """
    try:
        proxies = None
        if socks5:
            proxies = {
                "http": "socks5://" + socks5,
                "https": "socks5://" + socks5,
            }

        r = requests.session()
        r.keep_alive = False
        res = r.get("https://ipinfo.io/json", proxies=proxies, timeout=15).json()
        r.close()
        return res.get("ip")
    except Exception as e:
        logger.error(f"socket:{socks5} getIP2获取异常")
        # logger.error(f"socks5:{socks5} {e}", exc_info=True)
        raise e
    return None


def getIP3(socks5: str = None):
    """
    获取IP
    """
    try:
        proxies = None
        if socks5:
            proxies = {
                "http": "socks5://" + socks5,
                "https": "socks5://" + socks5,
            }

        r = requests.session()
        r.keep_alive = False
        res = r.get("http://icanhazip.com/", proxies=proxies, timeout=20)
        r.close()
        return res.text.strip()
    except Exception as e:
        logger.error(f"socks5:{socks5} {e}", exc_info=True)
        return getIP2(socks5)
    return None

def generate_public_ipv4():
    """生成公网IPv4地址"""
    while True:
        # 生成公网IPv4地址的第一个数字（1到223之间）
        first = random.randint(1, 223)

        # 生成公网IPv4地址的后三个数字（0到255之间）
        second = random.randint(0, 255)
        third = random.randint(0, 255)
        fourth = random.randint(0, 255)

        # 拼接生成的IPv4地址
        ip_address = f"{first}.{second}.{third}.{fourth}"

        # 检查生成的IPv4地址是否在私有地址范围内
        if not (
            (first == 10)
            or (first == 172 and 16 <= second <= 31)
            or (first == 192 and second == 168)
        ):
            return ip_address

def get_generate_204(socks5: str = None, hasIP = False):
    """
    获取代理延时 请求地址: http://www.qualcomm.cn/generate_204
    Keyword arguments:
    socks5 -- 代理
    Return: 延时时间 无效为-1
    """
    url = "http://www.qualcomm.cn/generate_204"
    # url = "https://socf-lok-live.leagueofkingdoms.com/socket.io/"
    try:
        proxies = None
        if socks5:
            proxies = {
                "http": "socks5://" + socks5,
                "https": "socks5://" + socks5,
            }

        r = requests.session()
        r.keep_alive = False
        start = time.time()
        res = None
        if hasIP:
            res = r.get("https://ipinfo.io/json", proxies=proxies, timeout=6)
        else:
            res = r.options(url, proxies=proxies, timeout=6)
        r.close()
        if res.status_code in [200,204,400]:
            if res.status_code == 200:
                return round(time.time() - start, 2),res.json().get("ip")
            return round(time.time() - start, 2),None
    except (requests.exceptions.Timeout, requests.exceptions.ConnectionError):
        logger.debug(f"socks5:{socks5} timeout")
    except Exception as e:
        logger.error(f"socks5:{socks5} {e}", exc_info=False)
    return -1,None
