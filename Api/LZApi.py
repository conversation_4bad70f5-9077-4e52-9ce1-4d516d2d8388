# /bin/env python3
# -*- coding: UTF-8 -*-

"""
功能描述：实现联众Api
编写人：darkedge
编写日期：2021年12月17日
   
"""

import base64
import json
import threading

import requests

from Unit.Logger import logger

# 昌哥飞啊飞
secret = "Hsxd87wuV15vy7U0DbprNzxt7LW0f8f5lGTkUCo6"
softwareId = "28528"
username = "hongchang0526"
password = "Jhc829923@"
captchaType = "1009"

threadPool = threading.BoundedSemaphore(5)


# 上传base64的图片
# 返回 recognition 结果 captchaId id
def LZUpdate(imageBase64):
    threadPool.acquire()
    url = "https://v2-api.jsdama.com/upload"

    data = {
        "softwareId": softwareId,
        "softwareSecret": secret,
        "username": username,
        "password": password,
        "captchaType": captchaType,
        "captchaData": str(imageBase64, encoding="utf-8"),
    }

    headers = {"Content-Type": "application/json;charset=UTF-8"}

    s = requests.session()
    try:
        r = s.post(url, data=json.dumps(data), headers=headers)
        if r.status_code == 200:
            rJson = r.json()
            if rJson["code"] == 0:
                return rJson["data"]

        else:
            logger.error("LZUpdate error: %s" % r.text)
    finally:
        s.close()
        threadPool.release()

    return None


# 错码反馈
def LZReportError(captchaId):

    url = "https://v2-api.jsdama.com/report-error"

    data = {
        "softwareId": softwareId,
        "softwareSecret": secret,
        "username": username,
        "password": password,
        "captchaId": captchaId,
    }

    headers = {"Content-Type": "application/json;charset=UTF-8"}

    s = requests.session()
    _r = s.post(url, data=json.dumps(data), headers=headers)
    s.close()
    # if r.status_code == 200:
    #     rJson = r.json()
    #     if rJson['code'] == 0:
    #         return rJson['data']

    # else:
    #     logger.error("LZReportError error: %s" % r.text)

    return None


def checkPoints():
    url = "https://v2-api.jsdama.com/check-points"

    data = {
        "softwareId": softwareId,
        "softwareSecret": secret,
        "username": username,
        "password": password,
    }

    headers = {"Content-Type": "application/json;charset=UTF-8"}

    s = requests.session()
    r = s.post(url, data=json.dumps(data), headers=headers)
    if r.status_code == 200:
        rJson = r.json()
        if rJson["code"] == 0:
            return rJson["data"]

    else:
        logger.error("LZReportError error: %s" % r.text)

    return None


if __name__ == "__main__":
    with open("captcha/<EMAIL>", "rb") as f:
        imageBase64 = base64.b64encode(f.read())
        LZUpdate(imageBase64)
