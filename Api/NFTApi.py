# /bin/env python3
# -*- coding: UTF-8 -*- 

'''
功能描述：nft查询Api
编写人：darkedge
编写日期：2022年07月26日
   
'''

import requests
from Unit.Logger import logger
# from Unit.FileTool import loadS5List
nftMainUrl = "https://api-lok-beta.leagueofkingdoms.com/"

def getTofunftSkin(priceMax=39):
    # tofunft皮肤
    # GET https://tofunft.com/_next/data/x2Re6Nox225NHfNDiK8py/en/collection/league-of-kingdoms-skin-polygon/items.json

    try:
        response = requests.get(
            url="https://tofunft.com/collection/league-of-kingdoms-skin-polygon/items?category=fixed-price&currency=0x0000000000000000000000000000000000000000&priceMax=39&sort=price_asc",            
            proxies={"http": "socks5://jj:<EMAIL>:36049"}
        )
        if response.status_code == 200:
            return response.json()
        else:
            print(response.status_code)
    except requests.exceptions.RequestException:
        print('HTTP Request failed')

def requestRentDirect(address,rentAddress,signed,tokenId,expireTime=7776000,shareRatio="9"):
    '''
    龙租
    '''

    try:
        url = nftMainUrl + 'api/drago/rent'
        data = {
            'address': address,
            'rentAddress': rentAddress,
            'signed': signed,
            'tokenId': tokenId,
            'expireTime': expireTime,
            'shareRatio': shareRatio
        }
        res = requests.post(url, json=data, timeout=20)
        if res.status_code == 200:
            resJson = res.json()
            if resJson["result"]:
                return True
            else:
                logger.info(f'租聘失败:{resJson}')
                return False
        else:
            logger.error(f'requestRentDirect 状态码错误:{res.status_code} {res.text}')
    except Exception as e:
        logger.error(f'requestRentDirect:{e}')


def requestRentCancel(address,rentAddress,signed,tokenId):
    '''
    龙租取消
    '''

    try:
        url = nftMainUrl + 'api/drago/rent/cancel'
        data = {
            'address': address,
            'rentAddress': rentAddress,
            'signed': signed,
            'tokenId': tokenId
        }
        res = requests.post(url, json=data, timeout=20)
        if res.status_code == 200:
            resJson = res.json()
            if resJson["result"]:
                return True
            else:
                logger.info(f'取消租聘失败:{resJson}')
                return False
        else:
            logger.error(f'requestRentCancel 状态码错误:{res.status_code} {res.text}')
    except Exception as e:
        logger.error(f'requestRentCancel:{e}')

if __name__ == '__main__':
    print(getTofunftSkin())