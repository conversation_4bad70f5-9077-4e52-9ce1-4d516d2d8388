#!/usr/bin/python3
# -*- coding: utf-8 -*-
"""
功能描述：推送功能API
编写人：darkedge
编写日期：2022年02月17日
   
"""
import json
import secrets
import requests

from Unit.DeviceInfo import (
    createChromeNotificationIdentifier,
    createIOSNotificationIdentifier,
)
from Unit.Logger import logger


class OneSignalApi(object):
    """推送Api"""

    def createPlayer(self, OS, proxies):
        """制造一个用户"""
        identifier = None
        device_type = 0
        device_os = ""
        device_model = f"iPhone10,{secrets.randbelow(3) + 1}"
        data = {
            "sdk": "151513",
            "app_id": "fae6e7cd-0a5e-4405-b04f-04d1f16a0da3",
            "timezone_id": "Asia/Shanghai",
            "notification_types": 1,
            "timezone": 28800,
            "language": "zh-Hans",
            # "identifier": "7abcd558f29d0b1f048083e2834ad8ea4b3d87d8ad9c088b33c132706ff445f1",
            # "device_type": 0,
            # "device_os": 98,
            # "device_model": "MacIntel"
        }

        if OS[:3] == "iOS":
            identifier = createIOSNotificationIdentifier()
            device_os = OS[4:]
        else:
            identifier = createChromeNotificationIdentifier()
            device_type = 5
            device_os = secrets.randbelow(10) + 97
            device_model = "MacIntel"
            data["web_auth"] = "Ae1h4NTMl3ZLzqj0DnXLig=="
            data["web_p256"] = (
                "BCZLajctQCK7jmHS1ce9TxQegDzxvHo0u8G951UKDVWZ0Gwh+vEMJZLS63jcyEffgmzQlJ/6r8caEpFLtBFLUnk="
            )

        data["identifier"] = identifier
        data["device_type"] = device_type
        data["device_os"] = device_os
        data["device_model"] = device_model

        s = requests.session()
        try:
            r = s.post(
                "https://onesignal.com/api/v1/players",
                headers={
                    "Content-Type": "application/json; charset=utf-8",
                },
                data=json.dumps(data),
                proxies=proxies,
                timeout=15,
            )
            if r.status_code == 200:
                rJson = r.json()
                if rJson["success"]:
                    return rJson["id"]

            else:
                logger.error("OneSignalApi error: %s" % r.text)
        except Exception:
            pass
        finally:
            s.close()
        return None


onceSignalApi = OneSignalApi()
