#!/usr/bin/python3
# -*- coding: utf-8 -*-

import requests

from Unit.Logger import logger

ProxyKey = "RDWYELQ1"
ProxyPwd = "D0609F88737F"


def changeLocalKey(key, pwd):
    """切换账号"""
    global ProxyKey, ProxyPwd
    ProxyKey = key
    ProxyPwd = pwd


def changeKeyTo1():
    changeLocal<PERSON>ey("4FR27QSY", "6A4F2B535A98")


def changeKeyTo2():
    changeLocalKey("580RVNW6", "6044808011E4")


def changeKeyTo3():
    changeLocal<PERSON>ey("A3328A6A", "CE59F21F51AC")


def changeKeyTo4():
    changeLocal<PERSON>ey("FL9MVIEG", "4A0E25BDF7FD")


def createRealSocks5(socks5):
    return f"{ProxyKey}:{ProxyPwd}@{socks5}"


def allocateIP(tmpKey=None, params=None):
    # 获取代理
    # GET https://proxy.qg.net/allocate
    if not tmpKey:
        tmpKey = ProxyKey
    if params is None:
        params = {
            "Key": tmpKey,
        }
    else:
        params["Key"] = tmpKey

    s = requests.session()
    s.keep_alive = False
    try:
        response = s.get(
            url="https://proxy.qg.net/allocate",
            params=params,
            timeout=20,
        )
        jsonRes = response.json()
        if jsonRes:
            code = jsonRes.get("Code")
            if code == 0:
                data = jsonRes.get("Data")
                return data[0].get("host")
            elif code == -102:
                logger.error("哥哥快去充钱!")

    except requests.exceptions.RequestException:
        logger.error("HTTP Request failed")
    finally:
        s.close()

    return None


def whiteAdd(ip: str):
    # 白名单添加
    # POST https://proxy.qg.net/whiteAdd
    # 参数：
    # Key: 08ZJT6QC
    # IP:

    try:
        response = requests.get(
            url="https://proxy.qg.net/whitelist/add",
            params={
                "Key": ProxyKey,
                "IP": ip,
            },
        )
        jsonRes = response.json()
        if jsonRes:
            code = jsonRes.get("Code")
            if code == 0:
                return True

    except Exception as e:
        logger.error(e)
    return False


def quota():
    """
    通道配额
    """

    # GET https://proxy.qg.net/quota
    # 参数：
    # Key: 08ZJT6QC
    # IP:

    try:
        response = requests.get(
            url="https://proxy.qg.net/info/quota",
            params={
                "Key": ProxyKey,
            },
        )
        jsonRes = response.json()
        if jsonRes:
            code = jsonRes.get("Code")
            if code == 0:
                return jsonRes

    except Exception as e:
        logger.error(e)
    return {}
