# /bin/env python3
# -*- coding: UTF-8 -*-

"""
功能描述：实现图鉴Api
编写人：darkedge
编写日期：2022年02月10日
   
"""

import base64
import json

# logger = None
import threading

import requests

from Unit.Logger import logger

# 昌哥飞啊飞
username = "ljh740"
password = "dGZwKBb3f7if"
typeid = "1"

threadPool = threading.BoundedSemaphore(5)


# 上传base64的图片
# 返回 recognition 结果 captchaId id
def LZUpdate(imageBase64):
    threadPool.acquire()
    url = "http://api.ttshitu.com/predict"

    data = {
        "username": username,
        "password": password,
        "typeid": typeid,
        "image": imageBase64.decode(),
    }

    headers = {"Content-Type": "application/json;charset=UTF-8"}

    s = requests.session()
    try:
        r = s.post(url, json=data, headers=headers)
        if r.status_code == 200:
            rJson = r.json()
            if rJson["success"]:
                data = rJson["data"]
                codeRes = {
                    "recognition": data["result"],
                    "captchaId": data["id"],
                }
                return codeRes
            else:
                logger.error("LZUpdate error: %s" % r.text)
        else:
            logger.error("LZUpdate error: %s" % r.text)
    finally:
        s.close()
        threadPool.release()

    return None


# 错码反馈
def LZReportError(captchaId):

    url = "http://api.ttshitu.com/reporterror.json"

    data = {"id": captchaId}

    headers = {"Content-Type": "application/json;charset=UTF-8"}

    s = requests.session()
    _r = s.post(url, data=json.dumps(data), headers=headers)
    s.close()
    # if r.status_code == 200:
    #     rJson = r.json()
    #     if rJson['code'] == 0:
    #         return rJson['data']

    # else:
    #     logger.error("LZReportError error: %s" % r.text)

    return None


def checkPoints():
    url = "http://api.ttshitu.com/queryAccountInfo.json"

    data = {"username": username, "password": password}

    headers = {"Content-Type": "application/json;charset=UTF-8"}

    s = requests.session()
    r = s.post(url, data=json.dumps(data), headers=headers)
    if r.status_code == 200:
        rJson = r.json()
        logger.info(rJson)
        if rJson["code"] == "0":
            return rJson["data"]

    else:
        logger.error("LZReportError error: %s" % r.text)

    return None


if __name__ == "__main__":
    with open("captcha/captcha_0b5c0198-d5b5-797a-9172-a924277c616b.png", "rb") as f:
        imageBase64 = base64.b64encode(f.read())
        print(LZUpdate(imageBase64))
