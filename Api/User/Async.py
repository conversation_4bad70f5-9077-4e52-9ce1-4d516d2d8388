# /bin/env python3
# -*- coding: UTF-8 -*-

"""
功能描述：用户异步请求模块
编写人：darkedge
编写日期：2021年12月31日
   
"""
import asyncio
# trunk-ignore(ruff/F401)
import base64
# trunk-ignore(ruff/F401)
import datetime
# trunk-ignore(ruff/F401)
import json
import threading
# trunk-ignore(ruff/F401)
import time

import aiohttp
# trunk-ignore(ruff/F401)
from aiohttp_socks import ChainProxyConnector, ProxyConnector, ProxyType

# trunk-ignore(ruff/F401)
from Api.User.Request import UserRequest, domain, knownErrCodes
from Model.UserError import UserError

concurrentLock = threading.BoundedSemaphore(1)


class UserAsync(UserRequest):
    """异步模块"""
    loop = None
    __connector = None

    def __del__(self):
        if self.loop is not None:
            try:
                if self.loop.is_running():
                    self.loop.stop()
                if not self.loop.is_closed():
                    self.loop.close()
            except Exception as e:
                self.debuglog(f"UserAsync del error: {e}")
        if hasattr(super(), '__del__'):
            super().__del__()  # 调用父类的 __del__ 方法（如果存在）

    @property
    def connector(self):
        """获取transport"""
        if self.__connector is None:
            if self.socks5:
                return ProxyConnector.from_url(f"socks5://{self.socks5}")
        return None

    async def asyncSleep(self, seconds):
        """异步睡眠"""
        await asyncio.sleep(seconds, loop=asyncio.get_event_loop())

    async def asyncRequest(
        self, url, data=None, isLogin=False, reRequest=False, isB64=False
    ):
        """
        异步post请求
        """
        if data is None:
            data = {}
        if isLogin is False and self.isLogin is False:
            self.invalidStatu = True

        if self.isInvalid or reRequest > 3:
            return {"result": False}

        try:
            async with aiohttp.ClientSession(
                connector=self.connector,
                headers=self.header,
                timeout=aiohttp.ClientTimeout(total=30),
            ) as session:
                async with session.post(
                    url, data=self.progressData(url, data, isB64=isB64)
                ) as response:
                    status_code = response.status
                    if status_code == 200:
                        resJson = await response.json()
                        self.tryDebugRequestDetail(url, resJson, isAsync=True)
                        if resJson.get("result") is False:
                            err: dict = resJson.get("err")
                            if self.resultFailPreProgress(
                                err, url, data, True, isB64=isB64
                            ):
                                return await self.asyncRequest(
                                    url, data, isLogin, reRequest
                                )
                        return resJson
                    else:
                        self.debuglog(
                            "c请求异常: %s\n %s: %s" % (status_code, url, data)
                        )
                        await self.asyncSleep(5)
                        return await self.asyncRequest(
                            url, data, isLogin, reRequest + 1
                        )
        except UserError as e:
            # 用户异常抛出
            self.invalidStatu = True
            raise e
        except Exception as e:
            self.errorLog(e, exc_info=True)
            self.debuglog("请求失败 重试error:%s %s\n" % (url, e))
            await self.asyncSleep(5)
            return await self.asyncRequest(url, data, isLogin, reRequest + 1)

    async def asyncRequestGet(self, url):
        """异步get请求"""
        if self.isInvalid:
            return None

        try:
            async with aiohttp.ClientSession(
                connector=self.connector,
                headers=self.header,
                timeout=aiohttp.ClientTimeout(total=30),
            ) as session:
                async with session.get(url) as response:
                    status_code = response.status
                    if status_code == 200:
                        response: aiohttp.ClientResponse = response
                        return response
        except Exception as e:
            self.errorLog(e, exc_info=True)
            self.debuglog("请求失败 get error:%s %s\n" % (url, e))
            return None

    def runTasks(self, tasks):
        if len(tasks):
            if self.loop is None:
                try:
                    self.loop = asyncio.get_event_loop()
                except Exception:
                    self.loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(self.loop)

            concurrentLock.acquire()
            res = self.loop.run_until_complete(asyncio.wait(tasks))
            concurrentLock.release()
            return res
        else:
            return []
