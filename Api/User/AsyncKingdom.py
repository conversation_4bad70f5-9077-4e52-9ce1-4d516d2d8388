# /bin/env python3
# -*- coding: UTF-8 -*-

"""
功能描述：用户城堡内异步接口
编写人：darkedge
编写日期：2021年12月31日
"""


from Api.User.AsyncX import UserAsync, domain
from Model.UserError import UserError

domainNew = domain


class UserAKingdom(UserAsync):
    """异步接口"""

    async def eventClaimA(self, eventId, eventTargetId, code):
        """领取事件奖励异步"""
        if self.isInvalid:
            return False
        data = {"eventId": eventId, "eventTargetId": eventTargetId, "code": code}
        res = await self.asyncRequest(domainNew + "api/event/claim", data)
        if res and res.get("result"):
            self.log("领取事件奖励a")
            self.bugCount += 1
            return res
        return None

    async def mailClaimA(self, mailId):
        """
        领取邮件
        """
        if self.isInvalid:
            return

        res = await self.asyncRequest(
            domainNew + "api/mail/claim/items", {"mailId": mailId}
        )
        if res and res.get("result"):
            self.log("领取邮件奖励a")
            self.bugCount += 1
            return res
        return None

    # 购买vip商品 10101008 vip经验100 4种资源 10101017 10101026 10101035 10101044
    async def vipshopBuyA(self, code, amount, dl=0):
        if self.isInvalid:
            return None

        if amount is None:
            amount = 1

        data = {
            "code": code,
            "amount": amount,
        }
        await self.asyncSleep(dl / 2.0)
        res = await self.asyncRequest(
            domainNew + "api/kingdom/vipshop/buy", data, isB64=True
        )
        if res and res.get("result"):
            self.log("购买vip商品a")
            self.bugCount += 1
            return res
        return None

    async def caravanBuyA(self, caravanItemId, transport=None):
        """商人购买"""
        if self.isInvalid:
            return None

        data = {
            "caravanItemId": caravanItemId,
        }
        res = await self.asyncRequest(
            domainNew + "api/kingdom/caravan/buy", data, transport=transport
        )
        if res and res.get("result"):
            self.log("购买商品a")
            self.bugCount += 1
            return res
        return None

        # 免费宝箱

    async def itemFreechestA(self, type):
        if self.isInvalid:
            return None

        data = {
            "type": type,
        }
        res = await self.asyncRequest(
            domainNew + "api/item/freechest", data, isB64=True
        )
        if res and res.get("result"):
            self.log("免费宝箱a")
            # trunk-ignore(ruff/F841)
            freeChest = res.get("freeChest")
            return res
        return None

    # vip信息
    async def vipInfoA(self):
        if self.isInvalid:
            return None

        res = await self.asyncRequest(domainNew + "api/kingdom/vip/info")
        if res and res.get("result"):
            self.log("vip信息a")
            vip = res.get("vip")
            self.vipPoint = vip.get("point")
            level = vip.get("level")
            self.vip = level

            return res
        return None

    # api/kingdom/treasure/equip
    async def treasureEquipA(self, itemId, equip):
        """宝物装备"""
        if self.isInvalid:
            return None

        data = {
            "itemId": itemId,
            "equip": equip,
        }
        res = await self.asyncRequest(domainNew + "api/kingdom/treasure/equip", data)
        if res and res.get("result"):
            self.log("宝物装备a")
            self.bugCount += 1
            return res
        return None

    async def treasureExchangeA(self, itemCode, amount):
        """换碎片"""
        # api/kingdom/treasure/exchange
        if self.isInvalid:
            return False

        res = await self.asyncRequest(
            domainNew + "api/kingdom/treasure/exchange",
            {"itemCode": itemCode, "amount": amount},
            isB64=True,
        )
        if res and res.get("result"):
            self.bugCount += 1
            return res
        return None

    async def requestStartA(self, marchType, toLoc, marchTroops, transport=None):
        """
        出兵接口
        """
        if self.isInvalid:
            return None

        data = {
            "fromId": self.fieldObjectId,
            "toLoc": toLoc,
            "marchTroops": marchTroops,
            "marchType": marchType,
        }
        res = await self.asyncRequest(
            domainNew + "api/field/march/start", data, transport=transport, isB64=True
        )
        return res

    async def fieldMarchReturnA(self, moId, needKingdomId=True):
        """出兵返回"""
        if self.isInvalid:
            return None

        data = {
            "moId": moId,
        }
        if needKingdomId:
            data["kingdomId"] = self.kingdomId
        res = await self.asyncRequest(
            domainNew + "api/field/march/return", data, isB64=True
        )
        if res and res.get("result"):
            self.bugCount += 1
            return res
        return None

    async def shrineSupportReturnA(self, moId):
        """神殿支援返回"""
        if self.isInvalid:
            return None

        res = await self.asyncRequest(
            domainNew + "api/alliance/shrine/support/return",
            {"kingdomId": self.kingdomId, "moId": moId},
        )
        if res and res.get("result"):
            self.bugCount += 1
            return res
        return None

    # api/kingdom/support/troops/return
    async def kingdomSupportReturnA(self, moId):
        """城堡支援返回"""
        if self.isInvalid:
            return None

        res = await self.asyncRequest(
            domainNew + "api/kingdom/support/troops/return", {"moId": moId}
        )
        if res and res.get("result"):
            self.bugCount += 1
            return res
        return None

    # 使用物品
    async def itemUseA(self, itemCode, amount):
        """
        10104130 T1士兵
        """
        if self.isInvalid:
            return None

        data = {
            "code": itemCode,
            "amount": amount,
        }
        res = await self.asyncRequest(domainNew + "api/item/use", data, isB64=True)
        if res and res.get("result"):
            return res
        return None

    # api/kingdom/treasure/page
    async def treasurePageA(self, page):
        """装备宝物"""
        if self.isInvalid:
            return None

        data = {
            "page": page,
        }
        res = await self.asyncRequest(domainNew + "api/kingdom/treasure/page", data)
        if res and res.get("result"):
            return res
        return None

    # api/skill/use
    async def skillUseA(self, code, targetId=None):
        """使用技能"""
        if self.isInvalid:
            return None

        res = await self.asyncRequest(
            domainNew + "api/skill/use",
            {"code": code, "targetId": targetId},
            reRequest=3,
            timeout=5,
        )
        if res and res.get("result"):
            self.bugCount += 1
            return res
        elif res.get("err"):
            code = res.get("err").get("code")
            if code and code == "yet_in_duration":
                self.errorLog(f"{code}技能cd中")
        return None

    async def treasureUpgradeA(self, itemId, skillIdx):
        """装备升级"""
        if self.isInvalid:
            return None

        data = {
            "itemId": itemId,
            "skillIdx": skillIdx,
        }
        res = await self.asyncRequest(
            domainNew + "api/kingdom/treasure/skill/upgrade", data, isB64=True
        )
        if res and res.get("result"):
            self.bugCount += 1
            return res
        return None

    # 打包资源
    # type 种类
    # 数量 对应1M 5M 10M
    async def mintA(self, type, num):
        if self.isInvalid:
            return False

        itemCodes = [
            [10101019, 10101020, 10101021],  # 粮食
            [10101028, 10101029, 10101030],  # 木头
            [10101037, 10101038, 10101039],  # 石头
            [10101046, 10101047, 10101048],  # 黄金
        ]

        itemCode = itemCodes[type][num]
        data = {"wallet": "walletconnect", "tokenType": "0", "itemCode": itemCode}
        res = await self.asyncRequest(domainNew + "api/nft/item/mint", data)
        if res:
            if res.get("result"):
                typeName = ["粮食", "木头", "石头", "黄金"]
                self.log("%s打包成功" % typeName[type])
                return True
            elif res.get("err"):
                code = res.get("err").get("code")
                if code:
                    if code == "mint_limit":
                        self.log("打包频繁")
                    elif code == "no_linked_wallet":
                        self.log("钱包未绑定")

        return False

    async def bugTrainTroopA(self, troopCode=50100101, delay=0):
        """制造bug"""
        if self.isInvalid:
            return None

        if delay > 0:
            await self.asyncSleep(delay)

        data = {"troopCode": troopCode, "amount": None}
        res = await self.asyncRequest(
            domainNew + "api/kingdom/barrack/train", data, reRequest=3, timeout=5
        )
        if res and res.get("result"):
            return True
        return False

    # vip领取
    async def vipClaimA(self):
        if self.isInvalid:
            return None

        res = await self.asyncRequest(domainNew + "api/kingdom/vip/claim", isB64=True)
        if res and res.get("result"):
            self.bugCount += 1
            self.debuglog("领取vip成功")
            return True
        return None

    # 收资源 104粮食 107 石头 108 金币 109 木头
    async def harvestA(self, position):
        if self.isInvalid:
            return None
        # if position == 105:
        #     # 跳过105兵营
        #     return True
        data = {"position": position}
        # self.randomSleep(2,5)
        res = await self.asyncRequest(
            domainNew + "api/kingdom/resource/harvest", data, isB64=True
        )
        if res and res.get("result"):
            self.bugCount += 1
            return True
            # resources = res.get("resources")
            # if resources:
            #     self.resources = resources
            # return True
        return False

    # 收集 5科技 105 兵
    async def claimA(self, position=105):
        if self.isInvalid:
            return None

        data = {"position": position}
        res = await self.asyncRequest(domainNew + "api/kingdom/task/claim", data)
        if res and res.get("result"):
            self.bugCount += 1
            return True
        return False

    # api/kingdom/task/cancel
    async def taskCancelA(self, taskId):
        if self.isInvalid:
            return False
        data = {"taskId": taskId}
        res = await self.asyncRequest(domainNew + "api/kingdom/task/cancel", data)
        if res and res.get("result"):
            self.bugCount += 1
            return True
        return False

        # 升级建筑

    async def buildingUpgradeA(self, position, level, instant=0):
        if self.isInvalid:
            return False

        data = {"position": position, "level": level, "instant": instant}
        res = await self.asyncRequest(
            domainNew + "api/kingdom/building/upgrade", data, isB64=True
        )
        if res and res.get("result"):
            return res
        elif res.get("err"):
            code = res.get("err").get("code")
            if code and code == "insufficient_resources":
                raise UserError.ininsufficientResources()
            elif code and code == "full_task":
                self.log("有队列任务，再等等。。垃圾服务器？")
            elif code and code == "not_enough_building":
                self.log("垃圾服务器吞了一口，没有建筑了。。。")
        return None

    # api/event/treasureHunt/spin
    async def treasureHuntSpinA(self, idx):
        """cvc神器活动"""

        if self.isInvalid:
            return

        res = await self.asyncRequest(
            domainNew + "api/event/treasureHunt/spin", {"idx": idx}
        )
        if res and res.get("result"):
            self.bugCount += 1
            return res
        return None

    # 礼包推荐信息
    async def recommendInfoA(self):
        if self.isInvalid:
            return None

        res = await self.asyncRequest(domainNew + "api/pkg/recommend")
        if res and res.get("result"):
            return res
        return None

    # 礼包列表
    async def pkgListA(self,pkgType=8):
        if self.isInvalid:
            return None
        data = {}
        if pkgType:
            data["pkgType"] = pkgType

        res = await self.asyncRequest(domainNew + "api/pkg/list",data=data)
        if res and res.get("result"):
            pkgs = res.get("pkgs")
            self.loadPkgs(pkgs)
            return pkgs
        return None

    # 任务列表
    async def taskAllA(self):

        res = await self.asyncRequest(domainNew + "api/kingdom/task/all", isB64=True)
        if res and res.get("result"):
            fieldTasks = res.get("fieldTasks")
            self.fieldTasks = fieldTasks
            return res
        return None

    # defaultlogin

    # api/chat/logs
    async def chatLogsA(self, chatChannel):
        """聊天频道"""
        if self.isInvalid:
            return None

        data = {"chatChannel": chatChannel}
        res = await self.asyncRequest(domainNew + "api/chat/logs", data)
        if res and res.get("result"):
            return res.get("chatLogs")
        return None

    async def requrestSetDeviceInfoA(self, pushId=None):
        """
        请求设置设备信息
        """
        deviceInfo = self.deviceInfo.copy()

        deviceInfo["pushId"] = pushId
        data = {"deviceInfo": deviceInfo}
        await self.asyncRequest(domainNew + "api/auth/setDeviceInfo", data)

    # 城墙信息
    async def wallInfoA(self):
        if self.isInvalid:
            return None

        res = await self.asyncRequest(domainNew + "api/kingdom/wall/info")
        if res and res.get("result"):
            wall = res.get("wall")
            if wall:
                durability = wall.get("durability")
                maxDurability = wall.get("maxDurability")
                return durability / maxDurability

        return None

        # 主线任务

    async def questMainA(self):
        if self.isInvalid:
            return False

        res = await self.asyncRequest(domainNew + "api/quest/main")
        if res and res.get("result"):
            return res
        return None

    # 物品列表
    async def itemListA(self):
        if self.isInvalid:
            return None

        res = await self.asyncRequest(domainNew + "api/item/list")
        if res and res.get("result"):
            items = res.get("items")
            if items:
                self.loadItemsInfo(items)
            return res
        return None

    async def treasureListA(self):
        """宝物列表"""
        if self.isInvalid:
            return False

        res = await self.asyncRequest(domainNew + "api/kingdom/treasure/list")
        if res and res.get("result"):
            self.checkTreasure(res.get("treasures", []))
            return res
        return None

    # 事件列表
    async def eventListA(self):
        if self.isInvalid:
            return False

        res = await self.asyncRequest(domainNew + "api/event/list")
        if res and res.get("result"):
            self.loadEvents(res.get("events"))
            return res
        return None

    async def cvcOpenA(self):
        if self.isInvalid:
            return False

        res = await self.asyncRequest(domainNew + "api/event/cvc/open")
        if res and res.get("result"):
            cvcEvent = res.get("cvcEvent")
            isOpen = cvcEvent.get("isOpen")
            self.cvcEventOpen = isOpen
            return True
        return False

    async def rouletteOpenA(self):
        if self.isInvalid:
            return False

        res = await self.asyncRequest(domainNew + "api/event/roulette/open")
        if res and res.get("result"):
            self.rouletteEvent = res.get("event")
            return True
        return False

    async def dragoLairListA(self):
        if self.isInvalid:
            return False

        res = await self.asyncRequest(domainNew + "api/drago/lair/list")
        if res and res.get("result"):
            return res
        return None

    # api/drago/fusion
    async def dragoFusionA(self, dragoId):
        """龙融合"""
        if self.isInvalid:
            return None
        data = {"dragoId": dragoId}

        res = await self.asyncRequest(domainNew + "api/drago/fusion", data)
        if res and res.get("result"):
            self.bugCount += 1
            return res
        return False

    # api/lord/mastery/train
    async def lordMasteryTrainA(self, code):
        if self.isInvalid:
            return False

        res = await self.asyncRequest(
            domainNew + "api/lord/mastery/train", {"code": code}
        )
        if res and res.get("result"):
            return res
        return None

    async def pkgDailyFreeClaimA(self, pkgId):
        """领取每日奖励"""
        if self.isInvalid:
            return None
        data = {"pkgId": pkgId}
        res = await self.asyncRequest(
            domainNew + "api/pkg/dailyFree/claim", data, reRequest=3, timeout=7
        )
        if res and res.get("result"):
            self.bugCount += 1
            return True
        return False

    def loginKingdomAsyncTasks(self):
        tasks = [
            self.wallInfoA(),
            self.questMainA(),
            self.itemListA(),
            self.treasureListA(),
            self.eventListA(),
            self.cvcOpenA(),
            self.rouletteOpenA(),
        ]
        self.runTasks(tasks)

    def enterKingdomAsyncTasks(self):
        tasks = [
            self.dragoLairListA(),
            self.recommendInfoA(),
            self.pkgListA(),
        ]
        self.runTasks(tasks)
        self.randomSleep(2)
        self.runTasks([self.taskAllA()])

    def anyOpenAsyncTasks(self):
        tasks = [self.cvcOpenA(), self.rouletteOpenA()]
        self.runTasks(tasks)
