# /bin/env python3
# -*- coding: UTF-8 -*-

"""
功能描述：用户异步请求模块
编写人：darkedge
编写日期：2021年12月31日
   
"""
import asyncio
# trunk-ignore(ruff/F401)
import base64
# trunk-ignore(ruff/F401)
import datetime
import json
import threading
# trunk-ignore(ruff/F401)
import time

import httpx
from httpx_socks import AsyncProxyTransport

# trunk-ignore(ruff/F401)
from Api.User.Request import UserRequest, domain, knownErrCodes
from Model.UserError import UserError

concurrentLock = threading.BoundedSemaphore(1)


class UserAsync(UserRequest):
    """异步模块"""
    loop = None
    needClose = False

    def __del__(self):
        if self.loop is not None and self.needClose:
            try:
                if self.loop.is_running():
                    self.loop.stop()
                if not self.loop.is_closed():
                    self.loop.close()
            except Exception as e:
                self.debuglog(f"UserAsync del error: {e}")
        if hasattr(super(), '__del__'):
            super().__del__()  # 调用父类的 __del__ 方法（如果存在）

    __transport = None

    @property
    def transport(self):
        """获取transport"""
        if self.__transport is None:
            if self.socks5:
                return AsyncProxyTransport.from_url(f"socks5://{self.socks5}")
        return None

    async def asyncSleep(self, seconds):
        """
        异步睡眠
        seconds: 睡眠秒数
        """
        await asyncio.sleep(seconds)

    async def asyncRequest(
        self,
        url,
        data=None,
        isLogin=False,
        reRequest=1,
        transport=None,
        timeout=15,
        isB64=False,
    ):
        """
        异步post请求
        """
        if data is None:
            data = {}
        if isLogin is False and self.isLogin is False:
            self.invalidStatu = True

        if self.isInvalid or reRequest > 3:
            return {"result": False}

        if transport is None:
            transport = self.transport

        client = httpx.AsyncClient(
            headers=self.header, transport=transport, timeout=timeout
        )
        try:
            self.asyncCount += 1
            response = await client.post(
                url, data=self.progressData(url, data, isB64=isB64)
            )
            # api = url[len(domain) :]
            res: httpx.Response = response
            if res.status_code == 200:
                resJson = None
                if res.headers["Content-Type"].find("application/json") < 0:
                    # resJson = json.loads(base64.b64decode(res.text).decode())
                    resJson = json.loads(self.decrypt(res.text))
                else:
                    resJson = res.json()
                self.tryDebugRequestDetail(url, resJson, isAsync=True)
                if resJson.get("result") is False:
                    err: dict = resJson.get("err")
                    if self.resultFailPreProgress(err, url, data, True, isB64=isB64):
                        return await self.asyncRequest(
                            url,
                            data,
                            isLogin=isLogin,
                            reRequest=reRequest,
                            transport=transport,
                            timeout=timeout,
                            isB64=isB64,
                        )
                return resJson
            else:
                if res.status_code in [403, 503, 504]:
                    return {"result": False}
                self.debuglog("a请求异常: %s\n %s: %s" % (res.status_code, url, data))
                await self.asyncSleep(5)
                return await self.asyncRequest(
                    url,
                    data,
                    isLogin=isLogin,
                    reRequest=reRequest + 1,
                    transport=transport,
                    timeout=timeout,
                    isB64=isB64,
                )
        except UserError as e:
            # 用户异常抛出
            self.invalidStatu = True
            raise e
        except Exception as e:
            self.debuglog("请求失败 重试error:%s %s\n" % (url, e))
            return {"result": False}
            # await self.asyncSleep(5)
            # return await return await self.asyncRequest(url, data, isLogin=isLogin, reRequest=reRequest+1,transport=transport,timeout=timeout,isB64=isB64)
        finally:
            await self.asyncSleep(0)
            await client.aclose()
            self.asyncCount -= 1

    async def asyncRequestGet(self, url):
        """异步get请求"""
        if self.isInvalid:
            return None

        client = httpx.AsyncClient(
            headers=self.header, transport=self.transport, timeout=20
        )
        try:
            response = await client.get(url)
            res: httpx.Response = response
            if res.status_code == 200:
                return res
            return None
        except Exception as e:
            self.debuglog("请求失败 get error:%s %s\n" % (url, e))
            return None
        finally:
            await self.asyncSleep(0)
            await client.aclose()

    def runTasks(self, tasks):
        """
        运行异步任务
        tasks: 任务列表，可以是协程或Task对象
        """
        if len(tasks):
            if self.loop is None:
                try:
                    self.loop = asyncio.get_event_loop()
                except Exception:
                    self.loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(self.loop)
                    self.needClose = True

            # 确保所有任务都是 Task 对象
            task_list = [
                task if isinstance(task, asyncio.Task)
                else self.loop.create_task(task)
                for task in tasks
            ]

            concurrentLock.acquire()
            try:
                res = self.loop.run_until_complete(asyncio.wait(task_list))
            finally:
                concurrentLock.release()
            return res
        else:
            return []
