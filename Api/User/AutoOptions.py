#!/usr/bin/env python3
# -*- coding: UTF-8 -*-

"""
功能描述：自动模块模块
编写人：darkedge
编写日期：2022年3月24日
   
"""

import json
import time

import Unit.enum as Enum
from Api.User.Development import Development
from Api.User.KingPower import UserKingPowerApi
from Unit.Redis import redisHelper, secondToDate
from Celery.redis import redisCeleryHelper
from Api.FlaskHelper import requestLogWrite
TreasureAttackPages = [
    10504015,  # 步兵神器
    10504004,  # 大宝剑
    10504010,  # 鞭子
    10504014,  # 弓兵神器
    10504009,  # 星星
    10504005,  # 心脏
    10503010,  # 小宝剑
    10503024,  # 龙刀
    10504003,  # 天盾
    10504013,  # 骑兵神器
]

TreasureCollectPages = [
    10504006,  # 女神
    10504013,  # 骑兵神器
    10504002,  # 跑鞋
    10503025,  # 龙戒
    10504012,  # 手套
    10504010,  # 鞭子
    10504011,  # 烧瓶
    10503013,  # 傀儡
    10504007,  # 自然
]

vipShopItemNames = {
    10101049: "能量10",
    10103002: "加速5分钟",
    10103004: "加速30m",
    10101051: "能量50",
    10103005: "加速1h",
    10103007: "加速8h",
    Enum.ITEM_CODE_ORB_OF_PORTAL_PIECE: "次元碎片",
    Enum.ITEM_CODE_PIECE_OF_LENGENDARY: "橙色碎片",
    Enum.ITEM_CODE_GOLD_CHEST: "黄金宝箱",
    Enum.ITEM_CODE_SPEEDUP_RESEARCH_1D: "研究1d",
    Enum.ITEM_CODE_SPEEDUP_TRAIN_1D: "增员1d",
    Enum.ITEM_CODE_RECOVER_1D: "治疗1d",
    Enum.ITEM_CODE_SPEEDUP_RESEARCH_3D: "研究3d",
    Enum.ITEM_CODE_SPEEDUP_TRAIN_3D: "增员3d",
    Enum.ITEM_CODE_RECOVER_3D: "治疗3d",
    Enum.ITEM_CODE_SPEEDUP_RESEARCH_7D: "研究7d",
    Enum.ITEM_CODE_RECOVER_7D: "治疗7d",
    Enum.ITEM_CODE_SPEEDUP_TRAIN_7D: "增员7d",
    Enum.ITEM_CODE_SPEEDUP_30D: "加速30d",
    Enum.ITEM_CODE_PLATINUM_CHEST: "白金宝箱",
    Enum.ITEM_CODE_VIP_10: "vip10",
    Enum.ITEM_CODE_VIP_100: "vip100",
    Enum.ITEM_CODE_VIP_500: "vip500",
}

filterVipShopItems = [
    Enum.ITEM_CODE_PIECE_OF_LENGENDARY,
    Enum.ITEM_CODE_GOLD_CHEST,
    Enum.ITEM_CODE_PLATINUM_CHEST,
    Enum.ITEM_CODE_SPEEDUP_TRAIN_1D,
    Enum.ITEM_CODE_RECOVER_1D,
    Enum.ITEM_CODE_SPEEDUP_RESEARCH_3D,
    Enum.ITEM_CODE_SPEEDUP_TRAIN_3D,
    Enum.ITEM_CODE_RECOVER_3D,
    Enum.ITEM_CODE_SPEEDUP_RESEARCH_7D,
    Enum.ITEM_CODE_RECOVER_7D,
    Enum.ITEM_CODE_SPEEDUP_TRAIN_7D,
    Enum.ITEM_CODE_VIP_500,
]

researchGradeMap = [
    {
        30103001: 10,
        30102034: 5,
    },
    {
        30101053: 1,
        30101054: 1,
        30101055: 1,
    },
    {
        30102044: 10,
    },
    {
        30101083: 5,
    },
    {
        30101069: 10,
    },
    {
        30103040: 10,
        30103037: 10,
        30103034: 10,
    },
    {30101083: 10},
]

researchGradeMap35 = [
    {
        30103001: 10,
        30102044: 5,
    },
    {
        30101064: 1,
    },
    {
        30102044: 10,
    },
    {
        30101083: 5,
    },
    {
        30101069: 10,
    },
    {
        30103040: 10,
        30103037: 10,
        30103034: 10,
    },
    {30101083: 10},
]

buildJson = None


# 读取build.json
def loadBuildJson(path="build.json"):
    global buildJson
    if buildJson is None:
        with open(path, "r", encoding="utf-8") as f:
            buildJson = json.loads(f.read())
    return buildJson


def freeBuildJson():
    global buildJson
    buildJson = None


# 获取建筑信息
def getBuildInfo(code, level):
    buildJson = loadBuildJson()
    for build in buildJson:
        if build.get("code") == code and build.get("level") == level:
            return build
    return None


resaerchJson = None


def loadResearchJson(path="research.json"):
    global resaerchJson
    if resaerchJson is None:
        with open(path, "r", encoding="utf-8") as f:
            resaerchJson = json.loads(f.read())
    return resaerchJson


def freeResearchJson():
    global resaerchJson
    resaerchJson = None


def getResearchInfo(code, level):
    resaerchJson = loadResearchJson()
    for research in resaerchJson:
        if research.get("code") == code and research.get("level") == level:
            return research
    return None


researchMiniMap = {}


def loadResearchMiniMap():
    if not researchMiniMap:
        if resaerchJson is None:
            loadResearchJson()
        for research in resaerchJson:
            code = research.get("code")
            level = research.get("level")
            if code not in researchMiniMap and level == 1:
                researchMiniMap[code] = research
    return researchMiniMap


class AutoOptions(Development,UserKingPowerApi):

    def importantVipList(self):
        """vip重要商品"""
        canBuys = []

        res = self.vipshopList()
        if res:
            vipShop = res.get("vipShop")
            if vipShop:
                items = vipShop.get("items")
                for item in items:
                    numRemain = item.get("numRemain")
                    code = item.get("code")
                    if self.isMaxTreasure:
                        if code in [
                            Enum.ITEM_CODE_PIECE_OF_LENGENDARY,
                            Enum.ITEM_CODE_GOLD_CHEST,
                        ]:
                            continue
                    if self.isMaxOrbPortal:
                        if code in [Enum.ITEM_CODE_ORB_OF_PORTAL_PIECE]:
                            continue
                    if self.level < 35:
                        if code in filterVipShopItems:
                            continue
                    if numRemain and numRemain > 0 and code in vipShopItemNames:
                        item["itemTitle"] = vipShopItemNames.get(code)
                        canBuys.append(item)
        return canBuys

    def autoBuyVipShopAll(self):
        """自动购买vip高级商品"""
        flag = redisHelper.getBuyVipFinish(self.key)
        if not flag:
            canBuys = self.importantVipList()
            if len(canBuys) > 0:
                for item in canBuys:
                    code = item.get("code")
                    amount = item.get("numRemain")
                    name = item.get("itemTitle")
                    canBuy = True
                    if code and amount:
                        needs = item.get("needs")
                        for need in needs:
                            needCode = need.get("code")
                            if needCode == 10100005:
                                needAmount = need.get("amount")
                                if self.crystal < needAmount * amount:
                                    self.log(f"当前砖石{self.crystal} 不足以购买{name}")
                                    canBuy = False
                                    break
                        if canBuy:
                            self.vipshopBuy(code, amount)
                            self.randomSleep(2, msg=f"购买vip商品:{name}")
            else:
                redisHelper.setBuyVipFinish(self.key)

    def autoOpenTreasureBox(self, minCount=1000):
        """自动开宝箱"""
        if self.itemsInfo and not self.isInvalid:
            items = [
                Enum.ITEM_CODE_SILVER_CHEST,
                Enum.ITEM_CODE_GOLD_CHEST,
                Enum.ITEM_CODE_PLATINUM_CHEST,
            ]
            itemNames = {
                Enum.ITEM_CODE_SILVER_CHEST: "白银宝箱",
                Enum.ITEM_CODE_GOLD_CHEST: "黄金宝箱",
                Enum.ITEM_CODE_PLATINUM_CHEST: "钻石宝箱",
            }

            for item in items:
                count = self.itemCount(item)
                if count > minCount:
                    res = self.itemUse(item, 40)
                    self.randomSleep(
                        2,
                        4,
                        msg=f'开宝箱{itemNames.get(item,"unknow")} {res is None and "失败" or "成功"} 剩余{count - 40}',
                    )
            if self.itemCount(Enum.ITEM_CODE_PLATINUM_CHEST) > 50:
                self.itemUse(Enum.ITEM_CODE_PLATINUM_CHEST, 1)
                self.randomSleep(2, 4, msg="开钻石宝箱")

    def autoSummon(self):
        """自动召唤"""
        if self.canSummon:
            self.canSummon = False
            self.skillUse(10023)
            self.randomSleep(2, 4, msg="召唤怪物")

    def autoBuildSkill(self):
        skills = self.unUseSkillList(isSuper=True)
        if skills:
            for skill in skills:
                code = skill.get("code")
                if code in [10021, 10022]:
                    self.skillUse(code)
                    self.randomSleep(2, 4, msg=f"使用技能{code}")

    def autoOpenResource(self):
        """ "自动吃资源"""
        items = [
            Enum.ITEM_CODE_FOOD_100K,
            Enum.ITEM_CODE_FOOD_500K,
            Enum.ITEM_CODE_LUMBER_100K,
            Enum.ITEM_CODE_LUMBER_500K,
            Enum.ITEM_CODE_STONE_100K,
            Enum.ITEM_CODE_STONE_500K,
            Enum.ITEM_CODE_GOLD_100K,
            Enum.ITEM_CODE_GOLD_500K,
            Enum.ITEM_CODE_VIP_10,
            Enum.ITEM_CODE_VIP_100,
            Enum.ITEM_CODE_VIP_500,
            Enum.ITEM_CODE_VIP_1K,
        ]

        self.itemList()
        for index, itemCode in enumerate(items):
            count = self.itemCount(itemCode)
            MAX_RESOURCE_VALUE = 4_000_000_000
            if self.level <= 30:
                MAX_RESOURCE_VALUE = 500_000_000
            if count > 0:
                realIndex = index // 2
                if realIndex < 4 and self.resources[realIndex] > MAX_RESOURCE_VALUE:
                    continue
                self.itemUse(itemCode, count)
                self.randomSleep(1, msg=f"使用物品:{itemCode} {count}")
        if self.level == 35 and self.resources[3] < 4_000_000_000:
            items = [
                Enum.ITEM_CODE_GOLD_1M,
                Enum.ITEM_CODE_GOLD_10M,
                Enum.ITEM_CODE_GOLD_100M,
            ]
            for itemCode in items:
                count = self.itemCount(itemCode)
                self.itemUse(itemCode, count)
                self.randomSleep(1, msg=f"使用物品:[研究]{itemCode} {count}")

    def tryUseRedDragon(self, count=1):
        """尝试使用红龙"""
        n = self.itemCount(Enum.ITEM_CODE_RED_DRAGON_EGG)
        if n >= count + 10:
            for _ in range(count):
                self.itemUse(Enum.ITEM_CODE_RED_DRAGON_EGG, 1)
                self.randomSleep(2, msg=f"使用红龙,剩余{n}")
            return True
        return False

    def autoTreasures(self, page: int, optionTreasures):
        res = self.treasureList()
        if res:
            pageId = res.get("page")
            equipped = res.get("equipped")
            treasures = res.get("treasures")
            if pageId != page:
                res = self.treasurePage(page)
                if res:
                    equipped = res.get("equipped")
                else:
                    self.log("切换装备失败？")
                    return

            needReEquipped = False
            for treasure in equipped:
                item = treasure.get("item")
                if item:
                    code = item.get("code")
                    if code in optionTreasures:
                        continue
                needReEquipped = True
                break

            if needReEquipped:
                for treasure in equipped:
                    item = treasure.get("item")
                    if item:
                        itemId = item.get("_id")
                        self.treasureEquip(itemId, False)
                        self.randomSleep(1, 2, msg=f"卸下装备:{item.get('code')}")

                itemCount = 0
                for itemCode in optionTreasures:
                    if itemCount >= 6:
                        break
                    maxLevel = min(itemCode // 1000 % 10 + 1, 5)
                    for treasure in treasures:
                        code = treasure.get("code")
                        level = treasure.get("level")
                        if itemCode != code or level < maxLevel:
                            continue
                        itemId = treasure.get("_id")
                        self.treasureEquip(itemId, True)
                        itemCount += 1
                        self.randomSleep(1, 2, msg=f"装备宝物:{code}")
                if itemCount != 6:
                    self.errorLog(f"切换装备失败:{itemCount} {page} {optionTreasures}")

            self.log("切换装备完成")
        else:
            self.log("获取装备失败")

    def autoCollectionEvent(self):
        dashBoard = self.gatheringDashBoard()
        if dashBoard:
            level = dashBoard.get("level")
            totalNeed = dashBoard.get("totalNeed")
            num = dashBoard.get("num")
            if level < 30 and num > totalNeed and num > 0 and totalNeed > 0:
                dashBoard = self.gatheringUse()
                if dashBoard:
                    level = dashBoard.get("level")
                    num = dashBoard.get("num")
                    self.log(f"收集事件完成{level}")
                    self.gatheringClaimAll()
            self.tmpParams["collectionLevel"] = level
            self.tmpParams["collectionCount"] = num
            if level >= 30:
                unlock = dashBoard.get("unlock")
                if unlock:
                    premium = unlock.get("premium")
                    cost = unlock.get("cost")
                    if premium is False:
                        if self.crystal > cost:
                            if self.gatheringUnlockPremium():
                                self.randomSleep(2,3)
                                res = self.gatheringClaimAll()
                                s = f'活动达成:{ res and "成功" or "失败"}'
                                self.barkNoti(s, isAdmin=True)
                                requestLogWrite("收集活动", self.name + s, type=0)
                            else:
                                self.errorLog("解锁Premium失败")
                                self.barkNoti("解锁Premium失败", isAdmin=True)
                                return False
                        else:
                            self.errorLog(f"解锁收集事件{level} 水晶不足")
                            self.barkNoti(f"解锁收集事件{level} 水晶不足", isAdmin=True)
                return True
        return False

    def autoLevelTo35(self):
        """自动升级到35级"""
        kingdomTasks = self.kingdomTasks()
        if kingdomTasks is None:
            return
        task1 = None
        task2 = None
        task3 = None
        kingdomCastle = None

        for task in kingdomTasks:
            if task.get("code") == 1:
                task1 = task
            if task.get("code") == 8:
                task2 = task
            if task.get("code") == 6:
                task3 = task
            param = task.get("param", {})
            buildingCode = param.get("buildingCode", 0)
            if buildingCode == Enum.BUILDING_CODE_MAP["castle"]:
                kingdomCastle = task

        if task1:
            self.log("检测到任务1 尝试加速")
            t = self.trySpeedUpUseItem(
                1, useAny=task1 == kingdomCastle, kingdomTasks=kingdomTasks, minTime=0
            )
            if t == 0:
                self.log("任务1加速完成")
                param = task1["param"]
                self.setBuildingLevel(param["position"], param["level"])
                task1 = None
            else:
                self.log(f"任务1加速失败 剩余时间{secondToDate(t)}")

        if task2:
            self.log("检测到任务2 尝试加速")
            t = self.trySpeedUpUseItem(
                8, useAny=task2 == kingdomCastle, kingdomTasks=kingdomTasks, minTime=0
            )
            if t == 0:
                self.log("任务2加速完成")
                param = task2["param"]
                self.setBuildingLevel(param["position"], param["level"])
                task2 = None
            else:
                self.log(f"任务2加速失败 剩余时间{secondToDate(t)}")

        if task3:
            self.log("检测到任务3 尝试加速")

            status = task3.get("status")
            if status == 3:
                if self.claim(5, training=True):
                    self.log("任务3领取成功")
                    param = task3["param"]
                    self.setResarchLevel(param["researchCode"], param["level"])
                    task3 = None
            elif status == 1:
                canSpeed = len(
                    list(filter(lambda x: x < 2_000_000_000, self.resources))
                )
                if canSpeed == 0:
                    t = self.trySpeedUpUseItem(3, kingdomTasks=kingdomTasks, minTime=0)
                    if t == 0:
                        self.log("任务3加速完成")
                        if self.claim(5, training=True):
                            self.log("任务3领取成功")
                            param = task3["param"]
                            self.setResarchLevel(param["researchCode"], param["level"])
                            task3 = None
                    else:
                        self.log(f"任务3加速失败 剩余时间{secondToDate(t)}")
                else:
                    self.log("资源不足不加速研究")
            else:
                self.log("任务3状态异常")

        for _ in [task1, task2]:
            if _ is not None:
                continue
            self.autoBuildSkill()
            # 遍历建筑等级 优先升级城堡外的建筑
            updateBuild = None
            for building in self.buildings:
                position = building.get("position")
                level = building.get("level")
                state = building.get("state")
                if level < self.level and state == 1 and position > 10:
                    updateBuild = building
                    break

            if not updateBuild:
                mainBuildRes = None
                if self.level < 35:
                    mainBuildRes = self.autoLevelUpBuild(
                        Enum.BUILDING_CODE_MAP["castle"],
                        self.getBuildingLevel(1) + 1,
                        1,
                    )

                if mainBuildRes is None:
                    academyLevel = self.getBuildingLevelWithCode(40100105)
                    academyBuild = self.getBuilding(40100105)
                    if (
                        academyLevel < 35
                        and self.level > academyLevel
                        and academyBuild.get("state") == 1
                    ):
                        self.log(f"学院等级低于{academyLevel}/35,优先升级学院")
                        self.autoLevelUpBuild(40100105, academyLevel + 1, 5)
                    else:
                        for building in self.buildings:
                            position = building.get("position")
                            level = building.get("level")
                            state = building.get("state")
                            code = building.get("code")
                            # 跳过联盟大厅和龙巢
                            if (
                                level < self.level
                                and state == 1
                                and position < 10
                                and code not in [40100110, 40100107]
                            ):
                                self.autoLevelUpBuild(code, level + 1, position)
                                break

            else:
                self.autoLevelUpBuild(
                    updateBuild.get("code"),
                    updateBuild.get("level") + 1,
                    updateBuild.get("position"),
                )

            time.sleep(5)

        if task3 is None:
            self.autoLevelResearchTo35()

        freeBuildJson()
        freeResearchJson()

    def autoLevelUpBuild(self, code, level, position):
        currentBuild = self.getBuilding(code)
        if currentBuild.get("state") != 1:
            if not self._kingdomTasks:
                self.enter()
            return
        buildInfo = getBuildInfo(code, level)
        need_code_5 = buildInfo.get("need_code_5")
        need_value_5 = buildInfo.get("need_value_5")
        if need_code_5 == 10104029 and need_value_5 > 0:
            # 检测是否足够
            self.itemList()
            if self.itemCount(need_code_5) >= need_value_5:
                pass
            elif self.crystal > 2000:
                if self.itemBuy(need_code_5, 1):
                    time.sleep(3)
                else:
                    self.log(f"购买{need_code_5} 失败")
                    return False
            else:
                if self.itemCount(Enum.ITEM_CODE_CRYSTAL_10) > 200:
                    if self.itemUse(Enum.ITEM_CODE_CRYSTAL_10, 200):
                        time.sleep(3)
                        if self.itemBuy(need_code_5, 1):
                            time.sleep(3)
                        else:
                            self.log(f"购买{need_code_5} 失败")
                            return False
                else:
                    self.log("水晶不足")
                    return False

        need_value_1 = buildInfo.get("need_value_1")
        need_value_2 = buildInfo.get("need_value_2")
        need_value_3 = buildInfo.get("need_value_3")
        need_value_4 = buildInfo.get("need_value_4")
        needResources = [need_value_1, need_value_2, need_value_3, need_value_4]
        for i in range(4):
            if needResources[i] > self.resources[i]:
                self.log(f"资源不足{i} {needResources[i]}")
                return False

        building_1 = buildInfo.get("building_1")
        building_level_1 = buildInfo.get("building_level_1")
        building_2 = buildInfo.get("building_2")
        building_level_2 = buildInfo.get("building_level_2")
        if building_2 != 0:
            building2 = self.getBuilding(building_2)
            building2Level = building2.get("level")
            if building2Level < building_level_2:
                self.log(f"{building_2} 等级不足 {building_level_2}")
                return self.autoLevelUpBuild(
                    building_2, building2Level, building2.get("position")
                )

        if building_1 != 0:
            building1 = self.getBuilding(building_1)
            building1Level = building1.get("level")
            if building1Level < building_level_1:
                self.log(f"{building_1} 等级不足 {building_level_1}")
                return self.autoLevelUpBuild(
                    building_1, building1Level, building1.get("position")
                )

        if self.buildingUpgrade(position, level):
            self.log(f"升级建筑{code}到{level}")
            return True
        else:
            self.log(f"升级建筑{code}失败 position:{position} level:{level}")
            return False

    def autoLevelResearchTo35(self):
        if self.researchGrade == 0:
            # 检测研究等级
            self.checkResearchLevel()
            self.log(f"研究等级:{self.researchGrade}")
        if self.researchGrade < 8 and self.researchGrade > 0:
            if self.level < 30:
                self.log(f"弟弟号暂停升级科技:{self.level} {self.researchGrade}")
            if self.level > 30 and self.level < 35 and self.researchGrade >= 2:
                self.log(f"弟弟号暂停升级科技:{self.level} {self.researchGrade}")
                return

            curResearchGradeMap = researchGradeMap
            descResearch = None
            isFull = False
            if self.level > 30:
                curResearchGradeMap = researchGradeMap35

            checkRange = range(self.researchGrade, self.researchGrade + 1)
            if self.researchGrade == 1:
                checkRange = range(1, 3)
            for index in checkRange:
                researches = curResearchGradeMap[index - 1]
                for researchCode, level in researches.items():
                    curLevel = self.getResearchInfoLevel(researchCode)
                    if level == 10:
                        upCode, _level = self.checkResearchFull(researchCode, level)
                        if upCode:
                            descResearch = upCode
                            isFull = True
                            break
                    if curLevel < level:
                        descResearch = researchCode
                        if level == 10:
                            isFull = True
                        break

                if descResearch is None:
                    self.checkResearchLevel()
                else:
                    if self.autoLevelResearch(
                        descResearch,
                        self.getResearchInfoLevel(descResearch) + 1,
                        isFull=isFull,
                    ):
                        break

    def autoLevelResearch(self, researchCode, level, isFull=False):
        if isFull:
            code, _level = self.checkResearchFull(researchCode, level)
            if code:
                return self.autoLevelResearch(code, _level)

        researchInfo = getResearchInfo(researchCode, level)

        pre_1 = researchInfo.get("pre_1")
        pre_1_level = researchInfo.get("pre_1_level")
        pre_2 = researchInfo.get("pre_2")
        pre_2_level = researchInfo.get("pre_2_level")
        pre_3 = researchInfo.get("pre_3")
        pre_3_level = researchInfo.get("pre_3_level")

        if pre_3:
            pre3Level = self.getResearchInfoLevel(pre_3)
            if pre3Level < pre_3_level:
                return self.autoLevelResearch(pre_3, pre_3_level, isFull=isFull)

        if pre_2:
            pre2Level = self.getResearchInfoLevel(pre_2)
            if pre2Level < pre_2_level:
                return self.autoLevelResearch(pre_2, pre_2_level, isFull=isFull)

        if pre_1:
            pre1Level = self.getResearchInfoLevel(pre_1)
            if pre1Level < pre_1_level:
                return self.autoLevelResearch(pre_1, pre_1_level, isFull=isFull)

        building_1 = researchInfo.get("building_1")
        building_level_1 = researchInfo.get("building_level_1")
        buildLevel = self.getBuildingLevelWithCode(building_1)

        if buildLevel < building_level_1:
            self.log(f"{building_1} 等级不足{buildLevel}/{building_level_1} 无法研究")
            return

        need_value_1 = researchInfo.get("need_value_1")
        need_value_2 = researchInfo.get("need_value_2")
        need_value_3 = researchInfo.get("need_value_3")
        need_value_4 = researchInfo.get("need_value_4")

        needResources = [need_value_1, need_value_2, need_value_3, need_value_4]
        for i in range(4):
            if needResources[i] > self.resources[i]:
                self.log(f"资源不足{i} {needResources[i]}")
                return False

        if self.research(researchCode):
            self.log(f"升级研究{researchCode}到{level}")
            return True
        else:
            self.log(f"升级研究{researchCode}失败")
            return False

    def checkResearchFull(self, researchCode, level):
        if not researchMiniMap:
            loadResearchMiniMap()

        researchInfo = researchMiniMap[researchCode]
        for k, v in researchInfo.items():
            if k.startswith("pre_") and len(k) == 5 and v > 0:
                preLevel = researchInfo.get(f"{k}_level")
                code, _level = self.checkResearchFull(v, preLevel)
                if code:
                    return code, _level

        maxLevel = researchInfo.get("max_level")
        curLevel = self.getResearchInfoLevel(researchCode)
        if curLevel < maxLevel:
            return researchCode, curLevel + 1
        return None, None

    def buildBattleMessage(self, battle, realWorldId = None, strFunc = None):
        valueStr = ""
        v = battle
        if not v:
            return valueStr

        if not realWorldId:
            realWorldId = self.realWorld

        def _strFunc(x):
            return x
        if not strFunc:
            strFunc = _strFunc
        leaderKingdomWorldId = v.get("leaderKingdomWorldId")
        if leaderKingdomWorldId and leaderKingdomWorldId == realWorldId:
            moreTroopMap = v.get("moreTroopMap", [])
            if moreTroopMap:
                moreStr = " ".join(moreTroopMap)
                valueStr += f'\n{v["leaderKingdomName"]} 发现混兵:{moreStr}\n'
            return valueStr
        troopMap = v["troopRunMap"]
        sum = 0
        for key in troopMap:
            sum += troopMap[key]
        if sum > 0:
            valueStr += f'\n{v["leaderKingdomName"]} 攻击 {v["targetKingdomName"]} 总兵力:{sum // 10000}万 剩余时间{strFunc(v["overTime"])} 距离:{v["distance"]} 消息:{v.get("message","")}\n'
            for key in troopMap:
                value = troopMap[key]
                if value > 0:
                    valueStr += f"{key}:{value} {round(value/sum * 100,2)}%\n"
        return valueStr

    def returnAllSupport(self, useLock):
        """撤回所有驻防"""
        for task in self.fieldTasks:
            code = task.get("code", 0)
            if code == 7:
                param = task.get("param", {})
                supportShrineId = param.get("supportShrineId")
                supportKingdomId = param.get("supportKingdomId")
                # marchType = param.get("marchType", 0)
                # # 驻防的要撤回
                # if marchType == 7:
                if supportShrineId or supportKingdomId:
                    moId = param.get("moId")
                    self.log(f"驻防队列: {moId}")
                    if moId:
                        res = self.kingdomSupportReturn(moId, useLock = useLock)
                        self.randomSleep(2,3,msg = f"撤回{moId} {res and '成功' or '失败'}")
            elif code == 4:
                expectedEnded = task.get("expectedEnded")
                self.log(f"挖矿队列剩余时间: {self.compareDateTimeWithNow(expectedEnded)}")
                break

    def tryReturnAllSupport(self):
        self.taskAll(useLock=True)
        self.returnAllSupport(True)

    def tryTeleport(self, loc):
        if self.worldId != loc[0]:
            self.errorLog(f"王国 无法传送到{loc} worldId:{self.worldId} 异常")
            return
        self.addBotWorkLog(f"传送开始 {loc}")
        with self.requestLock:
            while not self.isInvalid:
                self.taskAll(useLock=False)
                if len(self.fieldTasks) > 0:
                    self.returnAllSupport(False)
                    self.randomSleep(20, 30, msg="传送任务中,等待队列回归")
                    continue

                teleportType = 0
                distance = self.distanceWithLoc(loc)
                if distance <= 400:
                    # 尝试使用技能传送
                    skills = self.unUseSkillList()
                    filteredSkills = list(filter(lambda x: x.get("code") == 10008, skills))
                    if filteredSkills:
                        skill = filteredSkills[0]
                        level = skill.get("level")
                        if level > 1:
                            teleportType = 2
                        elif level == 1 and distance <= 200:
                            teleportType = 2
                teleportTypeStr = "技能" if teleportType == 2 else "高级"
                self.log(f"距离{distance} 使用{teleportTypeStr}传送")
                self.itemList(useLock = False)
                if self.itemCount(Enum.ITEM_CODE_ALLIANCE_TELEPORT) > 0 or teleportTypeStr == 2:
                    if self.teleport(loc, type = teleportType, useLock=False):
                        self.addBotWorkLog(f"传送成功 {loc}")
                        return
                self.log("传送结束")
                return

    def tryCvcMove(self, enter = False):
        title = enter and "进入" or "离开"
        self.addBotWorkLog(f"{title}cvc广播")
        with self.requestLock:
            while not self.isInvalid:
                self.taskAll(useLock=False)
                if len(self.fieldTasks) > 0:
                    for task in self.fieldTasks:
                        code = task.get("code", 0)
                        if code == 7:
                            param = task.get("param", {})
                            supportShrineId = param.get("supportShrineId")
                            # marchType = param.get("marchType", 0)
                            # # 驻防的要撤回
                            # if marchType == 7:
                            if supportShrineId:
                                moId = param.get("moId")
                                self.log(f"驻防队列: {moId}")
                                if moId:
                                    res = self.kingdomSupportReturn(moId, useLock = False)
                                    self.randomSleep(2,3,msg = f"撤回{moId} {res and '成功' or '失败'}")

                    self.randomSleep(20, 30, msg=f"{title}cvc中,等待队列回归")
                    continue

                if enter:
                    self.cvcEnter(useLock = False)
                else:
                    self.cvcLeave(useLock = False)
                self.log(f"{title}cvc结束")
                return

    def tryUseItem(self, code, count = 1):
        if self.hasItemBuff(code):
            self.addBotWorkLog(f"使用{code} {count} 失败 有buff")
            return False
        self.addBotWorkLog(f"使用{code} {count} 开始")
        with self.requestLock:
            self.itemList(useLock = False)
            self.randomSleep(1,2)
            if self.itemCount(code) >= count:
                if self.itemUse(code, count, useLock = False):
                    self.addBotWorkLog(f"使用{code} {count} 成功")
                    return True
                else:
                    self.addBotWorkLog(f"使用{code} {count} 失败")
                    return False
            else:
                self.addBotWorkLog(f"物品{code} 数量不足 {self.itemCount(code)}")
                return False
        self.addBotWorkLog(f"使用{code} {count} 结束")

    def tryCvcRankMonitor(self, autoKill = True, sendNoti = True) -> list:
        eventId = redisHelper.getCvcEventId()
        if not eventId:
            self.addBotWorkLog("大陆cvc活动未开启")
            return
        res = self.cvcRankContinent(eventId=eventId)
        if res:
            killStr = autoKill and "踢" or "手动踢"
            ranking = res.get("ranking")
            # for i in range(len(ranking)):
            #     kingdom = ranking[i].get("kingdom")
            #     alliance = kingdom.get("alliance")
            #     tag = alliance.get("tag")
            #     name = kingdom.get("name")
            #     kingdomId = kingdom.get("_id")
            #     if tag not in ["LDAT", "ATAT"]:
            #         self.log(f"非LDAT和ATAT联盟{tag} {name} RANK:{i} {kingdomId}")
            # return
            if not ranking:
                bfEventOpenData = res.get("bfEventOpenData")
                if bfEventOpenData:
                    self.barkNoti("获取排行榜异常")
                    self.clearSelf()
                    return
            cvcRankMap = self.getCVCRankList()
            kickList = []
            if cvcRankMap:
                for i in range(150):
                    nKingdom = ranking[i]
                    kingdom = nKingdom.get("kingdom")
                    point = nKingdom.get("point")
                    kingdomId = kingdom.get("_id")
                    worldId = kingdom.get("worldId")
                    oldKingdoms = list(filter(lambda x: x.get("kingdom",{}).get("_id") == kingdomId, cvcRankMap))
                    if oldKingdoms:
                        oldKingdom = oldKingdoms[0]
                        oldPoint = oldKingdom.get("point")
                        if point - oldPoint > redisCeleryHelper.getCvcRankLockPoint():
                            name = kingdom.get("name")
                            if self.cvcRankSafeKingdomCheck(kingdomId):
                                s = f'发现己方老六 :{name} {oldPoint} -> {point} {kingdomId} 已保护'
                                self.log(s)
                                if sendNoti:
                                    self.barkNoti(s, isAdmin=True)
                            else:
                                if autoKill:
                                    redisCeleryHelper.setCvcKingdomInfo(kingdomId, kingdom)
                                    redisCeleryHelper.addCvcBlockUsers(kingdomId)
                                    kingdomInfo = redisCeleryHelper.getCvcKingdomInfo(kingdomId)
                                    foId = kingdomInfo.get("foId")
                                    if foId and len(foId) > 0:
                                        kickList.append(foId)
                                    else:
                                        s += ' 没有foId'
                                s = f'发现老六 :{name} {oldPoint} -> {point} {kingdomId} {worldId} {killStr}'
                                self.log(s)
                                if sendNoti:
                                    self.barkNoti(s, isAdmin=True)
            self.saveCVCRankList(ranking)
            self.log(f"cvcRankMonitor 一轮结束 {len(kickList)}个需要踢出")
            return kickList

    def tryPrintResource(self):
        # 打印资源
        try:
            self.getTroops(useLock=False)
        except Exception as e:
            self.addBotWorkLog(f"打印资源请求异常 {e}")

        resourceFormatStr = ",".join(self.resourceFormat)
        s = f"资源:{resourceFormatStr} 加速:{self.loadSpeedItems(force = True)} 仓库资源:{self.loadResourceItems(force = True)} 兵力:{self.loadTroops()}"
        self.addBotWorkLog(s)

    def tryShrineSkill(self, skillCode):
        """尝试使用国王技能"""
        skillCode = int(skillCode)
        skills = self.shrineSkill()
        if skills:
            for skill in skills:
                if skill.get("skillCode") == skillCode:
                    nextSkillTime = skill.get("nextSkillTime")
                    endTime = skill.get("endTime")
                    t = -1
                    if nextSkillTime:
                        t = self.compareDateTimeWithNow(nextSkillTime)
                    if t < 0:
                        if endTime:
                            if self.compareDateTimeWithNow(endTime) <= 0:
                                res = self.shrineSkillUse(skillCode)
                                self.addBotWorkLog(f"国王技能{skillCode} 使用{res and '成功' or '失败'}")
                            else:
                                self.addBotWorkLog(f"国王技能{skillCode} 使用中")
                    else:
                        self.addBotWorkLog(f"国王技能{skillCode} 冷却中")
                    break

    @property
    def warJoinCount(self):
        """战争跟团数量"""
        return redisCeleryHelper.getWarJoinCount(self.realWorld)