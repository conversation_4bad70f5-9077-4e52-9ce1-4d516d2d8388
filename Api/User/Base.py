# /bin/env python3
# -*- coding: UTF-8 -*-

"""
功能描述：用户基础接口
编写人：darkedge
编写日期：2021年12月31日
"""

import datetime
import json
import secrets
import time

from Api.GoogleCaptcha import create_task, get_response, localGoogleCaptchaService
from Api.OneSignalApi import onceSignalApi
from Api.User.Request import UserRequest, checkB64ApiList, domain, returnB64ApiList
from Model.UserError import UserError
from Unit.Redis import redisHelper
from Unit.DeviceInfo import getUIVersion,getTableVersion
domainNew = domain
treasureMaxNums = [0, 5, 5, 5, 5, 5, 6, 6, 6, 6, 6, 7, 7, 7, 7, 7, 8, 8, 8, 8, 8, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 13, 14, 15, 16, 20]

class UserBaseApi(UserRequest):
    """基础接口"""

    pushId = None

    def saveLoginInfo(self, res):
        """保存登录信息"""
        user = res.get("user")
        self.id = user.get("__id")
        # property = res.get("property")
        self.token = res.get("token")
        redisHelper.saveToken(
            self.token,
            email=self.email,
            userKey=self.userKey,
            appleSubId=self.appleSubId,
        )
        lstProtect = res.get("lstProtect")
        if lstProtect:
            self.saveLstProtect(lstProtect)
        regionHash = res.get("regionHash")
        if not self.checkRegionHash(regionHash):
            self.barkNoti(
                f"regionHash不一致 {self.decodeRegionHash(regionHash)}", isAdmin=True
            )
            self.errorLog(f"regionHash不一致 {self.decodeRegionHash(regionHash)}")
            if self.autoExitIfChangeEncrypt:
                exit(-1)
            else:
                raise UserError.EncryptKeyChange()

        self.log("登陆成功")
        self.debuglog("登陆成功 token:%s" % self.token)
        self.requrestSetDeviceInfo()

    def requrestSetDeviceInfo(self, pushId=None):
        """
        请求设置设备信息
        """
        deviceInfo = self.deviceInfo.copy()

        deviceInfo["pushId"] = pushId
        data = {"deviceInfo": deviceInfo}
        self.request(domainNew + "api/auth/setDeviceInfo", data, useLock=False)

    def realTrySetDeviceInfo(self):
        if self.pushId is None:
            self.pushId = redisHelper.getPushId(self.email, self.userKey)
            if self.pushId is None:
                OS = self.deviceInfo.get("OS")
                pushId = onceSignalApi.createPlayer(OS, self.proxies)
                self.pushId = pushId
                if pushId is None:
                    pass
                    # raise UserError.pushIdError()
                else:
                    redisHelper.savePushId(pushId, self.email, self.userKey)

            # self.randomSleep(1,3)
            self.requrestSetDeviceInfo(self.pushId)

    def trySetDeviceInfo(self):
        tasks = []
        tasks.append(self.chatLogsA(f"w{self.realWorld}"))
        if self.allianceId:
            tasks.append(self.chatLogsA(f"{self.allianceId}"))
        self.runTasks(tasks)
        if self.level < 25:
            self.realTrySetDeviceInfo()

        self.loginKingdomAsyncTasks()

        # self.anyOpen()
        self.realTrySetDeviceInfo()

        self.enterKingdomAsyncTasks()

        self.getTroops(useLock=False)

    def login(self):
        """
        登录综合入口
        """
        if not self.hasInit:
            self.initLog()

        if self.token is None:
            token = self.loadRedisToken()
            if token:
                self.token = token
        if (
            not self.token
            and not self.pwd
            and not self.userKey
            and not self.googleToken
            and not self.appleSubId
        ):
            return False
        if self.token:
            try:
                checkB64ApiList()
                if not returnB64ApiList() or self.checkTokenExp():
                    if not returnB64ApiList():
                        self.errorLog("returnB64ApiList 无数据 重新登陆")
                    if (
                        not self.pwd
                        and not self.userKey
                        and not self.googleToken
                        and not self.appleSubId
                    ):
                        return False
                    self.requestLogin()
                if self.enter():
                    return True
            except UserError as e:
                if e.errorCode == -4:
                    self.clearSelf()
                    if self.isGoogle:
                        return False
                    return self.login()
                elif e.errorCode == -5:
                    self.clearSelf()
                    if self.isGoogle:
                        return False
                    return self.login()
                elif e.errorCode == 41:
                    raise e
                self.errorLog(e)
            except Exception as e:
                raise e

        if self.requestLogin():
            if self.googleToken:
                self.log(f"谷歌登录生成token :{self.token}")
                self.loadEmailWithToken()
            return self.enter()

        return False

    def requestLogin(
        self, isRegister=False, isV2=True, token=None, recaptcha=None, retry=0
    ):
        """
        请求登录接口
        """
        if retry > 3:
            return False
        if self.isGoogle and not self.googleToken:
            self.errorLog("谷歌账号登录没有gtoken")
            return False

        deviceInfo = self.deviceInfo.copy()
        # deviceInfo["pushId"] = "ad239e01-6652-48d1-a8b8-6f627f070c3c"

        data = {
            "deviceInfo": deviceInfo,
            # "version":2,
        }
        if isV2:
            if not isRegister and not self.email:
                data["version"] = 2

        if self.appleSubId:
            data["authType"] = "apple"
            data["token"] = self.appleToken
        elif self.googleToken:
            data["authType"] = "google"
            data["token"] = self.googleToken
        elif self.userKey:
            data["authType"] = "guest"
            data["userKey"] = self.userKey
        elif self.email:
            data["authType"] = "email"
            data["email"] = self.email
            data["password"] = self.pwd

        if not data.get("authType"):
            return False

        if recaptcha:
            data["recaptcha"] = recaptcha

        res = self.request(
            domainNew + "api/auth/login", data, isLogin=True, useLock=False
        )
        if res and res.get("result"):
            self.saveLoginInfo(res)
            return True
        elif res:
            err = res.get("err")
            if err:
                code = err.get("code")
                if code == "exceed_guest_limit":
                    raise UserError.exceedGuestLimit()
                elif code == "exceed_account_ip":
                    raise UserError.exceedAccountIp()
                elif code == "google_auth_error":
                    raise UserError.googleAuthError()
                elif code == "deleted_within_period":
                    raise UserError.deletedWithinPeriod()
                elif code == "exceed_limit_packet":
                    raise UserError.limitRetry()
                elif code == "need_recaptcha":
                    if isRegister:
                        self.useBarkNoti = True
                        self.barkNoti("注册被限制谷歌验证了", isAdmin=True)
                        exit(0)
                        key = err.get("key")
                        if token:
                            return self.requestLogin(
                                isRegister,
                                isV2,
                                recaptcha={"key": key, "token": token},
                                retry=retry + 1,
                            )
                        self.log("谷歌验证码token失效 二次验证")
                        recaptchaRes = None
                        if localGoogleCaptchaService:
                            localGoogleCaptchaService.getOneToken()
                        else:
                            taskId = create_task()
                            recaptchaRes = get_response(taskId)
                        if recaptchaRes:
                            return self.requestLogin(
                                isRegister,
                                isV2,
                                recaptcha={"key": key, "token": recaptchaRes},
                                retry=retry + 1,
                            )
                    else:
                        raise UserError.googleCaptcha()
            else:
                if err is not None and isRegister:
                    raise UserError.notAvailableRegister()
                if self.isInvalid:
                    return False

                self.log(f"网络超时重新登陆 s5:{self.socks5}")
                if self.noWSUser:
                    return False
                return self.requestLogin(
                    isRegister, isV2, token=token, recaptcha=recaptcha, retry=retry + 2
                )
            self.log("登陆失败 res:%s" % json.dumps(res))
        return False

    def requestConnect(self):
        """
        请求连接接口
        """
        if not self.isLogin:
            return self.login()

        res = self.request(
            domainNew + "api/auth/connect",
            {"deviceInfo": {"build": "global"}},
            isLogin=True,
            useLock=False,
        )
        if res and res.get("result"):
            self.saveLoginInfo(res)
            return True
        elif res:
            err = res.get("err")
            if err:
                code = err.get("code")
                if code:
                    # if code == "exceed_limit_packet":
                    #     self.errorLog("requestConnect 异常 尝试重新登陆")
                    # self.clearSelf()
                    # return False
                    self.errorLog("connect 报错 %s" % code)
            self.log("token登录失败，使用登陆接口")
            redisHelper.removeToken(
                email=self.email,
                userKey=self.userKey,
                appleSubId=self.appleSubId,
                kingdomId=self.kingdomId,
            )
            self.token = None

            return False
        return False

    # 绑定游客账号?
    # fetch("https://api-lok-live.leagueofkingdoms.com/api/auth/join", {
    #     "headers": {
    #         "accept": "*/*",
    #         "accept-language": "en-US,en;q=0.9",
    #         "content-type": "application/x-www-form-urlencoded",
    #         "sec-ch-ua": "\"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"102\", \"Google Chrome\";v=\"102\"",
    #         "sec-ch-ua-mobile": "?0",
    #         "sec-ch-ua-platform": "\"macOS\"",
    #         "sec-fetch-dest": "empty",
    #         "sec-fetch-mode": "cors",
    #         "sec-fetch-site": "same-site",
    #         "x-access-token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJfaWQiOiI2MzFhMGE2NDdmYzRkNjEzZTQ4ZDgyMWMiLCJraW5nZG9tSWQiOiI2MzFhMGE2NDdmYzRkNjEzZTQ4ZDgyMWQiLCJ3b3JsZElkIjo0NywidmVyc2lvbiI6MTU4NywiYnVpbGQiOjAsInBsYXRmb3JtIjoid2ViIiwidGltZSI6MTY3NjcyODQyNTIzMywiY2xpZW50WG9yIjoiMCIsImlhdCI6MTY3NjcyODQyNSwiZXhwIjoxNjc3MzMzMjI1LCJpc3MiOiJub2RnYW1lcy5jb20iLCJzdWIiOiJ1c2VySW5mbyJ9.Jp9txO4aenplQyettQC7rl5wpOTCQ1kEr1xkHiGL4fk"
    #     },
    #     "referrer": "https://play.leagueofkingdoms.com/",
    #     "referrerPolicy": "strict-origin-when-cross-origin",
    #     "body": "json=%7B%22authType%22%3A%22email%22%2C%22email%22%3A%22darkedge%40darkedge.cn%22%2C%22password%22%3A%22111111%22%2C%22version%22%3A2%2C%22deviceInfo%22%3A%7B%22build%22%3A%22global%22%2C%22OS%22%3A%22Mac%20OS%20X%2010_14_5%22%2C%22country%22%3A%22USA%22%2C%22language%22%3A%22English%22%2C%22bundle%22%3A%22%22%2C%22version%22%3A%221.1587.125.208%22%2C%22platform%22%3A%22web%22%2C%22pushId%22%3A%22%22%7D%7D",
    #     "method": "POST",
    #     "mode": "cors",
    #     "credentials": "omit"
    #     });
    def authJoin(self, recaptcha=None, token=None, retry=0):
        """
        注册接口
        """
        if not self.hasInit:
            self.initLog()

        if retry > 3:
            return False

        data = {
            "authType": "email",
            "email": self.email,
            "password": self.pwd,
            "version": 2,
            "deviceInfo": self.deviceInfo,
        }

        if recaptcha:
            data["recaptcha"] = recaptcha
        res = self.request(domainNew + "api/auth/join", data, isLogin=True)
        if res:
            if res.get("result"):
                return True
            else:
                err = res.get("err")
                if err:
                    code = err.get("code")
                    if code == "exceed_account_ip":
                        raise UserError.exceedAccountIp()
                    elif code == "need_recaptcha":
                        key = err.get("key")
                        if token:
                            return self.authJoin(
                                {"key": key, "token": token}, retry + 1
                            )
                        self.log("谷歌验证码token失效 二次验证")
                        recaptchaRes = None
                        if localGoogleCaptchaService:
                            localGoogleCaptchaService.getOneToken()
                        else:
                            taskId = create_task()
                            recaptchaRes = get_response(taskId)
                        if recaptchaRes:
                            return self.authJoin(
                                {"key": key, "token": recaptchaRes}, retry + 1
                            )

        return False

    def guestRegister(self, isV2=True, token=None):
        """
        游客注册
        """
        if not self.hasInit:
            self.initLog()
        return self.requestLogin(True, isV2=isV2, token=token)

    def googleRegister(self, token):
        """
        谷歌注册
        """
        if not self.hasInit:
            self.initLog()
        return self.requestLogin(True, token=token)

    def linkApple(self):
        """
        绑定apple
        """
        if self.isInvalid:
            return False
        data = {
            "authType": "apple",
            # "version": 2,
            "deviceInfo": self.deviceInfo,
            "token": self.appleToken,
        }

        res = self.request(domainNew + "api/auth/link", data)
        if res and res.get("result"):
            return True
        return False

    def deleteAccount(self):
        """
        删除账号
        """
        if self.isInvalid:
            return False
        res = self.request(domainNew + "api/auth/delete")
        if res and res.get("result"):
            self.invalidStatu = True
            return True
        return False

    def enter(self):
        """进入游戏获取所有信息"""
        if self.isInvalid:
            return None

        res = self.request(domainNew + "api/kingdom/enter", useLock=False)
        if res:
            if not res.get("result"):
                # 适配资源异常
                err = res.get("err")
                if err:
                    errors = err.get("errors")
                    if errors and errors.get("resources"):
                        if self.userKey:
                            self.loc = [20, 1005, 1005]
                            self.realWorld = 20
                            self.name = "异常小号"
                            self.level = 10
                            self.vip = 2
                            self.marchSize = 20000

                        else:
                            locs = self.localConfig["loc"]
                            self.loc = locs
                            self.resources = [
                                999999999,
                                999999999,
                                999999999,
                                999999999,
                            ]
                            self.realWorld = 20
                            self.name = "异常号"
                            self.level = 30
                            self.vip = 17
                            self.marchSize = 310000
                            self.kingdomId = "61c00a9922b56d1541efe9f4"
                            self.allianceId = "619df0c09001ff0c6017220f"
                            self.fieldObjectId = "61c00a9c22b56d1541efea13"

                        self.trySetDeviceInfo()
                        self.isBug = True
                        self.log("异常登录")
                        return True

                return False
            # self.debuglog(f'enter detai:{res}')
            kingdom = res.get("kingdom")
            self.setKingdomInfo(kingdom)
            boost = kingdom.get("boost")
            total = boost.get("total")

            networks = res.get("networks")
            self.setNetworks(networks)

            self.loadBoosts(total)
            v1 = total[1]
            self.marchSize = v1[2]
            if self.isMain and self.marchSize < 100:
                self.log("带兵数异常")
            if self.isMain and self.power < 40000:
                raise UserError.lowPower()

            troopSum = 0
            for troop in self.troops:
                if troop.get("code"):
                    amount = troop.get("amount")
                    troopSum += amount
                    if int(troop.get("code")) % 10 == 2:
                        self.isLevel2 = True

            self.numTroops = troopSum

            self.printSelf()

            if self.publicKey:
                self.log(f"钱包地址:{self.publicKey}")
            self.enterTime = time.time()
            self.insufficient_resources = False

            d = self.dataRecord()
            if d.checkLoc(self.loc):
                self.errorLog("我被搬家了!!!")
            d.updatePwd(self.pwd).updateBan(0).updateLevel(self.level).updateResources(
                self.resources
            ).updateCrystal(self.crystal).commit()

            captcha = res.get("captcha")
            self.captchaInfo = captcha

            self.trySetDeviceInfo()

            self.saveRedisLoc()
            return True

        return False

    # 不知道是啥的东西
    def anyOpen(self):
        self.cvcOpen()
        self.rouletteOpen()

    def bfOpen(self):
        if self.isInvalid:
            return False

        res = self.request(domainNew + "api/event/bf/open", useLock=False)
        if res and res.get("result"):
            return True
        return False

    def cvcOpen(self):
        if self.isInvalid:
            return False

        res = self.request(domainNew + "api/event/cvc/open", useLock=False)
        if res and res.get("result"):
            cvcEvent = res.get("cvcEvent")
            isOpen = cvcEvent.get("isOpen")
            isOnline = cvcEvent.get("isOnline")
            self.cvcEventOpen = isOpen and isOnline
            return True
        return False

    def rouletteOpen(self):
        """
        {
            "result": true,
            "event": {
                "_id": "652798b9a090e404be6a7248",
                "isOpen": true,
                "startDate": "2023-10-14T00:00:00.000Z",
                "endDate": "2023-10-17T00:00:00.000Z",
                "time": 259200
            }
        }
        """
        if self.isInvalid:
            return False

        res = self.request(domainNew + "api/event/roulette/open", useLock=False)
        if res and res.get("result"):
            self.rouletteEvent = res.get("event")
            return True
        return False

    # 进入游戏后
    def getTroops(self, useLock=True):
        """
        获取兵力信息
        troops: total/kingdom 兵力信息
        kingdom : 兵力信息
        troops.info :  marchSize 最大带兵数 maxTroops 最大军队数 numTroops 当前军队数
        """
        if self.isInvalid:
            return None
        res = self.request(domainNew + "api/kingdom/profile/troops", useLock=useLock)
        if res and res.get("result"):
            troops = res.get("troops")
            if troops.get("total"):
                self.troops = troops.get("total")
            if troops.get("kingdom"):
                self.kingdomTroops = troops.get("kingdom")
            info = troops.get("info")
            fields = troops.get("field")
            self.troopFields = fields or []
            if fields:
                self.kingdomId = fields[0].get("kingdomId")
            if info:
                self.marchLimit = info.get("marchLimit")
                self.marchSize = info.get("marchSize")
                self.maxTroops = info.get("maxTroops")
                self.numTroops = info.get("numTroops")
                self.dataRecord().updateLoc(self.loc).updateSumTroopsCount(
                    self.numTroops
                ).commit()

            return True
        return False

    def changeName(self, name):
        """
        修改名字
        """
        if self.isInvalid:
            return False
        data = {"name": name}
        res = self.request(domainNew + "api/kingdom/profile/change/name", data)
        if res and res.get("result"):
            self.name = name
            return True
        return False

    def changeFace(self, faceId=None):
        """
        修改头像
        """
        if self.isInvalid:
            return False
        data = {"faceCode": secrets.randbelow(10) + 2, "image": ""}
        res = self.request(domainNew + "api/kingdom/profile/change/face", data)
        if res and res.get("result"):
            return True
        return False

    # api/kingdom/treasure/equip
    def treasureEquip(self, itemId, equip):
        """宝物装备"""
        if self.isInvalid:
            return None

        data = {
            "itemId": itemId,
            "equip": equip,
        }
        res = self.request(domainNew + "api/kingdom/treasure/equip", data)
        if res and res.get("result"):
            return res
        return None

    # api/kingdom/treasure/page
    def treasurePage(self, page):
        """装备宝物"""
        if self.isInvalid:
            return None

        data = {
            "page": page,
        }
        res = self.request(domainNew + "api/kingdom/treasure/page", data)
        if res and res.get("result"):
            return res
        return None

    def treasureList(self):
        """宝物列表"""
        if self.isInvalid:
            return False

        res = self.request(domainNew + "api/kingdom/treasure/list")
        if res and res.get("result"):
            self.checkTreasure(res.get("treasures", []))
            return res
        return None

    def treasureOpen(self, itemCode):
        """合成宝物"""
        # api/kingdom/treasure/upgrade
        if self.isInvalid:
            return False

        res = self.request(
            domainNew + "api/kingdom/treasure/upgrade",
            {"itemCode": itemCode},
            isB64=True,
        )
        if res and res.get("result"):
            return res
        return None

    def treasureExchange(self, itemCode, amount):
        """换碎片"""
        # api/kingdom/treasure/exchange
        if self.isInvalid:
            return False

        res = self.request(
            domainNew + "api/kingdom/treasure/exchange",
            {"itemCode": itemCode, "amount": amount},
            isB64=True,
        )
        if res and res.get("result"):
            return res
        return None

    def logout(self):
        self.invalidStatu = True
        raise UserError.logout()

    def setKingdomInfo(self, kingdom):
        """
        设置自己的信息
        """
        from Unit.Redis import redisHelper

        redisHelper.setKingdomInfo(self.tokenWithKingomId(), kingdom)
        self.name = kingdom.get("name")
        self.userId = kingdom.get("userId")
        self.kingdomId = kingdom.get("_id")
        self.fieldObjectId = kingdom.get("fieldObjectId")
        self.loc = kingdom.get("loc")
        self.power = kingdom.get("power")
        self.publicKey = kingdom.get("publicKey")
        self.resources = kingdom.get("resources")
        self.troops = kingdom.get("troops")
        self.troopSum = 0
        for troop in self.troops:
            if troop.get("code")% 10 >= 5:
                amount = troop.get("amount")
                self.troopSum += amount
        self.level = kingdom.get("level")
        self.buildings = kingdom.get("buildings")
        self.researches = kingdom.get("researches")
        self.realWorld = kingdom.get("worldId")
        if kingdom.get("crystal"):
            self.crystal = kingdom.get("crystal")

        if self.saveSocks5:
            redisHelper.saveSocks5Info(self.key, self.kingdomId, self.socks5)

        self.allianceId = kingdom.get("allianceId")
        # 获取vip信息
        if kingdom.get("vip"):
            vip = kingdom.get("vip")
            level = vip.get("level")
            self.vip = level

        # # 获取活力点
        if kingdom.get("actionPoint"):
            self.actionPoint = kingdom.get("actionPoint").get("value")

        dragoActionPoint = kingdom.get("dragoActionPoint")
        if dragoActionPoint:
            value = dragoActionPoint.get("value")
            self.dragoActionPoint = value
        if kingdom.get("stats"):
            # 获取练兵数和采集数
            stats = kingdom.get("stats")
            economy = stats.get("economy")
            self.gatheringNum = economy.get("gathering")
            troops = stats.get("troops")
            self.trainingNum = troops.get("train")
            monster = stats.get("monster")
            self.killMonster = monster.get("kill")

        # 获取免费宝箱信息
        self.setFreeChestInfo(kingdom.get("freeChest"))
        tmpHarvestList = {}
        for build in self.buildings:
            code = build.get("code")
            if code and code in [40100202, 40100203, 40100204, 40100205]:
                tmpHarvestList[code] = build["position"]
        if len(tmpHarvestList) == 4:
            self.harvestList = list(tmpHarvestList.values())
        self.debuglog(f"harvestList:{self.harvestList}")

    def setFreeChestInfo(self, freeChest):
        """
        设置免费宝箱信息
        """
        if freeChest:
            silver = freeChest.get("silver")
            if silver:
                next = silver.get("next")
                num = silver.get("num")
                t = self.timestampWithdatetime(next)
                treasureHouseLevel = self.getBuildingLevel(4)
                maxNum = treasureMaxNums[treasureHouseLevel] - 1
                if num >= maxNum:
                    ex = (
                        time.mktime(datetime.date.today().timetuple())
                        + 32 * 3600
                        - time.time()
                    )
                    redisHelper.saveSignEndUser(
                        int(ex), email=self.email, userKey=self.userKey
                    )
                    t += 24 * 60 * 60
                else:
                    t += 60 * 60 * 8

                self.silverTime = t
                self.silverNum = num

            gold = freeChest.get("gold")
            if gold:
                next = gold.get("next")
                self.goldTime = self.timestampWithdatetime(next) + 60 * 60 * 8

            platinum = freeChest.get("platinum")
            if platinum:
                next = platinum.get("next")
                self.platinumTime = self.timestampWithdatetime(next) + 60 * 60 * 8

    # api/auth/analytics
    def analytics(self, url, param):
        """
        分析接口
        """
        if self.isInvalid:
            return False
        data = {"url": url, "param": param}
        res = self.request(domainNew + "api/auth/analytics", data, useLock=False)
        if res and res.get("result"):
            return True
        return False

    def authCheckVersion(self):
        """
        校验版本
        """
        data = {
            "platform": "web",
            "build": "global",
            "version": getUIVersion()
        }
        res = self.request(domainNew + "api/auth/check/version",data,isLogin=True)
        if res and res.get("result"):
            return res
        return False

    def version_live(self):
        """
        版本信息
        """
        res = self.requestGet("https://play.leagueofkingdoms.com/json/version-live.json")
        if res and res.json() and res.json()['table'] == getTableVersion():
            return True
        return False