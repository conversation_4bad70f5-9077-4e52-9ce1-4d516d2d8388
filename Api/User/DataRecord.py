# /bin/env python3
# -*- coding: UTF-8 -*-

"""
功能描述：用户数据记录模块
编写人：darkedge
编写日期：2021年12月31日
"""

from Api.DataRecord import DataRecord
from Model.User import User
from Unit.Redis import currentHour, redisHelper, todayUTCStr
from datetime import datetime

class UserDataRecord(User):
    def dataRecord(self) -> DataRecord:
        """数据记录"""
        return DataRecord(self.email, isEnable=self.isMain)

    @property
    def kingdomTodayCrystalKey(self):
        """今日获取水晶数量key"""
        return f"{self.kingdomId}_{todayUTCStr()}"

    def getKingdomTodayCrytstal(self):
        """获取王国今日水晶"""
        value = redisHelper.get(self.kingdomTodayCrystalKey)
        if not value:
            value = 0
        return int(value)

    def setKingdomTodayCrystal(self, value):
        """设置王国今日水晶"""
        redisHelper.set(self.kingdomTodayCrystalKey, value, ex=60 * 60 * 24 * 3)

    @property
    def hourCrystalKey(self):
        """获取王国当前小时水晶key"""
        return f"hourCrystal_{self.kingdomId}_{currentHour()}"

    def getCurrentHourGoBackValue(self):
        """获取当前小时水晶数量"""
        if not self.kingdomId:
            return 0
        value = redisHelper.get(self.hourCrystalKey)
        if not value:
            return 0
        return int(value)

    def increaseCurrentHourGoBackValue(self, value):
        """增加当前小时撤回水晶数量"""
        if not self.kingdomId:
            return

        oldValue = self.getCurrentHourGoBackValue()
        if not value:
            value = 0
        value = int(value)
        value += oldValue
        redisHelper.set(self.hourCrystalKey, value, ex=60 * 60 * 23)

    @property
    def gobacktodayUTCKey(self):
        return f"{self.kingdomId}_goback_{todayUTCStr()}"

    def getCurrentGoBackValue(self):
        """获取当前王国撤回水晶数量"""
        if not self.kingdomId:
            return 0

        value = redisHelper.get(self.gobacktodayUTCKey)
        if not value:
            value = 0
        return int(value)

    def increaseCurrentGoBackValue(self, value):
        """增加当前王国撤回水晶数量"""
        if not self.kingdomId:
            return

        oldValue = self.getCurrentGoBackValue()
        if not value:
            value = 0
        value = int(value)
        self.increaseCurrentHourGoBackValue(value)

        self.debuglog(
            f"增加撤回水晶数量{value} 今日总额{oldValue + value} 当前小时:{self.getCurrentHourGoBackValue()}"
        )
        value += oldValue
        if not value:
            value = 0
        redisHelper.set(self.gobacktodayUTCKey, value, ex=60 * 60 * 24 * 3)

    @property
    def kingdomTodayCrystalCountKey(self):
        """今日采集水晶次数"""
        return f"{self.kingdomId}_count_{todayUTCStr()}"

    def getKingdomTodayCrystalCount(self):
        """获取今日采集次数"""
        if not self.kingdomId:
            return 0
        value = redisHelper.get(self.kingdomTodayCrystalCountKey)
        if not value:
            return 0
        return int(value)

    def getDiffRecordCrystal(self):
        """获取今日收入砖石"""
        if not self.name:
            return 0
        value = redisHelper.getDiffRecordCrystal(self.name)
        if not value:
            return 0
        return int(value)

    def increaseKingdomTodayCrystalCountValue(self):
        """增加今天采集水晶次数"""
        if not self.kingdomId:
            return

        value = self.getKingdomTodayCrystalCount()
        value += 1
        redisHelper.set(self.kingdomTodayCrystalCountKey, value, ex=60 * 60 * 24 * 3)

    @property
    def exceedCrystalDailyQuotaKey(self):
        """每日采集次数限额"""
        return f"exceed_crystal_daily_quota_{self.key}_{todayUTCStr()}"

    def getExceedCrystalDailyQuota(self):
        return redisHelper.get(self.exceedCrystalDailyQuotaKey) and True or False

    def setExceedCrystalDailyQuota(self):
        redisHelper.set(self.exceedCrystalDailyQuotaKey, 1, ex=60 * 60 * 24)

    @property
    def putDragonDailyQuotaKey(self):
        """每日放龙次数限额"""
        return f"put_dragon_daily_quota_{todayUTCStr()}"

    def getPutDragonDailyQuota(self):
        """获取每日放龙次数"""
        value = redisHelper.get(self.putDragonDailyQuotaKey)
        if value:
            return int(value)
        return 0

    def setPutDragonDailyQuota(self, value):
        """设置每日放龙次数"""
        redisHelper.set(self.putDragonDailyQuotaKey, value, ex=60 * 60 * 24 * 7)

    def increasePutDragonDailyQuota(self):
        """增加每日放龙次数"""
        value = self.getPutDragonDailyQuota()
        if value < 100:
            value += 1
            self.setPutDragonDailyQuota(value)
            return True
        return False

    @property
    def todayMonsterKey(self):
        """今日打怪次数"""
        return f"today_monster_{todayUTCStr()}"

    def getTodayMonster(self):
        """获取今日打怪次数"""
        value = redisHelper.get(self.todayMonsterKey)
        if value:
            return int(value)
        return 0

    def increaseTodayMonster(self):
        """增加今日打怪次数"""
        value = self.getTodayMonster()
        value += 1
        redisHelper.set(self.todayMonsterKey, value, ex=60 * 60 * 24 * 7)

    def saveRedisLoc(self):
        key = f'loc_{self.kingdomId}'
        redisHelper.setJson(key, self.loc, ex=60 * 60 * 24 * 3)

    @classmethod
    def getUserLoc(cls, kingdomId):
        key = f'loc_{kingdomId}'
        return redisHelper.getJson(key)

    def saveCVCRankList(self, values):
        key = "cvc_rank_20"
        redisHelper.setJson(key, values)

    def getCVCRankList(self):
        key = "cvc_rank_20"
        values = redisHelper.getJson(key)
        if values:
            return values
        return []

    @classmethod
    def cls_saveCVCRankList(cls, values):
        key = "cvc_rank_20"
        redisHelper.setJson(key, values)

    @property
    def cvcRankSafeKey(self):
        return "cvc_rank_safe_%s"

    def cvcRankSafeKingdom(self, kingdomId):
        key = self.cvcRankSafeKey % kingdomId
        redisHelper.set(key, 1, ex=60 * 60 * 24 * 7)

    def cvcRankSafeKingdomRemove(self, kingdomId):
        key = self.cvcRankSafeKey % kingdomId
        redisHelper.removeKey(key)

    def cvcRankSafeKingdomCheck(self, kingdomId):
        key = self.cvcRankSafeKey % kingdomId
        return redisHelper.get(key) and True or False

    def cvcRankSafeKingdoms(self):
        key = self.cvcRankSafeKey % "*"
        keys = redisHelper.keys(key)
        if keys:
            return [key.decode("utf-8")[14:] for key in keys]
        return []

    @property
    def allianceResearchMaxKey(self):
        return f"alliance_research_max_{self.allianceId}"

    def getAllianceResearchMax(self):
        if not self.allianceId:
            return True
        value = redisHelper.get(self.allianceResearchMaxKey)
        if not value:
            return True
        return int(value) == 1

    def setAllianceResearchMax(self):
        if not self.allianceId:
            return
        redisHelper.set(self.allianceResearchMaxKey, 1, ex=60 * 60 * 24 * 7)

    def addBotWorkLog(self, msg: str):
        """
        添加机器人工作日志
        Args:
            msg: 日志消息
        """
        from Celery.redis import redisCeleryHelper
        self.log(msg)
        current_time = datetime.now().strftime("%H:%M:%S")
        formatted_msg = f"{current_time} {self.name} {msg}"
        redisCeleryHelper.addToBotWorkLog(formatted_msg)
