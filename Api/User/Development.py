# /bin/env python3
# -*- coding: UTF-8 -*-

"""
功能描述：发育模块
编写人：darkedge
编写日期：2022年3月24日
   
"""

import random
import time

from Api.User.AsyncKingdom import UserAKingdom
from Api.User.Kingdom import UserKingdom
from Celery.redis import redisCeleryHelper
from Model.UserError import UserError
from Unit.DeviceInfo import createAppleSubId, createName

buildLevelSleepTimes = [
    8,
    66,
    246,
    966,
    1446,
]
buildMainLevelSleepTimes = [
    8,
    66,
    306,
    1506,
    2886,
]
buildFrameLevelSleepTimes = [
    8,
    66,
    166,
    280,
    466,
]

speedItems = [
    [
        10103012,
        10103013,
        10103014,
        10103015,
        10103016,
        10103017,
        10103018,
        10103019,
        10103020,
    ],  # 建筑1-30
    [
        10103032,
        10103033,
        10103034,
        10103035,
        10103036,
        10103037,
        10103038,
        10103039,
        10103040,
    ],  # 练兵1-30
    [
        10103022,
        10103023,
        10103024,
        10103025,
        10103026,
        10103027,
        10103028,
        10103029,
        10103030,
    ],  # 研究1-30
    [
        10103001,
        10103002,
        10103003,
        10103004,
        10103005,
        10103006,
        10103007,
        10103008,
        10103009,
    ],  # 万能1-30
]
speedTimes = [1, 5, 10, 30, 60, 180, 480, 1440, 4320]


def removeCrystalListWithMember(worldId, level, loc):
    """移除全局水晶列表"""
    if len(loc) == 3:
        return removeCrystalListWithMember(worldId, level, loc[1:])
    keys = [level] + loc
    key = "_".join(map(str, keys))
    redisCeleryHelper.removeCrystalListWithMember(worldId, key)


def removeDragoMineListWithMember(worldId, level, loc):
    """移除全局龙矿列表"""
    if len(loc) == 3:
        return removeDragoMineListWithMember(worldId, level, loc[1:])
    keys = [level] + loc
    key = "_".join(map(str, keys))
    redisCeleryHelper.removeDragoMineListWithMember(worldId, key)


class Development(UserKingdom, UserAKingdom):
    buildPositionList = [1, 104, 105, 106, 107, 108, 4, 5, 9, 2, 7, 6, 8, 3]
    """新号建筑列表"""

    def buildNew(self, positionList):
        """建造新建筑"""
        for position in positionList:
            if self.getBuildingLevel(position) == 0:
                buildContinue = True
                while buildContinue and not self.isInvalid:
                    if self.build(position, self.buildTypeList(position)):
                        # self.log("建造1级%s成功" % self.buildList(position))
                        self.randomSleep(4, 5)
                        self.setBuildingLevel(position, 1)
                        buildContinue = False
                    else:
                        self.log(f"建筑1级{self.buildList(position)}失败")
                        self.randomSleep(10)

    # 升级建筑
    def levelBuild(self, level, list1=None, useSpeedup=True, instantFunc=None):
        if list1 is None:
            list1 = self.buildPositionList

        for position in list1:
            self.getSilverFree()
            if position == 7 and level > 3:
                # self.log("联盟大厅跳过")
                continue

            if self.getBuildingLevel(position) == level - 1:
                buildContinue = True
                retry = 0
                mainBuild = [1, 2, 4, 5]
                t = 0
                try:
                    if position in mainBuild:
                        t = buildMainLevelSleepTimes[level - 2]
                    elif position > 100:
                        t = buildFrameLevelSleepTimes[level - 2]
                    else:
                        t = buildLevelSleepTimes[level - 2]
                except Exception:
                    # self.debuglog("超出等级 直接使用6666")
                    t = 6666
                while buildContinue and not self.isInvalid:
                    if retry > 5:
                        retry = 0
                        self.enter()
                        return self.levelBuild(level)
                    try:
                        instant = 0
                        if callable(instantFunc):
                            instant = instantFunc(position, level)
                        if self.buildingUpgrade(position, level - 1, instant=instant):
                            self.debuglog(
                                "升级%d级%s成功" % (level, self.buildList(position))
                            )
                            if useSpeedup:
                                time.sleep(5)
                                t = self.trySpeedUp(t)

                            if t > 30:
                                while t > 30 and not self.isInvalid:
                                    # 3分钟补偿
                                    self.randomSleep(30)
                                    t -= 30
                                    t2 = self.checkBuilding()
                                    t = min(t, t2)
                                    sl = t / 60
                                    if sl // 5 > 0:
                                        # trunk-ignore(bandit/B311)
                                        rt = random.randint(120, 240)
                                        rt = min(t, rt)
                                        t -= rt
                                        self.randomSleep(
                                            rt, msg=f"升级建筑长时等待,剩余:{t}"
                                        )
                                if t > 0 and not self.isInvalid:
                                    self.randomSleep(t)
                            else:
                                self.randomSleep(t)
                            self.setBuildingLevel(position, level)
                            buildContinue = False
                            time.sleep(3)
                        else:
                            self.log(f"升级{level}级{self.buildList(position)} 异常")
                            self.randomSleep(int(t / 10) + 2)
                            retry += 1
                    except UserError as e:
                        if e.errorCode == 11:
                            self.log(
                                f"升级{level}级{self.buildList(position)} 资源不足 等60s"
                            )
                            self.printSelf()
                            self.randomSleep(60)
                        elif e.errorCode < 0:
                            raise e
                        else:
                            self.errorLog(e, exc_info=True)

    def checkBuilding(self):
        """检测建筑状态"""
        kingdomTasks = self.kingdomTasks()
        if kingdomTasks is None:
            self.randomSleep(10)
            kingdomTasks = self.kingdomTasks()
        if kingdomTasks:
            for task in kingdomTasks:
                code = task.get("code")
                if code and code == 1:
                    # started = task.get("started")
                    expectedEnded = task.get("expectedEnded")
                    t = self.compareDateTimeWithNow(expectedEnded)
                    return t
            # self.log("有人支援 完成了")
        return 0

    def tryHoldBuild(self):
        """检测未完成建筑"""
        t = self.checkBuilding()

        while t > 0 and not self.isInvalid:
            if t > 30:
                self.randomSleep(30)
            else:
                self.randomSleep(t)

            t = self.checkBuilding()
            if t > 0:
                t = self.trySpeedUp(t)
                sl = t / 60
                if sl / 5 > 0:
                    # trunk-ignore(bandit/B311)
                    randomt = random.randint(120, 240)
                    self.randomSleep(randomt)
                    t -= randomt

        if not self.isInvalid:
            self.resetBuilding()

    def speedupUpUseItem(self, taskId, itemCode, m, t):
        """
        使用加速 m分钟 t当前剩余时间
        """
        count = self.itemCount(itemCode)
        if count > 0:
            maxSub = 60 * m

            # 如果大于80%就可以使用 否则就不能使用
            while t >= maxSub and count > 0:
                useCount = 1
                if t > maxSub * 5 and count > 5:
                    useCount = 5
                    canCount = t // maxSub
                    if canCount < count:
                        useCount = canCount
                    else:
                        useCount = count

                res = self.speedup(taskId, itemCode, useCount)
                if res is True:
                    return 0
                elif res:
                    count -= useCount
                    # trunk-ignore(ruff/F841)
                    started = res.get("started")
                    expectedEnded = res.get("expectedEnded")
                    t = self.compareDateTimeWithNow(expectedEnded)
                    if t > 60:
                        time.sleep(self.level >= 30 and 1 or 3)
        return t

    def trySpeedUpUseItem(
        self, type, useAny=False, retry=0, kingdomTasks=None, minTime=48
    ):
        """
        加速使用道具 1 建筑 2 练兵 3 研究 8 建筑2
        """
        if self.isInvalid:
            self.errorLog("token已失效")
            raise UserError.noauth()

        if retry > 3:
            return None
        tcode = 0
        items = []
        anyItems = speedItems[3]
        if type == 1:
            tcode = 1
            items = speedItems[0]
        elif type == 8:
            tcode = 8
            items = speedItems[0]
        elif type == 2:
            tcode = 3
            items = speedItems[1]
        elif type == 3:
            tcode = 6
            items = speedItems[2]
        else:
            return None

        if kingdomTasks is None:
            kingdomTasks = self.kingdomTasks()
        hasTask = False
        if kingdomTasks:
            itemList = self.itemList()
            if itemList is None:
                return self.trySpeedUpUseItem(
                    type, useAny, retry + 1, kingdomTasks=kingdomTasks
                )
            for task in kingdomTasks:
                code = task.get("code")
                if code and code == tcode:
                    hasTask = True
                    # buildTime = task.get("time")
                    expectedEnded = task.get("expectedEnded")
                    t = self.compareDateTimeWithNow(expectedEnded)
                    t = int(t)

                    taskId = task.get("_id")
                    # amount = 0
                    # code = 0
                    # lessNum = buildTime / 60
                    # s = lessNum % 1
                    # num = int(lessNum)
                    # if s > 0.4:
                    #     num += 1

                    for index in range(len(items) - 1, -1, -1):
                        if t >= minTime:
                            self.randomSleep(1, 2)
                            t = self.speedupUpUseItem(
                                taskId,
                                items[index],
                                speedTimes[index],
                                t,
                            )

                    if useAny and t > 0:
                        for index in range(len(items) - 1, -1, -1):
                            if t >= minTime:
                                self.randomSleep(1, 2)
                                t = self.speedupUpUseItem(
                                    taskId,
                                    anyItems[index],
                                    speedTimes[index],
                                    t,
                                )
                            if t <= 0:
                                break
                    if t > 0:
                        if minTime == 0 and t < 60 and self.crystal > 0:
                            self.log("强制加速1分钟")
                            if self.speedup(taskId, 10103001, 1, isBuy=1):
                                return 0
                        return t
                    else:
                        return 0

        if not hasTask:
            return True
        return None

    def trySpeedUp(self, t):
        """尝试加速"""
        if t > 180:
            res = self.trySpeedUpUseItem(1, useAny=True)
            if res is None:
                self.log("加速失败")
            elif res == 0:
                # self.log("加速成功")
                t = 0
            else:
                # self.log(f"加速成功 剩余时间{res}")
                t = res
        return t

    def tryFreeChests(self):
        self.getSilverFree()
        self.getGoldFree()
        self.getPlatinumFree()

    def getPlatinumFree(self):
        """
        领取免费铂金宝箱
        """
        if self.platinumGet:
            if self.itemFreechest(2):
                self.log("领取白金宝箱成功")
            else:
                self.errorLog("领取白金宝箱异常")

    def getGoldFree(self):
        """
        领取免费黄金宝箱
        """
        if self.goldGet:
            if self.itemFreechest(1):
                self.log("领取黄金宝箱成功")
            else:
                self.errorLog("领取黄金宝箱异常")

    def getSilverFree(self):
        """
        领取免费银宝箱
        """
        s = f"{self.silverTime}/{time.time()}"
        if self.silverGet:
            if self.itemFreechest(0):
                self.log("领取白银宝箱成功")
            else:
                self.errorLog(f"领取白银宝箱异常 {s}")

    def tryClaimVip(self):
        """
        尝试领取vip
        """
        if time.time() - self.vipLastTime < 3600 * 6:
            return
        self.vipLastTime = time.time()
        res = self.vipInfo()
        if res:
            vip = res.get("vip")
            if vip:
                isClaimed = vip.get("isClaimed")
                if not isClaimed:
                    self.vipClaim()

    def tryClaimDsaVip(self):
        """
        尝试领取dsa vip
        """
        if time.time() - self.dsaVipLastTime < 3600 * 6:
            return
        self.dsaVipLastTime = time.time()
        res = self.dsaVipInfo()
        if res:
            vip = res.get("dsaVip")
            if vip:
                isClaimed = vip.get("isClaimed")
                level = vip.get("level", 0)
                if not isClaimed and level > 0:
                    self.dsaVipClaim()

    def tryBuyCaravanItem(self, retry=0):
        """
        尝试购买商人物品
        """
        caravanList = self.caravanList()
        if caravanList is None:
            if self.isInvalid:
                return None
            self.randomSleep(5, 10)
            return self.tryBuyCaravanItem(retry=retry + 1)
        minCost = 999999999
        caravanItemId = None
        for item in caravanList:
            costItemCode = item.get("costItemCode")
            if costItemCode == 10100005:
                continue
            cost = item.get("cost")
            if cost < minCost:
                minCost = cost
                caravanItemId = item.get("_id")
                amount = item.get("amount")
                if amount == 0:
                    self.log("商人物品已购买")
                    return

        if caravanItemId:
            res = self.caravanBuy(caravanItemId)
            if res:
                self.log("购买商人物品成功")
                return True
        self.log("商人物品购买失败")
        return False

    # 初始建造
    def build1(self):
        self.buildNew(self.buildPositionList[1:6])

    # 建造2
    def build2(self):
        self.buildNew([109, 110, 111])

    # 建造3
    def build3(self):
        self.buildNew([115, 116])

    def build4(self):
        self.buildNew([101, 102, 103, 114, 117, 118])

    # 尝试换头像
    def tryChangeFace(self):
        if self.isInvalid:
            return False
        profile = self.kingdomProfileMy()
        if profile:
            faceCode = profile.get("faceCode")
            if faceCode is not None and faceCode <= 1:
                self.changeFace()
            return profile.get("worldId")
        return 0

    # 新账号设置

    def newAccountEvent(self):
        # 改名
        if self.name[:5] == "GUEST":
            setSucceed = False
            for _i in range(0, 3):
                name = createName()
                if self.changeName(name):
                    self.log("设置名字成功：" + name)
                    setSucceed = True
                    break

            if not setSucceed:
                self.errorLog("无法设置名字，跳过账号")
                return

    def tryHarvestAll(self):
        """
        尝试收获
        """
        if self.isInvalid:
            return False
        [self.harvest(v) for v in self.harvestList]
        # self.runTasks(tasks)
        # self.debuglog("收获完成")

    def anyHarvest(self):
        """
        尝试收获
        """
        if self.isInvalid:
            return False
        if self.level == 1:
            return self.taskAll()
        # trunk-ignore(bandit/B311)
        return self.harvest(random.choice(self.harvestList))

    def tryJoinAlliance(self):
        """尝试加入联盟"""
        if self.allianceId is None:
            for i in range(10):
                self.randomSleep(1, 5)
                alliances = self.allianceSearch(i)
                if alliances is None:
                    continue
                for alliance in alliances:
                    option = alliance.get("option")
                    if (
                        option
                        and option.get("allowType")
                        and option.get("allowType") == 1
                    ):
                        maxMembers = alliance.get("maxMembers")
                        numMembers = alliance.get("numMembers")
                        if maxMembers and numMembers and maxMembers > numMembers + 3:
                            allianceId = alliance.get("_id")
                            res = self.allianceJoin(allianceId)
                            if res:
                                self.log("加入联盟成功")
                                self.allianceId = allianceId
                                codes = [31102001, 31102002, 31101001]
                                for code in codes:
                                    self.randomSleep(2, 5)
                                    if self.allianceResearchInfo(code):
                                        self.allianceResearchDonate(code)

                                return True
                            self.randomSleep(4, 7)
        return False

    def tryJoinAllianceBySystem(self, allianceId=None):
        # 通过系统推荐加盟
        alliance = self.allianceRecommend()
        if alliance:
            if not allianceId:
                allianceId = alliance.get("_id")
            res = self.allianceJoin(allianceId)
            if res:
                self.log("加入联盟成功")
                self.allianceId = allianceId
                return True
        return False

    def tryAllianceDonate(self):
        """尝试捐款并买v"""
        self.vipInfo()
        if self.vip == 1 and self.allianceId:
            res = self.allianceShopList()
            if res:
                alliancePoint = res.get("alliancePoint")
                allianceShopItems = res.get("allianceShopItems")
                vipItemAp = 0
                for shopItem in allianceShopItems:
                    if shopItem.get("code") == 10101008:
                        if shopItem.get("amount") > 0:
                            vipItemAp = shopItem.get("ap_1")
                        break

                if vipItemAp > 0:
                    gap = vipItemAp - alliancePoint
                    if gap > 0:
                        res = self.allianceResearchList()
                        if res:
                            # trunk-ignore(ruff/F841)
                            recommendResearch = res.get("recommendResearch")
                            researches = res.get("researches")
                            researcheLen = len(researches)
                            index = 0
                            research = researches[index].get("code")

                            research = researches[
                                # trunk-ignore(bandit/B311)
                                random.randint(0, researcheLen - 1)
                            ].get("code")
                            self.allianceResearchDonateAll(research)
                            research = researches[
                                # trunk-ignore(bandit/B311)
                                random.randint(0, researcheLen - 1)
                            ].get("code")
                            self.allianceResearchDonateAll(research)

                    res = self.allianceShopList()
                    if res:
                        alliancePoint = res.get("alliancePoint")
                        if alliancePoint >= vipItemAp:
                            if self.allianceShopBuy(1):
                                self.randomSleep(1, 3)
                                self.itemUse(10101008, 1)
                                self.randomSleep(1, 3)
                                if self.allianceShopBuy(1):
                                    self.randomSleep(1, 3)
                                    self.itemUse(10101008, 1)
                                self.randomSleep(1, 3)
                                self.vipClaim()
                                self.log("购买联盟vip并升级成功")

    def foreachAllianceDonateAll(self, researches, isBattle=True, maxLevel=20):
        for research in researches:
            code = research.get("code")
            level = research.get("level")
            if isBattle and code >= 31102000:
                continue
            if not isBattle and code < 31102000:
                continue
            if level >= maxLevel:
                continue
            if self.allianceMaxResearches.get(code) == level:
                continue
            if self.allianceResearchInfo(code):
                self.allianceResearchDonateAll(code)
                return True
            self.randomSleep(2, 5)
        return False

    def tryAllianceDonateAll(self, maxLevel=20):
        """尝试研究科技"""
        if self.allianceInfoMy():
            if self.allianceId in ["619df0c09001ff0c6017220f"]:
                self.debuglog("联盟满科技跳过")
                return True
            res = self.allianceResearchList()
            researches = res["researches"]
            numDonation = res["numDonation"]
            recommendResearch = res["recommendResearch"]
            if numDonation:
                researches.reverse()
                # 战斗科技
                if self.foreachAllianceDonateAll(
                    researches, isBattle=True, maxLevel=maxLevel
                ):
                    return True
                # 推荐升级
                if recommendResearch:
                    if self.allianceResearchInfo(recommendResearch):
                        self.allianceResearchDonateAll(recommendResearch)
                        return True
                # 生产科技
                if self.foreachAllianceDonateAll(
                    researches, isBattle=False, maxLevel=maxLevel
                ):
                    return True
                self.setAllianceResearchMax()
                return False
            else:
                return True
        return True

    def tryHelpAll(self):
        helpList = self.allianceHelpList()
        if helpList:
            if len(helpList) > 0:
                self.allianceHelpAll()
                return True
        return False

    def tryClaimPkg(self):
        pkgList = self.pkgList(pkgType=15)
        if not pkgList:
            return
        for pkg in pkgList:
            # 礼包bug已修复 需要判断礼包sku
            if pkg.get("numRemain", 0) > 0:
                package = pkg.get("package", {})
                if package.get("sku") == "0" and self.pkgDailyFreeClaim(pkg.get("_id")):
                    self.log("领取pkg成功 ")

    def tryRandomAppleLink(self):
        """尝试绑定苹果账号"""
        if self.isInvalid:
            return False
        if self.appleSubId:
            self.log("苹果账号已绑定")
            return False
        if self.level < 10:
            self.log("等级小于10，跳过绑定")
            return False
        self.appleSubId = createAppleSubId()
        if self.linkApple():
            self.log("绑定苹果账号成功")
            return True
        self.log("绑定异常")
        return False

    def tryRoulette_2023(self):
        # 2023.10.14 转盘活动
        if self.rouletteEvent:
            if self.rouletteEvent.get("isOpen"):
                dashBoard = self.rouletteDashBoard()
                if dashBoard:
                    freePickRemain = dashBoard.get("freePickRemain")
                    if freePickRemain:
                        self.rouletteSpin()

    def tryJoinAllianceByTag(self, tag):
        self.allianceInfoMy()
        if self.allianceId:
            return True
        alliances = self.allianceSearch(keyword=tag)
        if alliances:
            self.randomSleep(1, 2, msg=f"尝试加入联盟:{tag}")
            for alliance in alliances:
                allianceTag = alliance.get("tag")
                maxMembers = alliance.get("maxMembers")
                numMembers = alliance.get("numMembers")
                if allianceTag == tag:
                    if maxMembers > numMembers:
                        allianceId = alliance.get("_id")
                        res = self.allianceJoin(allianceId)
                        if res:
                            self.addBotWorkLog(f"加入联盟提交:{tag}")
                            return True
                        else:
                            self.addBotWorkLog(f"尝试加入联盟:{tag}失败 请求失败")
                            return False
                    else:
                        self.addBotWorkLog(f"尝试加入联盟:{tag}失败 人数已满")
                        return False
            self.addBotWorkLog(f"尝试加入联盟:{tag}失败 tag不对?")
        return False
