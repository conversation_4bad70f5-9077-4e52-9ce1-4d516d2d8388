# /bin/env python3
# -*- coding: UTF-8 -*-

"""
功能描述：国王用户接口
编写人：darkedge
编写日期：2024年01月08日
"""


# trunk-ignore(ruff/F401)
import Unit.enum as Enum
from Api.User.Base import UserBaseApi, domain
# trunk-ignore(ruff/F401)
from Model.UserError import UserError

domainNew = domain


class UserKingPowerApi(UserBaseApi):

    # api/shrine/title
    def shrineTitle(self):
        """官职列表"""
        if self.isInvalid:
            return None

        res = self.request(domainNew + "api/shrine/title")
        if res and res.get("result"):
            return res
        return None

    # api/shrine/title/change
    def shrineTitleChange(self, code, targetKingdomId):
        """修改官职"""
        if self.isInvalid:
            return None

        res = self.request(
            domainNew + "api/shrine/title/change",
            {"code": code, "targetKingdomId": targetKingdomId},
        )
        if res and res.get("result"):
            return True
        return None

    # api/field/cvc/king/kick
    def cvcKingKick(self, foId, useLock=True):
        """踢出cvc"""
        if self.isInvalid:
            return None

        res = self.request(domainNew + "api/field/cvc/king/kick", {"foId": foId}, useLock=useLock)
        if res and res.get("result"):
            fo = res.get("fo")
            occupied = fo.get("occupied")
            name = occupied.get("name")
            self.log(f'踢出cvc {name}')
            return name
        else:
            self.errorLog(f'踢出cvc失败 {res}')
        return None

    # api/shrine/bounty
    def shrineBounty(self):
        """国王礼物列表"""
        if self.isInvalid:
            return None

        res = self.request(domainNew + "api/shrine/bounty")
        if res and res.get("result"):
            return res
        return None

    # api/shrine/bounty/give
    def shrineBountyGive(self, code, targetKingdomId):
        """国王送礼"""
        if self.isInvalid:
            return None

        res = self.request(
            domainNew + "api/shrine/bounty/give",
            {"code": code, "targetKingdomId": targetKingdomId},
        )
        if res and res.get("result"):
            return True
        return None

    # api/event/cvc/ranking/continent
    # ranking 是列表 每个元素是字典
    #{
    #     "point": 82115455,
    #     "rank": 1,
    #     "kingdom": {
    #         "_id": "61a48132f1d5d54d73260bd7",
    #         "level": 35,
    #         "power": 576543678,
    #         "name": "罗叔叔",
    #         "alliance": {
    #             "tag": "LDAT"
    #         },
    #         "skin": {
    #             "itemId": "67273263508f783617eb1b74"
    #         },
    #         "faceCode": 7020140,
    #         "congressTitle": 0,
    #         "lord": {
    #             "level": 50,
    #             "xp": 20172340,
    #             "point": 0
    #         },
    #         "worldId": 20
    #     }
    # }
    def cvcRankContinent(self, eventId = None, worldId = 20):
        """cvc大陆排名"""
        if self.isInvalid:
            return None
        if not eventId:
            return None
        res = self.request(domainNew + "api/event/cvc/ranking/continent",{"eventId":eventId, "worldId":worldId})
        if res and res.get("result"):
            return res
        return None

    # api/shrine/skill skillCode 101 采集加速 102 治疗 103 训练 104 水晶
#     [
#   {
#     "skillCode": 104,
#     "abilities": [
#       [
#         129,
#         3,
#         100
#       ]
#     ],
#     "nextSkillTime": "2025-01-06T01:38:23.097Z",
#     "endTime": "2025-01-05T13:38:23.097Z",
#     "coolTime": 43200,
#     "duration": 0,
#     "needMedal": 3000
#   }
# ]
    def shrineSkill(self):
        """国王技能"""
        if self.isInvalid:
            return None

        res = self.request(domainNew + "api/shrine/skill")
        if res and res.get("result"):
            return res.get("skills")
        return None

    # api/shrine/skill/use
    def shrineSkillUse(self, skillId):
        """使用国王技能"""
        if self.isInvalid:
            return None

        res = self.request(domainNew + "api/shrine/skill/use", {"code": skillId})
        if res and res.get("result"):
            return True
        return None
