# /bin/env python3
# -*- coding: UTF-8 -*-

"""
功能描述：用户城堡内接口
编写人：darkedge
编写日期：2021年12月31日
"""

# trunk-ignore(ruff/F401)
import datetime

# trunk-ignore(ruff/F401)
import random
import time

import Unit.enum as Enum
from Api.User.Base import UserBaseApi, domain
from Model.UserError import UserError

domainNew = domain


class UserKingdom(UserBaseApi):
    dailyLevel5 = False

    # 主线任务
    def questMain(self):
        if self.isInvalid:
            return False

        res = self.request(domainNew + "api/quest/main")
        if res and res.get("result"):
            return res
        return None

    # 支线任务
    def questList(self):
        if self.isInvalid:
            return False

        self.randomSleep(1, 2)
        res = self.request(domainNew + "api/quest/list")
        if res and res.get("result"):
            return res
        return None

    # 日常任务
    def questDaily(self):
        if self.isInvalid:
            return False

        self.randomSleep(1, 2)
        res = self.request(domainNew + "api/quest/list/daily")
        if res and res.get("result"):
            dailyQuest = res.get("dailyQuest")
            self.dailyPoint = dailyQuest.get("point")
            rewards = dailyQuest.get("rewards", [])
            for reward in rewards:
                if reward.get("status") == 2 and reward.get("level") == 5:
                    self.dailyLevel5 = True
            return dailyQuest
        return None

    # 完成任务
    def questClaim(self, questId, code):
        if self.isInvalid:
            return False
        data = {"questId": questId, "code": code}
        # self.randomSleep(1,3)
        res = self.request(domainNew + "api/quest/claim", data, isB64=True)
        if res and res.get("result"):
            self.log("完成任务")
            self.randomSleep(1, 2)
            return res
        return None

    # 完成日常任务
    def questClaimDaily(self, questId, code):
        if self.isInvalid:
            return False
        data = {"questId": questId, "code": code}
        res = self.request(domainNew + "api/quest/claim/daily", data)
        if res and res.get("result"):
            self.log("完成日常任务")
            if code == 71100015:
                self.lastHarvest500 = True
                self.log("完成金矿日常")
            self.randomSleep(1, 2)
            return res
        return None

    # 日常等级任务
    def questClaimDailyLevel(self, level):
        if self.isInvalid:
            return False
        data = {"level": level}
        res = self.request(domainNew + "api/quest/claim/daily/level", data)
        if res and res.get("result"):
            self.log(f"完成日常等级任务{level}")
            self.randomSleep(1, 2)
            return res
        return None

    # 事件信息
    def eventInfo(self, rootEventId):
        if self.isInvalid:
            return False
        data = {"rootEventId": rootEventId}
        res = self.request(domainNew + "api/event/info", data)
        if res and res.get("result"):
            return res
        return None

    # api/field/fieldobject/info 野外信息
    def fieldObjectInfo(self, foId):
        if self.isInvalid:
            return False
        data = {"foId": foId}
        res = self.request(domainNew + "api/field/fieldobject/info", data)
        if res and res.get("result"):
            return res
        return None

    # 领取事件奖励
    def eventClaim(self, eventId, eventTargetId, code):
        if self.isInvalid:
            return False
        data = {"eventId": eventId, "eventTargetId": eventTargetId, "code": code}
        self.randomSleep(2, 4)
        res = self.request(domainNew + "api/event/claim", data)
        if res and res.get("result"):
            self.log("领取事件奖励")
            return res
        return None

    # 事件列表
    def eventList(self):
        if self.isInvalid:
            return False

        res = self.request(domainNew + "api/event/list")
        if res and res.get("result"):
            self.loadEvents(res.get("events"))
            return res
        return None

    # 任务列表
    def taskList(self):
        if self.isInvalid:
            return False

        res = self.request(domainNew + "api/task/all")
        if res and res.get("result"):
            return res
        return None

    def taskAll(self, useLock=True):
        """
        野外/建筑研究任务列表
        {
        "result": true,
        "fieldTasks": [
            {
            "_id": "61cc69b5c0f8c76d253745c8",
            "code": 4,
            "status": 1,
            "started": "2021-12-29T13:59:17.997Z",
            "expectedEnded": "2021-12-29T14:18:56.997Z",
            "time": 1179,
            "param": {
                "moId": "61cc69ab98e1e8101c7e4aa1",
                "foLoc": [
                21,
                231,
                1452
                ],
                "foId": "61cc2b8bb0046e0f5545279f",
                "gatheringSpeed": 12.11111111111111,
                "amount": 14269,
                "lastUpdated": "2021-12-29T13:59:17.995Z",
                "gatherValue": 0,
                "gatherType": 0
            }
            }
        ],
        "kingdomTasks": [
            {
            "_id": "61cc6c81c95e9e11632ec086",
            "code": 1,
            "status": 1,
            "started": "2021-12-29T14:11:13.178Z",
            "expectedEnded": "2021-12-29T15:07:49.404Z",
            "time": 3396.2264150943392,
            "param": {
                "level": 7,
                "position": 5,
                "buildingCode": 40100105
            }
            },
            {
            "_id": "61cc6c10e2d7c20fdc116907",
            "code": 3,
            "status": 1,
            "started": "2021-12-29T14:09:20.743Z",
            "expectedEnded": "2021-12-29T14:59:20.743Z",
            "time": 3000,
            "param": {
                "troopCode": 50100101,
                "amount": 1000
            }
            }
        ]
        }
        """

        res = self.request(
            domainNew + "api/kingdom/task/all", useLock=useLock, isB64=True
        )
        if res and res.get("result"):
            fieldTasks = res.get("fieldTasks")
            self.fieldTasks = fieldTasks
            kingdomTasks = res.get("kingdomTasks")
            self._kingdomTasks = kingdomTasks
            return res
        return None

    def kingdomTasks(self, retry=0):
        if self.isInvalid:
            return None
        if retry > 3:
            return None
        res = self.taskAll()
        if res:
            return res.get("kingdomTasks")
        self.randomSleep(5, 10)
        return self.kingdomTasks(retry + 1)

    # api/kingdom/task/cancel
    def taskCancel(self, taskId):
        if self.isInvalid:
            return False
        data = {"taskId": taskId}
        res = self.request(domainNew + "api/kingdom/task/cancel", data)
        if res and res.get("result"):
            self.log("取消任务")
            return True
        return False

    # api/kingdom/building/demolish
    def demolish(self, position):
        """拆除建筑"""
        if self.isInvalid:
            return None

        data = {"position": position}
        res = self.request(domainNew + "api/kingdom/building/demolish", data)
        if res and res.get("result"):
            return res
        return None

    # 建造
    def build(self, position, buildingCode, returnRes=False, instant=0):
        if self.isInvalid:
            return False

        data = {"position": position, "buildingCode": buildingCode, "instant": instant}
        res = self.request(domainNew + "api/kingdom/building/build", data)
        if res and res.get("result"):
            kingdom = res.get("kingdom")
            if kingdom:
                self.buildings = kingdom.get("buildings")
            resources = res.get("resources")
            if resources:
                self.resources = resources
            if returnRes:
                return kingdom
            return True
        return None

    # 升级建筑
    def buildingUpgrade(self, position, level, instant=0):
        if self.isInvalid:
            return False

        data = {"position": position, "level": level, "instant": instant}
        res = self.request(domainNew + "api/kingdom/building/upgrade", data, isB64=True)
        if res and res.get("result"):
            resources = res.get("resources")
            if resources:
                self.resources = resources
            updateBuilding = res.get("updateBuilding")
            if updateBuilding:
                self.setBuilding(updateBuilding.get("position"))
            return res
        elif res.get("err"):
            code = res.get("err").get("code")
            if code and code == "insufficient_resources":
                raise UserError.ininsufficientResources()
            elif code and code == "full_task":
                self.log("有队列任务，再等等。。垃圾服务器？")
            elif code and code == "not_enough_building":
                self.log("垃圾服务器吞了一口，没有建筑了。。。")
        return None

    # 加速
    def speedup(self, taskId, code, amount, isBuy=0):
        if self.isInvalid:
            return False

        amount = int(amount)
        data = {"taskId": taskId, "code": code, "amount": amount, "isBuy": isBuy}
        res = self.request(domainNew + "api/kingdom/task/speedup", data, isB64=True)
        if res and res.get("result"):
            self.debuglog(f"加速成功 {code} {amount}")
            self.analytics("item/use", f"{code}|{amount}")
            return res.get("task")
        else:
            if res and res.get("err"):
                code = res.get("err").get("code")
                if code == "not_running_task":
                    return True
        return None

    def speedupHeal(self, code, amount, isBuy=0):
        """加速治疗"""

        if self.isInvalid:
            return False

        data = {"code": code, "amount": amount, "isBuy": isBuy}

        res = self.request(domainNew + "api/kingdom/heal/speedup", data, isB64=True)
        if res and res.get("result"):
            wounded = res.get("wounded")
            return wounded

        return None

    # 练兵 50100101 步兵 100兵 5分钟
    def trainTroop(self, troopCode=50100101, amount=100, training=False, instant=0):
        if self.isInvalid:
            return None

        if not training:
            if self.insufficient_resources or self.numTroops > self.maxTroops - 1:
                # 资源不足不练兵
                return None
            if self.isLevel2:
                troopCode += 1

        data = {
            "troopCode": troopCode,
            "amount": amount,
            "instant": instant,
        }
        res = self.request(domainNew + "api/kingdom/barrack/train", data)
        if res and res.get("result"):
            return True
        elif res and res.get("err"):
            code = res.get("err").get("code")
            if code == "insufficient_resources":
                self.insufficient_resources = True
                self.log("资源不足不练兵")
                return None
        return False

    # 兵种升级 api/kingdom/barrack/promote
    def barrackPromote(self, troopCode, amount, instant=0):
        if self.isInvalid:
            return None

        data = {
            "troopCode": troopCode,
            "amount": amount,
            "instant": instant,
        }

        res = self.request(domainNew + "api/kingdom/barrack/promote", data)
        if res and res.get("result"):
            return True
        elif res and res.get("err"):
            code = res.get("err").get("code")
            if code == "insufficient_resources":
                self.insufficient_resources = True
                self.log("资源不足不练兵")
                return None
        return False

    # 研究科技
    def research(self, researchCode, instant=0):
        if self.isInvalid:
            return None

        data = {"researchCode": researchCode, "instant": instant}
        res = self.request(domainNew + "api/kingdom/arcademy/research", data)
        if res and res.get("result"):
            kingdom = res.get("kingdom")
            if kingdom:
                self.resources = kingdom.get("resources")
                self.researches = kingdom.get("researches")
            return True
        return False

    # 科技列表
    def researchList(self):
        if self.isInvalid:
            return None

        res = self.request(domainNew + "api/kingdom/arcademy/research/list")
        if res and res.get("result"):
            self.researches = res.get("researches")
            return res
        return None

    # 收集 5科技 105 兵
    def claim(self, position, training=False):
        if self.isInvalid:
            return None

        if not training:
            if self.insufficient_resources or self.numTroops > self.maxTroops - 2000:
                # 资源不足不练兵 或者兵太多
                return None

        data = {"position": position}
        res = self.request(domainNew + "api/kingdom/task/claim", data)
        if res and res.get("result"):
            self.randomSleep(2, 4)
            return True
        return False

    # 收资源 104粮食 107 石头 108 金币 109 木头
    def harvest(self, position):
        if self.isInvalid:
            return None
        # if position == 105:
        #     # 跳过105兵营
        #     return True
        data = {"position": position}
        self.randomSleep(1, 3)
        res = self.request(domainNew + "api/kingdom/resource/harvest", data, isB64=True)
        if res and res.get("result"):
            resources = res.get("resources")
            if resources:
                self.resources = resources
            return True
        return False

    # 收兵
    def claimTroop(self):
        return self.claim(105)

    # 撤兵
    def dismissTroop(self, troop):
        data = {
            "code": troop.get("code"),
            "amount": troop.get("amount"),
        }
        res = self.request(domainNew + "api/kingdom/profile/troops/dismiss", data)
        if res and res.get("result"):
            self.log("撤销兵力成功")
            return True
        return False

    # 治疗
    def troopRecover(self):
        self.randomSleep(1)
        res = self.request(domainNew + "api/kingdom/hospital/recover", isB64=True)
        if res and res.get("result"):
            return True
        return False

    def wounded(self):
        """医院情况"""
        if self.isInvalid:
            return None

        res = self.request(domainNew + "api/kingdom/hospital/wounded")
        if res and res.get("result"):
            return res.get("wounded")
        return None

    def myresources(self):
        """
        ntf 资源
        """
        if self.isInvalid:
            return None
        if self.checkMintTime():
            return None
        data = {
            "fromId": self.fieldObjectId,
        }
        res = self.request(domainNew + "api/land/myresources", data)
        if res and res.get("result"):
            canMint = res.get("canMint")
            return canMint
        return None

    # api/auth/wallet
    def wallet(self):
        if self.isInvalid:
            return None
        res = self.request(domainNew + "api/auth/wallet")
        if res and res.get("result"):
            return res.get("wallets")
        return None

    # api/auth/wallet/info
    def walletInfo(self):
        """钱包信息"""
        #         {
        #   "result": true,
        #   "wallet": {
        #     "publicKey": "******************************************",
        #     "network": "eth"
        #   },
        #   "nft": {
        #     "land": 0,
        #     "skin": 0,
        #     "resources": 0,
        #     "drago": -1
        #   }
        # }
        if self.isInvalid:
            return None
        res = self.request(domainNew + "api/auth/wallet/info")
        if res and res.get("result"):
            return res
        return False

    # api/auth/wallet/verify
    def walletVerify(self, wallet="metamask"):
        #         {
        #   "result": true,
        #   "verifyId": "623b3b4a2558fa04bb2024e3"
        # }
        if self.isInvalid:
            return None

        data = {"wallet": wallet, "platform": "web"}
        res = self.request(domainNew + "api/auth/wallet/verify", data)
        if res and res.get("result"):
            return res.get("verifyId")
        return None

    # api/auth/wallet/certify
    def walletCertify(self, signedKey, verifyId):
        if self.isInvalid:
            return None

        data = {"signedKey": signedKey, "verifyId": verifyId}
        res = self.request(domainNew + "api/auth/wallet/certify", data)
        if res and res.get("result"):
            self.publicKey = res.get("publicKey")
            return True
        return False

    # api/auth/wallet/network/change arenaz
    def walletNetworkChange(self, network="polygon"):
        if self.isInvalid:
            return None

        data = {"network": network}
        res = self.request(domainNew + "api/auth/wallet/network/change", data)
        if res and res.get("result"):
            return True
        return False

    # api/kingdom/setMainLand
    def setMainLand(self, landId=0):
        """设置主土地 134588为高级地 0为取消"""
        if self.isInvalid:
            return None

        data = {"landId": landId}
        res = self.request(domainNew + "api/kingdom/setMainLand", data)
        if res and res.get("result"):
            return True
        return False

    # api/nft/item/list
    def nftItemList(self):
        if self.isInvalid:
            return None
        data = {"tokenType": 0, "reload": True}
        res = self.request(domainNew + "api/nft/item/list", data)
        if res and res.get("result"):
            self.publicKey = res.get("publicKey")
            return res
        else:
            if res.get("err"):
                if res.get("err").get("code") == "no_linked_wallet":
                    return False
        return None

    # 打包资源
    # type 种类
    # 数量 对应1M 5M 10M
    def mint(self, type, num):
        if self.isInvalid:
            return False

        itemCodes = [
            [10101019, 10101020, 10101021],  # 粮食
            [10101028, 10101029, 10101030],  # 木头
            [10101037, 10101038, 10101039],  # 石头
            [10101046, 10101047, 10101048],  # 黄金
        ]

        itemCode = itemCodes[type][num]
        data = {"wallet": "metamask", "tokenType": "0", "itemCode": itemCode}
        res = self.request(domainNew + "api/nft/item/mint", data)
        if res:
            if res.get("result"):
                typeName = ["粮食", "木头", "石头", "黄金"]
                self.log("%s打包成功" % typeName[type])
                self.dataRecord().updateMintCount(1).commit()
                self.setMintTime()
                return True
            elif res.get("err"):
                code = res.get("err").get("code")
                if code:
                    if code == "mint_limit":
                        self.log("打包频繁")
                    elif code == "no_linked_wallet":
                        self.log("钱包未绑定")

                # 有错误就缓存当前账号时间
                self.setMintTime()

        return False

    def getUrlParam(self, url, key):
        from urllib.parse import parse_qs, urlparse

        parsed_url = urlparse(url)
        query_params = parse_qs(parsed_url.query)
        param_value = query_params.get(key, [])
        if param_value:
            return param_value[0]
        return None

    def mintNft(self, nftItemId):
        """发行nft"""
        if self.isInvalid:
            return None
        data = {"wallet": "metamask", "nftItemId": nftItemId}
        res = self.request(domainNew + "api/nft/item/mint", data)
        if res and res.get("result"):
            txData = res.get("txData")
            if not txData:
                txUrl = res.get("url")
                if txUrl:
                    txData = self.getUrlParam(txUrl, "param")
            return txData
        return False

    # api/nft/item/mint/tx
    def mintTx(self, code, txHash):
        """确认区块信息"""
        if self.isInvalid:
            return None
        data = {"code": code, "txHash": txHash}
        res = self.request(domainNew + "api/nft/item/mint/tx", data)
        if res and res.get("result"):
            return True
        return False

    # api/nft/item/use
    def useNft(self, tokenId, tokenType=0, wallet="metamask"):
        """使用nft"""
        if self.isInvalid:
            return None
        data = {
            "tokenType": tokenType,
            "tokenId": tokenId,
            "wallet": wallet,
        }

        res = self.request(domainNew + "api/nft/item/use", data)
        if res and res.get("result"):
            return res.get("txData")
        return False

    # api/nft/drago/list
    def nftDragoList(self, onlyDrago=True, retry=0):
        """nft龙列表"""
        if retry > 3:
            return None
        if self.isInvalid:
            return None
        res = self.request(domainNew + "api/nft/drago/list", useLock=False)
        if res and res.get("result"):
            if onlyDrago:
                return res.get("dragos")
            else:
                return res
        else:
            if res.get("err"):
                if res.get("err").get("code") == "no_linked_wallet":
                    return False

        return self.nftDragoList(onlyDrago=onlyDrago, retry=retry + 1)

    # api/drago/info
    def dragoInfo(self, dragoId):
        """龙信息"""
        if self.isInvalid:
            return None
        data = {"dragoId": dragoId}
        res = self.request(domainNew + "api/drago/info", data, useLock=False)
        if res and res.get("result"):
            return res
        return None

    # api/nft/item/listDragoMint
    def listDragoMint(self):
        """龙列表"""
        """lair.status == 1 可以mint"""
        if self.isInvalid:
            return None

        res = self.request(domainNew + "api/nft/item/listDragoMint", {})
        if res and res.get("result"):
            return res
        return False

    # api/nft/item/getDragoMintInfo
    def getDragoMintInfo(self, dragoId):
        """龙mint信息"""
        """reducePercentage 降低的百分比 requireTime:[{amount,mintTimeInSecond}] 需要的时间"""
        if self.isInvalid:
            return None
        data = {"dragoId": dragoId}
        res = self.request(domainNew + "api/nft/item/getDragoMintInfo", data)
        if res and res.get("result"):
            return res
        return False

    # api/nft/item/prepareDragoMint
    # {
    #   "result": true,
    #   "tokenType": 0,
    #   "itemCode": 10101061,
    #   "waitTime": 165888,
    #   "endedTime": "2023-04-03T00:24:02.583Z"
    # }
    def prepareDragoMint(self, dragoId, itemCode, tokenType=0):
        """龙mint资源"""
        if self.isInvalid:
            return None

        data = {"dragoId": dragoId, "itemCode": itemCode, "tokenType": tokenType}

        res = self.request(domainNew + "api/nft/item/prepareDragoMint", data)
        if res and res.get("result"):
            return res
        elif res and res.get("err"):
            code = res.get("err").get("code")
            if code == "drago_multiple_minting":
                raise UserError.dragoMultipleMinting()
            elif code == "insufficient_resources":
                raise UserError.ininsufficientResources()
        return False

    # api/nft/item/cancelDragoMint
    def cancelDragoMint(self, dragoId):
        """发行取消"""
        if self.isInvalid:
            return None

        data = {"dragoId": dragoId}

        res = self.request(domainNew + "api/nft/item/cancelDragoMint", data)
        if res and res.get("result"):
            return res
        return False

    # api/nft/item/claimDragoMint
    def claimDragoMint(self, dragoId, wallet="metamask"):
        """领取龙mint"""
        if self.isInvalid:
            return None

        data = {"dragoId": dragoId, "wallet": wallet}

        res = self.request(domainNew + "api/nft/item/claimDragoMint", data)
        if res and res.get("result"):
            self.log(f"领取龙mint成功 {dragoId}")
            nftItem = res.get("nftItem")
            if nftItem:
                self.publicKey = nftItem.get("minterAddress")
            return res
        return False

    # api/drago/fusion
    def dragoFusion(self, dragoId):
        """龙融合"""
        if self.isInvalid:
            return None
        data = {"dragoId": dragoId}

        res = self.request(domainNew + "api/drago/fusion", data)
        if res and res.get("result"):
            return res
        return False

    # api/nft/dst/take/try
    def dstTakeTry(self, amount):
        """dst兑换"""
        if self.isInvalid:
            return None
        data = {"amount": amount}

        res = self.request(domainNew + "api/nft/dst/take/try", data, useLock=False)
        if res and res.get("result"):
            return res
        return False

    # api/nft/dst/take
    def dstTake(self, amount, itemId):
        """dst兑换"""
        """itemId通过itemCode获得 10104137"""
        """txCode txData"""
        """合约 0x2Ba7788d9a865Db427D465431F90B29D79b25B3A"""
        if self.isInvalid:
            return None
        data = {"amount": amount, "itemId": itemId}

        res = self.request(domainNew + "api/nft/dst/take", data, useLock=False)
        if res and res.get("result"):
            return res
        return False

    # api/drago/lair/join
    def dragoLairJoin(self, dragoId):
        """加入龙窝"""
        if self.isInvalid:
            return None
        data = {"dragoId": dragoId}

        res = self.request(domainNew + "api/drago/lair/join", data)
        if res and res.get("result"):
            return res.get("dragos")
        elif res.get("err"):
            err = res.get("err")
            code = err.get("code")
            if code == "already_joined_drago":
                return [{"_id": dragoId}]
        return False

    # api/drago/lair/leave
    def dragoLairLeave(self, dragoId):
        """离开龙窝"""
        if self.isInvalid:
            return None
        data = {"dragoId": dragoId}

        res = self.request(domainNew + "api/drago/lair/leave", data, useLock=False)
        if res and res.get("result"):
            return res.get("dragos")
        return False

    # 切换世界
    def worldChange(self, worldId):
        if self.isInvalid:
            return None
        data = {
            "worldId": worldId,
        }
        res = self.request(domainNew + "api/kingdom/world/change", data)
        if res and res.get("result"):
            fo = res.get("fo")
            if fo and fo.get("loc"):
                self.loc = fo.get("loc")
            return res
        elif res and res.get("err"):
            code = res.get("err").get("code")
            if code:
                if code == "world_change_alliance":
                    return False
        return None

    # 传送
    def teleport(self, loc, type = 0, useLock=True):
        if self.isInvalid:
            return None

        data = {
            "loc": loc,
            "type": type,
        }
        res = self.request(
            domainNew + "api/field/teleport", data, useLock=useLock, isB64=True
        )
        if res:
            if res.get("result"):
                self.log("传送成功")
                self.loc = loc
                self.saveRedisLoc()
                return True
            elif res.get("err"):
                code = res.get("err").get("code")
                errorCodes = ["cant_move", "duplicated_field_object"]
                if code and code in errorCodes:
                    self.errorLog("传送失败 存在物体")
                elif code and code == "teleport_in_banteleport":
                    self.errorLog("传送失败 在禁传区")
                    raise UserError.banteleport()
                elif code and code == "insufficient_item":
                    self.errorLog("传送失败 没有飞行道具")
                    raise UserError.noTeleportItem()
                elif code and code == "march_existed":
                    self.errorLog("传送失败 已经有行军")
                    raise UserError.hasMarch()
                else:
                    self.errorLog("传送失败 : %s" % res)
                    return None
        return False

    # 修理
    def repairWall(self):
        if self.isInvalid:
            return False

        res = self.request(domainNew + "api/kingdom/wall/repair")
        if res:
            return True
        return False

    # 城墙信息
    def wallInfo(self):
        if self.isInvalid:
            return None

        res = self.request(domainNew + "api/kingdom/wall/info")
        if res and res.get("result"):
            wall = res.get("wall")
            if wall:
                durability = wall.get("durability")
                maxDurability = wall.get("maxDurability")
                return durability / maxDurability

        return None

    # 礼包推荐信息
    def recommendInfo(self):
        if self.isInvalid:
            return None

        res = self.request(domainNew + "api/pkg/recommend")
        if res and res.get("result"):
            return res
        return None

    # 礼包列表
    def pkgList(self, pkgType=8):
        if self.isInvalid:
            return None
        data = {}
        if pkgType:
            data["pkgType"] = pkgType

        res = self.request(domainNew + "api/pkg/list", data=data)
        if res and res.get("result"):
            pkgs = res.get("pkgs")
            self.loadPkgs(pkgs)
            return pkgs
        return None

    # 物品列表
    def itemList(self,useLock = True):
        if self.isInvalid:
            return None

        res = self.request(domainNew + "api/item/list", useLock= useLock)
        if res and res.get("result"):
            items = res.get("items")
            if items:
                self.loadItemsInfo(items)
            return res
        return None

    # 使用物品
    def itemUse(self, itemCode, amount, useLock = True):
        """
        10104130 T1士兵
        """
        if self.isInvalid:
            return None

        data = {
            "code": itemCode,
            "amount": amount,
        }
        res = self.request(domainNew + "api/item/use", data, isB64=True, useLock = useLock)
        if res and res.get("result"):
            self.analytics("item/use", f"{itemCode}|{amount}")
            resources = res.get("resources")
            if resources:
                self.resources = resources
            if self.level < 30:
                self.randomSleep(1, 2)
            return res
        elif res and res.get("err"):
            code = res.get("err").get("code")
            if code == "shield_in_attacking":
                self.addBotWorkLog(f"使用{itemCode} {amount} 失败 被攻击无法开盾")
        return None

    def itemBuyUse(self, itemCode, amount):
        """
        购买并使用
        10101040 - 10101046
        5K 10K 50K 100K 500K 1M
        5 9 40 70 300 500
        """
        if self.isInvalid:
            return None

        data = {
            "code": itemCode,
            "amount": amount,
        }
        res = self.request(domainNew + "api/item/buyuse", data)
        if res and res.get("result"):
            return res
        return None

    # api/kingdom/shop/items/buy
    def itemBuy(self, itemCode, amount):
        if self.isInvalid:
            return None

        data = {
            "code": itemCode,
            "amount": amount,
        }
        res = self.request(domainNew + "api/kingdom/shop/items/buy", data)
        if res and res.get("result"):
            return res
        return None

    # 免费宝箱
    def itemFreechest(self, type):
        if self.isInvalid:
            return None

        data = {
            "type": type,
        }
        res = self.request(domainNew + "api/item/freechest", data, isB64=True)
        if res and res.get("result"):
            freeChest = res.get("freeChest")
            self.setFreeChestInfo(freeChest)
            return res
        elif res and res.get("err"):
            err = res.get("err")
            code = err.get("code")
            if code == "free_chest_not_yet":
                if type == 2:
                    self.platinumTime = time.time() + 60 * 60 * 24 * 7
                if type == 1:
                    self.goldTime = time.time() + 60 * 60 * 24
                elif type == 0:
                    self.silverTime = time.time() + 60 * 60 * 24
        return None

    def caravanList(self):
        """
        商人商品列表 items
        costItemCode 10100005 砖石
        cost 费用
        """
        if self.isInvalid:
            return None

        res = self.request(domainNew + "api/kingdom/caravan/list")
        if res and res.get("result"):
            caravan = res.get("caravan")
            if caravan:
                return caravan.get("items")
        return None

    def caravanBuy(self, caravanItemId):
        """商人购买"""
        if self.isInvalid:
            return None

        data = {
            "caravanItemId": caravanItemId,
        }
        res = self.request(domainNew + "api/kingdom/caravan/buy", data)
        if res and res.get("result"):
            return res
        return None

    # api/kingdom/dsavipshop/list
    def dsavipshopList(self):
        # dsavip限时商店
        if self.isInvalid:
            return None

        res = self.request(domainNew + "api/kingdom/dsavipshop/list")
        if res and res.get("result"):
            return res.get("dsaVipShop")
        return None

    # api/kingdom/dsavipshop/buy
    def dsavipshopBuy(self, code, amount):
        # dsavip限时商店
        if self.isInvalid:
            return None

        data = {
            "code": code,
            "amount": amount,
        }
        res = self.request(domainNew + "api/kingdom/dsavipshop/buy", data)
        if res and res.get("result"):
            return res
        return None

    # vip列表
    def vipshopList(self):
        if self.isInvalid:
            return None

        res = self.request(domainNew + "api/kingdom/vipshop/list")
        if res and res.get("result"):
            return res
        return None

    # 购买vip商品 10101008 vip经验100 4种资源 10101017 10101026 10101035 10101044
    def vipshopBuy(self, code, amount):
        if self.isInvalid:
            return None

        if amount <= 0:
            return None
        data = {
            "code": code,
            "amount": amount,
        }
        res = self.request(domainNew + "api/kingdom/vipshop/buy", data, isB64=True)
        if res and res.get("result"):
            self.randomSleep(1, 2)
            return res
        return None

    # vip信息
    def vipInfo(self):
        if self.isInvalid:
            return None

        res = self.request(domainNew + "api/kingdom/vip/info")
        if res and res.get("result"):
            vip = res.get("vip")
            self.vipPoint = vip.get("point")
            level = vip.get("level")
            self.vip = level

            return res
        return None

    # vip领取
    def vipClaim(self):
        if self.isInvalid:
            return None

        res = self.request(domainNew + "api/kingdom/vip/claim", isB64=True)
        if res and res.get("result"):
            self.log("领取vip成功")
            return res
        return None

    # dsavip/info
    def dsaVipInfo(self):
        if self.isInvalid:
            return None

        res = self.request(domainNew + "api/kingdom/dsavip/info")
        if res and res.get("result"):
            return res
        return None

    # api/kingdom/dsavip/claim
    def dsaVipClaim(self):
        if self.isInvalid:
            return None

        res = self.request(domainNew + "api/kingdom/dsavip/claim", isB64=True)
        if res and res.get("result"):
            self.log("领取dsavip成功")
            return res
        return None

    # api/alliance/search
    def allianceSearch(self, page=1, keyword=""):
        """
        联盟搜索
        alliances:[
            {
                "_id": "61c194e962a45610b7514215",
                "flag": {
                    "backShape": 16,
                    "patternShape": 0
                },
                "maxMembers": 62,
                "power": 297837945,
                "langCode": "all",
                "name": "FightAllTheWay",
                "tag": "FATW",
                "option": {
                    "allowType": 2 # 1自由
                },
                "numMembers": 62
            }
        ]
        """
        if self.isInvalid:
            return None

        data = {"page": page, "keyword": keyword}
        res = self.request(domainNew + "api/alliance/search", data)
        if res and res.get("result"):
            return res.get("alliances")
        return None

    # api/alliance/building/list
    def allianceBuildingList(self):
        """联盟建筑"""
        if self.isInvalid:
            return None

        res = self.request(domainNew + "api/alliance/building/list")
        if res and res.get("result"):
            return res
        return None

    # /api/alliance/recommend
    def allianceRecommend(self):
        """联盟推荐"""
        if self.isInvalid:
            return None

        res = self.request(domainNew + "api/alliance/recommend")
        if res and res.get("result"):
            return res.get("alliance")
        return None

    # api/alliance/gift/list
    def allianceGiftList(self):
        """联盟礼物"""
        """
        [{
            "item": {
                "code": 10105013,
                "amount": 1
            },
            "claimed": false,
            "gainItems": [],
            "_id": "630e1e2075e5f839cee53de9",
            "fromName": "高大DA",
            "expireTime": "2022-08-31T14:26:40.735Z"
        }]
        """
        if self.isInvalid:
            return None

        res = self.request(domainNew + "api/alliance/gift/list")
        if res and res.get("result"):
            return res.get("gifts")
        return None

    # api/alliance/gift/claim/all
    def allianceGiftClaimAll(self):
        """联盟礼物领取"""
        if self.isInvalid:
            return None

        res = self.request(domainNew + "api/alliance/gift/claim/all")
        if res and res.get("result"):
            return res.get("gifts")
        return None

    # api/alliance/member/fo
    def allianceMemberFo(self, targetId):
        """获取盟友位置"""
        if self.isInvalid:
            return None

        data = {
            "targetId": targetId,
        }

        res = self.request(domainNew + "api/alliance/member/fo", data)
        if res and res.get("result"):
            return res.get("fo")
        return None

    # api/alliance/members/list
    def allianceMembers(self, allianceId=""):
        """联盟成员"""
        if self.isInvalid:
            return None

        res = self.request(
            domainNew + "api/alliance/members/list", {"allianceId": allianceId}
        )
        if res and res.get("result"):
            return res.get("members")
        return None

    # api/alliance/member/disband
    def allianceMemberDisband(self, memberKingdomId):
        """剔除联盟成员"""
        if self.isInvalid:
            return None

        res = self.request(
            domainNew + "api/alliance/member/disband",
            {"memberKingdomId": memberKingdomId},
        )
        if res and res.get("result"):
            return True
        return False

    # api/alliance/join
    def allianceJoin(self, allianceId):
        """
        联盟加入
        """
        if self.isInvalid:
            return None

        data = {"allianceId": allianceId}
        res = self.request(domainNew + "api/alliance/join", data)
        if res and res.get("result"):
            alliance = res.get("alliance")
            if alliance:
                self.allianceId = allianceId
                return True
            return res
        return None

    # api/alliance/leave
    def allianceLeave(self):
        """退盟"""
        if self.isInvalid:
            return None

        res = self.request(domainNew + "api/alliance/leave")
        if res and res.get("result"):
            self.allianceId = None
            return res
        return None

    # api/alliance/info
    def allianceInfo(self, allianceId):
        """联盟信息"""
        if self.isInvalid or not allianceId:
            return None

        res = self.request(domainNew + "api/alliance/info", {"allianceId": allianceId})
        if res and res.get("result"):
            return res.get("alliance")
        return None

    # api/alliance/info/my
    def allianceInfoMy(self):
        """
        我的联盟信息
        """
        if self.isInvalid:
            return None

        res = self.request(domainNew + "api/alliance/info/my")
        if res and res.get("result"):
            alliance = res.get("alliance")
            self.allianceId = alliance.get("_id")
            leaderKingdomId = alliance.get("leaderKingdomId")
            self.isAllianceLeader = leaderKingdomId == self.kingdomId
            return res
        return None

    # api/alliance/help/list
    def allianceHelpList(self):
        """
        联盟求助列表
        """
        if self.isInvalid or not self.allianceId:
            return None

        res = self.request(domainNew + "api/alliance/help/list")
        if res and res.get("result"):
            return res.get("otherTasks")
        elif res and res.get("err"):
            code = res.get("err").get("no_alliance")
            if code == "no_alliance":
                self.allianceId = None
        return None

    # api/alliance/help/all
    def allianceHelpAll(self):
        """
        支援联盟
        """
        self.debuglog("allianceHelpAll")
        if self.isInvalid:
            return None

        res = self.request(domainNew + "api/alliance/help/all")
        if res and res.get("result"):
            return res
        return None

    # api/alliance/research/list
    def allianceResearchList(self):
        """
        联盟研究列表
        """
        if self.isInvalid:
            return None

        res = self.request(domainNew + "api/alliance/research/list")
        if res and res.get("result"):
            return res
        return None

    # api/alliance/research/info
    def allianceResearchInfo(self, researchCode):
        """
        联盟研究详情
        """
        if self.isInvalid:
            return None

        data = {"researchCode": researchCode}
        res = self.request(domainNew + "api/alliance/research/info", data)
        if res and res.get("result"):
            status = res.get("status")
            if status > 3:
                return True
            elif status == 3:
                level = res.get("level")
                self.allianceMaxResearches[researchCode] = level - 1
            return False
        return None

    # api/alliance/research/donate
    def allianceResearchDonate(self, code=31102001, method=0):
        """
        联盟研究资金
        """
        if self.isInvalid:
            return None

        data = {"code": code, "method": method}
        self.randomSleep(1, 2)
        res = self.request(domainNew + "api/alliance/research/donate", data)
        if res and res.get("result"):
            gainRP = res.get("gainRP")
            return gainRP
        if res and res.get("err"):
            err = res.get("err")
            code = err.get("code")
            if code and code == "no_donation":
                return False

        return None

    # api/alliance/research/donateAll
    def allianceResearchDonateAll(self, code=31102001):
        """
        联盟研究梭哈
        """
        if self.isInvalid:
            return None

        data = {
            "code": code,
        }
        researchName = Enum.ALLIANCE_RESEARCHS.get(code, "UNKNOWN")
        if researchName:
            self.debuglog(f"联盟研究梭哈:{researchName}")
        res = self.request(domainNew + "api/alliance/research/donateAll", data)
        if res and res.get("result"):
            return res
        return None

    # api/alliance/shop/list
    def allianceShopList(self):
        """
        联盟商店列表
        """
        if self.isInvalid:
            return None

        res = self.request(domainNew + "api/alliance/shop/list")
        if res and res.get("result"):
            return res
        return None

    # api/alliance/shop/buy
    def allianceShopBuy(self, amount, code=10101008):
        """
        联盟商店购买 10101008vip点数
        """
        if self.isInvalid:
            return None

        data = {"code": code, "amount": amount}
        res = self.request(domainNew + "api/alliance/shop/buy", data)
        if res and res.get("result"):
            return res
        return None

    # api/kingdom/profile/my
    def kingdomProfileMy(self):
        """
        我的信息
        """
        if self.isInvalid:
            return None

        res = self.request(domainNew + "api/kingdom/profile/my")
        if res and res.get("result"):
            profile = res.get("profile")
            self.name = profile.get("name")
            if self.loc is None:
                worldId = profile.get("worldId")
                self.loc = [worldId, 0, 0]
            actionPoint = profile.get("actionPoint")
            if actionPoint:
                value = actionPoint.get("value")
                self.actionPoint = value
            dragoActionPoint = profile.get("dragoActionPoint")
            if dragoActionPoint:
                value = dragoActionPoint.get("value")
                self.dragoActionPoint = value

            return profile
        return None

    # api/kingdom/profile/my/history
    def profileHistory(self):
        """
        我的历史记录
        """
        if self.isInvalid:
            return None

        res = self.request(domainNew + "api/kingdom/profile/my/history")
        if res and res.get("result"):
            # stats.economy.gathering
            return res.get("history")
        return None

    # api/chat/logs
    def chatLogs(self, chatChannel, lastLogId=None):
        """聊天频道"""
        if self.isInvalid:
            return None

        data = {"chatChannel": chatChannel}
        if lastLogId:
            data["lastLogId"] = lastLogId
        res = self.request(domainNew + "api/chat/logs", data)
        if res and res.get("result"):
            return res.get("chatLogs")
        return None

    # api/chat/new
    def chatNew(self, loc, text, chatChannel=2, chatType=2):
        """
        发送聊天
        """
        if self.isInvalid:
            return None

        data = {
            "param": {
                "loc": loc,
            },
            "text": text,
            "chatChannel": chatChannel,
            "chatType": chatType,
        }
        res = self.request(domainNew + "api/chat/new", data)
        if res and res.get("result"):
            return res
        return None

    ################################################ 活动api分割线 #####################################################

    def gatheringDashBoard(self):
        if self.isInvalid:
            return None

        res = self.request(domainNew + "api/event/gathering/dashboard")
        if res and res.get("result"):
            return res.get("dashboard")
        return None

    def gatheringUse(self, amount=0):
        if self.isInvalid:
            return None

        data = {"amount": amount}
        res = self.request(domainNew + "api/event/gathering/use", data)
        if res and res.get("result"):
            return res.get("dashboard")
        return None

    def gatheringClaimAll(self):
        if self.isInvalid:
            return None

        res = self.request(domainNew + "api/event/gathering/claim/all")
        if res and res.get("result"):
            return res
        return None

    # api/event/gathering/unlock/premium
    def gatheringUnlockPremium(self):
        if self.isInvalid:
            return None

        res = self.request(domainNew + "api/event/gathering/unlock/premium")
        if res and res.get("result"):
            return res
        return None

    # api/mail/read
    def mailRead(self, mailId, sent=False):
        """读取邮件"""
        if self.isInvalid:
            return None

        data = {"mailId": mailId, "sent": sent}
        res = self.request(domainNew + "api/mail/read", data)
        if res and res.get("result"):
            return res.get("mail")
        return None

    # 清空邮件
    def clearMail(self, category=1):
        """清空邮件"""
        if self.isInvalid:
            return None

        res = self.request(domainNew + "api/mail/claim/all", {"category": category})
        if res and res.get("result"):
            return True
        return None

    def deleteMail(self, category=3):
        """删除已读邮件"""
        if self.isInvalid:
            return None

        res = self.request(domainNew + "api/mail/delete/all", {"category": category})
        if res and res.get("result"):
            return True
        return None

    def deleteMails(self, mailIds):
        """删除邮件"""
        if self.isInvalid:
            return None
        res = self.request(domainNew + "api/mail/delete", {"mailIds": mailIds})
        if res and res.get("result"):
            return True
        return None

    def mailList(self, category=3):
        """
        邮件列表 0 个人 1 战报 2 联盟 3 系统
        {
            "result": true,
            "mails": [
                {
                "_id": "61c959bccaae991ccd98226a",
                "from": {
                    "name": "Admin"
                },
                "type": 11,
                "status": 1,
                "receiveDate": "2021-12-27T06:14:20.365Z",
                "subject": {
                    "subject": "Thanks for your patience."
                },
                "content": "Enjoy! Here are rewards for the sages who waits.",
                "like": false
                }
            ],
            "category": 3
        }
        """
        if self.isInvalid:
            return

        res = self.request(domainNew + "api/mail/list", {"category": category})
        if res and res.get("result"):
            return res
        return None

    def mailClaim(self, mailId):
        """
        领取邮件
        """
        if self.isInvalid:
            return

        self.randomSleep(2, 4)
        res = self.request(domainNew + "api/mail/claim/items", {"mailId": mailId})
        if res and res.get("result"):
            return res
        return None

    # /api/mail/unlike
    def mailLike(self, mailId):
        """收藏"""
        if self.isInvalid:
            return None

        res = self.request(domainNew + "api/mail/like", {"mailId": mailId})
        if res and res.get("result"):
            return True
        return False

    def mailUnlike(self, mailId):
        """取消收藏"""
        if self.isInvalid:
            return None

        res = self.request(domainNew + "api/mail/unlike", {"mailId": mailId})
        if res and res.get("result"):
            return True
        return False

    def skillList(self):
        """技能列表"""
        if self.isInvalid:
            return None

        res = self.request(domainNew + "api/skill/list", useLock=False)
        if res and res.get("result"):
            return res.get("skills")
        return None

    # api/skill/use
    def skillUse(self, code, targetId=None):
        """使用技能"""
        if self.isInvalid:
            return None

        data = {"code": code}
        if targetId:
            data["targetId"] = targetId

        res = self.request(domainNew + "api/skill/use", data)
        if res and res.get("result"):
            return res
        elif res.get("err"):
            code = res.get("err").get("code")
            if code and code == "yet_in_duration":
                self.errorLog(f"{code}技能cd中")
        return None

    def rouletteDashBoard(self):
        """转盘活动"""
        if self.isInvalid:
            return

        res = self.request(domainNew + "api/event/roulette/dashboard")
        if res and res.get("result"):
            return res.get("dashboard")
        return None

    def rouletteCheckShare(self):
        """分享推特"""
        if self.isInvalid:
            return

        res = self.request(
            domainNew + "api/event/roulette/checkShare", {"url": "", "type": "follow"}
        )
        if res and res.get("result"):
            return res.get("dashboard")
        return None

    def rouletteSpin(self):
        """旋转"""
        if self.isInvalid:
            return

        res = self.request(domainNew + "api/event/roulette/spin")
        if res and res.get("result"):
            item = res.get("item")
            target = res.get("target")
            if item and target:
                self.log(f"rouletteSpin:{item} {target}")
                nftStatus = item.get("nftStatus")
                if nftStatus is not None:
                    self.log("rouletteSpin:我出货了！")
                    from Api.FlaskHelper import requestLogWrite

                    requestLogWrite("rouletteSpin", f"{self.name}出货了", type=0)
            return res
        return None

    # api/event/treasureHunt/dashboard
    def treasureHunt(self):
        """cvc神器活动"""

        if self.isInvalid:
            return

        res = self.request(domainNew + "api/event/treasureHunt/dashboard")
        if res and res.get("result"):
            return res
        return None

    # api/event/treasureHunt/spin
    def treasureHuntSpin(self, idx):
        """cvc神器活动"""

        if self.isInvalid:
            return

        res = self.request(domainNew + "api/event/treasureHunt/spin", {"idx": idx})
        if res and res.get("result"):
            return res
        return None

    # api/event/treasureHunt/spinAll
    def treasureHuntSpinAll(self, pkgCode=None):
        """cvc神器活动梭哈"""

        if self.isInvalid:
            return
        data = {}
        if pkgCode:
            data["pkgCode"] = pkgCode

        res = self.request(domainNew + "api/event/treasureHunt/spinAll", data=data)
        if res and res.get("result"):
            return res
        return None

    # /api/event/treasureHunt/reset
    def treasureHuntReset(self):
        """cvc神器活动重置"""

        if self.isInvalid:
            return

        res = self.request(domainNew + "api/event/treasureHunt/reset")
        if res and res.get("result"):
            return res
        return None

    # api/event/treasureHunt/key
    def treasureHuntKey(self):
        """cvc神器活动钥匙"""

        if self.isInvalid:
            return

        res = self.request(domainNew + "api/event/treasureHunt/key")
        if res and res.get("result"):
            return res
        return None

    def unlinkWallet(self):
        """解除绑定"""
        if self.isInvalid:
            return

        res = self.request(domainNew + "api/auth/wallet/unlink", useLock=False)
        if res and res.get("result"):
            self.publicKey = None
            self.log("解绑钱包")
            return res
        return None

    def healInstant(self):
        """治疗"""
        if self.isInvalid:
            return False

        res = self.request(domainNew + "api/kingdom/heal/instant", isB64=True)
        if res and res.get("result"):
            wounded = res.get("wounded")
            return wounded
        return None

    def dragoLairList(self):
        """龙巢列表"""
        if self.isInvalid:
            return False

        res = self.request(domainNew + "api/drago/lair/list", useLock=False)
        if res and res.get("result"):
            return res.get("dragos")
        return None

    def pkgDailyFreeClaim(self, pkgId):
        """领取每日奖励"""
        if self.isInvalid:
            return None
        data = {"pkgId": pkgId}
        res = self.request(domainNew + "api/pkg/dailyFree/claim", data)
        if res and res.get("result"):
            return True
        return False

    # api/item/enchant/skin
    def itemEnchantSkin(self, itemId):
        """皮肤升级"""
        if self.isInvalid:
            return None
        data = {"itemId": itemId}
        res = self.request(domainNew + "api/item/enchant/skin", data)
        if res and res.get("result"):
            return True
        return False

    # api/item/combine/skin/legends
    def itemCombineSkinLegends(self, skinIds):
        """合成皮肤"""
        if self.isInvalid:
            return None
        data = {"skinIds": skinIds}
        res = self.request(domainNew + "api/item/combine/skin/legends", data)
        if res and res.get("result"):
            return res
        return False

    # api/pkg/purchase/start
    def pkgPurchaseStart(self, kingdomPkgId, payment, wallet=""):
        """购买开始"""
        if self.isInvalid:
            return None
        data = {"kingdomPkgId": kingdomPkgId, "payment": payment, "wallet": wallet}
        res = self.request(domainNew + "api/pkg/purchase/start", data)
        if res and res.get("result"):
            return res
        return None

    # api/pkg/purchase/confirm
    def pkgPurchaseConfirm(self, payCode, token, costCode):
        """购买确认"""
        if self.isInvalid:
            return None
        data = {"payCode": payCode, "token": token, "costCode": costCode}
        res = self.request(domainNew + "api/pkg/purchase/confirm", data)
        if res and res.get("result"):
            return res
        return False

    # api/event/list/cvc
    #     {
    #     "result": true,
    #     "events": [
    #         {
    #             "_id": "6685cc442a05d99838e90966",
    #             "status": 1,
    #             "mastCode": 601079,
    #             "time": 1209600,
    #             "startDate": "2024-07-08T00:00:00.000Z",
    #             "endDate": "2024-07-22T00:00:00.000Z",
    #             "reddot": 0,
    #             "order": 10
    #         }
    #     ]
    # }
    def cvcEventList(self):
        """cvc活动列表"""
        if self.isInvalid:
            return None
        res = self.request(domainNew + "api/event/list/cvc")
        if res and res.get("result"):
            events = res.get("events")
            if events:
                self.loadCvcEvent(events)
            return events
        return None

    # api/event/claim/cvc/point/reward
    def cvcPointReward(self, cvcEventId, type, level):
        """cvc积分奖励"""
        if self.isInvalid:
            return None
        self.randomSleep(2, 3)
        data = {"cvcEventId": cvcEventId, "type": type, "level": level}
        res = self.request(domainNew + "api/event/claim/cvc/point/reward", data)
        if res and res.get("result"):
            self.log(f"领取cvc积分奖励{type} {level}")
            return res
        return None

    # api/lord/mastery/reset
    def lordMasteryReset(self):
        """掌握重置"""
        if self.isInvalid:
            return None

        res = self.request(domainNew + "api/lord/mastery/reset")
        if res and res.get("result"):
            self.log("掌握重置")
            return res
        return None

    # api/lord/mastery 11-16
    def lordMasteryGroup(self, group):
        """掌握点"""
        if self.isInvalid:
            return None

        res = self.request(domainNew + "api/lord/mastery", {"group": group})
        if res and res.get("result"):
            return res
        return None
