# /bin/env python3
# -*- coding: UTF-8 -*-

"""
功能描述：用户请求模块
编写人：darkedge
编写日期：2021年12月31日
   
"""

import base64
import datetime
import json
import random
import sys
import threading
import time
from urllib.parse import quote

import requests

from Api.Ipinfo import getIP
from Api.LZApi import LZReportError, LZUpdate
from Api.User.DataRecord import UserDataRecord
from Model.UserError import UserError
from Unit.Redis import redisHelper
# trunk-ignore(ruff/F401)
from Unit.UserEncrypt import UserEncrypt,refreshNodKey

b64ApiList = []
requests.adapters.DEFAULT_RETRIES = 5
sys.setrecursionlimit(10000)

knownErrCodes = [
    "no_fieldobject",  # info 中的无效格子
    "attack_kingdom_too_many",  # start 中攻击频繁
    "mint_limit",  # mint 频繁
    "no_linked_wallet",
    "world_change_marching",  # 在跨服中
    "in_shield",  # in_shield 带盾
    "duplicated_field_object",  # 已经有相同的格子
    "cant_move",  # 不能移动
    "no_need_repair_wall",  # 不需要修墙
    "full_task",  # 任务已满
    "no_complete_train",  # 没有完成的练兵
    "insufficient_troops",  # 撤军异常
    "insufficient_item",  # 使用物品异常
    "different_world",  # 不同世界
    "insufficient_resources",  # 资源不足
    "not_claim_building",  # 没有可领取的建筑
    "teleport_in_banteleport",  # 禁止床送
    "no_field_object",  # 无效格子
    "march_existed",  # 还在挂机中
    "need_time_for_repair",  # 需要时间修复
    "exceed_guest_limit",  # 超出游客限制
    "exceed_account_ip",  # ip超过限制
    "no_complete_research",  # 没有完成的研究
    "exceed_train_max_amount",  # 军队超过最大限制
    "not_enough_building",  # 建筑等级不够
    "insufficient_crystal",  # 砖石不够
    "insufficient_vip_item",  # 数量不够
    "already_claimed",  # 已经收取
    "free_chest_not_yet",  # 免费宝箱还没有开启
    "not_event_target_completed",  # 没有事件完成
    "no_xmas",  # 没有圣诞礼物
    "duplicated",  # 领取重复
    "not_completed",  # 未完成
    "no_alliance",  # 没有联盟
    "not_running_task",  # 没有任务
    "no_donation",  # 没有捐献
    "insufficient_actionpoint",  # 活力不足
    "exceed_alliance_members",  # 重复加盟
    "caravan_soldout",  # 货车商品已售罄
    "invalide_amount",  # 数量不合法
    "cannot_gathering",  # 不能采集
    "already_helped",  # 已经帮助
    "yet_in_cooltime",  # 冷却中
    "insufficient_piece",  # 碎片不足
    "max_teasure_skill_level",  # 最大技能等级
    "deleted_within_period",  # 已经被删除
    "not_running",  # 已撤回队列
    "not_marchobject",  # 没有这个行军
    "shrine_not_enemy",  # 不是敌方神殿
    "exceed_crystal_daily_quota",  # 超过单日采集水晶限制
    "not_in_rally",  # 无效聚会
    "same_target_rally",# 同一目标
    "no_march_slot",  # 没有队列
    "exceed_max_rally_troops",  # 超过最大军队
    "already_joined_drago",  # 已经加入龙
    "not_enough_condition",  # 条件不足 捐赠
    "invalid_name_length",  # 无效的名字长度
    "yet_in_duration",  # 技能还在cd
    "no_recommend",  # 没有推荐
    "no_drago_action_point",  # 龙ap
    "drago_multiple_minting",  # 龙无法mint
    "gathering_restriction_1",  # 需要2级权限
    "gathering_restriction_2",  # 需要3级权限
    "gathering_restriction_3",  # 需要4级权限
    "gathering_restriction_4",  # 需要5级权限
    "gathering_restriction_5",  # 需要6级权限
    "shrine_enemy", # 敌对建筑
    "shield_in_attacking", # 被攻击无法开盾
    "not_open_gate", # 没有门权
]
"""已知请求异常"""

noDetails = [
    "api/enter",
    "api/chat/logs",
    "api/kingdom/wall/info",
    "api/event/list",
    "api/pkg/recommend",
    "api/alliance/help/list",
    "api/item/list",
    "api/mail/list",
    "alliance/battle/list/v2",
    "api/alliance/battle/info",
    "api/drago/lair/list",
    "api/mail/delete",
    "api/mail/read",
    "api/kingdom/hospital/recover",
]
"""不需要详细日志"""

domain = "https://api-lok-live.leagueofkingdoms.com/"
domainNew = domain


def returnB64ApiList():
    return b64ApiList


def saveB64ApiList(value):
    if value:
        global b64ApiList
        b64ApiList = value
        redisHelper.saveLstProtect(value)


def checkB64ApiList():
    global b64ApiList
    lstList = redisHelper.getLstProtect()
    if lstList:
        b64ApiList = lstList


class UserRequest(UserDataRecord, UserEncrypt):
    """API基类"""

    s5Ip = None
    __proxies = None
    __request = None
    __requestLock = None
    limitRetryCount = 0
    notOnline = 0
    duplicatedCount = 0
    errorRequestCodeCount = 0
    retry441 = 0
    """触发441重试次数"""
    asyncCount = 0
    """异步标识符"""

    @property
    def proxies(self):
        """获取proxies"""
        if self.__proxies is None:
            if self.socks5:
                self.__proxies = {"https": f"socks5://{self.socks5}"}
        return self.__proxies

    @property
    def requestSession(self) -> requests.Session:
        """获取session"""
        if self.__request is None:
            self.__request = requests.Session()
            self.__request.keep_alive = False
        return self.__request

    @property
    def requestLock(self) -> threading.BoundedSemaphore:
        """获取锁"""
        if self.__requestLock is None:
            self.__requestLock = threading.BoundedSemaphore(1)
        return self.__requestLock

    def tryUnLockRequest(self, useLock):
        if useLock:
            try:
                self.requestLock.release()
            # trunk-ignore(bandit/B110)
            except Exception:
                pass

    def initLog(self, retry=0, needTestS5=True, isLog=True):
        self.crystalTasks = []
        self.__proxies = None
        self.hasInit = True
        self.makeDeviceInfo()
        checkB64ApiList()
        s = " 初始化^"
        if self.pwd is not None and len(self.pwd) > 0:
            s += " 密码:" + self.pwd + " "
        if self.googleToken or self.isGoogle:
            self.isGoogle = True
            s += "谷歌号 "

        if self.saveSocks5:
            socks5 = redisHelper.getSocks5Info(
                self.key, self.kingdomId, socks5=self.socks5
            )
            if socks5 != self.socks5:
                s += f' 使用保存代理 {socks5} 原:{self.socks5}'
            else:
                s += f" 使用代理 {socks5}"
            self.socks5 = socks5
        if self.socks5 and self.socks5 in redisHelper.getSocks5TimeoutList():
            s += ' 代理超时'
            backupSocks5 = redisHelper.getSocks5TimeoutBackup(self.socks5)
            if backupSocks5:
                self.socks5 = backupSocks5
                s += f" 使用备用代理:{self.socks5} "

        if self.token:
            redisHelper.saveToken(
                self.token,
                email=self.email,
                userKey=self.userKey,
                appleSubId=self.appleSubId,
            )
        if self.socks5 and needTestS5:
            try:
                ip = getIP(self.socks5)
                s += "使用代理:%s,ip:%s" % (self.socks5, ip)
                self.s5IP = ip
            except Exception as _e:
                if retry > 3:
                    # trunk-ignore(ruff/B904)
                    raise UserError.sock5Error()
                else:
                    if self.noWSUser:
                        retry = 3
                    return self.initLog(retry + 1)
        if self.fakeIP:
            s += f"使用fakeIP:{self.fakeIP}"
        if isLog:
            self.log(s)

    def returnB64ApiList(self):
        return returnB64ApiList()

    def saveLstProtect(self, lstProtect):
        tmpB64ApiList = json.loads(base64.b64decode(lstProtect).decode())
        saveB64ApiList(tmpB64ApiList)

    def apiNeedBase64(self, api, isB64=False):
        if self.returnB64ApiList():
            return api in self.returnB64ApiList()
        return isB64

    def progressData(self, url, data=None, isB64=False):
        """处理发送数据"""
        if data is None:
            data = {}
        jsonString = json.dumps(data, separators=(",", ":"))
        api = f"/{url[len(domain):]}"
        if self.apiNeedBase64(api, isB64):
            jsonString = self.encrypt(
                jsonString
            )  # base64.b64encode(jsonString.encode()).decode()

        value = quote(jsonString, "utf-8")
        return "json=" + value

    def resultFailPreProgress(
        self, err, url=None, data=None, isAsync=False, isB64=False
    ):
        if err:
            code: str = err.get("code")
            api = url[len(domain) :]
            if code:
                if code in knownErrCodes:
                    if code == "duplicated":
                        # 系统频繁请求警告
                        if isAsync:
                            self.log(f"异步调用触发duplicated {api}")
                            return None
                        self.duplicatedCount += 1
                        isB64 = self.apiNeedBase64(f"/{api}", isB64)
                        if api == "api/kingdom/task/all":
                            self.debuglog(
                                f"{api}可能加密了 注意查看 {isB64} count:{self.duplicatedCount}"
                            )
                        else:
                            if isB64:
                                self.errorLog(f"{api} 调用频繁 count:{self.duplicatedCount}")
                            else:
                                self.errorLog(f"{api}可能加密了 注意查看 {isB64} count:{self.duplicatedCount}")

                        if self.duplicatedCount > 5:
                            self.invalidStatu = True
                            raise UserError.noauth("duplicated异常引起错误")

                        if api == "api/field/march/start":
                            return True
                        self.randomSleep(10, 20, msg="系统频繁请求警告")
                        return True
                    pass
                elif code == "invalid_token" or code == "no_auth":
                    self.log("token失效 重新登陆")
                    self.invalidStatu = True
                    self.barkNoti("token失效")
                    raise UserError.noauth()
                elif code == "need_email_auth":
                    self.errorLog("需要邮箱验证")
                    raise UserError.needEmailAuth()
                elif code == "need_recaptcha":
                    key = err.get("key")
                    if key:
                        self.errorLog("需要谷歌验证码")
                elif code == "exceed_limit_packet":
                    logins = [
                        "api/auth/connect",
                        "api/auth/login",
                        "api/kingdom/enter",
                        "api/event/bf/open",
                        "api/event/roulette/open",
                        "api/auth/setDeviceInfo",
                    ]
                    if api in logins or isAsync:
                        return None
                    self.limitRetryCount += 1

                    # trunk-ignore(bandit/B311)
                    t = random.randint(360, 900)
                    self.errorLog(f"超出限制等待,{t}秒后重试")
                    self.randomSleep(t)

                    if self.limitRetryCount > 3:
                        self.clearSelf()
                        raise UserError.limitRetry()
                        # self.clearSelf()
                        # self.errorLog("限制访问重新登陆")
                        # if self.login():
                        #     self.log("重新登陆成功")
                        #     self.limitRetryCount = 0
                        # else:
                        #     self.log("重新登陆异常 抛出异常")
                        #     raise UserError.limitRetry()

                    return True

                elif code == "need_captcha":
                    self.errorLog("需要验证码")
                    self.captchaLock.acquire()
                    if time.time() - self.captchaTime < 6:
                        self.captchaLock.release()
                        # await self.asyncRequest(url, data, isLogin, reRequest)
                        return True
                    else:
                        if self.getCaptcha():
                            self.captchaTime = time.time()
                            self.captchaLock.release()
                            return True
                        self.captchaLock.release()
                        raise UserError.captchaError()
                elif code == "mismatch_password":
                    raise UserError.mismatchPassword()
                elif code == "invalid_email":
                    raise UserError.invalidEmail()
                elif code == "no_account":
                    self.errorLog("账号不存在")
                    raise UserError.noAccount()
                elif code[:8] == "ban_user":
                    ban_period = err.get("ban_period")
                    self.errorLog(f"账号被ban了 {ban_period}")
                    redisHelper.setBan(self.key)
                    raise UserError.banUser1()
                elif code == "not_available_email":
                    self.errorLog("邮箱渠道关闭")
                    raise UserError.notAvailableEmail()
                elif code == "no_kingdom":
                    raise UserError.noKingdom()
                elif code == "not_online":
                    if self.noWSUser:
                        return None
                    if self.notOnline > 2:
                        self.log("ws异常 登录失效")
                        self.notOnline = 0
                        raise UserError.noauth()
                    self.notOnline += 1
                    hasRestart = False
                    if isAsync:
                        self.log("异步noOnline")
                        return True
                    self.log("重设dif")
                    self.trySetDeviceInfo()
                    # if self.level < 12:
                    #     self.requrestSetDeviceInfo(self.pushId)
                    # self.anyOpen()
                    if self.checkWSWithKingdomApp():
                        self.log("重启ws")
                        hasRestart = True
                        self.wsWithKingdomRestart()

                    if not hasRestart and self.level >= 25:
                        self.wsWithKingdomRestart()
                    # self.randomSleep(1,3)
                    self.randomSleep(3, 5)
                    return True
                elif code.find("not_running") >= 0:
                    pass
                else:
                    self.errorRequestCodeCount += 1
                    self.errorLog("b请求异常: %s\n %s: %s" % (code, url, data))
                    if self.errorRequestCodeCount > 10:
                        self.errorLog("异常错误码次数过多~!主动退出")
                        self.invalidStatu = True
                        raise UserError.threadExit()
        return None

    def requestGet(self, url, data=None):
        """get"""
        if self.isInvalid:
            return None

        res = self.requestSession.get(
            url, headers=self.header, data=data, proxies=self.proxies
        )
        if res.status_code == 200:
            return res
        return None

    def tryDebugRequestDetail(self, url, msg, isAsync=False):
        try:
            api = url[len(domain) :]
            if api in noDetails and random.randint(0,10) != 0:
                msg = "hidden"
            res = msg
            if isinstance(msg,dict):
                msg = json.dumps(msg)
            headerStr = f'{isAsync and "A" or ""}api:{api}'
            if len(res) > 5000:
                self.debuglog(f"{headerStr} 返回数据过大 res:{res[:1000]}")
            else:
                self.debuglog(f"{headerStr} res:{res}")
        except Exception:
            self.errorLog("tryDebugRequestDetail异常", exc_info=True)

    # 请求集合
    def request(
        self,
        url,
        data=None,
        isLogin=False,
        reRequest=1,
        useLock=True,
        timeout=15,
        isB64=False,
        isAsync=False,
    ):
        """post"""
        if self.hasInit is False:
            self.initLog()
        if isLogin is False and self.isLogin is False:
            self.invalidStatu = True

        if self.isInvalid or reRequest > 3:
            return {"result": False}

        if useLock:
            self.requestLock.acquire()
            if self.isInvalid:
                return {"result": False}

            if self.captchaInfo:
                self.tryCaptcha()
            self.randomSleep(1, 2)

        if data is None:
            data = {}

        try:
            res = self.requestSession.post(
                url,
                data=self.progressData(url, data, isB64=isB64),
                headers=self.header,
                proxies=self.proxies,
                timeout=timeout,
            )  # ,verify=False)
            if res.status_code == 200:
                resJson = None
                if res.headers["Content-Type"].find("application/json") < 0:
                    # resJson = json.loads(base64.b64decode(res.text).decode())
                    resJson = json.loads(self.decrypt(res.text))
                else:
                    resJson = res.json()
                if useLock:
                    self.randomSleep(0, 1)
                # trunk-ignore(ruff/F841)
                api = url[len(domain) :]
                self.tryDebugRequestDetail(url, resJson)
                if resJson.get("result") is False:
                    err: dict = resJson.get("err")
                    if self.resultFailPreProgress(
                        err, url, data, isAsync=isAsync, isB64=isB64
                    ):
                        self.tryUnLockRequest(useLock)
                        return self.request(
                            url,
                            data,
                            isLogin=isLogin,
                            reRequest=reRequest,
                            useLock=useLock,
                            timeout=timeout,
                            isB64=isB64,
                        )
                else:
                    if useLock:
                        self.notOnline = 0
                        self.duplicatedCount = 0
                self.tryUnLockRequest(useLock)
                return resJson
            elif res.status_code == 403:
                self.tryUnLockRequest(useLock)
                self.randomSleep(5, 10)
                return self.request(
                    url,
                    data,
                    isLogin=isLogin,
                    reRequest=reRequest + 1,
                    useLock=useLock,
                    timeout=timeout,
                    isB64=isB64,
                )
            elif res.status_code >= 440 and res.status_code <= 450:
                status_code = res.status_code
                if status_code == 441 and self.userKey:
                    self.clearSelf()
                    self.errorLog(f"触发441 {url} 重新登陆")
                    self.retry441 += 1
                    if self.retry441 < 3:
                        raise UserError.noauth()
                    else:
                        raise UserError.threadExit()
                else:
                    self.invalidStatu = True
                    self.errorLog(f"触发{status_code} {url} data:{data} isB64:{isB64}")
                    self.barkNoti(f"触发{status_code} {url}", isAdmin=True)
                    self.tryUnLockRequest(useLock)
                    return {"result": False}
            else:
                self.debuglog("d请求异常: %s\n %s: %s" % (res.status_code, url, data))
                self.tryUnLockRequest(useLock)
                time.sleep(5)
                return self.request(
                    url,
                    data,
                    isLogin=isLogin,
                    reRequest=reRequest + 1,
                    useLock=useLock,
                    timeout=timeout,
                    isB64=isB64,
                )
        except UserError as e:
            # 用户异常抛出
            self.invalidStatu = True
            self.tryUnLockRequest(useLock)
            raise e
        except requests.exceptions.ReadTimeout:
            self.debuglog("请求超时 没有返回数据:%s \n" % (url))
            self.tryUnLockRequest(useLock)
            return {"result": False, "err": {"code": "timeout"}}
        except Exception as e:
            self.debuglog("请求失败 重试error:%s %s\n" % (url, e))
            if not self.noWSUser:
                time.sleep(5)
            self.tryUnLockRequest(useLock)
            if self.noWSUser:
                return {"result": False}
            return self.request(
                url,
                data,
                isLogin=isLogin,
                reRequest=reRequest + 1,
                useLock=useLock,
                timeout=timeout,
                isB64=isB64,
            )

    # 获取验证码
    def getCaptcha(self, errorCount=0):
        """
        获取验证码
        """
        if self.isInvalid:
            return None

        if errorCount > 5:
            raise UserError.captchaError()

        res = self.requestGet(domainNew + "api/auth/captcha")
        if res:
            fileName = "./captcha/captcha_%s.png" % (self.key)
            with open(fileName, "wb") as f:
                f.write(res.content)
            imageBase64 = base64.b64encode(res.content)
            codeRes = LZUpdate(imageBase64)
            if codeRes:
                self.dataRecord().updateCaptchaCount().commit()
                recognition = codeRes.get("recognition")
                if self.confirmCaptcha(recognition):
                    self.log("打码成功")
                    return True
                else:
                    LZReportError(codeRes.get("captchaId"))
                    return self.getCaptcha(errorCount)
            self.log("打码失败")

        return self.getCaptcha(errorCount + 1)

    # 提交验证码
    def confirmCaptcha(self, captcha):
        if self.isInvalid:
            return None

        data = {"value": captcha}
        res = self.request(domainNew + "api/auth/captcha/confirm", data, useLock=False)
        if res and res.get("result") and res.get("valid"):
            return True
        else:
            self.debuglog("提交验证码失败 %s" % res)
        return None

    def tryCaptcha(self, rightNow=False):
        """尝试打码"""
        needCaptcha = rightNow
        if needCaptcha is False and self.captchaInfo is not None:
            next = self.captchaInfo.get("next")
            nextT = 0
            last = self.captchaInfo.get("last")
            lastT = 0
            if next:
                nextT = (
                    datetime.datetime.strptime(
                        next, "%Y-%m-%dT%H:%M:%S.%fZ"
                    ).timestamp()
                    + 60 * 60 * 8
                )
                # trunk-ignore(bandit/B311)
                if time.time() + random.randint(30, 120) > nextT:
                    needCaptcha = True

            if last:
                lastT = (
                    datetime.datetime.strptime(
                        last, "%Y-%m-%dT%H:%M:%S.%fZ"
                    ).timestamp()
                    + 60 * 60 * 8
                )
                if abs(time.time() - lastT) < 20:
                    needCaptcha = True

        if needCaptcha:
            self.captchaLock.acquire()
            if self.getCaptcha():
                self.hasCaptcha = True
                if lastT > 0:
                    self.captchaInfo["last"] = None
                else:
                    self.captchaInfo = None

            self.captchaLock.release()
            return True
        return False

    def randomSleep(self, a, b=None, c:int=0, msg:str=None):
        """随机休眠"""
        t = c
        if isinstance(t, str):
            t = 0
            msg = c
        if b:
            # trunk-ignore(bandit/B311)
            t += random.randint(a, b)
        else:
            t += a

        if msg:
            self.log(f"{msg} 休息{t}秒")
        time.sleep(t)

    def login(self):
        self.errorLog("没登录成功？？？？")
