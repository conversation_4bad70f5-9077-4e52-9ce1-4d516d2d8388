# /bin/env python3
# -*- coding: UTF-8 -*-

"""
功能描述：龙模块
编写人：darkedge
编写日期：2022年3月24日
   
"""

import secrets
import re

import Unit.enum as Enum
from Api.User.Kingdom import UserKingdom
from Model.UserError import UserError


class Drago(UserKingdom):

    def dsaItem(self, itemList=None):
        """获取dsa道具信息"""
        dsaItem = None

        if itemList is None:
            itemList = self.tmpItems

        for item in itemList:
            if item["code"] == Enum.ITEM_CODE_DSA:
                dsaItem = item
                break

        return dsaItem

    def dsaId(self, itemList=None):
        """获取dsa道具id"""
        dsaItem = self.dsaItem(itemList)
        if dsaItem:
            return dsaItem["_id"]
        return None

    def dsaAmount(self, itemList=None):
        """获取dsa道具数量"""
        dsaItem = self.dsaItem(itemList)
        if dsaItem:
            return dsaItem["amount"]
        return 0

    def checkDsaMail(self):
        mailList = self.mailList(3)
        mails = mailList.get("mails")
        if mails:
            for mail in mails:
                if mail.get("type") == 15:
                    content = mail.get("content")
                    match = re.search(r"0x\w{64}", content)
                    if match:
                        self.log(f"dsa提现打款:{match.group(0)}")
                        return True
        return False

    # 龙穴发行
    def tryDragoMintReal(self, dragoId, index, relay=0):
        if relay > 3:
            return False
        try:
            if self.getDragoMintInfo(dragoId):
                itemCode = Enum.ITEM_CODE_FOOD_100M + index * 2
                res = self.prepareDragoMint(dragoId, itemCode)
                if res:
                    waitTime = res.get("waitTime")
                    # self.setMintTime()
                    typeName = ["粮食", "木头", "石头", "黄金"]
                    self.log("%s打包成功" % typeName[index])
                    return int(waitTime)
        except UserError as e:
            if e.errorCode == 11:
                return self.tryDragoMintReal(dragoId, (index + 1) % 4, relay=relay + 1)
            elif e.errorCode == -4:
                self.errorLog("龙穴发行异常! 可能需要更换钱包")
                self.barkNoti("发行异常", isAdmin=True)
            else:
                self.errorLog("龙穴发行异常 %s" % e)
        except Exception as e:
            self.errorLog("龙穴发行异常 %s" % e)
        return False

    # 使用龙穴发行
    def tryUseDragonMint(self, dragoId=None):
        """dragoTokenId = 20186"""
        if self.checkMintTime() or not self.publicKey:
            return

        res = self.listDragoMint()
        if res:
            mint = res.get("mint")
            dragos = res.get("dragos", [])
            if len(mint) > 0:
                for mintInfo in mint:
                    expectedEnded = self.compareDateTimeWithNow(
                        mintInfo.get("expectedEnded")
                    )
                    if expectedEnded < 0:
                        drago = mintInfo.get("drago")
                        dragoId = drago.get("_id")
                        self.claimDragoMint(dragoId=dragoId)
                        self.randomSleep(2)
                        return self.tryUseDragonMint()

            if len(mint) >= self.dragoMaxMintCount:
                self.setMintTime()
                return

            if len(dragos) == 0:
                self.setMintTime()
                self.debuglog("龙穴没有龙")
                return

            if dragoId is None:
                dragos = list(filter(lambda drago:drago.get("lair",{}).get("status",0) == 1, dragos))
                if self.workDragos:
                    # 排除工作龙
                    dragos = list(filter(lambda drago:drago.get("tokenId") not in self.workDragos, dragos))
                if dragos:
                    dragos.sort(key=lambda dargo:dargo.get("level",0),reverse=True)
                    dragoId = dragos[0].get("_id")

            if dragoId is None:
                self.setMintTime()
                self.debuglog("龙穴没有空闲龙")
                return

            canMintResource = [
                index
                for index, value in enumerate(self.resources)
                if value > 2_000_000_000
            ]

            if len(canMintResource) == 0:
                canMintResource = [
                    index
                    for index, value in enumerate(self.resources)
                    if value > 200_000_000
                ]
                self.log("低资源发行")

            if len(canMintResource) == 0:
                self.setMintTime()
                self.debuglog("没有足够资源")
                return

            index = secrets.choice(canMintResource)
            return self.tryDragoMintReal(dragoId, index)
        return False

    def tryDragoMint(self, dragoId="642e1c333f83432c365805c5"):
        """20186 = 642e1c333f83432c365805c5"""
        if self.checkMintTime() or not self.publicKey:
            return

        res = self.listDragoMint()
        if res:
            mint = res.get("mint")
            if len(mint) < self.dragoMaxMintCount:
                self.log("移除龙")
                self.dragoLairLeave(dragoId)
                self.log("加入龙")
                self.dragoLairJoin(dragoId)
                self.log("龙穴发行")
                if self.tryUseDragonMint(dragoId):
                    return self.tryDragoMint(dragoId=dragoId)
            else:
                hasClaim = False
                for info in mint:
                    expectedEnded = info.get("expectedEnded")
                    leftSecond = self.compareDateTimeWithNow(expectedEnded)
                    if leftSecond < 0:
                        drago = info.get("drago")
                        dragoId = drago.get("_id")
                        if self.claimDragoMint(dragoId):
                            self.randomSleep(3, msg="领取发行资源成功")
                            hasClaim = True
                    else:
                        self.log(f"发行资源还未到期 剩余:{leftSecond}秒")
                if hasClaim:
                    return self.tryDragoMint(dragoId=dragoId)
                else:
                    self.setMintTime()
