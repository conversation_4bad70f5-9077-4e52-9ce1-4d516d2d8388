# /bin/env python3
# -*- coding: UTF-8 -*-

"""
功能描述：用户交互钱包模块
编写人：darkedge
编写日期：2022年3月24日
   
"""

from Api.User.Kingdom import UserKingdom
from Unit.EthUnit import EthService


class UserWeb3(UserKingdom):
    def tryLinkWallet(self, privateKey):
        """
        功能描述：尝试链接钱包
        参数：
            privateKey:钱包私钥
        返回值：
            成功：返回钱包信息
            失败：返回None
        """
        if self.isInvalid:
            return None
        if not privateKey:
            return None

        es = EthService(privateKey=privateKey)

        walletInfo = self.walletInfo()
        if walletInfo.get("wallet") is None:
            verifyId = self.walletVerify()
            signMessage = es.signMessage(verifyId)
            if self.walletCertify(f"{signMessage}_{es.address.lower()}", verifyId):
                return True
            else:
                self.log("绑定异常")
                return False
        else:
            wallet = walletInfo.get("wallet")
            publicKey = wallet.get("publicKey")
            if es.toChecksumAddress(publicKey) != es.address:
                self.unlinkWallet()
                self.randomSleep(3, 5, msg="解绑钱包等待...")
                return self.tryLinkWallet(privateKey)
            else:
                return True

        return False
