# /bin/env python3
# -*- coding: UTF-8 -*-

"""
功能描述：用户websocket请求模块
编写人：darkedge
编写日期：2022年1月4日
   
"""
# trunk-ignore(ruff/F401)
import asyncio
# trunk-ignore(ruff/F401)
import base64
# trunk-ignore(ruff/F401)
import datetime
import json
import math
import os
import random
import re
import threading
import time
import cachetools

import websocket
from websocket._abnf import ABNF

import Unit.enum as Enum
# trunk-ignore(ruff/F401)
from Api.User.Request import UserRequest, knownErrCodes
from Model.Socks5 import Socks5
# trunk-ignore(ruff/F401)
from Model.UserError import UserError
from Unit.LandZoneUnit import returnAdjacentCrystalLands

Match3LevelLimit = [0, 9, 9, 9, 9, 19, 19, 19, 19, 19, 29, 39, 49, 59, 69, 79, 89, 99, 109, 119, 149, 179, 209, 239, 269, 299, 329, 359, 389, 419, 449, 479, 509, 539, 569, 1000]

fieldNames = {
    20100101: "农场",
    20100102: "伐木场",
    20100103: "采石场",
    20100104: "黄金矿",
    20100105: "水晶矿",
    20100106: "龙矿",
    20200101: "兽人",
    20200102: "骷髅",
    20200103: "傀儡",
    20200104: "宝藏精灵",
    20200201: "死亡骑士",
    20200202: "绿龙",
    20200203: "红龙",
    20200204: "金龙",
    20200206: "庞大固埃",
    20200207: "卡冈图亚",
    20700101: "门",
    20700201: "堡垒A.",
    20700202: "堡垒B.",
    20700301: "古庙",
    20700405: "食人魔",
    20700406: "饿狼",
    20700407: "独眼巨人",
    20700505: "摩尔达尔",
    20700506: "斯巴托伊",
    20700601: "农场cvc",
    20700602: "伐木场cvc",
    20700603: "采石场cvc",
    20700604: "黄金矿cvc",
    20300101: "城堡",
    20400401: "神殿",
    20500101: "buff",
    20500102: "buff",
    20500103: "buff",
    20500104: "buff",
    20600101: "指挥中心",
    20600102: "联盟塔",
}

charmCodes = {
    140: "捍卫者的魅力",
    130: "能量恢复符咒",
    120: "军队装载符咒",
    110: "军队速度符咒",
    100: "军队攻击符咒",
    90: "军队体力符咒",
    80: "训练符咒",
    70: "建设符咒",
    60: "资源符咒",
    50: "研究符咒",
    40: "黄金生产符咒",
    30: "石材生产符咒",
    20: "木材生产符咒",
    10: "食量生产符咒",
}
levelNames = ["", "普通", "魔法", "史诗", "传说"]

localMap = {}
oldCharmCode = {}
useV3WSProxy = True
useV4WSProxy = True
if os.getenv("V3"):
    if int(os.getenv("V3")) == 0:
        useV3WSProxy = False
        print("不使用V3WS代理")
if os.getenv("V4"):
    if int(os.getenv("V4")) == 0:
        useV4WSProxy = False
        print("不使用V4WS代理")

class LokWebSocketApp(websocket.WebSocketApp):
    is502 = False
    u1 = None  # 先声明为 None，后续再赋值
    retryCount = 0

class UserWebSocket(UserRequest):
    """ws模块"""

    __s5m: Socks5 = None
    __wsKingdom: LokWebSocketApp = None
    __wsField: LokWebSocketApp = None
    __wsMatch: LokWebSocketApp = None
    captchaCounter = 0
    oldZone = None
    attacked = 0
    spyed = 0
    newRally = True
    """新的团战"""
    newMailCount = 0
    newHelp = False

    __kingdomAppParams = (False, False, False)
    __match3AppParams = (False, False)

    type15MsgGet = False
    rallyMoIdList = None
    selfRallyList = None
    buffList = None

    def __post_init__(self):
        super().__post_init__()
        self.rallyMoIdList = cachetools.FIFOCache(100)
        self.selfRallyList = cachetools.FIFOCache(20)
        self.buffList = []
    @property
    def s5m(self):
        """获取socks5"""
        if self.__s5m is None:
            self.__s5m = Socks5(self.socks5)
        return self.__s5m

    @property
    def webSocketSuffix(self):
        return f"?EIO=4&transport=websocket&token={self.token}"

    def checkWSMethodPass(self, method, isGame=False):
        methods = [
            "/alliance/gift/new",
            "/alliance/build/finish",
            "/alliance/build/start",
            "/alliance/build/destroyed",
            "/alliance/rank/down",
            "/alliance/rank/up",
            "/alliance/rank/leader",
            "/alliance/member/leave",
            "/alliance/join",
            "/alliance/member/disband",
            "/alliance/member/change",
            "/boost/update",
            "/buff/list",
            "/buff/list/world",
            "/building/update",
            "/building/update/v2",
            "/research/update",
            "/item/update",
            "/lord/exp/update",
            "/mail/new",
            "/mail/new/v2",
            "/mail/update",
            "/reddot/list",
            "/reddot/update",
            "/skin/legends",
            "/quest/update",
            "/vip/update",
            "/lord/level/update",
            "/pkg/new",
            "/pkg/update",
            "/wall/update",
            "/shrine/siege",
            "/shrine/siege/final",
            "/shrine/siege/first",
            "/field/object/update",
            "/dragoActionPoint/update",
            "/skin/mythic",
            "/skin/enchant",
            "/notice/display",
            "/messenger",
        ]
        hasMethod = method not in methods
        if hasMethod:
            self.barkNoti(f"收到未知{isGame and 'game' or ''}ws:{method}", isAdmin=True)
            self.debuglog(f"no record method:{method}")
        return hasMethod

    def wsRequest(self, url, isMultithread=False,retry=0) -> websocket.WebSocket:
        """连接ws"""
        ws = websocket.WebSocket(enable_multithread=isMultithread)
        ws.timeout = 15
        try:
            options = {"timeout": 15}
            if self.s5m:
                auth = None
                if self.s5m.user:
                    auth = (self.s5m.user, self.s5m.pwd)
                options["http_proxy_host"] = self.s5m.host
                options["http_proxy_port"] = self.s5m.port
                options["proxy_type"] = "socks5"
                if auth:
                    options["http_proxy_auth"] = auth

            ws.connect(url, **options)
            return ws
        except websocket._exceptions.WebSocketBadStatusException as e:
            if retry > 5:
                self.errorLog(f"ws连接失败：{e} 重试超限")
                return None
            else:
                self.debuglog(f"ws连接失败：{e} 重试{retry}")
                time.sleep(0.5)
                return self.wsRequest(url, isMultithread = isMultithread, retry = retry + 1)
        except Exception as e:
            self.errorLog(f"ws连接失败：{e} {self.socks5}")
            return None

    def wsWithKingdomRestart(self):
        self.wsWithKingdomClose()
        self.randomSleep(3, 5)
        self.wsWithKingdomApp(*self.__kingdomAppParams)
        self.randomSleep(4, 6)

    def wsWithKingdomClose(self):
        """关闭王国连接"""
        if self.__wsKingdom:
            self.__wsKingdom.close(reason="Active close")
            self.__wsKingdom = None

    def checkWSWithKingdomApp(self):
        if self.__wsKingdom and not self.__wsKingdom.keep_running:
            self.__wsKingdom = None
            return True
        return self.__wsKingdom is None

    def kingdomAppTryAddTask(self, task):
        hasTask = False
        tId = task.get("_id")
        for t in self.fieldTasks:
            id = t.get("_id")
            if id == tId:
                hasTask = True
                break
        if not hasTask:
            self.fieldTasks.append(task)

    def kingdomAppTryRemoveTask(self, task):
        tId = task.get("_id")
        for t in self.fieldTasks:
            id = t.get("_id")
            if id == tId:
                self.fieldTasks.remove(t)
                break

    def kingdomAppWithTaskNew(self, task):
        """ws kingdom 收到新任务"""
        code = task.get("code", 0)
        if code in [4, 5, 10, 11]:
            status = task.get("status", 0)
            if status == 1:
                self.kingdomAppTryAddTask(task)
        self.clearFieldTasks()

    def kingdomAppWithTaskUpdate(self, task):
        """ws kingdom 收到更新任务"""
        code = task.get("code", 0)
        if code in [2, 4, 5, 10, 11]:
            status = task.get("status", 0)
            if status == 1:
                self.kingdomAppTryAddTask(task)
                if code == 11:
                    self.checkRallyTaskSpeed(task)
            elif status in [2, 99]:
                self.kingdomAppTryRemoveTask(task)

        self.clearFieldTasks()

    def checkRallyTaskSpeed(self,task):
        from Unit.Redis import redisHelper
        param = task.get("param",{})
        time = task.get("time", -1)
        fromLoc = param.get("fromLoc")
        if fromLoc == self.loc:
            distance = param.get("distance",-1)
            speed = distance / time
            rallyMoId = param.get("rallyMoId")
            if rallyMoId in self.selfRallyList:
                del self.selfRallyList[rallyMoId]
            if speed < 5:
                toLoc = param.get("toLoc")
                rallyInfo = redisHelper.getRallySolwInfo(rallyMoId)
                moSpeed = rallyInfo.get("speed",10)
                if moSpeed < speed:
                    name = rallyInfo.get("name","unknown")
                    self.log(f"低速团{round(speed,2)},存在弟弟 rallyInfo:{name} {moSpeed} toLoc:{toLoc}")

    def wsWithKingdomApp(self, show=False, useThread=True, isDebug=False):
        """
        连接ws并获取kingdom信息
        """
        if self.wsKingdom is None or self.noWSUser:
            return
        if not self.token:
            self.debuglog("wsWithKingdomApp token无效")
            return
        realUrl = f"wss{self.wsKingdom[5:]}{self.webSocketSuffix}"
        if self.__wsKingdom:
            return
        self.showDel = True

        isDebug = bool(os.getenv("LEAGUEDEBUG", default=isDebug))
        # self.log(f"启动kingdomApp {show} {useThread} {isDebug}")
        self.__kingdomAppParams = (show, useThread, isDebug)

        def onMessage(ws: LokWebSocketApp, message):
            if isDebug:
                self.debuglog(f"kwsMsg:{message}")
            if self.isInvalid:
                ws.close(reason=b"invalid")

            if message is None or message == "" or len(message) == 0:
                return None
            if message[0] == "0":
                return None
            elif message == "40":
                # 开始发送数据
                self.kingdomEnter(ws)
                return

            # s = message.split("[")
            match = re.match(r"^(\d+)", message)
            code = int(match[0])
            if code == 44:
                self.errorLog("wsApp ws验证错误？？？")
                ws.close(reason=b"invalid")
            elif code == 42:
                value = message[2:]
                jsonValue = json.loads(value)
                method = jsonValue[0]
                if method == "/resource/update":
                    resourceIdx = jsonValue[1].get("resourceIdx")
                    value = jsonValue[1].get("value")
                    delta = jsonValue[1].get("delta")
                    if value is None and delta is None:
                        self.isBug = True
                        self.resources[resourceIdx] = 99999999
                        return
                    if value is None:
                        return
                    if isinstance(value, list):
                        if value[0] is None:
                            return
                        self.resources = value
                    elif isinstance(value, float):
                        self.resources[resourceIdx] = value
                    elif isinstance(value, int):
                        self.resources[resourceIdx] = value
                    else:
                        self.errorLog(f"资源更新错误：{value}")
                elif method == "/crystal/update":
                    value = jsonValue[1].get("value")
                    self.crystal = value
                elif method == "/captcha/set":
                    self.captchaInfo = jsonValue[1].get("captcha")
                    self.debuglog(f"收到captcha/set {jsonValue[1]}")
                    self.barkNoti("收到验证码提示", isAdmin=True)
                elif method == "/quest/main/update":
                    quest = jsonValue[1]
                    status = quest.get("status")
                    if status and status == 2:
                        self.addQuest(quest)
                elif method == "/power/update":
                    power = jsonValue[1].get("power")
                    self.power = power
                elif method == "/actionPoint/update":
                    value = jsonValue[1].get("value")
                    self.actionPoint = value
                elif method == "/dragoActionPoint/update":
                    value = jsonValue[1].get("value")
                    self.dragoActionPoint = int(value)
                    self.debuglog(f"龙行动点更新{value}")
                elif method == "/alarm/update":
                    acount = jsonValue[1].get("count")
                    attack = acount.get("attack")
                    spy = acount.get("spy")
                    if isinstance(attack, int):
                        self.attacked += attack
                    else:
                        self.errorLog(f"攻击数据异常{attack}")
                    if isinstance(spy, int):
                        self.spyed += spy
                    else:
                        self.errorLog(f"侦查数据异常{spy}")

                    if attack > 0:
                        self.errorLog("被攻击了")
                    if spy > 0:
                        self.errorLog("被侦查了")
                elif method == "/task/new":
                    # 新的任务
                    self.kingdomAppWithTaskNew(jsonValue[1])
                elif method == "/task/update":
                    # 更新任务
                    self.kingdomAppWithTaskUpdate(jsonValue[1])
                elif method == "/alliance/join":
                    allianceInfo = jsonValue[1]
                    allianceId = allianceInfo.get("_id")
                    self.allianceId = allianceId
                    self.crystalMaxLevel = 10
                    name = allianceInfo.get("name")
                    tag = allianceInfo.get("tag")
                    self.addBotWorkLog(f"加入联盟 {name} {tag} 成功")
                elif method == "/alliance/rally/new":
                    self.newRally = True
                elif method[:9] == "/mail/new":
                    # self.debuglog(f"收到邮件 {jsonValue[1]}")
                    # {'mailInbox': {'_id': '678e0be7736f24dc4b9c02ee', 'type': 23}, 'num': 57}
                    mailInbox = jsonValue[1]["mailInbox"]
                    mailType = mailInbox["type"]
                    if mailType == 0:
                        fromDict = mailInbox.get("from")
                        detail = ""
                        if fromDict:
                            name = fromDict["name"]
                            content = mailInbox["content"]
                            detail = f"{name}:{content}"
                            self.errorLog(f"收到邮件:{detail}")
                        else:
                            pass
                            # {'_id': '678e1a126d51ae899f8d62ca', 'type': 0}
                            # self.debuglog(f"收到邮件x:{mailInbox}")
                        self.newMailCount += 1
                        self.barkNoti(f"收到邮件 {detail}")
                    # elif mailType == 15:
                    #     self.type15MsgGet = True
                elif method[:9] == "/chat/new":
                    self.debuglog(f"收到私聊 {jsonValue[1]}")
                    self.barkNoti(f"收到私聊 {jsonValue[1]}", isAdmin=True)
                elif method == "/boost/update":
                    # 状态更新
                    boost = jsonValue[1]
                    self.loadBoosts(boost)
                    self.debuglog("收到状态更新")
                elif method == "/buff/list":
                    self.buffList = jsonValue[1]
                    self.debuglog("收到buff 更新")
                elif method == "/ap/update":
                    self.debuglog(f"收到/ap/update {jsonValue}")
                else:
                    if self.checkWSMethodPass(method) and isDebug:
                        self.debuglog(f"no record method:{method} msg:{value}")

            if show and self.sumResources <= self.maxSafeResource and self.showDel:
                self.log("资源达标 可以删号了")
                self.showDel = False
                # ws.close(reason=b"resource enough")

        def onError(ws, error):
            if isinstance(error, websocket._exceptions.WebSocketTimeoutException):
                self.debuglog("kws ping/pong time out")
            elif isinstance(error, websocket._exceptions.WebSocketBadStatusException):
                if error.status_code == 502:
                    ws.is502 = True
                else:
                    self.log(f"kws error:{error}")
            else:
                self.debuglog(f"kws error:{error}", exc_info=True)
                self.info(f"kws error:{error}")
            self.__wsKingdom = None
            threading.current_thread()

        def onClose(ws, code, reason):
            if code or reason:
                self.log(f"kclose {code} {reason}")
            self.__wsKingdom = None
            if ws.is502:
                ws.retryCount += 1
                if ws.retryCount > 5:
                    self.errorLog("kws502连接失败 重试超限")
                else:
                    self.debuglog(f"kws502连接失败 重试{ws.retryCount}")
                    self.wsWithKingdomApp(*self.__kingdomAppParams)
                    time.sleep(0.5 * ws.retryCount)

        def onPong(ws, data):
            if self.isInvalid:
                ws.close(reason=b"invalid")
                return

            self.ping()
            ws.send("2")
            if show and self.resources:
                sumResources = round(self.sumResources / 1000 / 1000.0, 2)
                resources = json.dumps(
                    [round(int(v or 1) / 1000 / 1000.0, 2) for v in self.resources]
                )
                if show:
                    self.log(f"当前总资源{sumResources} 分布{resources}")

        def onPing(ws, data):
            self.log("ping")

        def onOpen(ws):
            pass

        # headers = {
        #     "Cache-Control": "no-cache",
        #     "Pragma": "no-cache",
        #     "Sec-WebSocket-Extensions": "permessage-deflate; client_max_window_bits",
        #     "Accept-Encoding": "gzip, identity",
        #     "TE": "identity",
        #     "User-Agent": "BestHTTP",
        #     "Content-Length": "0",
        # }
        wsApp = LokWebSocketApp(
            realUrl,
            on_open=onOpen,
            on_message=onMessage,
            on_error=onError,
            on_close=onClose,
            on_pong=onPong,
            on_ping=onPing,
        )
        wsApp.u1 = self

        def runWsApp(wsApp):
            self.__wsKingdom = wsApp
            if self.s5m:
                auth = None
                if self.s5m.user:
                    auth = (self.s5m.user, self.s5m.pwd)
                # sslopt={"cert_reqs": ssl.CERT_NONE},
                wsApp.run_forever(
                    http_proxy_host=self.s5m.host,
                    http_proxy_port=self.s5m.port,
                    proxy_type="socks5",
                    http_proxy_auth=auth,
                    ping_interval=25,
                    ping_timeout=5,
                )
            else:
                wsApp.run_forever()

        if useThread:
            thread = threading.Thread(
                target=runWsApp, args=(wsApp,), name=f"wsApp_{self.key}"
            )
            thread.setDaemon(True)
            thread.start()
        else:
            runWsApp(wsApp)

    def checkWSWithFieldApp(self):
        if self.__wsField:
            if not self.__wsField.keep_running:
                self.__wsField = None
                return True
        return self.__wsField is None

    def wsWithFieldApp(
        self,
        isDebug=False,
        zone=None,
        fieldCallBack=None,
        autoCloseTime=120,
        autoRestart=True,
    ):
        """
        连接ws并获取Field信息
        """
        realUrl = f"wss{self.wsField[5:]}{self.webSocketSuffix}"
        isDebug = bool(os.getenv("LEAGUEDEBUG", default=isDebug))
        if self.__wsField:
            return

        if not self.token:
            self.debuglog("wsWithFieldApp token无效")

        if zone is None:
            zone = self.coordinateTransformation(self.loc)

        def onMessage(ws: LokWebSocketApp, message):
            if isDebug:
                if len(message) < 1000:
                    ws.u1.debuglog(f"fwsMsg:{message}")
                else:
                    ws.u1.debuglog(f"fwsMsg:{message[:1000]}")
            if ws.u1.isInvalid:
                ws.close(reason=b"invalid")

            if message is None or message == "" or len(message) == 0:
                return None
            if message[0] == "0":
                return None
            elif message == "40":
                # 开始发送数据
                self.fieldEnter(ws, isDebug=isDebug)
                return

            # s = message.split("[")
            # code = int(s[0])
            match = re.match(r"^(\d+)", message)
            code = int(match[0])
            if code == 44:
                self.errorLog("wsField ws验证错误？？？")
                ws.close(reason=b"invalid")
            elif code == 42:
                ws.lastMsgTime = time.time()
                value = message[2:]
                jsonValue = json.loads(value)
                method = jsonValue[0]
                if method[:12] == "/field/enter":
                    isV3 = method == "/field/enter/v3"
                    self.handlingFieldEnterResult(
                        ws, jsonValue[1], zone, isV3=isV3, isDebug=isDebug
                    )

                elif method == "/field/object/update":
                    fieldInfo = jsonValue[1]
                    fo = fieldInfo.get("fo")
                    state = fo.get("state")
                    if state == 1 and callable(ws.fieldCallBack):
                        ws.fieldCallBack(fo)

            if time.time() - ws.lastMsgTime > 60:
                ws.close(reason=b"no msg")
            elif time.time() - ws.startTime > autoCloseTime:
                ws.close(reason=b"need restart")

        def onError(ws, error):
            if isinstance(error, websocket._exceptions.WebSocketTimeoutException):
                self.debuglog("fws ping/pong time out")
            else:
                self.debuglog(f"fws error:{error}", exc_info=True)
                self.info(f"fws error:{error}")
            ws.u1.__wsField = None

        def onClose(ws, code, reason):
            if code or reason:
                ws.u1.log(f"fclose {code} {reason}")
            ws.u1.__wsField = None

        def onPong(ws, data):
            if ws.u1.isInvalid:
                ws.close(reason=b"invalid")
                return
            ws.send("2")

        def onPing(ws, data):
            ws.u1.log("ping")

        def onOpen(ws):
            pass

        wsApp = LokWebSocketApp(
            realUrl,
            on_open=onOpen,
            on_message=onMessage,
            on_error=onError,
            on_close=onClose,
            on_pong=onPong,
            on_ping=onPing,
        )
        wsApp.u1 = self
        wsApp.oldZone = zone
        wsApp.lastMsgTime = time.time()
        wsApp.startTime = time.time()
        wsApp.fieldCallBack = fieldCallBack
        if isDebug:
            wsApp.oldSend = wsApp.send

            def debugSend(data, opcode=ABNF.OPCODE_TEXT):
                self.debuglog(f"fwsSend:{data}")
                wsApp.oldSend(data, opcode)

            wsApp.send = debugSend

        def runWsApp(wsApp: LokWebSocketApp):
            self.__wsField = wsApp
            if self.s5m:
                auth = None
                if self.s5m.user:
                    auth = (self.s5m.user, self.s5m.pwd)
                wsApp.run_forever(
                    http_proxy_host=self.s5m.host,
                    http_proxy_port=self.s5m.port,
                    proxy_type="socks5",
                    http_proxy_auth=auth,
                    ping_interval=20,
                    ping_timeout=5,
                )
            else:
                wsApp.run_forever(ping_interval=20, ping_timeout=5)

        # runWsApp(wsApp)
        # self.log(f"启动fws")
        thread = threading.Thread(
            target=runWsApp, args=(wsApp,), name=f"wsApp_Field_{self.key}"
        )
        thread.setDaemon(True)
        thread.start()

    def wsFieldsSendNewEnter(self):
        if self.__wsField is None:
            self.wsWithFieldApp()

        ws = self.__wsField
        self.zoneLeaveListWithList(self.oldZone, ws)
        zone = self.coordinateTransformation(self.loc)
        self.zoneEnterListWithList(zone, ws)
        self.randomSleep(4, 6)
        self.zoneLeaveListWithList(zone, ws)
        self.fieldLeave(ws)
        try:
            ws.close()
        # trunk-ignore(bandit/B110)
        except Exception:
            pass

    def wsGetFields(
        self,
        loc=None,
        power=2,
        more=False,
        marchCallBack=None,
        isCVC=False,
        isCrystalOnly=False,
        zone=None,
        isDebug=False,
        waitMarch=False,
    ):
        """
        单次获取指定坐标周围的地图信息
        """
        if loc is None:
            loc = self.loc
        loc = loc.copy()
        loc[1] = max(int(loc[1]), 0)
        loc[1] = min(int(loc[1]), 2045)
        loc[2] = max((loc[2]), 0)
        loc[2] = min(int(loc[2]), 2045)
        cvcLoc = self.cvcBlockLoc(loc)

        isDebug = bool(os.getenv("LEAGUEDEBUG", default=isDebug))
        if not self.token or self.isInvalid:
            self.debuglog("wsGetFields token无效")
            return None
        realUrl = f"wss{self.wsField[5:]}{self.webSocketSuffix}"
        ws = self.wsRequest(realUrl)
        if ws is None:
            return None

        ws.u1 = self

        codeMap = {}

        try:
            if zone is None:
                if isCVC:
                    zone = self.cvcCoordinateTransformation(cvcLoc, power=power)
                else:
                    if isCrystalOnly:
                        zone = [loc[0]] + returnAdjacentCrystalLands(loc)
                    else:
                        zone = self.coordinateTransformation(loc, power=power)
            # zone = zone[:46]
            needAccept = math.ceil((len(zone) - 1) / 9)
            currentAccept = 0
            # self.log(f'zoneSize:{len(zone)},{zone}')
            while not self.isInvalid:
                message = None
                try:
                    message = ws.recv()
                except Exception as e:
                    self.debuglog(f"wsGetFields some error:{e}")
                    pass

                if message and isDebug:
                    self.debuglog(f"fwsMsg:{message[:22]}")

                if message is None or message == "":
                    return None
                if message[0] == "0":
                    continue
                elif message == "40":
                    # 开始发送数据
                    self.fieldEnter(ws, isDebug=isDebug)
                    continue
                # s = message.split("[")
                # code = int(s[0])
                match = re.match(r"^(\d+)", message)
                code = int(match[0])
                if code == 44:
                    self.errorLog("wsGetFields ws验证错误？？？")
                    self.invalidStatu = True
                    ws.close(reason=b"invalid")
                elif code == 42:
                    value = message[2:]
                    jsonValue = json.loads(value)
                    method = jsonValue[0]
                    # passList = [
                    #     # '/field/objects',
                    #     "/field/object/update",
                    #     "/march/object/update",
                    # ]
                    if method[:12] == "/field/enter":
                        isV3 = method == "/field/enter/v3"
                        self.handlingFieldEnterResult(
                            ws,
                            jsonValue[1],
                            zone,
                            isV3=isV3,
                            isMore=more,
                            isDebug=isDebug,
                        )
                    elif method == "/march/objects":
                        if callable(marchCallBack):
                            compType = jsonValue[1].get("compType")
                            objects = jsonValue[1].get("objects")
                            if compType is not None:
                                packs = jsonValue[1].get("packs")
                                if compType == 0:
                                    objects = packs.get("objects")
                                elif compType == 2:
                                    rawValue = self.uint8ArrayToJson(packs, b64=False)
                                    objects = rawValue.get("objects")
                                elif compType == 3:
                                    rawValue = self.uint8ArrayToJson(packs, b64=True)
                                    objects = rawValue.get("objects")
                            if objects and len(objects) > 0:
                                marchCallBack(objects)
                        waitMarch = False
                        if currentAccept >= needAccept and not waitMarch:
                            break
                    elif method[:14] == "/field/objects":
                        rawValue = jsonValue[1]
                        if method == "/field/objects/v3":
                            rawValue = self.base64StringToJson(rawValue)

                        elif method == "/field/objects/v4":
                            isPackCompressed = rawValue["isPackCompressed"]
                            if isPackCompressed:
                                packs = rawValue["packs"]
                                try:
                                    rawValue = self.uint8ArrayToJson(packs)
                                except Exception:
                                    self.errorLog(f"解析packs失败:{packs}")
                                    rawValue = {}

                        objects = rawValue.get("objects", [])
                        if isDebug:
                            self.debuglog(f"fields {currentAccept}:{len(objects)}")
                        if objects and len(objects) > 0:
                            # 20100101 农田
                            # 20100102 木头
                            # 20100103 石头
                            # 20100104 黄金
                            # 20100105 钻石
                            # 20200101 兽人
                            # 20200102 骷髅
                            # 20200103 傀儡
                            # 20200104 宝藏精灵
                            # 20200201 死亡骑士
                            # 20200203 红龙
                            # 20300101 城堡
                            # 20400401 神殿
                            # 20500101 buff
                            # 20500102 buff
                            # 20500104 buff
                            # 20600101 指挥中心
                            # 20600102 联盟塔
                            for o in objects:
                                code = o.get("code")
                                floc = o.get("loc")
                                if not more:
                                    if code >= 20200201 and not isCVC:
                                        if code not in [20700405, 20700406, 20700407]:
                                            continue
                                if isCVC:
                                    floc = self.cvcBlockLoc(floc)
                                    if floc[1] != cvcLoc[1] or floc[2] != cvcLoc[2]:
                                        continue

                                if codeMap.get(code) is None:
                                    codeMap[code] = []
                                codeMap[code].append(o)
                            # currentZone = [zone[0]] + zone[1+currentAccept * 9:10 + currentAccept * 9]
                            # self.zoneLeaveListWithList(currentZone, ws,isDebug=isDebug)
                            currentAccept += 1
                            if currentAccept >= needAccept and not waitMarch:
                                break
            self.zoneLeaveListWithList(zone, ws, isDebug=isDebug)
            self.fieldLeave(ws, isDebug=isDebug)
            if more:
                self.refreshUndergroundAndAlliance(
                    codeMap=codeMap, searchLoc=loc, isCVC=isCVC
                )
            return codeMap
        except BrokenPipeError as e:
            self.log(f"wsGetFields BrokenPipeError:{e}")
            return codeMap
        except Exception as e:
            self.errorLog(e, exc_info=True)
            return codeMap
        finally:
            ws.close()

    def searchFields(
        self,
        codes,
        loc=None,
        power=2,
        minLevel=1,
        maxLevel=99,
        noHuman=False,
        show=True,
        noNull=True,
        marchCallBack=None,
        isCrystalOnly=False,
    ):
        """
        搜索资源
        """
        resList = []
        resObjects = []
        objects = self.wsGetFields(
            loc,
            power=power,
            more=True,
            marchCallBack=marchCallBack,
            isCrystalOnly=isCrystalOnly,
        )
        if objects is None:
            return None, None
        else:
            for code in codes:
                fieldName = fieldNames[code] or str(code)
                crystalInfo = None
                try:
                    crystalInfo = objects.get(code)
                # trunk-ignore(bandit/B110)
                except Exception:
                    pass
                if crystalInfo is None:
                    if show and noNull:
                        self.log(f"没有{fieldName}")
                    continue
                rawLoc = loc
                if crystalInfo:
                    allList = {}
                    for info in crystalInfo:
                        loc = info.get("loc")
                        level = info.get("level")
                        if level > maxLevel or level < minLevel:
                            continue

                        distance = self.distanceWithLoc(loc, rawLoc)
                        if allList.get(distance):
                            # trunk-ignore(bandit/B311)
                            distance += random.randint(1, 10) / 10.0
                        allList[distance] = info

                    sortList = [i for i in allList.keys()]
                    sortList.sort()
                    for distance in sortList:
                        info = allList.get(distance)
                        loc = info.get("loc")
                        level = info.get("level")

                        humanStr = ""
                        if code < 20200101:
                            occupied = info.get("occupied")
                            humanStr = "没人"
                            if occupied:
                                if noHuman:
                                    continue
                                name = occupied.get("name")
                                if localMap.get(name):
                                    localMap[name] += 1
                                else:
                                    localMap[name] = 1
                                humanStr = name  #'有人'

                        desc = None
                        if code >= 20500101 and code <= 20500104:
                            param = info.get("param")
                            charmCode = param.get("charmCode")
                            readCharmCode = int(charmCode / 10)

                            desc = f"发现{level}级{charmCodes[readCharmCode]}{fieldName}{loc[1:]} 距离{distance}"
                        else:
                            desc = f"发现{level}级{fieldName}{loc[1:]} {humanStr} 距离{distance}"

                        if desc:
                            if show:
                                self.log(desc)
                            resList.append(desc)
                            resObjects.append(info)

                else:
                    if show and noNull:
                        self.log(f"没有{fieldName}")
        return resList, resObjects

    def searchCrystal(self, loc=None, power=2, minLevel=1, maxLevel=99):
        """
        搜索水晶
        """
        return self.searchFields(
            [20100105], loc=loc, power=power, minLevel=minLevel, maxLevel=maxLevel
        )

    def searchDK(self, loc=None, power=2, minLevel=1, maxLevel=99):
        """
        搜索死亡骑士
        """
        return self.searchFields(
            [20200201], loc=loc, power=power, minLevel=minLevel, maxLevel=maxLevel
        )

    ################################计算类###########################################
    def zoneEnterList(self, world=22, zones=None):
        """
        坐标计算规则 y坐标/32 *64 + x坐标/32
        """
        if zones is None:
            zones = []
        while len(zones) < 4:
            zones.insert(0, 0)
        value = [str(k) for k in zones]
        if useV3WSProxy:
            data = [
                "/zone/enter/list/v3",
                self.jsonToBase64String(
                    {"world": world, "zones": "[" + ",".join(value) + "]"}
                ),
            ]

            if useV4WSProxy:
                data[0] = "/zone/enter/list/v4"
                data[1] = self.jsonToBase64String(
                    {
                        "world": world,
                        "zones": "[" + ",".join(value) + "]",
                        "compType": 3,
                    }
                )
            return data
        else:
            data = [
                "/zone/enter/list/v2",
                {"world": world, "zones": "[" + ",".join(value) + "]"},
            ]
            return data

    def zoneLeaveList(self, world=22, zones=None):
        if zones is None:
            zones = []
        value = [str(k) for k in zones]
        data = [
            "/zone/leave/list/v2",
            {"world": world, "zones": "[" + ",".join(value) + "]"},
        ]
        return data

    def zoneEnterListWithList(self, zone: list, ws: websocket.WebSocket, isDebug=False):
        if zone is None:
            zone = []
        if len(zone) > 10:
            # 分割发送
            worldId = zone[0]
            onceZone = []
            for i in range(1, len(zone)):
                onceZone.append(zone[i])
                if len(onceZone) == 9:
                    self.zoneEnterListWithList(
                        [worldId] + onceZone, ws, isDebug=isDebug
                    )
                    onceZone = []
            if len(onceZone) > 0:
                self.zoneEnterListWithList([worldId] + onceZone, ws, isDebug=isDebug)
        else:
            sendStr = "42" + json.dumps(self.zoneEnterList(zone[0], zone[1:]))
            if isDebug:
                self.debuglog(f"sendDecodeStr: {zone[1:]}")
                self.debuglog(f"sendStr:{sendStr}")
            try:
                ws.send(sendStr)
            # trunk-ignore(bandit/B110)
            except Exception:
                pass

    def zoneLeaveListWithList(self, zone: list, ws: websocket.WebSocket, isDebug=False):
        if zone is None:
            zone = [self.worldId, 0, 64, 1, 65]

        if len(zone) > 10:
            # 分割发送
            worldId = zone[0]
            onceZone = []
            for i in range(1, len(zone)):
                onceZone.append(zone[i])
                if len(onceZone) == 9:
                    self.zoneLeaveListWithList(
                        [worldId] + onceZone, ws, isDebug=isDebug
                    )
                    onceZone = []
            if len(onceZone) > 0:
                self.zoneLeaveListWithList([worldId] + onceZone, ws, isDebug=isDebug)
        else:
            sendStr = "42" + json.dumps(self.zoneLeaveList(zone[0], zone[1:]))
            if isDebug:
                self.debuglog(f"sendStr:{sendStr}")
            if ws.sock:
                try:
                    ws.send(sendStr)
                # trunk-ignore(bandit/B110)
                except Exception:
                    pass

    def zoneLeaveListWithListAndIndex(
        self, zone: list, ws: websocket.WebSocket, index: int
    ):
        if zone is None:
            zone = [self.worldId, 0, 64, 1, 65]
        sendStr = "42" + json.dumps(
            self.zoneLeaveList(zone[0], zone[index * 9 + 1 : 9])
        )
        # ws.u1.debuglog(sendStr)
        if ws.sock:
            try:
                ws.send(sendStr)
            # trunk-ignore(bandit/B110)
            except Exception:
                pass

    def cvcBlockLoc(self, loc):
        """
        计算cvc坐标
        """
        x = int(loc[1])
        y = int(loc[2])
        return [loc[0], x // 400 * 400, y // 400 * 400]

    def cvcCoordinateTransformation(self, loc, power):
        """
        计算cvc坐标转换
        """
        oldZones = self.coordinateTransformation(loc, power)
        worldId = loc[0]
        x = loc[1]
        y = loc[2]
        maxX = (x // 400 + 1) * 400
        maxY = (y // 400 + 1) * 400
        minX = x // 400 * 400
        minY = y // 400 * 400

        def checkZone(zoneId):
            y = (zoneId // 64) * 32  # 行号 * 32
            x = (zoneId % 64) * 32   # 列号 * 32
            return minX <= x <= maxX and minY <= y <= maxY

        newZones = list(filter(checkZone, oldZones[1:]))
        return [worldId] + newZones

    def coordinateTransformation(self, loc, power=2):
        """
        坐标系转换
        计算规则:
        1.根据power 计算出横向最大zone数量
        2.根据你横向zone计算出纵向zone的位移
        3.
        """
        worldId = loc[0]
        x = loc[1]
        y = loc[2]
        point = int(y / 32) * 64 + int(x / 32)
        # z = [-65,-64,-63,-32,-2,-1,0,1,2,3,64,65,66,67]
        x1 = [0]

        for i in range(1, power):
            x1.append(i)
            x1.append(-i)
        z = x1.copy()

        for i in range(1, power):
            z += [v + i * 64 for v in x1]
            z += [v - i * 64 for v in x1]

        points = [point + i for i in z]
        for i in z:
            v = point + i
            if v < 0:
                v = 0
            elif v > 4095:
                v = 4095
            points.append(v)
        points = list(set(points))
        points.sort()

        return [worldId] + points

    def kingdomEnter(self, ws: websocket.WebSocket):
        """
        进入王国
        """
        data = ["/kingdom/enter", {"token": self.token}]
        sendStr = "42" + json.dumps(data)
        ws.send(sendStr)

    def fieldEnter(self, ws: websocket.WebSocket, isDebug=False):
        """
        进入资源
        """
        data = ["/field/enter", {"token": self.token}]
        if useV3WSProxy:
            data = ["/field/enter/v3", self.jsonToBase64String({"token": self.token})]
        sendStr = "42" + json.dumps(data)
        if isDebug:
            self.debuglog(f"sendStr:{sendStr}")
        ws.send(sendStr)

    def handlingFieldEnterResult(
        self,
        ws: websocket.WebSocket,
        data,
        zone,
        isV3=False,
        isMore=False,
        isDebug=False,
    ):
        fieldInfo = data
        if isV3:
            fieldInfo = self.base64StringToJson(data)

        self.zoneLeaveListWithList([zone[0]], ws, isDebug=isDebug)
        enterLoc = fieldInfo.get("loc")
        kingdom = fieldInfo.get("kingdom")
        occupied = kingdom.get("occupied")
        kingdomId = occupied.get("id")
        if not isMore:
            self.loc = enterLoc
        self.kingdomId = kingdomId
        self.debuglog(f"enter {enterLoc} {kingdomId}")
        self.zoneEnterListWithList(zone, ws, isDebug=isDebug)

    def fieldLeave(self, ws: websocket.WebSocket, isDebug=False):
        """
        离开资源
        """
        data = ["/field/leave", {"token": self.token}]
        sendStr = "42" + json.dumps(data)
        try:
            ws.send(sendStr)
            if isDebug:
                self.debuglog(f"sendStr:{sendStr}")
            ws.send("41")
        # trunk-ignore(bandit/B110)
        except Exception:
            pass

    def charmCodeCheck(self, info):
        code = info["code"]
        if code // 10 == 2050010 and code % 10 > 2:
            # loc = info.get("loc")
            level = info.get("level")
            param = info.get("param")
            charmCode = param.get("charmCode")
            readCharmCode = int(charmCode / 10)
            name = charmCodes[readCharmCode]
            levelName = levelNames[level]
            charm = f"Lv.{level}{name}（{levelName}）"
            if oldCharmCode.get(charm):
                return None
            oldCharmCode[charm] = True
            return charm
        return None

    def refreshUndergroundAndAlliance(self, codeMap, searchLoc=None, isCVC=False):
        """
        刷新地下城和联盟
        """
        if self.loc is not None:
            searchLoc = self.loc

        # self.log(f"刷新地下城和联盟 loc:{searchLoc}")

        if codeMap:
            obstacles = [20300101, 20400401, 20600101]
            obstacleFields = {}
            alliances = []
            if codeMap.get(20600101):
                [
                    alliances.append(info.get("loc")[1:])
                    for info in codeMap.get(20600101)
                ]

            for code in codeMap:
                if code in obstacles:
                    for info in codeMap[code]:
                        loc = info.get("loc")
                        key = f"{loc[1]}_{loc[2]}"
                        obstacleFields[key] = 1

                        if info.get("name") is None:
                            if info.get("occupied"):
                                occupied = info.get("occupied")
                                if occupied.get("name") is not None:
                                    info["name"] = occupied.get("name")
                                    info["distance"] = self.distanceWithLoc(
                                        loc, searchLoc
                                    )

            for code in codeMap:
                if code not in obstacles:
                    fields = codeMap[code]
                    for info in fields:
                        loc = info.get("loc")
                        code = info.get("code")
                        name = ""
                        try:
                            name = fieldNames[code]
                            param = info.get("param")
                            if param:
                                charmCode = param.get("charmCode")
                                if charmCode:
                                    readCharmCode = int(charmCode / 10)
                                    name = charmCodes[readCharmCode]
                        except Exception:
                            name = "未知"
                            pass
                        info["name"] = name
                        info["distance"] = self.distanceWithLoc(loc, searchLoc)
                        info["hidden"] = False
                        info["inAlliance"] = False
                        occupied = info.get("occupied")
                        if occupied:
                            occupiedName = occupied.get("name")
                            info["occupiedName"] = occupiedName

                        for i in range(1, 4):
                            x = i % 2
                            y = i // 2
                            key = f"{loc[1] - x}_{loc[2] - y}"
                            if key in obstacleFields or self.locInShrine(loc[1:]):
                                info["hidden"] = True
                                break

                        for allianceLoc in alliances:
                            x = loc[1]
                            y = loc[2]

                            territoryX = allianceLoc[0]
                            territoryY = allianceLoc[1]
                            if abs(x - territoryX) < 12 and abs(y - territoryY) < 12:
                                info["inAlliance"] = True
                                break
        return codeMap

    def checkWSWithMatch3App(self):
        if self.__wsMatch:
            if not self.__wsMatch.keep_running:
                self.__wsMatch = None
                return True
        return self.__wsMatch is None

    def wsWithMatch3App(self, autoCloseTime=60 * 10, isDebug=False):
        realUrl = f"wss{self.wsMatch3[5:]}{self.webSocketSuffix}"
        isDebug = bool(os.getenv("LEAGUEDEBUG", default=isDebug))
        if self.__wsMatch:
            return
        self.__match3AppParams = (autoCloseTime, isDebug)

        if not self.token:
            self.debuglog("wsWithMatch3App token无效")

        def onMessage(ws: LokWebSocketApp, message):
            if isDebug:
                if len(message) < 1000:
                    self.debuglog(f"gwsMsg:{message}")
                else:
                    self.debuglog(f"gwsMsg:{message[:1000]}")
            if self.isInvalid:
                ws.close(reason=b"invalid")

            if message is None or message == "" or len(message) == 0:
                return None
            if message[0] == "0":
                return None
            elif message == "40":
                # 开始发送数据
                self.match3GameEnter(ws, isDebug=isDebug)
                return

            # s = message.split("[")
            # code = int(s[0])
            match = re.match(r"^(\d+)", message)
            code = int(match[0])
            if code == 44:
                self.errorLog("wsGame ws验证错误？？？")
                ws.close(reason=b"invalid")
            elif code == 42:
                ws.lastMsgTime = time.time()
                value = message[2:]
                jsonValue = json.loads(value)
                method = jsonValue[0]
                if method[:13] == "/puzzle/enter":
                    self.handlingMatch3EnterResult(ws, jsonValue[1], isDebug=isDebug)

                elif method == "/puzzle/start":
                    startInfo = self.base64StringToJson(jsonValue[1])
                    if startInfo.get("code"):
                        startCode = startInfo.get("code")
                        if startCode == "level_limit":
                            self.log("wsgame 等级限制 停止")
                            ws.close(reason=b"level_limit")
                            # self.startMatch3Game(ws, {'life':1,'stageNum':1}, isDebug=isDebug)
                        elif startCode == "no_life":
                            self.log("wsgame 体力不足 停止")
                            ws.close(reason=b"no life")
                        else:
                            self.errorLog(f"wsgame start error:{startInfo}")
                            ws.close(reason=b"start error")
                        return

                    status = startInfo.get("status")
                    stageNum = status.get("stageNum")

                    endTh = threading.Timer(
                        # trunk-ignore(bandit/B311)
                        random.randint(10, 30),
                        function=self.endMatch3Game,
                        args=(
                            ws,
                            stageNum,
                        ),
                        kwargs={"isDebug": isDebug},
                    )
                    endTh.name = f"endMatch3Game_{self.key}"
                    endTh.setDaemon(True)
                    endTh.start()
                elif method == "/puzzle/end":
                    startInfo = self.base64StringToJson(jsonValue[1])
                    if startInfo:
                        stageInfo = startInfo.get("stageInfo")
                        if stageInfo:
                            reward_code_1 = stageInfo.get("reward_code_1")
                            reward_value_1 = stageInfo.get("reward_value_1")
                            stage = stageInfo.get("stage")
                            if reward_code_1 and reward_value_1:
                                itemNames = {
                                    Enum.ITEM_CODE_SILVER_CHEST: "白银宝箱",
                                    Enum.ITEM_CODE_GOLD_CHEST: "黄金宝箱",
                                    Enum.ITEM_CODE_PLATINUM_CHEST: "钻石宝箱",
                                }
                                name = itemNames.get(reward_code_1, "未知")
                                self.log(
                                    f"lv:{stage}小游戏获得{name} x {reward_value_1}"
                                )
                        else:
                            isClear = startInfo.get("isClear")
                            if isClear is not None:
                                ws.close(reason=b"exit")
                                return

                    status = startInfo.get("status")
                    startTh = threading.Timer(
                        2,
                        function=self.startMatch3Game,
                        args=(
                            ws,
                            status,
                        ),
                        kwargs={"isDebug": isDebug},
                    )
                    startTh.name = f"startMatch3Game_{self.key}"
                    startTh.setDaemon(True)
                    startTh.start()
                else:
                    if self.checkWSMethodPass(method, isGame=True) and isDebug:
                        self.debuglog(f"no record method:{method} msg:{value}")

            if time.time() - ws.lastMsgTime > 60:
                ws.close(reason=b"no msg")
            elif time.time() - ws.startTime > autoCloseTime:
                ws.close(reason=b"need restart")

        def onError(ws, error):
            if isinstance(error, websocket._exceptions.WebSocketTimeoutException):
                self.debuglog("gws ping/pong time out")
            elif isinstance(error, websocket._exceptions.WebSocketBadStatusException):
                if error.status_code == 502:
                    ws.is502 = True
                else:
                    self.log(f"gws error:{error}")
            else:
                self.debuglog(f"gws error:{error}", exc_info=True)
            self.__wsMatch = None

        def onClose(ws, code, reason):
            if code or reason:
                self.log(f"gclose {code} {reason}")
            self.__wsMatch = None
            if ws.is502:
                ws.retryCount += 1
                if ws.retryCount > 5:
                    self.errorLog("gws502连接失败 重试超限")
                else:
                    self.debuglog(f"gws502连接失败 重试{ws.retryCount}")
                    self.wsWithMatch3App(*self.__match3AppParams)
                    time.sleep(0.5 * ws.retryCount)

        def onPong(ws, data):
            if self.isInvalid:
                ws.close(reason=b"invalid")
                return
            ws.send("2")

        def onPing(ws, data):
            self.log("ping")

        def onOpen(ws):
            pass

        wsApp = LokWebSocketApp(
            realUrl,
            on_open=onOpen,
            on_message=onMessage,
            on_error=onError,
            on_close=onClose,
            on_pong=onPong,
            on_ping=onPing,
        )
        wsApp.lastMsgTime = time.time()
        wsApp.startTime = time.time()
        wsApp.u1 = self

        if isDebug:
            wsApp.oldSend = wsApp.send

            def debugSend(data, opcode=ABNF.OPCODE_TEXT):
                self.debuglog(f"gwsSend:{data}")
                wsApp.oldSend(data, opcode)

            wsApp.send = debugSend

        def runWsApp(wsApp: LokWebSocketApp):
            self.__wsMatch = wsApp
            if self.s5m:
                auth = None
                if self.s5m.user:
                    auth = (self.s5m.user, self.s5m.pwd)
                wsApp.run_forever(
                    http_proxy_host=self.s5m.host,
                    http_proxy_port=self.s5m.port,
                    proxy_type="socks5",
                    http_proxy_auth=auth,
                    ping_interval=20,
                    ping_timeout=5,
                )
            else:
                wsApp.run_forever(ping_interval=20, ping_timeout=5)

        thread = threading.Thread(
            target=runWsApp, args=(wsApp,), name=f"wsApp_Match3_{self.key}"
        )
        thread.setDaemon(True)
        thread.start()

    def match3GameEnter(self, ws: websocket.WebSocket, isDebug=False):
        """
        进入游戏
        """
        data = ["/puzzle/enter", {"token": self.token}]
        sendStr = "42" + json.dumps(data)
        if isDebug:
            self.debuglog(f"sendStr:{sendStr}")
        ws.send(sendStr)

    def handlingMatch3EnterResult(self, ws: websocket.WebSocket, data, isDebug=False):
        info = self.base64StringToJson(data)

        if info:
            # stageInfo = info.get("stageInfo")
            # if not stageInfo:
            #     ws.close(reason=b'error level info')
            #     return
            status = info.get("status")
            self.debuglog(f"handlingMatch3EnterResult info:{info}")
            self.startMatch3Game(ws, status, isDebug=isDebug)
        else:
            ws.close(reason=b"enter info invalid")

    def startMatch3Game(self, ws: websocket.WebSocket, status, isDebug=False):
        stageNum = status.get("stageNum")
        life = status.get("life")
        if life > 0:
            # if stageNum > 500:
            #     self.log("已经500 暂停")
            if Match3LevelLimit[self.level] < stageNum:
            # if self.level < 20 and stageNum > (self.level // 5 + 1) * 10:
                self.log(f"超过等级限制:{self.level} {Match3LevelLimit[self.level]} 暂停")
            else:
                data = ["/puzzle/start", {"stageNum": str(stageNum)}]
                sendStr = "42" + json.dumps(data)
                if isDebug:
                    self.debuglog(f"sendStr:{sendStr}")
                self.debuglog(f"开始游戏 当前体力:{life} 关卡:{stageNum}")
                ws.send(sendStr)
                return
        elif life == 0:
            self.endMatch3Game(ws, 0, isDebug=isDebug)
        ws.close(reason=b"start close")

    def endMatch3Game(self, ws: websocket.WebSocket, stageNum, isDebug=False):
        # if stageNum > 500:
        #     if random.randint(0,3) == 0:
        #         self.log(f'超过500 放弃了 :{stageNum}')
        #         stageNum = 0
        data = ["/puzzle/end", self.jsonToBase64String({"isClear": stageNum})]
        sendStr = "42" + json.dumps(data)
        if isDebug:
            self.debuglog(f"sendStr:{sendStr}")
        if stageNum == 0:
            self.debuglog("结束游戏 没体/放弃")
        else:
            self.debuglog(f"完成游戏 关卡:{stageNum}")

        ws.send(sendStr)

# 在类定义完成后设置类型提示
LokWebSocketApp.u1: UserWebSocket = None
