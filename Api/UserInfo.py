#!/usr/bin/env python3
# -*- coding: UTF-8 -*-

import asyncio
import math
import random
import secrets
import threading
import time
import cachetools
import re
from concurrent.futures import ThreadPoolExecutor
from types import FunctionType
from enum import IntEnum

import Unit.enum as Enum
from Api.FlaskHelper import requestSuperGo
from Api.User.AutoOptions import (
    AutoOptions,
    # trunk-ignore(ruff/F401)
    freeBuildJson,
    # trunk-ignore(ruff/F401)
    loadBuildJson,
    # trunk-ignore(ruff/F401)
    vipShopItemNames,
)
from Api.User.Development import (
    Development,
      # trunk-ignore(ruff/F401)
    buildFrameLevelSleepTimes,
      # trunk-ignore(ruff/F401)
    buildLevelSleepTimes,
      # trunk-ignore(ruff/F401)
    buildMainLevelSleepTimes,
    removeCrystalListWithMember,
    removeDragoMineListWithMember,
)
from Api.User.External import UserExternal

# trunk-ignore(ruff/F401)
from Api.User.Request import refreshNod<PERSON>ey
from Api.User.UDrago import Drago
from Api.User.Web3 import UserWeb3
from Api.User.WebSocket import UserWebSocket, charmCodes, fieldNames
from Model.Kingdom import Kingdom
from Model.Task import StartTaskEnum
from Model.User import limitTroopQuantity
from Model.UserError import UserError
from Unit.ZeroUnit import UserZero
from Unit.enum import (
    ITEM_CODE_FOOD_BOOST_1D,
    ITEM_CODE_GATHERING_BOOST_1D,
    ITEM_CODE_GOLD_BOOST_1D,
    ITEM_CODE_LUMBER_BOOST_1D,
    ITEM_CODE_STONE_BOOST_1D,
)
from Unit.LandZoneUnit import (
    MaxSingleZoneSize,
    MaxSixThreadZoneSize,
    returnAdjacentCrystalLands,
)

# trunk-ignore(ruff/F401)
from Unit.Redis import redisHelper, secondToDate, todayStr, todayUTCStr

rewrardCodes = [
    10104132,  # 圣诞灯笼
    10101002,  # 100钻石
    10101003,  # 500钻石
    10101004,  # 1000钻石
    10101005,  # 5000钻石
    10101006,  # 1W钻石
    10104024,  # 白银
    10104025,  # 黄金
]


"""倍领的奖励列表"""

shopItemNames = {
    10101007: "VIP10",
    10101008: "VIP100",
    10101009: "VIP500",
    10101010: "VIP1000",
    10101011: "VIP5000",
    10101012: "VIP1w",
    10101049: "能量10",
    10101050: "能量20",
    10101051: "能量50",
    10101052: "能量100",
    10102021: "低级防偷窥",
    10102022: "高级防偷窥",
    10102009: "低级获取",
    10102010: "高级获取",
    10102012: "高级规模",
    10102014: "高级体力",
    10102016: "高级防御",
    10102018: "高级攻击",
    10102020: "高级速度",
    10103001: "加速1分钟",
    10103002: "加速5分钟",
    10103003: "加速10分钟",
    10103004: "加速30分钟",
    10103005: "加速1小时",
    10103006: "加速3小时",
    10103007: "加速8小时",
    10103008: "加速1天",
    10103009: "加速3天",
    10103010: "加速7天",
}
"""重要商店物品"""

crystalItems = [
    10101007,
    10101008,
    10101009,
    10101049,
    10101050,
    10101051,
    10101052,
    10103007,
    10103008,
    10103009,
    10103010,
]

resourceItems = crystalItems + [
    10102022,
    10102010,
    10102012,
    10102014,
    10102016,
    10102018,
    10102020,
    10103001,
    10103002,
    10103003,
    10103004,
    10103005,
    10103006,
]

epicFragments = {
    10505001: "绿-山铜匕首",
    10505002: "绿-风暴破坏者",
    10505003: "绿-恶魔克星",
    10505004: "绿-古代手镯",
    10505005: "绿-审判节杖",
    10505006: "绿-真理之镜",
    10504013: "金-cvc骑",
    10504014: "金-cvc弓",
    10504015: "金-cvc步",
    10504008: "金-号角",
    10504003: "金-天界盾牌",
    10504002: "金-勇敢的翅膀",
    10504010: "金-指挥官之鞭",
    10504004: "金-毁灭之刃",
    10504009: "金-星星的心脏",
    10504006: "金-女神的祝福",
    10504007: "金-自然的力量",
    10504005: "金-天界的浆果",
    10504011: "金-晶体烧瓶",
    10504012: "金-专家之手",
    10504001: "金-英雄的灵魂",
    10503001: "紫-次元门",
    10503007: "紫-战士的标记",
    10503009: "紫-战争长矛",
    10503013: "紫-傀儡的碎片",
    10503026: "紫-火焰的花瓣",
    10503011: "紫-骨头胸甲",
    10503004: "紫-黄金斧头",
    10503024: "紫-龙的方",
    10503025: "紫-龙灵",
    10503016: "紫-系收卷轴",
    10503023: "紫-龙甲",
    10503018: "紫-毁灭之锤",
    10503022: "紫-矮子的礼物",
    10503021: "紫-圣人帽子",
    10503019: "紫-生命之符",
    10503020: "紫-生命宝石",
    10503008: "紫-闪亮箭头",
    10503010: "紫-被诅咒的剑",
    10503014: "紫-污染的魔杖",
    10503015: "紫-黑曜石",
    10503003: "紫-黄金谷物",
    10503012: "紫-吸血鬼之手",
    10503005: "紫-黄金锤子",
    10503002: "紫-女神的垂饰",
    10503017: "紫-黑暗水晶",
}
"""史诗装备碎片"""


skillNames = {
    10001: "及时收集",
    10002: "收集加速",
    10003: "立即撤回",
    10004: "攻击加成",
    10005: "防御加成",
    10006: "体力加成",
    10007: "速度加成",
    # 10008:"次元门",
    10009: "降低攻击",
    10014: "降低采集",
    10015: "降低训练",
    10018: "生产加成",
    10020: "训练加成",
    10021: "研究加成",
    10022: "建筑加成",
    10023: "召唤怪物",
    10025: "步兵加成",
    10026: "弓兵加成",
    10027: "骑兵加成",
}

serverTmpMap = {}
"""服务器海盗船缓存"""


class Result:
    value = None
    hasOccupied = False
    loc = None

    def __init__(self, loc):
        self.loc = loc


class RallyMoIdStatus(IntEnum):
    """团战状态枚举"""
    INIT = 0
    """初始状态"""
    JOINED = 1
    """已加入"""
    REPEATED = 2
    """重复团"""
    ENEMY = 3
    """敌对团"""
    T7_TEAM = 4
    """T7团"""
    NO_TIME = 5
    """时间不够"""
    NO_GATE = 94
    """没有门权"""
    NO_TROOP = 95
    """没有部队"""
    INVALID = 96
    """无效团"""
    NO_STATE = 97
    """状态不对"""
    FAR_DISTANCE = 98
    """距离太远"""
    HAS_MESSAGE = 99
    """带消息的团"""
    NEED_MONITOR = 100
    """需要监控的团"""
    MONITORED = 101
    """已监控"""


class UserInfo(
    UserExternal,
    UserWebSocket,
    UserWeb3,
    AutoOptions,
    Drago,
    Development,
    UserZero
):
    """对外聚合接口"""

    canUseItem = True
    crystalTime = 0

    rallyList = None
    caravaTime = 0
    lastGobackTime = 0
    lastVipBuyTime = 0
    accelerationItemTime = 0
    attackedList = None
    crystalHistory = None

    noWsDataCount = 0
    lastMoTime = 0
    woundFinishTime = 0
    """已治疗时间"""

    def __post_init__(self):
        super().__post_init__()
        self.attackedList = []
        self.rallyList = cachetools.FIFOCache(50)
        self.crystalHistory = []

    def __del__(self):
        if hasattr(super(), '__del__'):
            super().__del__()  # 调用父类的 __del__ 方法（如果存在）
        try:
            self.debuglog("UserInfo deinit")
        except Exception:
            pass

    def checkNoWsDataCount(self):
        """确认没有数据次数"""
        if self.noWsDataCount > 10:
            self.invalidStatu = True
            raise UserError.noauth("ws不给数据引起登出")
        self.noWsDataCount += 1

    def resetNoWsDataCount(self):
        """重置没有数据次数"""
        self.noWsDataCount = 0

    # 攻击一个王国
    def attackKingdom(self, kingdom, troops, onlyInfantry=False):
        marchTroops = []
        sum = 0
        # if self.isLevel2:
        #     troops.reverse()
        newTroops = []
        for troop in troops:
            if troop.get("amount") > 0:
                code = int(troop.get("code"))
                if code in [50100101, 50100102, 50100103, 50100104, 50100105]:
                    newTroops.insert(0, troop)
                else:
                    if onlyInfantry:
                        continue
                    if code % 10 > 1:
                        newTroops.insert(0, troop)
                    else:
                        newTroops.append(troop)

        for troop in newTroops:
            marchTroop = self.buildMarchTroop(troop["code"])
            troopNum = troop["amount"]
            if sum >= self.marchSize:
                marchTroop["amount"] = 0
            if sum + troopNum > self.marchSize:
                marchTroop["amount"] = self.marchSize - sum
                sum = self.marchSize
            else:
                marchTroop["amount"] = troopNum
                sum += troopNum

            marchTroops.append(marchTroop)

        if sum < limitTroopQuantity:
            self.debuglog("攻击%s失败 带兵不足" % kingdom.name)
            return False
        if onlyInfantry:
            if sum < self.marchSize:
                return False
        return self.startAttack(kingdom.loc, marchTroops, kingdom.name)

    def attackMonster(
        self, loc, level=0, isTreasure=False, isWeb=False, uTroopNum=0, wsUser=None
    ):
        """
        攻击怪兽
        """
        if len(self.attackedList) > 50:
            self.attackedList.pop(0)

        if loc in self.attackedList:
            return True

        if redisHelper.getSafeMonsterLoc(loc):
            return True

        if not isWeb and self.actionPoint < 10:
            return None
        monsterLevels = [
            3000,
            10000,
            31000,
            95000,
            185000,
            250000,
            300000,
            300000,
            300000,
            300000,
        ]
        # if level >= 7:
        #     monsterLevels = [self.marchSize for v in range(9)]
        if self.level >= 35:
            monsterLevels = [
                1000,
                5000,
                10000,
                50000,
                185000,
                250000,
                300000,
                300000,
                400000,
                500000,
            ]
        elif self.level > 28:
            monsterLevels = [
                1000,
                5000,
                10000,
                50000,
                185000,
                250000,
                300000,
                300000,
                300000,
                300000,
            ]
            if self.email == "<EMAIL>" or self.isBug:
                monsterLevels = [
                    1000,
                    5000,
                    10000,
                    30000,
                    150000,
                    250000,
                    300000,
                    400000,
                    400000,
                    400000,
                ]

        marchTroops = []
        sum = 0
        info = self.getInfoAndCheck(loc, wsUser=wsUser)
        if info is None:
            return False

        if level == 0:
            level = info.get("fo").get("level")

        numMarch = info.get("numMarch")
        if numMarch == self.marchLimit:
            # self.debuglog("队列满了")
            return None

        code = info.get("fo").get("code")
        name = fieldNames.get(code) or code
        if self.level < 30 and monsterLevels[level - 1] > self.marchSize:
            self.debuglog(f"攻击{level}级{name}失败 带兵不足")
            return False
        if code not in Enum.OBJECT_MONSTER_CODE_LIST:
            self.debuglog(f"攻击{level}级{name}失败 不是怪兽")
            self.attackedList.append(loc)
            return False

        troops = info.get("troops")
        newTroops = []
        # 第一响应军团
        firstNum = 0
        for troop in troops:
            if troop.get("amount") > 0:
                code = int(troop.get("code"))
                if code % 10 == 7:
                    # 过滤T7兵种
                    continue
                # 5级以上不使用T3步兵
                if level > 5:
                    if code % 10 < 4 and code // 100 % 10 == 1:
                        continue

                # 骑兵弓兵优先
                if int(code) // 100 % 10 > 1:
                    # 宝藏模式下 骑兵优先
                    if isTreasure:
                        if int(code) // 100 == 501003:
                            newTroops.insert(0, troop)
                            firstNum += 1
                        else:
                            newTroops.insert(firstNum, troop)
                    else:
                        newTroops.insert(0, troop)
                else:
                    newTroops.append(troop)

        maxSize = min(monsterLevels[level - 1], self.marchSize)
        if self.level == 30 and level > 6:
            maxSize = max(monsterLevels[level - 1], self.marchSize)

        if self.level == 30 and isWeb and uTroopNum > 0:
            maxSize = uTroopNum

        for troop in newTroops:
            marchTroop = self.buildMarchTroop(troop["code"])
            troopNum = troop["amount"]
            if sum >= maxSize:
                marchTroop["amount"] = 0
                continue
            if sum + troopNum > maxSize:
                marchTroop["amount"] = maxSize - sum
                sum = maxSize
            else:
                marchTroop["amount"] = troopNum
                sum += troopNum

            marchTroops.append(marchTroop)

        if sum < maxSize:
            self.debuglog(f"攻击{level}级{name}失败 带兵不足")
            return False

        res = self.requestStart(5, loc, marchTroops)
        if res and res.get("result"):
            self.increaseTodayMonster()
            redisHelper.setSafeMonsterLoc(loc)
            self.actionPoint -= 10
            self.attackedList.append(loc)
            newTask = res.get("newTask", {})
            taskTime = newTask.get("time", 0)
            self.randomSleep(
                1,
                2,
                msg=f"攻击{level}级{name}成功 距离{self.distanceWithLoc(loc)} 耗时:{round(taskTime,2)} 坐标:{loc[-2]},{loc[-1]}",
            )
            return True
        elif res:
            err = res.get("err")
            if err and err.get("code"):
                code = err.get("code")
                if code == "insufficient_actionpoint":
                    self.log("活力点不足动力不足")
                    self.actionPoint = 0
                    return None
                elif code == "full_task":
                    self.debuglog(f"攻击{level}级怪兽失败 队列满了")
                    return None
                elif code == "no_troops":
                    self.debuglog(f'攻击{level}级怪兽失败 带兵不足')
                    return None
                elif code == "timeout":
                    self.debuglog(f'攻击{level}级怪兽失败 超时')
                    return True
                self.log(f"攻击{level}级怪兽失败 {err}")
            else:
                self.log(f"攻击{level}级怪兽失败")

        return False

    def quickCrystal(
        self,
        info,
        historyLoc,
        troopCode=50100306,
        amount=100,
        needRealInfo=True,
        wsUser=None,
    ) -> StartTaskEnum:
        """挖水晶矿"""
        if self.getExceedCrystalDailyQuota():
            self.log("异常进入 限额")
            return StartTaskEnum.limitMax
        # self.log(f'限额Key: {self.exceedCrystalDailyQuotaKey} 值{self.getExceedCrystalDailyQuota() and "有" or "无"}')
        loc = info.get("loc")
        level = info.get("level")
        code = info.get("code", 20100105)
        if len(self.crystalHistory) > 20:
            self.crystalHistory.pop(0)

        if level > self.crystalMaxLevel:
            self.crystalHistory.append(loc)
            self.debuglog(f"内控等级{level}的水晶")
            return StartTaskEnum.fail

        occupied = info.get("occupied")
        noQuick = True  # 停用水晶加速
        if occupied:
            self.crystalHistory.append(loc)
            redisHelper.setSafeCrystalLoc(loc)
            removeCrystalListWithMember(loc[0], level, loc)
        else:
            param = info.get("param")
            value = param.get("value")
            if redisHelper.getSafeCrystalLoc(loc):
                self.debuglog(f"水晶矿{loc} {level} {value} 已被采集")
                self.crystalHistory.append(loc)
                removeCrystalListWithMember(loc[0], level, loc)
                return StartTaskEnum.fail
            if value % 50 != 0:
                redisHelper.setSafeCrystalLoc(loc)
                removeCrystalListWithMember(loc[0], level, loc)
                if needRealInfo:
                    self.debuglog(f"{loc} {level} {value}异常尾矿")
                    self.crystalHistory.append(loc)
                if value < 100:
                    noQuick = True
                    return StartTaskEnum.fail
            if needRealInfo:
                self.log(f"水晶矿{loc} {level} {value}")
            if loc in historyLoc:
                return StartTaskEnum.fail
            if needRealInfo:
                realInfo = self.getInfoAndCheck(loc, wsUser=wsUser)
                if not realInfo:
                    redisHelper.setSafeCrystalLoc(loc)
                    removeCrystalListWithMember(loc[0], level, loc)
                    self.randomSleep(0.5, msg="无效水晶矿?")
                    return StartTaskEnum.fail
                fo = realInfo.get("fo")
                occupied = fo.get("occupied")
                if occupied:
                    redisHelper.setSafeCrystalLoc(loc)
                    removeCrystalListWithMember(loc[0], level, loc)
                    self.randomSleep(0.5, msg="被占领的水晶矿")
                    return StartTaskEnum.fail
                if realInfo["numMarch"] == self.marchLimit:
                    self.randomSleep(3, msg="队列满了")
                    return StartTaskEnum.fail

            marchTroops = []
            if self.kingdomTroops:
                marchTroop = self.buildMarchTroopFromKingdom(troopCode, amount)
                if marchTroop:
                    marchTroops.append(marchTroop)
                else:
                    self.log(f"quickCrystal兵力不足1 {troopCode} {amount}")
                    self.debuglog(f"quickCrystal兵力不足1 {self.kingdomTroops}")
                    return StartTaskEnum.fail
            else:
                marchTroops.append(self.buildMarchTroop(troopCode, amount))

            if len(marchTroops) == 0:
                if troopCode % 10 > 1:
                    return self.quickCrystal(
                        info,
                        historyLoc,
                        troopCode=troopCode - 1,
                        amount=amount,
                        needRealInfo=False,
                        wsUser=wsUser,
                    )
                self.log(f"quickCrystal兵力不足2 {troopCode}")
                return StartTaskEnum.fail

            if redisHelper.getSafeCrystalLoc(loc):
                self.debuglog(f"水晶矿{loc} {level} {value} 已被采集")
                self.crystalHistory.append(loc)
                removeCrystalListWithMember(loc[0], level, loc)
                return StartTaskEnum.fail
            else:
                redisHelper.setSafeCrystalLoc(loc)
                removeCrystalListWithMember(loc[0], level, loc)

            res = self.requestStart(1, loc, marchTroops, useLock=False)
            if res and res.get("result"):
                redisHelper.setSafeCrystalLoc(loc)
                name = fieldNames.get(code) or code
                newTask = res.get("newTask")
                self.increaseKingdomTodayCrystalCountValue()
                if newTask:
                    taskTime = newTask.get("time")
                    param = newTask["param"]
                    moId = param.get("moId")
                    distance = param.get("distance", self.distanceWithLoc(loc))
                    if taskTime > 8 and level > 3 and not noQuick:
                        self.log(f"水晶加速队列:{level} {value} {int(taskTime)} {moId}")
                        if self.crystalTasks is not None:
                            self.crystalTasks.append(moId)
                    if value < 20:
                        self.gobackList[moId] = True
                if True:
                    removeCrystalListWithMember(loc[0], level, loc)
                self.randomSleep(
                    0,
                    1,
                    0.4,
                    f"采集资源{level}级{name}成功，等待结果 距离{distance} 速度{round(distance / taskTime,2)}",
                )
                historyLoc.append(loc)
                return StartTaskEnum.success
            elif res:
                err = res.get("err")
                if err and err.get("code"):
                    code = err.get("code")
                    if code == "duplicated":
                        self.randomSleep(1, msg="采集duplicated")
                        return self.quickCrystal(
                            info,
                            historyLoc,
                            troopCode=troopCode,
                            amount=amount,
                            needRealInfo=False,
                            wsUser=wsUser,
                        )
                    elif code == "full_task":
                        return StartTaskEnum.full
                    elif code == "exceed_crystal_daily_quota":
                        self.setExceedCrystalDailyQuota()
                        self.log("今日已限额 1小时候再试")
                    elif code[:22] == "gathering_restriction_":
                        self.errorLog("内控挖矿等级")
                        self.crystalMaxLevel = min(self.crystalMaxLevel, level - 1)
                    else:
                        self.log(f"异常错误{err.get('code')}")
        return StartTaskEnum.fail

    def collectionResourceA(self, locs, troopCode=50100307, amount=1):
        """T5骑兵"""
        # AsyncProxyTransport.from_url(f'socks5://{self.socks5}')
        from httpx_socks import AsyncProxyTransport

        from Unit.FileTool import loadS5List

        s5List = loadS5List()
        marchTroops = []
        marchTroops.append(self.buildMarchTroop(troopCode, amount))
        tasks = []
        for loc in locs:
            tasks.append(
                self.requestStartA(
                    1,
                    loc,
                    marchTroops,
                    transport=AsyncProxyTransport.from_url(
                        f"socks5://{secrets.choice(s5List)}"
                    ),
                )
            )
        self.runTasks(tasks)

    def collectionResource(
        self,
        loc,
        maxCount=3000,
        minCount=100,
        isCrystal=False,
        noT5=False,
        useDrago=False,
        historyLoc=None,
        loadingMax = 0,
        wsUser=None,
        level=None,
    ) -> StartTaskEnum:
        if useDrago:
            self.log("使用龙采集")

        marchTroops = []
        sum = 0
        sumLoading = 0
        info = None
        if historyLoc and loc in historyLoc:
            return StartTaskEnum.fail

        if wsUser:
            realInfo = self.getInfoAndCheck(loc, wsUser=wsUser)
            if not realInfo:
                removeDragoMineListWithMember(self.worldId, level, loc)
                self.randomSleep(0.5, msg="无效资源?")
                return StartTaskEnum.fail
            fo = realInfo.get("fo")
            occupied = fo.get("occupied")
            if occupied:
                removeDragoMineListWithMember(self.worldId, level, loc)
                self.randomSleep(0.5, msg="被占领的资源")
                return StartTaskEnum.fail
            if realInfo["numMarch"] == self.marchLimit:
                self.randomSleep(3, msg="队列满了")
                return StartTaskEnum.full
            info = realInfo
        else:
            info = self.info(loc)

        if info is None:
            return StartTaskEnum.none

        if len(self.crystalHistory) > 20:
            self.crystalHistory.pop(0)
        
        fo = info.get("fo")
        code = fo.get("code")
        param = fo.get("param")
        if code == Enum.OBJECT_CODE_DRAGON_SOUL_CAVERN:
            value = param.get("value")
            if value < 1000:
                removeDragoMineListWithMember(self.worldId, level, loc)
                self.debuglog(f"龙矿{loc} {level} {value} 残矿")
                return StartTaskEnum.fail

        numMarch = info.get("numMarch")
        if numMarch == self.marchLimit:
            # self.debuglog("队列满了")
            return StartTaskEnum.full
        troops = info.get("troops")

        newTroops = []
        for troop in troops:
            if troop.get("amount") > 0:
                code = troop.get("code")
                if code % 10 == 7:
                    # 过滤T7兵种
                    continue
                if isCrystal and int(code / 10) == 5010030:
                    if noT5 and code % 10 == 5:
                        continue
                    newTroops.insert(0, troop)
                else:
                    newTroops.append(troop)
        maxSize = min(maxCount, self.marchSize)
        if loadingMax > 0:
            newTroops.reverse()
            for troop in newTroops:
                marchTroop = self.buildMarchTroop(troop["code"])
                troopNum = troop["amount"]
                if sumLoading >= loadingMax:
                    marchTroop["amount"] = 0
                else:
                    leftLoading = loadingMax - sumLoading
                    amount,loading = self.getLoadingForTroop(troop["code"],leftLoading,troopNum)
                    if amount > 0:
                        marchTroop["amount"] = amount
                        sumLoading += loading
                        sum += amount

                marchTroops.append(marchTroop)

            if sumLoading < loadingMax:
                self.debuglog(f"带兵负重太少不适合采集 {sumLoading}/{loadingMax}")
                return StartTaskEnum.limit
        else:
            for troop in newTroops:
                marchTroop = self.buildMarchTroop(troop["code"])
                troopNum = troop["amount"]
                if sum >= maxSize:
                    marchTroop["amount"] = 0
                if sum + troopNum > maxSize:
                    marchTroop["amount"] = maxSize - sum
                    sum = maxSize
                else:
                    marchTroop["amount"] = troopNum
                    sum += troopNum

                marchTroops.append(marchTroop)

        if sum < minCount:
            self.debuglog("带兵太少不适合采集")
            return StartTaskEnum.limit

        fo = info.get("fo")
        code = fo.get("code")
        level = fo.get("level")
        dragoId = None
        dragoToken = None
        if code == Enum.OBJECT_CODE_DRAGON_SOUL_CAVERN or useDrago:
            self.debuglog("龙矿 查龙")
            dragos = self.dragoLairList()
            availableDragos = []
            for drago in dragos:
                lair = drago.get("lair")
                if lair and lair.get("status") == 1:
                    availableDragos.append(drago)
            workDragos = self.canWorkDragos(availableDragos)
            if workDragos:
                drago = workDragos[0]
                dragoId = drago.get("_id")
                dragoToken = drago.get("tokenId")
                self.log(f'使用工作龙 {dragoId} {dragoToken}')
            if dragoId is None and len(availableDragos) > 0:
                drago = availableDragos[0]
                if drago:
                    dragoId = drago.get("_id")
                    dragoToken = drago.get("tokenId")
                    self.log(f'使用可用龙 {dragoId} {dragoToken}')
            if dragoId is None:
                self.log("龙矿没龙采集失败")
                return StartTaskEnum.none

        res = self.requestStart(1, loc, marchTroops, dragoId=dragoId)
        if res and res.get("result"):
            self.crystalHistory.append(loc)
            name = fieldNames.get(code) or code
            if code == Enum.OBJECT_CODE_CRYSTAL_MINE:
                if self.kingdomId:
                    self.increaseKingdomTodayCrystalCountValue()
                else:
                    self.errorLog("没有kingdomId采集资源")
            if code == Enum.OBJECT_CODE_DRAGON_SOUL_CAVERN:
                removeDragoMineListWithMember(self.worldId, level, loc)

            if code >= 20500101 and code <= 20500104:
                param = fo.get("param")
                charmCode = param.get("charmCode")
                readCharmCode = int(charmCode / 10)
                name = charmCodes[readCharmCode]
            if not self.noWSUser:
                s = ""
                if loadingMax > 0:
                    s = f"负重{round(sumLoading,0)}"
                if dragoToken:
                    s += f" 龙{dragoToken}"
                self.randomSleep(
                    0,
                    1,
                    1,
                    f"采集资源{level}级{name}成功，等待结果 距离{self.distanceWithLoc(loc)} {s}",
                )
            if historyLoc is not None:
                historyLoc.append(loc)
            return StartTaskEnum.success
        elif res:
            err = res.get("err")
            if err and err.get("code"):
                if err.get("code") == "duplicated":
                    pass
                elif err.get("code") == "full_task":
                    return StartTaskEnum.full
                elif err.get("code") == "no_drago_action_point":
                    self.log("没有龙AP")
                    return StartTaskEnum.limit
                else:
                    self.log(f"异常错误{err.get('code')}")
                    if err.get('code',"")[:22] == "gathering_restriction_":
                        if dragoId:
                            self.addBotWorkLog("龙矿内控?没有联盟？")
                            return StartTaskEnum.limitMax

        self.log("采集资源失败")

        return StartTaskEnum.fail

    # 连续攻击
    def attackKingdomAlone(self, kingdom: Kingdom, failCount=0, onlyInfantry=False):
        if kingdom.currentCount > kingdom.maxCount:
            return False
        if self.checkFrequentKingdom(kingdom):
            return False

        if failCount > 5:
            self.log("%s 攻击失败 超过5次" % kingdom.name)
            return False

        # self.log("即将攻击 %s" % kingdom.name)
        info = self.info(kingdom.loc)
        if info:
            fo = info.get("fo")
            code = fo.get("code")
            if code != 20300101:
                self.log(f"攻击失败 目标不是一个城堡 {kingdom.loc}")
                return False
            troops = info.get("troops")
            if self.checkTroopsMax(troops):
                self.debuglog("攻击%s失败 带兵不足" % kingdom.name)
                self.randomSleep(2, 10, c=failCount * 10)
                return self.attackKingdomAlone(kingdom, failCount + 1, onlyInfantry)

            if self.level < 30:
                self.randomSleep(1, 2)
            if self.attackKingdom(kingdom, troops, onlyInfantry):
                kingdom.currentCount += 1
                self.attackCount += 1
                sum = self.sumTroops(troops)
                if sum // self.marchSize > 1:
                    self.randomSleep(1, 2)
                else:
                    self.randomSleep(5, 10)
                failCount = 0
            else:
                # self.log("攻击异常 休息5秒")
                if onlyInfantry:
                    self.randomSleep(5)
                else:
                    self.randomSleep(10)
                    failCount += 1
            return self.attackKingdomAlone(kingdom, failCount, onlyInfantry)

    # 遍历九宫格
    def gridInfo(self, findKingdom, skipLevel=6, descLoc=None):
        if descLoc is None:
            descLoc = self.loc
        startLoc = [self.loc[1] - 2, self.loc[2] - 2]
        for i in range(1, 9):
            x = startLoc[0] + i % 3 * 2
            y = int(startLoc[1] + i / 3 * 2)
            if x <= 0 or y <= 0 or x >= 2046 or y >= 2046:
                continue
            try:
                info = self.info([x, y], isGrid=self.isMain)
                if info:
                    marchType = info.get("marchType")
                    if marchType == 2:
                        fo = info.get("fo")
                        occupied = fo.get("occupied")
                        expired = fo.get("expired")
                        if expired:
                            # 是个资源
                            continue
                        if occupied is not None:
                            name = occupied.get("name")
                            loc = fo.get("loc")
                            if loc == self.loc:
                                # 跳过自己
                                continue
                            shield = occupied.get("shield")
                            if shield and shield == 1:
                                # 开盾了
                                continue
                            level = fo.get("level")
                            if level and level > skipLevel:
                                self.debuglog(
                                    "跳过大于%d级的王国 %s" % (skipLevel, name)
                                )
                                continue

                            # self.debuglog("九宫格发现王国: %s" % name)
                            if isinstance(findKingdom, FunctionType):
                                if findKingdom(Kingdom(name, loc[1:])):
                                    return
                        else:
                            code = fo.get("code")
                            if code:
                                if code % 20000 == 0:
                                    # 是个空地可能？
                                    if self.isMain:
                                        loc = fo.get("loc")
                                        redisHelper.addLoc(loc)

                self.randomSleep(5)
            except UserError as e:
                # 自定义异常继续抛出
                raise e
            except Exception as e:
                self.errorLog(e, True)

    def claimAndTrainTroop(self):
        """收兵练兵"""
        self.claimTroop()
        self.trainTroop()

    # 开始循环掠夺九宫格 连续模式
    def runGridAttackForAlonely(self, callBack=None):
        self.log("攻击九宫格单一连续模式-自动打包")
        try:
            checkTroopsFlag = True
            selfInfo = self.info(self.loc[1:])
            isShield = self.checkShield(selfInfo)
            if isShield:
                self.log("需要破盾 等待10秒")
                self.randomSleep(10)
                if self.tryBreakShield(self.loc):
                    self.log("破盾成功")
                else:
                    self.errorLog("破盾失败 当前坐标:%s" % self.loc)
            else:
                redisHelper.addNoShieldLoc(self.loc)

            # 清空邮件
            self.clearMail()
            self.tryUseItems()
            while self.isInvalid is not True:
                # self.tryHarvestAll()
                if callBack:
                    callBack(self)
                self.getTroops()
                if self.numTroops < limitTroopQuantity:
                    num = 35000 - self.numTroops
                    if self.checkResourceEmpty(num):
                        raise UserError.lowPower()
                    sum = self.sumResources
                    if sum < 5000000:
                        raise UserError.lowPower()

                self.mintMaxResource()
                if checkTroopsFlag:
                    self.troopRecover()
                    self.loopTrainingTroops()
                    checkTroopsFlag = False

                self.claimAndTrainTroop()

                if self.attackCount > 0:
                    self.troopRecover()

                self.attackCount = 0
                self.gridInfo(lambda kingdom: self.attackKingdomAlone(kingdom))
                sl = 300
                if self.attackCount == 0:
                    sl = 600

                self.log("休息%ld秒 本轮攻击成功数%d" % (sl, self.attackCount))
                self.randomSleep(300)
                if self.attackCount == 0:
                    self.claimAndTrainTroop()
                    self.randomSleep(5, 20, 300)
        except UserError as e:
            if e.errorCode == -2:
                self.log(e.message)
            elif e.message == UserError.lowPower().message:
                # 加入待删除 移到小号
                redisHelper.removeMainLoc(self.loc)
                redisHelper.addRemoveAccount(
                    self.email, "|".join([self.email, self.pwd, self.socks5])
                )
                self.errorLog("战五渣删除账号")
                self.invalidStatu = True
            else:
                raise e
        except Exception as e:
            self.errorLog("异常引起结束 %s" % e, True)

    # 循环练兵
    def loopTrainingTroops(self):
        if self.isInvalid:
            return

        self.getTroops()
        if self.numTroops < limitTroopQuantity:
            self.log("带兵数不够 开始练兵")
            count = int((limitTroopQuantity - self.numTroops) / 100) + 30
            for _i in range(count):
                self.claimAndTrainTroop()
                self.randomSleep(5, 20, 300)

            return self.loopTrainingTroops()

    # 最高资源打包
    def mintMaxResource(
        self,
        needRefreash=False,
        allMint=False,
        passOne=False,
        minResource=0,
        randomAny=False,
    ):
        if self.checkMintTime():
            return

        if needRefreash:
            if self.enter() is not True:
                return
        if self.resources is None and not self.isBug:
            self.errorLog("资源异常引起打包重新登陆")
            return self.mintMaxResource(needRefreash=True)

        index = -1
        maxResource = 0
        for i in range(0, 4):
            resource = self.resources[i]
            if minResource > resource:
                continue
            if resource >= maxResource:
                if passOne and i == 0:
                    continue
                index = i
                maxResource = resource

        if randomAny:
            randomIndex = secrets.randbelow(3)
            if index != randomIndex:
                resource = self.resources[randomIndex]
                if resource > 14000000 and resource > minResource:
                    index = randomIndex
                    maxResource = resource

        num = -1
        if maxResource > 14000000:
            num = 2
        elif allMint and maxResource > 7000000:
            num = 1
        elif allMint and maxResource > 1400000:
            num = 0
        else:
            pass
            # 60分钟刷新一次
            # if needRefreash is False and time.time() - self.enterTime > 60 * 60:
            #     return self.mintMaxResource(True, allMint)

        if num >= 0 and self.myresources():
            try:
                self.mint(index, num)
            except UserError as e:
                if e.errorCode == -4:
                    redisHelper.removeMainLoc(self.loc)
                    self.dataRecord().updateBan(1).commit()
                    # trunk-ignore(ruff/B904)
                    raise UserError.threadExit()
        else:
            self.log("打包资源不足")
            self.setMintTime()

    # 批量撤军
    def dismissAllTroops(self):
        if self.isInvalid:
            return False

        self.kingdomProfileMy()

        self.getTroops()

        for troop in self.kingdomTroops:
            if troop.get("amount") > 0:
                if self.dismissTroop(troop):
                    self.randomSleep(1)
                else:
                    self.randomSleep(1, 2)
                    self.dismissTroop(troop)
                    self.randomSleep(1, 2)

        self.log("撤军完成")

    # 尝试修复城墙
    def tryRepairWall(self):
        sum = self.sumResources
        if sum > 3 * 1000 * 1000:
            if self.repairWall():
                self.log("维修成功")
            else:
                self.log("维修失败")
        else:
            self.errorLog("垃圾号可以扔了")
            self.invalidStatu = True
            return True

    def hasResourceItems(self, retry=0):
        # 判断是否有资源道具
        if retry > 3:
            self.errorLog("获取资源道具失败")
            return None

        res = self.itemList()
        if res is None:
            self.randomSleep(2, 5)
            return self.hasResourceItems(retry + 1)

        items = res.get("items", [])
        for item in items:
            # 10101048
            code = int(item.get("code"))
            if code <= 10101048:
                return True
        return False

    # def tryUseNormalItems(self):
    # 使用所有物品
    def tryUseItems(self, useTroop=False, noPower=False, useShield=False, retry=0):
        # self.itemUseLock.acquire()
        if self.isInvalid:
            self.errorLog("token已经异常")
            # self.itemUseLock.release()
            raise UserError.noauth()

        if retry > 3:
            self.errorLog("使用物品失败 超过重试次数")
            # self.itemUseLock.release()
            return None

        # self.log("开始使用物品")
        res = self.itemList()
        if res is None:
            self.randomSleep(2, 5)
            # self.itemUseLock.release()
            return self.tryUseItems(
                useTroop=useTroop, noPower=noPower, useShield=useShield, retry=retry + 1
            )
        items = res.get("items")
        codeList = [
            10104024,
            10104025,
            10104097,
            10104098,
            10104099,
            10104100,
        ]

        speedCodelist = [10104101, 10104102, 10104103, 10104104]
        if items:
            useList = []
            tasks = []
            for item in items:
                code = int(item.get("code"))
                if code == 10104002:
                    self.canTeleport = True
                elif useShield and code == 10102026:
                    self.itemUse(10102026, 1)
                    self.log("我开盾了！")
                elif useTroop and code == 10104130:
                    self.itemUse(code, item.get("amount"))

                elif code < 10102000:
                    if noPower:
                        energys = [10101049, 10101050, 10101051, 10101052]
                        if code in energys:
                            continue
                    useList.append(item)
                elif useTroop and code < 10102020:
                    useList.append(item)
                elif code in speedCodelist:
                    self.itemUse(code, item.get("amount"))
                elif code in codeList:
                    if code in [10104024, 10104025]:
                        amount = item.get("amount")
                        while amount > 0:
                            n = amount
                            if n > 20:
                                n = 20
                                amount -= n
                            else:
                                amount = 0
                            self.itemUse(code, n)

                    self.itemUse(code, item.get("amount"))
                    tasks.append(code)
                    # res = self.itemUse(code, item.get("amount"))
                    # if res:
                    #     needAgain = True
                    # gainItems = res.get("gainItems")
                    # if gainItems:
                    #     for g in gainItems:
                    #         if int(g.get("code")) < 10102000:
                    #             useList.append(g)
            if len(tasks):
                # await self.runTasks(tasks)

                self.randomSleep(3)
                # self.itemUseLock.release()
                return self.tryUseItems(
                    useTroop=useTroop, noPower=noPower, useShield=useShield, retry=retry
                )

            for item in useList:
                self.itemUse(item.get("code"), item.get("amount"))
                # await self.asyncSleep(1)
        self.log("使用物品完成")
        # self.itemUseLock.release()
        return items
        # self.log("使用物品完成")

    # 破盾
    def tryBreakShield(self, withloc):
        if self.isInvalid:
            return False
        locs = redisHelper.allNoShieldLocs()
        count = len(locs)
        if count > 10:
            tmpIndexs = []
            for _i in range(0, 10):
                tmpIndexs.append(random.randint(0, count - 2))
            for index in tmpIndexs:
                loc = locs[index]
                try:
                    if (
                        int(loc[0]) == int(withloc[0])
                        and abs(int(loc[1]) - int(withloc[1]))
                        + abs(int(loc[2]) - int(withloc[2]))
                        < 1100
                    ):
                        res = self.startInvestigation(loc)
                        if res and res.get("result"):
                            return True
                except Exception as e:
                    self.log("破盾异常 %s,%s,%s" % (loc, withloc, e))

        for loc in locs:
            if (
                int(loc[0]) == int(withloc[0])
                and abs(int(loc[1]) - int(withloc[1]))
                + abs(int(loc[2]) - int(withloc[2]))
                < 1100
            ):
                res = self.startInvestigation(loc)
                if res and res.get("result"):
                    return True

        # 固定点破盾
        staticLocs = [
            ["21", "500", "503"],
            ["21", "502", "1507"],
            ["21", "1510", "501"],
            ["21", "1521", "1506"],
            ["7", "997", "1065"],
            ["7", "488", "494"],
            ["7", "519", "1488"],
            ["7", "1510", "497"],
            ["7", "1499", "1498"],
        ]

        for loc in staticLocs:
            if (
                int(loc[0]) == int(withloc[0])
                and abs(int(loc[1]) - int(withloc[1]))
                + abs(int(loc[2]) - int(withloc[2]))
                < 1100
            ):
                res = self.startInvestigation(loc)
                if res and res.get("result"):
                    self.log("使用固定点破盾")
                    return True

        return False

    def tryClaimQuest(self):
        """被动领取奖励"""
        if len(self.questFinishList):
            while len(self.questFinishList):
                quest = self.questFinishList.popitem()[1]
                questId = quest.get("_id")
                code = quest.get("targetCode")
                self.questClaim(questId, code)
            return True
        return False

    def tryAddClaimQuest(self):
        """添加代领取列表"""
        res = self.questList()
        if res:
            mainQuests = res.get("mainQuests")
            if mainQuests:
                for quest in mainQuests:
                    if quest.get("status") == 2:
                        self.addQuest(quest)

            sideQuests = res.get("sideQuests")
            if sideQuests:
                for quest in sideQuests:
                    if quest.get("status") == 2:
                        self.addQuest(quest)

    # 领主线和支线奖励Once
    def questClaimAllOnce(self):
        hasGet = False
        res = self.questList()
        if res is None or self.isInvalid:
            return False
        mainQuests = res.get("mainQuests")
        if mainQuests and len(mainQuests):
            mainQuest = mainQuests[0]
            if mainQuest and mainQuest.get("status") == 2:
                self.questClaim(mainQuest.get("_id"), mainQuest.get("targetCode"))
                hasGet = True

        sideQuests = res.get("sideQuests")
        if sideQuests:
            for sideQuest in sideQuests:
                if sideQuest.get("status") == 2:
                    res = self.questClaim(
                        sideQuest.get("_id"), sideQuest.get("targetCode")
                    )
                    # self.log("完成支线任务")
                    hasGet = True

        return hasGet

    # 领主线和支线奖励
    def questClaimAll(self):
        """
        遍历主线奖励
        """
        hasGet = False
        res = self.questList()
        if res is None:
            return False

        mainTrue = True
        while mainTrue:
            mainQuests = res.get("mainQuests")
            if len(mainQuests) > 0:
                mainQuest = mainQuests[0]
                if mainQuest and mainQuest.get("status") == 2:
                    res = self.questClaim(
                        mainQuest.get("_id"), mainQuest.get("targetCode")
                    )
                    # self.log("完成主线任务")
                    if res is None:
                        return True
                    hasGet = True
                else:
                    mainTrue = False
            # else:
            #     self.errorLog("任务异常 下号")
            #     self.invalidStatu = True
            #     return False

        sideTrue = True
        while sideTrue:
            if res is None:
                return hasGet
            sideQuests = res.get("sideQuests")
            if sideQuests is None:
                return False
            canContinue = False
            for sideQuest in sideQuests:
                if sideQuest.get("status") == 2:
                    res = self.questClaim(
                        sideQuest.get("_id"), sideQuest.get("targetCode")
                    )
                    # self.log("完成支线任务")
                    canContinue = True
                    hasGet = True
                    break
            if not canContinue:
                sideTrue = False

        return hasGet

    def tryClaimDaily(self, getLevel5=True):
        """
        领取每日任务
        """
        if self.isInvalid:
            return False
        dailyQuest = self.questDaily()
        if dailyQuest is None:
            return False
        quests = dailyQuest.get("quests")
        hasGet = False
        hasGoldDay = False
        for quest in quests:
            status = quest.get("status")
            code = quest.get("code")
            if code == 71100015:
                hasGoldDay = True
            if status == 2:
                self.questClaimDaily(quest.get("_id"), quest.get("code"))
                hasGet = True
        if not hasGoldDay:
            self.lastHarvest500 = True
        if hasGet:
            return self.tryClaimDaily(getLevel5=getLevel5)
        else:
            rewards = dailyQuest.get("rewards")
            for reward in rewards:
                if reward.get("status") == 2:
                    level = reward.get("level")
                    if level == 5 and not getLevel5:
                        continue
                    self.log(f"领取{level} 状态:{getLevel5}")
                    self.questClaimDailyLevel(level)
                    hasGet = True
        return hasGet

    def beforeChangeWorld(self):
        """换区前准备"""
        if self.allianceId:
            self.allianceLeave()

        wait = True
        while wait:
            if self.getTroops():
                if len(self.troopFields) > 0:
                    self.invalidStatu = True
                    self.randomSleep(30, msg="等待军队撤回")
                    self.invalidStatu = False
                else:
                    wait = False

        wait = True
        while wait:
            self.troopRecover()
            wounded = self.wounded()
            if wounded is not None and len(wounded) == 0:
                wait = False
            else:
                self.invalidStatu = True
                self.randomSleep(30, msg=f"等待军队治疗 {wounded}")
                self.invalidStatu = False

    def changeWorld(self, worldId, joinAlliance=True, needClear=False, allianceId=None):
        """切换世界"""

        itemList = self.itemList()
        if itemList:
            value = self.itemCount([10104129])
            if len(value) == 0:
                self.log("老号 继续")
                return

        if needClear:
            self.beforeChangeWorld()

        currentWorldId = None
        profile = self.kingdomProfileMy()
        if profile:
            currentWorldId = profile.get("worldId")

        # 改服务器
        if currentWorldId is None:
            currentWorldId = self.worldId
        if currentWorldId != worldId:
            self.wsWithFieldApp(True)
            self.randomSleep(3, 5)
            self.worldmapObjects(currentWorldId)
            self.randomSleep(2, 3)
            self.worldList()
            self.randomSleep(2, 3)
            res = self.worldChange(worldId)
            if res:
                # world_change_alliance
                self.changeWorldFinish(joinAlliance, allianceId=allianceId)
                return
            elif res is False:
                self.tryLeaveAlliance()
                self.randomSleep(5)
                res = self.worldChange(worldId)
                if res:
                    self.changeWorldFinish(joinAlliance, allianceId=allianceId)
                    return

            self.errorLog("切换世界失败,跳过账号")

    def changeWorldFinish(self, joinAlliance=True, allianceId=None):
        self.wsFieldsSendNewEnter()
        self.randomSleep(1, msg="切换世界成功")
        if secrets.randbelow(2) == 0:
            if joinAlliance:
                self.tryJoinAllianceBySystem(allianceId=allianceId)
                self.randomSleep(3, 5)

    def changeWorldWithImmigration(self, worldId):
        """换区移民"""
        currentWorldId = None
        profile = self.kingdomProfileMy()
        if profile:
            currentWorldId = profile.get("worldId")

        # 改服务器
        if currentWorldId is None:
            currentWorldId = self.worldId
        if currentWorldId != worldId:
            if self.crystal < 1000:
                raise UserError.noEnoughCrystals()

            if self.sumResources < self.maxSafeResource * 2:
                raise UserError.noEnoughResources()

            self.beforeChangeWorld()

            self.wsWithFieldApp(True)
            self.randomSleep(3, 5)
            self.worldmapObjects(currentWorldId)
            self.randomSleep(2, 3)
            self.worldList()
            self.randomSleep(2, 3)
            res = self.checkImmigration(worldId)
            if res and res.get("hasKing"):
                res = self.worldImmigration(worldId)
                if res:
                    # world_change_alliance
                    self.changeWorldFinish(False)
                    return
                elif res is False:
                    self.tryLeaveAlliance()
                    self.randomSleep(5)
                    res = self.worldImmigration(worldId)
                    if res:
                        self.changeWorldFinish(False)
                        return
            else:
                raise UserError.noKing()

    # display
    def display(self):
        """
        展示信息
        """
        if self.login():
            self.enter()

    def sign(self):
        """
        签到
        """
        if not self.isLogin:
            if self.login():
                self.enter()

        self.getGoldFree()
        self.getSilverFree()

    def tryBuyVipShop(self, retry=0):
        """清空vip商城资源"""
        if self.vip == 1:
            return False

        if retry > 5:
            self.errorLog("重试5次都没有购买成功,我尽力了")
            return False

        if self.checkWSWithKingdomApp():
            self.enter()
            self.wsWithKingdomApp()

        crystal = self.crystal
        num = int(crystal / 14)
        if num == 0:
            return False
        buys = [10101044, 10101035, 10101026, 10101017]
        buyNumRemains = {}

        res = self.vipshopList()
        if res is None:
            self.randomSleep(3, 5)
            return self.tryBuyVipShop(retry + 1)
        vipshop = res.get("vipShop")
        items = vipshop.get("items")
        for item in items:
            code = item.get("code")
            numRemain = item.get("numRemain")
            if code in buys and numRemain > 0:
                buyNumRemains[code] = numRemain

        for code in buys:
            numRemain = buyNumRemains.get(code)
            if numRemain:
                if num >= numRemain:
                    if self.vipshopBuy(code, numRemain):
                        num -= numRemain
                else:
                    if num > 0:
                        if self.vipshopBuy(code, num):
                            num = 0
                            break

        return True

    # 领事件
    def tryClaimEvents(self):
        self.claimEvent()
        self.tryClaimMail()
        self.clearMail(3)
        # self.deleteMail(3)

    def tryClaimMail(self):
        """
        尝试领取邮件
        """
        res = self.mailList()
        if res:
            mails = res["mails"]
            for mail in mails:
                type = mail["type"]
                if type == 11:
                    mailId = mail["_id"]
                    self.tryMailClaim(mailId, 10)

    def tryMailClaim(self, mailId, taskNum=1):
        """
        多次领取奖励
        """
        if self.level <= 25:
            self.mailClaim(mailId)
            return

        async def mailClaim(u1, mailId):
            await u1.mailClaimA(mailId)

        tasks = [mailClaim(self, mailId) for i in range(taskNum)]
        self.runTasks(tasks)
        self.randomSleep(5, 10)

    def claimEvent(self):
        """
        领取奖励事件
        """
        res = self.eventList()
        if res and res.get("events"):
            events = res.get("events")
            for event in events:
                eventId = event.get("_id")
                reddot = event.get("reddot")
                if eventId and reddot > 0:
                    self.claimEventWithId(eventId)

    def claimCVCEvent(self):
        if not self.cvcEventOpen:
            return
        events = self.cvcEventList()
        if events:
            for event in events:
                eventId = event.get("_id")
                mastCode = event.get("mastCode")
                status = event.get("status")
                if status != 1:
                    continue
                if mastCode != 601079:
                    self.claimEventWithId(eventId)
                    self.randomSleep(2,3)
                else:
                    self.claimCVCEventPoint(eventId)

    def claimCVCEventPoint(self, eventId):
        res = self.eventInfo(eventId)
        if res:
            eventKingdom = res.get("eventKingdom")
            cvcEventId = eventKingdom.get("cvcEventId")
            cvcAlliancePoint = eventKingdom.get("cvcAlliancePoint")
            cvcAllianceRewardClaimed = eventKingdom.get("cvcAllianceRewardClaimed")
            cvcContinentPoint = eventKingdom.get("cvcContinentPoint")
            cvcContinentRewardClaimed = eventKingdom.get("cvcContinentRewardClaimed")
            cvcPoint = eventKingdom.get("cvcPoint")
            cvcRewardClaimed = eventKingdom.get("cvcRewardClaimed")
            if int(cvcPoint) >= 1000000:
                for i in range(1, 11):
                    if i not in cvcRewardClaimed:
                        self.cvcPointReward(cvcEventId, 0, i)
            else:
                return
            
            if int(cvcAlliancePoint) >= 30000000:
                for i in range(1, 11):
                    if i not in cvcAllianceRewardClaimed:
                        self.cvcPointReward(cvcEventId, 1, i)
            
            if int(cvcContinentPoint) >= 200000000:
                for i in range(1, 11):
                    if i not in cvcContinentRewardClaimed:
                        self.cvcPointReward(cvcEventId, 2, i)

    def claimEventWithId(self, eventId):
        eventInfo = self.eventInfo(eventId)
        if eventInfo:
            eventKingdom = eventInfo.get("eventKingdom")
            eventKingdoms = eventInfo.get("eventKingdoms")
            event1 = eventInfo.get("event")
            hasClaim = False
            if eventKingdom and event1:
                eventId = eventKingdom.get("eventId")
                # event1s = event1.get("events")
                eves = eventKingdom.get("events")
                for e in eves:
                    status = e.get("status")
                    if status == 2:
                        code = e.get("code")
                        eventTargetId = self.findEventTargetIdWithEvents(
                            code, eventInfo.get("events")
                        )
                        # rewards = e.get("rewards")
                        # self.checkRewards(rewards)
                        self.tryEventClaim(eventId, eventTargetId, code, taskNum=1)
                        hasClaim = True

                if not hasClaim:
                    for ek in eventKingdoms:
                        # point = ek.get("point")
                        # if point and point > 0:
                        eves = ek.get("events")
                        eventId = ek.get("eventId")
                        for e in eves:
                            status = e.get("status")
                            if status == 2:
                                code = e.get("code")
                                eventTargetId = self.findEventTargetIdWithEvents(
                                    code, eventInfo.get("events")
                                )
                                # rewards = e.get("rewards")
                                # needConcurrent = self.checkRewards(rewards)
                                self.tryEventClaim(
                                    eventId, eventTargetId, code, taskNum=1
                                )

    def tryEventClaim(self, eventId, eventTargetId, code, taskNum=1):
        """
        多次领取奖励
        """
        if True:  # self.level <= 25:
            self.eventClaim(eventId, eventTargetId, code)
            return

        async def eventClaim(u1, eventId, eventTargetId, code):
            await u1.eventClaimA(eventId, eventTargetId, code)

        tasks = [eventClaim(self, eventId, eventTargetId, code) for i in range(taskNum)]
        self.runTasks(tasks)
        self.randomSleep(5, 10)

    def checkRewards(self, rewasrds: list):
        for reward in rewasrds:
            code = reward.get("code")
            if code in rewrardCodes:
                return True
        return False

    def findEventTargetIdWithEvents(self, code, events):
        """
        查找事件id
        """
        for event in events:
            events2 = event.get("events")
            for e in events2:
                ecode = e.get("code")
                if ecode == code:
                    return e.get("_id")
        return None

    def tryMiningCrystal(self, info, historyLoc, maxTroopNum=2000, noT5=False):
        """挖水晶矿"""
        loc = info.get("loc")
        level = info.get("level")
        occupied = info.get("occupied")
        if occupied is None:
            self.log(f"水晶矿{loc} {level}")
            if loc in historyLoc:
                return False

            return self.collectionResource(
                loc,
                maxCount=maxTroopNum,
                minCount=maxTroopNum,
                isCrystal=True,
                noT5=noT5,
            )

    def tryAttackTreasure(
        self, info, historyLoc, minLevel=1, maxLevel=5, maxDistance=9999
    ):
        """打精灵"""
        loc = info.get("loc")
        level = info.get("level")
        if self.actionPoint < 10:
            self.log("活力点不足10点")
            return False
        if level > maxLevel or loc in historyLoc:
            return False
        return self.attackMonster(loc, level, isTreasure=True)

    def tryAttackMonster(
        self,
        monsterId=None,
        maxLevel=3,
        minLevel=1,
        maxDistance=50,
        power=2,
        mining=False,
        onlyCavalry=False,
    ):
        """
        尝试攻击怪物 20200101 兽人 20200102骷髅 20200103傀儡 20200104宝藏
            20700405:"食人魔",20700406:"饿狼",20700407:"独眼巨人",
        """

        def attMonster(objects):
            # treasureMode = False

            if self.actionPoint < 10:
                self.log("活力点不足10点")
                return None

            monsters = [20200101, 20200102, 20200103, 20700405, 20700406, 20700407]
            historyLoc = []
            for field in self.troopFields:
                toLoc = field.get("toLoc")
                historyLoc.append(toLoc)

            allMonsters = {}
            if monsterId:
                monsters = [monsterId]
            if len(objects) == 1:
                # treasureMode = True
                monsters = [20200104]

            if power > 4 and len(objects) > 7:
                self.log("发现宝藏，切换宝藏模式")
                crystals = objects.get(20100105)
                if mining and crystals:
                    crystals = self.returnSortFields(crystals)
                    for info in crystals:
                        self.tryMiningCrystal(info, historyLoc)

                result = objects.get(20200104)
                if result:
                    result = self.returnSortFields(
                        result, maxLevel=maxLevel, historyLoc=historyLoc
                    )
                    for info in result:
                        loc = info.get("loc")
                        level = info.get("level")

                        res = self.attackMonster(loc, level, isTreasure=onlyCavalry)
                        if res:
                            historyLoc.append(loc)
                        #     return True

            for monster in monsters:
                result = objects.get(monster)
                if result:
                    for info in result:
                        loc = info.get("loc")
                        level = info.get("level")
                        if level > maxLevel:
                            continue
                        if level < minLevel:
                            continue
                        if loc in historyLoc:
                            continue

                        distance = self.distanceWithLoc(loc)
                        if distance <= maxDistance:
                            allMonsters[distance] = info
                        else:
                            pass
                            # self.log(f"超出距离的怪物:{loc}:{distance}")

            sortList = [i for i in allMonsters.keys()]
            sortList.sort()

            hasAttack = False
            if len(sortList) == 0:
                self.log("周围没有怪物？？")
            for distance in sortList:
                info = allMonsters[distance]

                loc = info.get("loc")
                level = info.get("level")
                res = self.attackMonster(loc, level)
                if res:
                    hasAttack = True
                else:
                    break
                self.randomSleep(1, 2)

            return hasAttack

        if self.actionPoint > 10:
            if self.getTroops():
                if len(self.troopFields) < self.marchLimit:
                    objects = self.wsGetFields(power=power)
                    if objects:
                        self.resetNoWsDataCount()
                        return attMonster(objects)
                    else:
                        self.checkNoWsDataCount()
                        self.errorLog("ws没有返回数据")
                else:
                    # self.log("队列满了")
                    pass
        else:
            self.log("活力点不足10点")
            return None
        return False

    def tryGathering(
        self,
        maxCount=3000,
        minCount=100,
        dsecType=None,
        maxDistance=100,
        minLevel=1,
        maxLevel=10,
        mustLast=False,
        power=2,
    ):
        """
        自动采集 20100105水晶矿
        """
        self.index = 0

        def gatheringSome(objects):
            types = [20100101, 20100102, 20100103, 20100104]
            if self.worldId > 100000:
                types = [20700601, 20700602, 20700603, 20700604]
            historyLoc = []
            for field in self.troopFields:
                toLoc = field.get("toLoc")
                historyLoc.append(toLoc)
            if mustLast:
                try:
                    result = objects.get(20100105)
                    for info in result:
                        self.tryMiningCrystal(info, historyLoc)
                except Exception:
                    pass
            type = types[self.index % 4]
            if dsecType:
                type = dsecType
            result = objects.get(type)
            if result:
                allList = {}
                for info in result:
                    occupied = info.get("occupied")
                    if occupied:
                        # 有人了
                        continue
                    loc = info.get("loc")
                    level = info.get("level")
                    if loc in historyLoc:
                        continue
                    # 尾矿判断
                    param = info.get("param")
                    if mustLast and param:
                        value = param.get("value")
                        if value > minCount * 2.5:
                            continue

                    distance = self.distanceWithLoc(loc)
                    if distance > maxDistance:
                        continue
                    if minLevel > level or maxLevel < level:
                        continue
                    if allList.get(distance):
                        distance += random.randint(1, 10) / 10.0
                    allList[distance] = loc

                sortList = [i for i in allList.keys()]
                sortList.sort()

                if len(sortList) == 0:
                    self.errorLog("周围没有采集点？？")
                    self.index += 1
                for distance in sortList:
                    loc = allList.get(distance)
                    res = self.collectionResource(
                        loc, maxCount=maxCount, minCount=minCount
                    )
                    if StartTaskEnum.success == res:
                        self.index += 1
                        return True
                    elif res in [StartTaskEnum.limit,StartTaskEnum.full]:
                        return False

            self.log(f"没有数据{type}")
            return False

        while self.index < 4 and not self.isInvalid:
            if self.getTroops():
                if len(self.troopFields) < self.marchLimit:
                    isCVC = self.worldId > 100000
                    if isCVC:
                        power = random.randint(1, 4)
                    objects = self.wsGetFields(power=power, isCVC=isCVC)
                    if objects:
                        self.resetNoWsDataCount()
                        if gatheringSome(objects):
                            self.randomSleep(30)
                        else:
                            self.debuglog("采集失败")
                    else:
                        self.checkNoWsDataCount()
                        self.errorLog("ws没有返回数据")
                else:
                    # self.log("队列满了")
                    self.randomSleep(int(maxCount / 3000) * 30)

            self.randomSleep(20, 30)

    def crystalOrTreasure(self, power=4, maxLevel=5, loc=None, maxTroopNum=2000):
        """水晶和宝藏"""
        if self.getTroops():
            if len(self.troopFields) < self.marchLimit:
                objects = self.wsGetFields(loc=loc, power=power)
                if objects:
                    self.resetNoWsDataCount()
                    historyLoc = []

                    for field in self.troopFields:
                        toLoc = field.get("toLoc")
                        historyLoc.append(toLoc)
                        if len(self.crystalHistory) > 10:
                            self.crystalHistory.pop(0)
                        self.crystalHistory.append(toLoc)

                    try:
                        result = objects.get(20100105)
                        result = self.returnSortFields(
                            result, historyLoc=self.crystalHistory
                        )
                        for info in result:
                            self.tryMiningCrystal(
                                info, historyLoc, maxTroopNum=maxTroopNum
                            )
                    except Exception:
                        pass

                    if self.actionPoint > 30:
                        try:
                            result = objects.get(20200104)
                            result = self.returnSortFields(
                                result, maxLevel=maxLevel, historyLoc=historyLoc
                            )
                            for info in result:
                                if self.actionPoint > 20:
                                    self.tryAttackTreasure(
                                        info, historyLoc, maxLevel=maxLevel
                                    )
                        except Exception:
                            pass
                    # return True

                else:
                    self.checkNoWsDataCount()
                    self.errorLog("ws没有返回数据")
            else:
                # self.log("队列满了")
                pass

        return False

    def clearCrystal(self, crystal, descIndex: int = None):
        """清空砖石"""
        if crystal == 0:
            return
        cousts = [1, 5, 9, 40, 70, 300, 500]
        codeList = [
            10101013,
            10101022,
            10101031,
            10101040,
        ]
        minIndex = 0
        # minResource = 9999999999
        # for i in range(len(self.resources)):
        #     resource = self.resources[i]
        #     if resource < minResource:
        #         minIndex = i
        #         minResource = resource

        minIndex = random.randint(1, 3)
        # 50%的概率买金子
        if random.randint(1, 2) == 1:
            minIndex = -1

        if minIndex > len(codeList):
            minIndex = -1

        if descIndex is not None:
            minIndex = descIndex

        code = codeList[minIndex]
        codes = [i + code for i in range(7)]

        # for i in range(7):
        # codes = [10101040,10101041,10101042,10101043,10101044,10101045,10101046]
        for i in range(6, -1, -1):
            coust = cousts[i]
            if crystal >= coust:
                itemCode = codes[i]
                n = int(crystal / coust)
                self.itemBuyUse(itemCode, n)
                crystal -= coust * n

        while self.itemBuyUse(code, 1):
            time.sleep(1)

    def checkDragon(self, items=None):
        """查龙"""
        dragonList = [10104105, 10104106, 10104107]
        dragonNames = {
            10104105: "绿龙",
            10104106: "红龙",
            10104107: "金龙",
        }
        dragonMap = {}

        if items is None:
            res = self.itemList()
            if res:
                items = res.get("items")
        if items:
            for item in items:
                code = item.get("code")
                if code in dragonList:
                    amount = item.get("amount")
                    dragonMap[code] = amount

        if len(dragonMap):
            s = f"我有龙：{self.loc[0]} "
            n = 0
            for dragonCode in dragonMap:
                s += f"{dragonNames.get(dragonCode)}{dragonMap.get(dragonCode)}个 "
                n += dragonMap.get(dragonCode)
            self.log(s)
            return n
        return 0

    def tryUseDragon(self, maxCount=100):
        """
        开蛋 10104105 绿龙 10104106 红龙 10104107 金龙
        """
        dragonList = [10104105, 10104106, 10104107]
        dragonMap = {}
        res = self.itemList()
        items = res.get("items")
        if items:
            for item in items:
                code = item.get("code")
                if code in dragonList:
                    amount = item.get("amount")
                    dragonMap[code] = amount

        currentCount = 0
        for dragonCode in dragonMap:
            amount = dragonMap.get(dragonCode)
            for _i in range(amount):
                if currentCount > maxCount:
                    break
                currentCount += 1
                self.itemUse(dragonCode, 1)
                self.randomSleep(1, 2)

    def tryRoulette(self):
        # 活动结束
        return
        remain = 1
        dashBoard = self.rouletteDashBoard()
        if dashBoard:
            remain = int(dashBoard.get("remain"))
            gain = dashBoard.get("gain")
            if gain:
                follow = gain.get("follow")
                if follow is False:
                    db = self.rouletteCheckShare()
                    if db:
                        remain = int(db.get("remain"))

            if remain > 0:
                for _i in range(remain):
                    self.rouletteSpin()
                    self.randomSleep(3, 5)
            else:
                next = dashBoard.get("next")
                if next is None:
                    pass

    def gatheringValue(self):
        """获取当前采集量"""
        history = self.profileHistory()
        if history:
            stats = history.get("stats")
            economy = stats.get("economy")
            gathering = economy.get("gathering")
            return float(gathering)

        return 0

    def tryAllianceJoin(self, allianceId):
        """尝试加入联盟"""
        if self.allianceSearch():
            self.randomSleep(2, 5)
            res = self.allianceInfo(allianceId)
            if res:
                maxMembers = res.get("maxMembers")
                numMembers = res.get("numMembers")
                if numMembers < maxMembers:
                    if self.allianceJoin(allianceId):
                        self.allianceInfoMy()
                        return True
        return False

    def tryLeaveAlliance(self):
        if self.allianceId:
            self.allianceLeave()

    def harassmentAttack(self, loc, selfCode=None):
        """攻击骚扰"""
        info = self.info(loc)
        if info:
            numMarch = info.get("numMarch")
            if numMarch == self.marchLimit:
                self.debuglog("队列满了")
                return False

            troops = info.get("troops")
            newTroops = []

            # 第一响应军团
            firstNum = 0
            onlyCavalry = True

            for troop in troops:
                if troop.get("amount") > 0:
                    code = int(troop.get("code"))
                    if onlyCavalry:
                        if code // 100 % 10 == 3:
                            newTroops.insert(0, troop)
                        continue
                    if selfCode and code // 100 == selfCode:
                        firstNum += 1
                        newTroops.insert(0, troop)
                    elif code % 10 > 3 and len(newTroops) > 0:  # 优先T3
                        # if int(troop.get("code")) // 100 % 10 > 1:#优先骑兵弓兵
                        newTroops.insert(firstNum, troop)
                    else:
                        newTroops.append(troop)
            # sum = 0
            marchTroops = []
            for troop in newTroops:
                marchTroop = self.buildMarchTroop(troop["code"], 1)
                marchTroops.append(marchTroop)
                break

            return self.startAttack(
                loc,
                marchTroops,
                f"{random.randint(100000,100000000)}",
            )

    def tryJoinAttackAllianceBattle(self, rallyMoId, troopNum, mainTroop):
        info = self.allianceBattleInfo(rallyMoId)
        if info:
            rallyTroops = info.get("rallyTroops")
            troopsOne = rallyTroops[0]
            troops = troopsOne.get("troops")
            troop = troops[0]
            code = troop.get("code")
            numTroops = info.get("numTroops")
            maxTroops = info.get("maxTroops")
            toLoc = info.get("toLoc")
            # fromLoc = info.get("fromLoc")

            if maxTroops == numTroops:
                self.log("大哥满了")
                return False
            state = info.get("state")
            if state != 4:
                self.log("大哥状态不对")
                return False
            ldleTroop = maxTroops - numTroops - 100000
            selfCode = code // 100
            if troopNum <= 1:
                ldleTroop = 1
                # selfCode = code // 100#int(code / 10)*10 + 3
            elif ldleTroop > troopNum:
                ldleTroop = troopNum

            marchTroops = []
            # if selfCode:
            #     marchTroops.append(
            #         {
            #             "code": selfCode,
            #             "level": 0,
            #             "select": 0,
            #             "amount": troopNum,
            #             "dead": 0,
            #             "wounded": 0,
            #             "seq": 0,
            #         })
            # else:
            maxSize = ldleTroop
            info = self.info(toLoc[1:], rallyMoId=rallyMoId)
            if not info:
                return False

            numMarch = info.get("numMarch")
            if numMarch == self.marchLimit:
                self.debuglog("队列满了")
                return False

            troops = info.get("troops")
            newTroops = []

            # 第一响应军团
            firstNum = 0
            distance = info.get("distance", 9999)
            if distance > 800:
                return False

            for troop in troops:
                if troop.get("amount") > 0:
                    code = int(troop.get("code"))

                    if selfCode and code // 100 == selfCode:
                        firstNum += 1
                        newTroops.insert(0, troop)
                    # elif code % 10 > 3 and len(newTroops) > 0:#优先T3
                    #     # if int(troop.get("code")) // 100 % 10 > 1:#优先骑兵弓兵
                    #         newTroops.insert(firstNum, troop)
                    # else:
                    #     newTroops.append(troop)
            sum = 0
            for troop in newTroops:
                marchTroop = self.buildMarchTroop(troop["code"])
                _troopNum = troop["amount"]
                if sum >= maxSize:
                    marchTroop["amount"] = 0
                elif sum + _troopNum > maxSize:
                    marchTroop["amount"] = maxSize - sum
                    sum = maxSize
                else:
                    marchTroop["amount"] = _troopNum
                    sum += _troopNum
                marchTroops.append(marchTroop)

            if sum > troopNum:
                self.log(f"超过预设的带兵数 {sum} {troopNum} {ldleTroop} {maxSize}")
            if sum == 0:
                self.debuglog("总兵力为0？？？")
                return False
            res = self.rallyJoin(rallyMoId, marchTroops)
            if res and res.get("result"):
                self.log(f"进团{rallyMoId}成功")
                # self.rallyList[rallyMoId] += 1
            elif res is False:
                self.log("进团异常")
                return None

    def joinWarfareAllianceBattle(self, troopNum=1, leagueKingdomName=""):

        battles = self.allianceBattleList()
        if battles:
            for battle in battles:
                isJoined = battle.get("isJoined")
                isRally = battle.get("isRally")
                maxTroops = battle.get("maxTroops")
                targetMonster = battle.get("targetMonster")
                if isRally and not isJoined and not targetMonster:
                    rallyMoId = battle.get("_id")
                    rallyCount = self.rallyList.get(rallyMoId)
                    if rallyCount is not None:
                        if rallyCount >= RallyMoIdStatus.REPEATED:
                            self.log("重复团 跳过")
                            continue
                    else:
                        self.rallyList[rallyMoId] = RallyMoIdStatus.INIT
                    if maxTroops % 1000000 != 0 and maxTroops < 4000000:
                        self.rallyList[rallyMoId] = RallyMoIdStatus.REPEATED
                        self.log(f"弟弟团 上限 {maxTroops} 不能进")
                        continue
                    # self.debuglog(f"未加入的团{rallyMoId}")
                    leaderKingdom = battle.get("leaderKingdom")
                    if leaderKingdom:
                        leaderName = leaderKingdom.get("name")
                        canJoin = False
                        if leagueKingdomName == leaderName:
                            canJoin = True
                        elif leagueKingdomName is None or leagueKingdomName == "不限":
                            canJoin = True
                        targetWorldId = battle.get("targetWorldId")
                        if targetWorldId and targetWorldId == self.realWorld:
                            # 不进打自己大陆的团
                            canJoin = False
                            continue
                        allianceId = battle.get("allianceId")
                        if (
                            not allianceId
                            or not self.allianceId
                            or allianceId != self.allianceId
                        ):
                            canJoin = False
                            self.debuglog("不是自己盟的团")

                        if canJoin:
                            info = self.allianceBattleInfo(rallyMoId)
                            if info:
                                rallyTroops = info.get("rallyTroops")
                                troopsOne = rallyTroops[0]
                                troops = troopsOne.get("troops")
                                troop = troops[0]
                                code = troop.get("code")
                                numTroops = info.get("numTroops")
                                maxTroops = info.get("maxTroops")
                                toLoc = info.get("toLoc")
                                # fromLoc = info.get("fromLoc")

                                if maxTroops == numTroops:
                                    self.log("大哥满了")
                                    continue
                                state = info.get("state")
                                if state != 4:
                                    self.log("大哥状态不对")
                                    continue
                                ldleTroop = maxTroops - numTroops - 100000
                                selfCode = code // 100
                                if troopNum <= 1:
                                    ldleTroop = 1
                                    # selfCode = code // 100#int(code / 10)*10 + 3
                                elif ldleTroop > troopNum:
                                    ldleTroop = troopNum

                                marchTroops = []
                                # if selfCode:
                                #     marchTroops.append(
                                #         {
                                #             "code": selfCode,
                                #             "level": 0,
                                #             "select": 0,
                                #             "amount": troopNum,
                                #             "dead": 0,
                                #             "wounded": 0,
                                #             "seq": 0,
                                #         })
                                # else:
                                maxSize = ldleTroop
                                info = self.info(toLoc[1:], rallyMoId=rallyMoId)
                                if not info:
                                    return False

                                numMarch = info.get("numMarch")
                                if numMarch == self.marchLimit:
                                    self.debuglog("队列满了")
                                    return False

                                troops = info.get("troops")
                                newTroops = []

                                # 第一响应军团
                                firstNum = 0
                                distance = info.get("distance", 9999)
                                # onlyCavalry = False
                                if distance > 800:
                                    continue

                                for troop in troops:
                                    if troop.get("amount") > 0:
                                        code = int(troop.get("code"))

                                        if selfCode and code // 100 == selfCode:
                                            firstNum += 1
                                            newTroops.insert(0, troop)
                                        # elif code % 10 > 3 and len(newTroops) > 0:#优先T3
                                        #     # if int(troop.get("code")) // 100 % 10 > 1:#优先骑兵弓兵
                                        #         newTroops.insert(firstNum, troop)
                                        # else:
                                        #     newTroops.append(troop)
                                sum = 0
                                for troop in newTroops:
                                    marchTroop = self.buildMarchTroop(troop["code"])
                                    _troopNum = troop["amount"]
                                    if sum >= maxSize:
                                        marchTroop["amount"] = 0
                                    elif sum + _troopNum > maxSize:
                                        marchTroop["amount"] = maxSize - sum
                                        sum = maxSize
                                    else:
                                        marchTroop["amount"] = _troopNum
                                        sum += _troopNum
                                    marchTroops.append(marchTroop)

                                if sum > troopNum:
                                    self.log(
                                        f"超过预设的带兵数 {sum} {troopNum} {ldleTroop} {maxSize}"
                                    )
                                if sum == 0:
                                    self.debuglog("总兵力为0？？？")
                                    continue
                                res = self.rallyJoin(rallyMoId, marchTroops)
                                if res and res.get("result"):
                                    self.log(f"进团{leagueKingdomName}成功")
                                    self.rallyList[rallyMoId] += 1
                                elif res is False:
                                    self.log("活力不足")
                                    return None
                        else:
                            self.debuglog(f"非指定人{leagueKingdomName}，跳过")
                            continue
                else:
                    self.debuglog(f"已经加入或无效团,{isJoined},{isRally}")
            return True
        else:
            self.debuglog("没有团斗")
            return False

    def joinAllianceBattle(
        self,
        troopNum=1,
        minLevel=1,
        mustList=None,
        minGroupCount=1200000,
        detailKillList=None,
        warJoin=False,
    ):
        """加入团战"""
        if mustList is None:
            mustList = []

        if not self.allianceId:
            self.log("没有联盟joinAllianceBattle")
            self.placeHolderStr = "没有联盟joinAllianceBattle"
            return False
        battles = self.allianceBattleList()
        if battles:
            for battle in battles:
                isJoined = battle.get("isJoined")
                isRally = battle.get("isRally")
                maxTroops = battle.get("maxTroops")
                fromLoc = battle.get("fromLoc")
                distance = self.distanceWithLoc(fromLoc)
                # distance = battle.get("distance",99999)
                rallyMoId = battle.get("_id")
                if isJoined:
                    kingdomId = battle.get("kingdomId")
                    if kingdomId == self.kingdomId:
                        # self.debuglog(f"自己团{rallyMoId}")
                        if battle.get("targetMonsterId"):
                            # self.debuglog(f"有怪物{rallyMoId}")
                            self.selfRallyList[rallyMoId] = 1
                        continue
                elif isRally and not isJoined:
                    self.debuglog(f"未加入的团{rallyMoId}")
                    rallyCount = self.rallyList.get(rallyMoId)
                    if rallyCount is not None:
                        if rallyCount in [RallyMoIdStatus.ENEMY, RallyMoIdStatus.NEED_MONITOR]:
                            self.debuglog('敌对团待检测')
                            numTroops = battle.get("numTroops")
                            if maxTroops == numTroops:
                                if self.loadBattleInfo(battle):
                                    battle = self.monitoringAllianceBattleOnce(rallyMoId=rallyMoId, battle=battle)
                                    msg = self.buildBattleMessage(battle)
                                    if rallyCount == RallyMoIdStatus.NEED_MONITOR:
                                        self.barkNoti(msg, isAdmin=True)
                                    self.addBotWorkLog(msg)
                                    self.rallyList[rallyMoId] = RallyMoIdStatus.MONITORED
                        if rallyCount > 90:
                            continue
                        if rallyCount >= RallyMoIdStatus.REPEATED:
                            self.debuglog("重复团 跳过")
                            continue
                    else:
                        self.rallyList[rallyMoId] = RallyMoIdStatus.INIT
                    if distance > 700:
                        leaderKingdom = battle.get("leaderKingdom")
                        leaderName = ""
                        if leaderKingdom:
                            leaderName = leaderKingdom.get("name")
                        self.log(f"距离{distance} 跳过 {fromLoc} {leaderName}")
                        self.rallyList[rallyMoId] = RallyMoIdStatus.FAR_DISTANCE
                        continue

                    state = battle.get("state")
                    if state != 4:
                        self.debuglog(f"大哥状态不对{state}")
                        self.rallyList[rallyMoId] = RallyMoIdStatus.NO_STATE
                        continue

                    # leaderKingdom = battle.get("leaderKingdom")
                    # name = leaderKingdom.get("name")
                    targetMonster = battle.get("targetMonster")
                    marchType = battle.get("marchType")
                    if marchType == 5 and not targetMonster:
                        self.rallyList[rallyMoId] = RallyMoIdStatus.INVALID
                        toLoc = battle.get("toLoc")
                        self.addBotWorkLog(f"脱衣被偷了！{toLoc[1]},{toLoc[2]}")
                        continue

                    # 战争跟团
                    if warJoin and not targetMonster:
                        self.joinWarBattle(battle)
                        continue

                    message = battle.get("message")
                    if self.realWorld != 20 and message and len(message) > 0:
                        self.rallyList[rallyMoId] = RallyMoIdStatus.HAS_MESSAGE
                        self.log(f"带消息的团 跳过 : {message}")
                        continue

                    if targetMonster:
                        level = targetMonster.get("level")
                        monsterCode = targetMonster.get("code")
                        monsterCodeStr = str(monsterCode)
                        # 活动怪 停止开团
                        if monsterCode in [Enum.OBJECT_CODE_PANTAGRUEL, Enum.OBJECT_CODE_GARGANTUA]:
                            self.stopOpenRallyTime = time.time()

                        if detailKillList:
                            # 如果有参团表 根据参团表过滤
                            if monsterCodeStr in detailKillList:
                                # 指定怪物
                                codeLevel = detailKillList.get(monsterCodeStr)
                                if codeLevel > level:
                                    # self.log(f"{code} 指定等级{codeLevel} 超过当前等级{level} 跳过")
                                    continue
                            else:
                                # 任意等级 如果没有 跳过
                                anyLevel = detailKillList["anyLevel"]
                                if anyLevel is None:
                                    self.log(f"{monsterCode} 没有指定等级 跳过")
                                    continue
                                if anyLevel > level:
                                    # self.log(f"{code} 指定等级{anyLevel} 超过当前等级{level} 跳过")
                                    continue
                        name = fieldNames.get(monsterCode) or monsterCode
                        if not detailKillList and minLevel > level:
                            if monsterCode not in mustList:
                                # self.log(f"{name} 等级{level} 低于{minLevel} 跳过")
                                continue
                        if level >= 5 and maxTroops < minGroupCount:
                            # self.log("垃圾团跳过")
                            continue
                        info = self.allianceBattleInfo(rallyMoId)
                        if info:
                            rallyTroops = info.get("rallyTroops")
                            troopsOne = rallyTroops[0]
                            troops = troopsOne.get("troops")
                            troop = troops[0]
                            code = troop.get("code")
                            numTroops = info.get("numTroops")
                            maxTroops = info.get("maxTroops")
                            toLoc = info.get("toLoc")

                            if maxTroops == numTroops:
                                self.log("大哥满了")
                                continue
                            state = info.get("state")
                            if state != 4:
                                self.log(f"大哥状态不对{state}")
                                self.rallyList[rallyMoId] = RallyMoIdStatus.NO_STATE
                                continue
                            ldleTroop = maxTroops - numTroops - 100000
                            selfCode = code // 100
                            if troopNum <= 1:
                                ldleTroop = 1
                                # selfCode = code // 100#int(code / 10)*10 + 3
                            elif ldleTroop > troopNum:
                                ldleTroop = troopNum

                            marchTroops = []
                            # if selfCode:
                            #     marchTroops.append(
                            #         {
                            #             "code": selfCode,
                            #             "level": 0,
                            #             "select": 0,
                            #             "amount": troopNum,
                            #             "dead": 0,
                            #             "wounded": 0,
                            #             "seq": 0,
                            #         })
                            # else:
                            maxSize = ldleTroop
                            with self.requestLock:
                                info = self.info(toLoc[1:], rallyMoId=rallyMoId)
                                if not info:
                                    return False

                                numMarch = info.get("numMarch")
                                if numMarch == self.marchLimit:
                                    self.debuglog(f"队列满了 {numMarch} {self.marchLimit}")
                                    return False

                                troops = info.get("troops")
                                newTroops = []

                                # 第一响应军团
                                firstNum = 0
                                onlyCavalry = False
                                if selfCode // 100 == 501003:
                                    onlyCavalry = True
                                if distance > 200 and not onlyCavalry:
                                    # if monsterCode in [
                                    #     Enum.OBJECT_CODE_PANTAGRUEL,
                                    #     Enum.OBJECT_CODE_GARGANTUA,
                                    # ]:
                                    #     if distance > 500:
                                    #         self.debuglog(f"活动怪距离{distance} 跳过")
                                    #         continue
                                    #     self.rallyList[rallyMoId] = 0
                                    # else:
                                    onlyCavalry = True

                                for troop in troops:
                                    if troop.get("amount") > 0:
                                        code = int(troop.get("code"))
                                        if code % 10 == 7:
                                            # 过滤T7兵种
                                            continue
                                        if onlyCavalry:
                                            if code // 100 % 10 == 3:
                                                newTroops.insert(0, troop)
                                            continue
                                        if selfCode and code // 100 == selfCode:
                                            firstNum += 1
                                            newTroops.insert(0, troop)
                                        elif code % 10 > 3 and len(newTroops) > 0:  # 优先T3
                                            # if int(troop.get("code")) // 100 % 10 > 1:#优先骑兵弓兵
                                            newTroops.insert(firstNum, troop)
                                        else:
                                            newTroops.append(troop)
                                sum = 0
                                for troop in newTroops:
                                    marchTroop = self.buildMarchTroop(troop["code"])
                                    _troopNum = troop["amount"]
                                    if _troopNum == 0:
                                        continue
                                    if sum >= maxSize:
                                        marchTroop["amount"] = 0
                                        continue
                                    elif sum + _troopNum > maxSize:
                                        marchTroop["amount"] = maxSize - sum
                                        sum = maxSize
                                    else:
                                        marchTroop["amount"] = _troopNum
                                        sum += _troopNum
                                    marchTroops.append(marchTroop)

                                if len(marchTroops) > 1:
                                    # 如果部队数量大于1，补偿去除其他兵种
                                    troop = marchTroops[0]
                                    troop["amount"] = 1
                                    marchTroops = [troop]

                                if sum > troopNum:
                                    self.log(
                                        f"超过预设的带兵数 {sum} {troopNum} {ldleTroop} {maxSize}"
                                    )
                                if sum == 0:
                                    self.debuglog("总兵力为0？？？")
                                    continue
                                res = self.rallyJoin(rallyMoId, marchTroops, useLock=False)
                                if res and res.get("result"):
                                    self.log(
                                        f"进团{level}级{name}成功 {onlyCavalry and '骑兵团' or ''}"
                                    )
                                    self.rallyList[rallyMoId] += 1
                                elif res is False:
                                    self.log("活力不足")
                                    self.actionPoint = 0
                                    return None
                    else:
                        self.debuglog("非怪物，跳过")
                        self.rallyList[rallyMoId] = RallyMoIdStatus.INVALID
                        continue
                else:
                    self.debuglog(f"已经加入或无效团,{isJoined},{isRally}")
            self.newRally = False
            return True
        else:
            self.debuglog("没有团斗")
            self.newRally = False
            return False

    def joinWarBattle(self, battle, maxSize=210000):
        """战争跟团"""
        # 战争跟团标记时间
        self.lastWarJoinTime = int(time.time())

        rallyMoId = battle.get("_id")
        maxTroops = battle.get("maxTroops")
        fromLoc = battle.get("fromLoc")
        distance = self.distanceWithLoc(fromLoc)
        endTime = battle.get("endTime")
        entT = self.compareDateTimeWithNow(endTime)

        # 不是百万团或者高战团
        if maxTroops % 1000000 != 0 and maxTroops < 5_000_000:
            self.rallyList[rallyMoId] = RallyMoIdStatus.REPEATED
            return

        leaderKingdom = battle.get("leaderKingdom")
        if leaderKingdom:
            leaderName = leaderKingdom.get("name")
            worldId = leaderKingdom.get("worldId")
            if worldId != self.realWorld:
                self.info(f"敌对团1 {leaderName}")
                self.rallyList[rallyMoId] = RallyMoIdStatus.ENEMY
                if self.checkSelfAttacked(battle):
                    self.rallyList[rallyMoId] = RallyMoIdStatus.NEED_MONITOR
                return
            targetWorldId = battle.get("targetWorldId")
            if targetWorldId and targetWorldId == self.realWorld:
                # 不进打自己大陆的团
                self.info("敌对团2")
                self.rallyList[rallyMoId] = RallyMoIdStatus.ENEMY
                if self.checkSelfAttacked(battle):
                    self.rallyList[rallyMoId] = RallyMoIdStatus.NEED_MONITOR
                return

            allianceId = battle.get("allianceId")
            if not allianceId or not self.allianceId or allianceId != self.allianceId:
                self.debuglog(f"不是自己盟的团 {leaderName}")
                self.rallyList[rallyMoId] = RallyMoIdStatus.INVALID
                return

            message = battle.get("message","")
            pattern = r'[Tt]7'
            if re.findall(pattern, message.lower()):
                needCancel = True
                t7Rate = 1
                # 检测T7含量
                info = self.allianceBattleInfo(rallyMoId)
                if info:
                    rallyTroops = info.get("rallyTroops")
                    troopsOne = rallyTroops[0]
                    troops = troopsOne.get("troops")
                    troopSum = 0
                    t7Sum = 0
                    for troop in troops:
                        amount = troop.get("amount",0)
                        if troop.get("code") % 10 == 7:
                            t7Sum += amount
                        troopSum += amount
                    if troopSum > 0:
                        t7Rate = round(t7Sum / troopSum, 2)
                    if t7Rate < 0.3:
                        needCancel = False
                        self.info(f"T7团 车头纯度低于30% {t7Rate}%")

                if needCancel:
                    self.rallyList[rallyMoId] = RallyMoIdStatus.T7_TEAM
                    self.info(f"T7团 车头纯度: {t7Rate}% {message}")
                    return

            info = self.allianceBattleInfo(rallyMoId)
            if info:
                rallyTroops = info.get("rallyTroops")
                troopsOne = rallyTroops[0]
                troops = troopsOne.get("troops")
                isSlow = len(list(filter(lambda x: x["code"] // 100 % 10 == 1, troops)))
                needUp = False
                if isSlow > 0:
                    if entT - 10 < distance / 2:
                        if entT - 10 < distance / 4:
                            self.rallyList[rallyMoId] = RallyMoIdStatus.NO_TIME
                            self.log("含步兵团 时间不够")
                            return
                        else:
                            self.log("含步兵团 需要加速")
                            needUp = True
                else:
                    if entT - 10 < distance / 3:
                        if entT - 10 < distance / 6:
                            self.rallyList[rallyMoId] = RallyMoIdStatus.NO_TIME
                            self.log("不含步兵团 时间不够")
                            return
                        else:
                            self.log("不含步兵团 需要加速")
                            needUp = True


                lowLevel = 6
                if self.realWorld != 20:
                    lowLevel = 5
                lowTroops = [x["code"] for x in troops if x["code"] % 10 < lowLevel]
                if len(lowTroops) > 0:
                    self.info(f"含有低级兵 不跟 {lowTroops}")
                    self.rallyList[rallyMoId] = RallyMoIdStatus.INVALID
                    return

                numTroops = info.get("numTroops")
                maxTroops = info.get("maxTroops")
                toLoc = info.get("toLoc")
                ldleTroop = maxTroops - numTroops

                if maxTroops == numTroops:
                    self.log("大哥满了")
                    self.rallyList[rallyMoId] = RallyMoIdStatus.INVALID
                    return

                state = info.get("state")

                if state != 4:
                    self.log("大哥状态不对")
                    return

                if ldleTroop < maxSize:
                    self.log(f"大哥满了,不上了 {ldleTroop} {maxTroops} {numTroops}")
                    return

                highMaxSize = 0
                if maxTroops < 6_000_000:
                    tmpSize = maxTroops // 1_000_000 * 70000 * 1.2
                    if tmpSize <= ldleTroop:
                        highMaxSize = min(int(tmpSize), 50_0000)
                    # 如果战争跟团数量大于0 并且小于等于剩余兵力 则上高兵
                    warJoinCount = self.warJoinCount
                    if warJoinCount > 0 and warJoinCount <= ldleTroop:
                        highMaxSize = warJoinCount
                    self.debuglog(f"战争跟团数量{warJoinCount} 剩余空间{ldleTroop} 高兵{highMaxSize}")

                if maxTroops > 6_000_000:
                    # 最大为50w 或者 2倍
                    highMaxSize = min(ldleTroop, maxSize * 2)
                    highMaxSize = min(highMaxSize, 50_0000)

                # 取最小能上的数量
                # maxSize = min(ldleTroop, maxSize)

                marchTroops = []
                info = self.info(toLoc[1:], rallyMoId=rallyMoId)
                if info is None:
                    self.debuglog("joinWarBattle info None")
                    return
                selfTroops = info.get("troops")
                mixType = True
                # 2 表示需要上高兵 1 表示需要上低兵 0 兵力不够
                amountType = 1

                if len(troops) == 1:
                    # 单兵种团 上20w
                    mixType = False
                    troop = troops[0]
                    code = troop.get("code")
                    selfCode = code // 100
                    # T7降T6
                    if selfCode % 10 == 7:
                        selfCode -= 1
                    for selfTroop in selfTroops:
                        selfAmount = selfTroop.get("amount")
                        if selfAmount > 0:
                            code = int(selfTroop.get("code"))

                            if selfCode and code // 100 == selfCode and code % 10 == 6:
                                if selfAmount >= maxSize:
                                    if highMaxSize > 0 and selfAmount > highMaxSize:
                                        selfTroop["amount"] = highMaxSize
                                    else:
                                        amountType = 1
                                        selfTroop["amount"] = maxSize
                                    marchTroops.insert(0, selfTroop)
                                else:
                                    amountType = 0
                                    self.log(f"自己兵力不足 纯兵 {code} {selfAmount} / {maxSize}")
                                    self.rallyList[rallyMoId] = RallyMoIdStatus.NO_TROOP
                                break
                else:
                    # 混兵团 上20w
                    sumAmount = 0
                    codeMap = {}
                    for troop in troops:
                        code = troop.get("code")
                        amount = troop.get("amount")
                        if code % 10 == 7:
                            code -= 1
                        troopType = code // 100 % 10
                        if codeMap.get(troopType) is None:
                            codeMap[troopType] = 0
                        codeMap[troopType] += amount
                        sumAmount += amount

                    unAmountString = ""
                    # 如果有高兵上限,先尝试上高兵
                    if highMaxSize > 0:
                        amountType = 2
                    # 遍历每种兵种
                    for troopType in codeMap:
                        # 获取该兵种数量
                        value = codeMap[troopType]
                        # 计算该兵种在总兵力中的占比
                        rate = value / sumAmount
                        # 过滤出自己拥有的该兵种 不上T7
                        filterTroops = list(filter(lambda x: x["code"] // 100 % 10 == troopType and x["code"] % 10 == 6, selfTroops))
                        if len(filterTroops) > 0:
                            # 获取自己该兵种的数量
                            amount = filterTroops[0]["amount"]
                            # 如果是高兵模式
                            if amountType == 2:
                                # 根据占比计算需要的高兵数量(向上取整到100)
                                needAmount = int((rate * highMaxSize // 100 + 1) * 100)
                                # 如果自己兵力不足高兵数量,降级到普通兵力
                                if needAmount > amount:
                                    amountType = 1
                            # 如果是普通兵力模式
                            if amountType == 1:
                                # 根据占比计算需要的普通兵力数量(向上取整到100)
                                needAmount = int(rate * maxSize // 100 + 1) * 100
                                # 如果自己兵力不足普通数量,标记为兵力不足
                                if needAmount > amount:
                                    amountType = 0
                                    unAmountString = f"{troopType} {amount}/ {needAmount}"
                                    break
                        else:
                            # 如果没有该兵种,标记为兵力不足
                            amountType = 0
                            unAmountString = f"{troopType}"
                            break

                    # 如果兵力不足,跳过
                    if amountType == 0:
                        self.log(f"自己兵力不足 混兵 {unAmountString}")
                        self.rallyList[rallyMoId] = RallyMoIdStatus.NO_TROOP
                        return

                    # 遍历每种兵种,位置
                    keyLen = len(codeMap)
                    totalAmount = 0
                    buildMaxSize = amountType == 2 and highMaxSize or maxSize
                    for i, (troopType, amount) in enumerate(codeMap.items()):
                        code = (501000 + troopType) * 100 + 6
                        if i == keyLen - 1:
                            needAmount = buildMaxSize - totalAmount
                            marchTroops.append(self.buildMarchTroop(code, needAmount))
                            totalAmount += needAmount
                            continue
                        else:
                            # 计算该兵种在总兵力中的占比
                            rate = amount / sumAmount
                            needAmount = int(rate * buildMaxSize // 100 + 1) * 100
                            marchTroops.append(self.buildMarchTroop(code, needAmount))
                            totalAmount += needAmount

                if marchTroops:
                    marchTroops = self.reSortMarchTroops(marchTroops)
                    res = self.rallyJoin(rallyMoId, marchTroops)
                    if res and res.get("result"):
                        newTask = res.get("newTask")
                        taskTime = round(newTask.get("time", 0), 2)
                        buildMaxSize = amountType == 2 and highMaxSize or maxSize
                        rallyMoIdSub = rallyMoId[:6]
                        self.addBotWorkLog(f"战争跟团进团成功 {leaderName}[{rallyMoIdSub}] {mixType and '混兵' or '纯兵'} {amountType == 2 and '高兵' or '低兵'} {buildMaxSize} 时间:{taskTime}")
                        self.rallyList[rallyMoId] += 1
                        if needUp:
                            param = newTask.get("param")
                            moId = param.get("moId")
                            rallyMoId = param.get("rallyMoId")
                            if entT - 10 < taskTime:
                                self.randomSleep(
                                    1, 2, msg = f"战争跟团 加速团 {moId} 团战ID{rallyMoId}"
                                )
                                if not self.fieldMarchBoost(moId):
                                    self.log(f"战争跟团加速异常 {moId}")
                            else:
                                self.log(f"战争跟团 加速团 {moId} 团战ID{rallyMoId} 不需要加速")

                    elif res and res.get("err",{}).get("code") == "not_open_gate":
                        self.log(f"战争跟团进团失败 {leaderName} 没有门权")
                        self.rallyList[rallyMoId] = RallyMoIdStatus.NO_GATE
                        return None
                    elif res is False:
                        self.log("战争跟团异常？")
                        return None

    def cacheAllianceBattleListWithTasks(self):
        """通过自身队列缓存团战信息"""
        if not self.allianceId:
            return False
        self.enterKingdomAsyncTasks()
        rallyMoIds = []
        for task in self.fieldTasks:
            param = task.get("param")
            if param:
                rallyMoId = param.get("rallyMoId")
                if rallyMoId:
                    rallyMoIds.append(rallyMoId)

        battles = redisHelper.getAllianceBattleList(self.allianceId)

        if battles:
            for battle in battles:
                rallyMoId = battle.get("_id")
                if rallyMoId and rallyMoId in rallyMoIds:
                    rallyMoIds.remove(rallyMoId)

        if len(rallyMoIds) > 0:
            for rallyMoId in rallyMoIds:
                battle = self.allianceBattleInfo(rallyMoId)
                if self.loadBattleInfo(battle):
                    battles.append(battle)

            redisHelper.saveAllianceBattleList(self.allianceId, battles)
            return True
        else:
            return False

    def cacheAllianceBattleList(self):
        """缓存团战信息"""
        if not self.allianceId:
            return False
        battles = self.avaiableAllianceBattleList()
        if battles:
            redisHelper.saveAllianceBattleList(self.allianceId, battles)

    def cacheGetAllianceBattleList(self):
        """获取缓存的团战信息"""
        if not self.allianceId:
            return []
        battles = redisHelper.getAllianceBattleList(self.allianceId)
        if battles:
            for battle in battles:
                fromAlliance = battle.get("fromAlliance")
                allianceId = fromAlliance.get("_id")
                battle["self"] = allianceId == self.allianceId
                endTime = battle.get("endTime")
                t = self.compareDateTimeWithNow(endTime)
                battle["overTime"] = int(t)

        return battles

    def loginAndTeleportAndUseDragon(self, loc):
        if self.login():
            self.wsWithKingdomApp()
            self.randomSleep(3, 5)
            self.teleport(loc)
            self.randomSleep(3, 5)
            self.tryUseDragon()
        else:
            self.errorLog("弟弟登录失败了")

    def chargeEnergy(self, maxEnergy=90):
        """充能"""
        if self.isInvalid:
            return 0
        energys = [10101049, 10101050, 10101051, 10101052]
        energyNums = [10, 20, 50, 100]
        energys.reverse()
        energyNums.reverse()
        itemList = self.itemList()
        if itemList is None:
            self.randomSleep(10, 20, msg="充能-物品列表异常")
            return self.chargeEnergy(maxEnergy=maxEnergy)
        currentEnergy = 0

        stockEnergy = 0
        for code in energys:
            num = self.itemCount(code)
            energy = energyNums[energys.index(code)]
            stockEnergy += energy * num
            if num > 0 and currentEnergy < maxEnergy:
                maxNum = math.ceil((maxEnergy - currentEnergy) / energy)
                if num >= maxNum:
                    self.itemUse(code, maxNum)
                    currentEnergy += energy * maxNum
                    num -= maxNum
                else:
                    self.itemUse(code, num)
                    currentEnergy += energy * num
                    num = 0

                self.randomSleep(1)
        self.log(f"本次充能 {currentEnergy} 库存{stockEnergy - currentEnergy}")
        self.actionPoint += currentEnergy

        return currentEnergy

    def tryRebuildSomeOne(self):
        """重建一个建筑"""
        if len(self.buildings) > 0:
            index = None
            miniLevel = 99
            for building in self.buildings:
                position = building.get("position")
                level = building.get("level")
                if position < 109:
                    continue
                if level > miniLevel:
                    continue

                index = position
                miniLevel = level

            if index is not None:
                self.log(f"重建{index},当前等级{level}")
                self.demolish(index)
                self.randomSleep(1, 2)
                self.build(index, 40100205)
                return True
        else:
            self.log("没有建筑")
            return False

    def tryFragmentsExchange(self):
        res = self.eventList()
        if res and res.get("events"):
            events = res.get("events")
            for event in events:
                mastCode = event.get("mastCode")
                if mastCode == 601031:
                    eventId = event.get("_id")
                    eventInfo = self.eventInfo(eventId)
                    if eventInfo:
                        event = eventInfo.get("event")
                        eventId = event.get("_id")
                        codes = [60103115]  # ,60103115,60103116]
                        events1 = event.get("events")
                        for event1 in events1:
                            code = event1.get("code")
                            if code in codes:
                                self.bugCount = 0
                                eventTargetId = event1.get("_id")
                                tasks = [
                                    self.eventClaimA(eventId, eventTargetId, code)
                                    for _ in range(100)
                                ]
                                self.runTasks(tasks)
                                self.log(f"卡碎片成功{self.bugCount}次")
                                return

    def gobackMultiple(self, timeout=60):
        """并发撤回"""
        if self.troopFields is None:
            self.errorLog("没有在野数据")
            return
        for field in self.troopFields:
            self.kingdomId = field.get("kingdomId")
            moId = field.get("_id")
            marchType = field.get("marchType")
            state = field.get("state")

            if marchType == 1 and state == 2:
                endTime = field.get("endTime")
                t = self.compareDateTimeWithNow(endTime)
                if t < timeout:
                    if self.gobackList.get(moId) is None:
                        self.gobackList[moId] = True
                    else:
                        continue
                    self.bugCount = 0
                    tasks = [self.fieldMarchReturnA(moId) for _ in range(30)]
                    self.runTasks(tasks)
                    self.randomSleep(3, 5, msg=f"撤回{moId}成功 {self.bugCount}次")
            else:
                continue

    def canGobackList(self, timeout=600):
        """可以回撤的列表"""
        self.enterKingdomAsyncTasks()
        canGoBackList = []
        canGoBackMap = {}
        for task in self.fieldTasks:
            # moId = field.get("_id")
            # marchType = field.get("marchType")
            status = task.get("status")
            if status == 1:
                param = task.get("param")
                gatherType = param.get("gatherType")
                moId = param.get("moId")
                amount = param.get("amount")
                maxT = task.get("time")
                foLoc = param.get("foLoc")

                if gatherType and gatherType == 4 and moId:
                    endTime = task.get("expectedEnded")
                    t = self.timestampWithdatetime(endTime) - time.time() + 8 * 60 * 60
                    v = {"t": int(t), "moId": moId, "amount": amount, "maxT": maxT}
                    if foLoc and self.loc:
                        d = self.distanceWithLoc(foLoc, self.loc)
                        v["d"] = d
                    canGoBackMap[t] = v
                    # canGoBackList.append({"t":int(t),"moId":moId,"amount":amount,"maxT":maxT})
        if canGoBackMap:
            keys = list(canGoBackMap.keys())
            keys.sort()
            for key in keys:
                canGoBackList.append(canGoBackMap[key])

        return canGoBackList

    def gobackMoId(self, moId, count=30, isSuper=False):
        """回撤单个"""
        if self.gobackList.get(moId) is not None:
            return -1

        if time.time() - self.lastGobackTime < 20 and not isSuper:
            return -1

        self.log(f"准备撤回{moId}")
        self.bugCount = 0
        tasks = [self.shrineSupportReturnA(moId) for _ in range(30)]
        self.runTasks(tasks)
        if self.bugCount > 0:
            self.lastGobackTime = time.time()
            self.gobackList[moId] = True
        self.randomSleep(0, msg=f"web成功撤回{moId}成功 {self.bugCount}次")
        return self.bugCount

    def gobackOnlyOne(
        self,
        timeout=70,
        domain=False,
        needRefresh=True,
        gobackCallBack=None,
        maxValue=22000,
        hourLimit=True,
    ):
        """并发撤回 最近一个"""
        if needRefresh:
            self.taskAll(useLock=False)
        if self.fieldTasks is None:
            self.debuglog("没有在野数据")
            return

        if gobackCallBack is None:
            gobackCallBack = requestSuperGo

        lastId = None
        lastValue = 0
        lastTime = 999999999999
        lastDistance = 999999999999
        togetherCount = 0
        crystalTimeList = {}
        needRemoves = []
        gobackCount = 25
        for task in self.fieldTasks:
            # moId = field.get("_id")
            # marchType = field.get("marchType")
            status = task.get("status")
            endTime = task.get("expectedEnded")
            t = self.timestampWithdatetime(endTime)
            if t - time.time() + 8 * 60 * 60 < 0:
                needRemoves.append(task)
                continue
            if status == 1:
                param = task.get("param")
                gatherType = param.get("gatherType")
                moId = param.get("moId")

                if self.gobackList.get(moId) is not None:
                    needRemoves.append(task)
                    continue

                if gatherType is not None and gatherType <= 4 and moId:
                    crystalTimeList[t] = task
        if len(needRemoves) > 0:
            self.debuglog(f"移除撤回队列{needRemoves}")
            [self.fieldTasks.remove(task) for task in needRemoves]

        if len(self.fieldTasks) > 10:
            self.log("已经在多倍中,等待主动刷新")
            return -1

        keys = list(crystalTimeList.keys())
        keys.sort()
        for key in keys:
            t = key
            task = crystalTimeList.get(key)
            if abs(t - lastTime) < 60 * 5:
                togetherCount += 1
            # key = f'group{t // (60 * 5)}'
            # 计算5分钟内的有效队列
            # if timeMap.get(key) is None:
            #     timeMap[key] = 1
            # else:
            #     timeMap[key] += 1
            param = task.get("param")
            moId = param.get("moId")
            gatherType = param.get("gatherType")
            amount = param.get("amount")
            if t < lastTime:
                if self.gobackList.get(moId) is None:
                    lastTime = t
                    lastId = moId
                    foLoc = param.get("foLoc")
                    lastDistance = self.distanceWithLoc(foLoc)
                    togetherCount = 1
                    # 限制多倍挖矿一天额度
                    if gatherType == 4:
                        lastValue = amount
                    else:
                        lastValue = 0

        if lastId:
            t = lastTime - time.time() + 8 * 60 * 60
            if domain:
                # key = f'group{lastTime // (60 * 5)}'
                # count = timeMap.get(key)
                count = togetherCount
                if count > 1:
                    callBackTime = lastDistance / 4
                    if callBackTime > 180:
                        timeout = callBackTime + 20
                    else:
                        if callBackTime < 30:
                            callBackTime = 30
                        timeout = (count + 1) * callBackTime + 20
                    if timeout > 300:
                        timeout = 300
                else:
                    # timeout = (count + 1) * 40 + 10
                    timeout = 50
                self.debuglog(
                    f"撤回动态计算当前撤回时间{timeout},同区间内有{count}个任务"
                )
                # if timeout > newTime:
                #     timeout = newTime
            if t < timeout:
                if t < 0:
                    self.debuglog(f"撤回时间小于0,{t}")
                    self.gobackList[lastId] = True
                    for task in self.fieldTasks:
                        status = task.get("status")
                        if status == 1:
                            param = task.get("param")
                            gatherType = param.get("gatherType")
                            moId = param.get("moId")
                            if moId == lastId:
                                self.fieldTasks.remove(task)
                                break

                    return
                # 检测今日挖矿额度
                if lastValue:
                    v = self.getCurrentHourGoBackValue()
                    if hourLimit and v > 1700:
                        self.log(f"当前小时多倍已达上限:{v}")
                        self.gobackList[lastId] = True
                        return
                    v = self.getCurrentGoBackValue()
                    availableValue = maxValue - v
                    if availableValue - lastValue * 20 < 0:
                        maxGoBackCount = availableValue // lastValue
                        if maxGoBackCount > 2:
                            gobackCount = maxGoBackCount
                        else:
                            self.log(f"今日多倍已达上限:{v}")
                            self.gobackList[lastId] = True
                            return

                self.log(f"满足撤回条件 准备撤回{lastId} 撤回次数{gobackCount}")
                bugCount = gobackCallBack(
                    self.token, self.kingdomId, lastId, count=gobackCount
                )
                if bugCount is None or bugCount < 0:
                    self.log("洗脚城撤回失败?")
                else:
                    self.log(f"洗脚城成功撤回{lastId}成功 {bugCount}次")
                    if lastValue and bugCount:
                        self.increaseCurrentGoBackValue(lastValue * bugCount * 0.95)
                self.gobackList[lastId] = True

            else:
                self.debuglog(
                    f"最近队列{lastId} 剩余{int(t)}秒 不满足{timeout}秒撤回条件"
                )
                self.lastMoTime = int(t)

        else:
            self.debuglog("没有可撤回的")
        return

    @property
    def taskStr(self):
        """任务详情"""
        crystalNum = 0
        sum = 0
        for task in self.fieldTasks:
            sum += 1
            param = task.get("param")
            gatherType = param.get("gatherType")
            if gatherType == 4:
                crystalNum += 1

        return f"总队列:{sum} 水晶队列{crystalNum}"

    def startGarrisonWithMainTroopAndNum(self, toLoc, mainTroop, num):
        """指定兵种驻防"""
        marchTroops = []
        if mainTroop < 4:
            marchTroops.append(self.buildMarchTroop(int(f"50100{mainTroop}05"), num))
        else:
            midNum = num // 3
            marchTroops.append(self.buildMarchTroop(int("50100105"), midNum))
            marchTroops.append(self.buildMarchTroop(int("50100205"), midNum))
            marchTroops.append(self.buildMarchTroop(int("50100305"), num - 2 * midNum))
        res = self.startGarrison(toLoc, marchTroops=marchTroops)
        if res:
            self.debuglog("驻防成功")
            return True
        else:
            return False

    def startRally(self, loc, mainTroop, uTroopNum, message="", marchType=5) -> StartTaskEnum:
        if len(self.attackedList) > 50:
            self.attackedList.pop(0)

        if loc in self.attackedList:
            return StartTaskEnum.none

        info = self.info(loc)
        if info is None:
            return StartTaskEnum.fail
        level = info.get("fo").get("level")
        code = info.get("fo").get("code")
        name = fieldNames.get(code) or code
        # if mainTroop == 3:
        #     message = "骑兵团！骑兵团！注意距离!"

        numMarch = info.get("numMarch")
        if numMarch == self.marchLimit:
            self.debuglog("开团队列满了")
            return StartTaskEnum.full

        troops = info.get("troops")
        newTroops = []
        marchTroops = []
        # 第一响应军团
        firstNum = 0
        for troop in troops:
            if troop.get("amount") > 0:
                code = int(troop.get("code"))
                if int(code) // 100 % 10 == mainTroop:
                    newTroops.insert(0, troop)
                    firstNum += 1
                else:
                    newTroops.append(troop)

        maxSize = min(uTroopNum, self.marchSize)
        if self.level >= 30 and uTroopNum > self.marchSize:
            maxSize = uTroopNum
        sum = 0

        for troop in newTroops:
            marchTroop = self.buildMarchTroop(troop["code"])
            troopNum = troop["amount"]
            if sum >= maxSize:
                marchTroop["amount"] = 0
            if sum + troopNum > maxSize:
                marchTroop["amount"] = maxSize - sum
                sum = maxSize
            else:
                marchTroop["amount"] = troopNum
                sum += troopNum

            marchTroops.append(marchTroop)

        if sum < maxSize or sum == 0:
            self.debuglog(f"攻击{level}级{name}失败 带兵不足")
            return StartTaskEnum.limit

        res = self.rallyStart(marchType, loc, marchTroops, message=message)
        if res and res.get("result"):
            self.attackCount += 1
            self.actionPoint -= 30
            self.attackedList.append(loc)
            self.log(f"开团{level}级{name}成功 距离{self.distanceWithLoc(loc)} {loc[1]},{loc[2]}")
            return StartTaskEnum.success
        elif res:
            err = res.get("err")
            if err and err.get("code"):
                code = err.get("code")
                if code == "insufficient_actionpoint":
                    self.log("活力点不足动力不足")
                    self.actionPoint = 0
                    return StartTaskEnum.limit
                elif code == "same_target_rally":
                    self.log("重复开团")
                    self.attackedList.append(loc)
                    self.debuglog(f'重复开团 {self.attackedList}')
                    return StartTaskEnum.limit
                elif code == "full_task":
                    self.log("队列满了")
                    return StartTaskEnum.full
                self.log(f"开团攻击{level}级怪兽失败 :{err.get('code')}")
            else:
                self.log(f"开团攻击{level}级怪兽失败 :{res}")

        return StartTaskEnum.fail

    def sendChat(self, info):
        """分享聊天"""
        sendStr = ""
        code = info.get("code")
        loc = info.get("loc")
        level = info.get("level")
        param = info.get("param")
        charmCode = param.get("charmCode")
        if charmCode:
            readCharmCode = int(charmCode / 10)
            name = charmCodes[readCharmCode]
            levelName = level == 3 and "史诗" or "传说"
            sendStr = f"Lv.{level}{name}（{levelName}）"
        else:
            name = fieldNames.get(code)
            sendStr = f"Lv.{level}{name}Lv.{level}"

        self.chatNew(loc, sendStr)
        return True

    def importantCaravanList(self, needResourceItem=False, resourceAll=False):
        """重要的商店列表"""
        cheapList = []

        items = self.caravanList()
        if items:
            for i in range(len(items)):
                info = {"index": i}

                item = items[i]
                costItemCode = item.get("costItemCode")
                amount = item.get("amount")
                if amount == 0:
                    continue

                code = item.get("code")
                cost = item.get("cost")
                saleRate = item.get("saleRate")
                isCrystalItem = costItemCode == 10100005
                if not isCrystalItem and not needResourceItem:
                    continue
                # if isCrystalItem and saleRate < 50:
                #     continue
                itemName = None
                try:
                    itemName = shopItemNames.get(code)
                except Exception:
                    pass

                if itemName is None:
                    if code // 10000 == 1060:
                        if saleRate < 90 and not needResourceItem:
                            continue
                        ns = ["普通碎片", "魔法碎片", "史诗碎片", "传说碎片"]
                        try:
                            newCode = int(f"1050{code % 10000}")
                            itemName = epicFragments.get(newCode)
                        except Exception:
                            pass
                        finally:
                            if itemName is None:
                                itemName = ns[code % 10000 // 1000 - 1]
                    else:
                        if not isCrystalItem and resourceAll:
                            itemName = "杂物"
                        else:
                            itemName = "未知"
                            continue

                itemTitle = f"{itemName},{saleRate}%"
                info["itemTitle"] = itemTitle
                info["isCrystalItem"] = isCrystalItem
                info["cost"] = cost
                info["saleRate"] = saleRate
                info["caravanItemId"] = item.get("_id")
                info["code"] = code
                serverTmpMap[info["caravanItemId"]] = itemTitle
                cheapList.append(info)

        return cheapList

    def tryBuyNormalShop(self, caravanItemId, count=15):
        """尝试购买普通商品"""
        name = ""
        try:
            name = serverTmpMap.get(caravanItemId)
        except Exception:
            pass

        self.log(f"卡海盗船 {count}次 {name}")
        self.bugCount = 0
        # from httpx_socks import AsyncProxyTransport

        # from Unit.FileTool import loadS5List

        # s5List = loadS5List()
        # s5 = None
        tasks = []
        for _i in range(count):
            # if i % 10 == 0:
            #     s5 = secrets.choice(s5List)
            transport = None  # AsyncProxyTransport.from_url(f'socks5://{s5}')
            tasks.append(self.caravanBuyA(caravanItemId, transport=transport))

        self.runTasks(tasks)
        self.log(f"卡海盗船 成功{self.bugCount}次 {name}")
        return self.bugCount

    def tryBuyVipShopWeb(self, code, amount, count=15, isSuper=False):
        """尝试购买vip商品"""
        if time.time() - self.lastVipBuyTime < 30 and not isSuper:
            return -1
        self.lastVipBuyTime = time.time()

        def run():
            asyncio.set_event_loop(asyncio.new_event_loop())
            self.log(f"购买vip商城{count}次")
            self.bugCount = 0
            tasks = [self.vipshopBuyA(code, amount, dl=i) for i in range(count)]
            self.runTasks(tasks)
            self.log(f"购买vip商城成功{self.bugCount}次")

        threading.Thread(target=run, daemon=True).start()
        return 1

    def unUseSkillList(self, isSuper=False):
        """未使用的技能列表"""
        skillList = self.skillList()
        if skillList is None:
            skillList = []
        unUseSkillList = []
        for skill in skillList:
            nextSkillTime = skill.get("nextSkillTime")
            endTime = skill.get("endTime")
            t = -1
            if nextSkillTime:
                t = self.compareDateTimeWithNow(nextSkillTime)
            if t < 0:
                if endTime:
                    if self.compareDateTimeWithNow(endTime) > 0:
                        continue
                code = skill.get("code")
                level = skill.get("level", 1)
                codeName = skillNames.get(code)
                if codeName is None:
                    continue
                if not isSuper and code == 10001:
                    continue
                unUseSkillList.append(
                    {"code": code, "codeName": f"{codeName} Lv{level}"}
                )
        return unUseSkillList

    def tryUseSkill(self, code, count=15, isSuper=False, targetId=None):
        """尝试使用技能"""
        if code is None:
            return 0
        # if not isSuper:
        #     if code == 10023:
        #         code = 5
        self.bugCount = 0
        tasks = [self.skillUseA(code, targetId=targetId) for i in range(count)]
        self.runTasks(tasks)
        self.log(f"{skillNames.get(int(code))}使用 {count}次 使用成功{self.bugCount}次")
        return self.bugCount

    def tryUseCollectionSkill(self):
        """尝试使用收集技能"""
        if time.time() - self.skillUseLastTime < 600:
            return
        skills = self.unUseSkillList(isSuper=True)
        if skills:
            self.skillUseLastTime = time.time()
        canUp = False
        canCollection = False
        canSpeedUp = False
        canMoveSpeedUp = False
        for skill in skills:
            code = skill["code"]
            if code == 10001:
                canCollection = True
            elif code == 10018:
                canUp = True
            elif code == 10002:
                canSpeedUp = True
            elif code == 10007:
                canMoveSpeedUp = True
            elif code == 10023:
                self.canSummon = True

        upCount = 0
        upMaxCount = 0
        if canUp:
            upMaxCount = 1  # random.randint(3,5)
            upCount = self.tryUseSkill(10018, upMaxCount)
            self.randomSleep(3, 5)
        if canMoveSpeedUp:
            self.tryUseSkill(10007, 1)
            self.randomSleep(3, 5)

        if canCollection:
            sumResources = self.sumResources
            collectionMaxCount = 1  # random.randint(13,15)
            collectionCount = self.tryUseSkill(10001, collectionMaxCount)
            newSumResources = self.sumResources
            upSum = round((newSumResources - sumResources) / 1000.0 / 1000.0, 2)
            self.log(
                f"使用增益成功{upCount}/{upMaxCount}次，收集{collectionCount}/{collectionMaxCount}次, 收取{upSum}M"
            )

        if canSpeedUp:
            self.randomSleep(2, 3)
            speedCount = self.tryUseSkill(10002, 1)
            self.log(f"使用增益成功收集加速{speedCount}次")

    def canUpgradeTreasureList(self, isSuper=False):
        """可升级的宝物列表"""
        treasureList = self.treasureList()
        canUpgradeTreasureList = []
        treasures = treasureList.get("treasures")
        for treasure in treasures:
            code = treasure.get("code")
            piece = treasure.get("piece")
            level = treasure.get("level")
            levelCode = code // 1000 % 10
            if level != levelCode + 1:
                if piece >= 40 or isSuper:
                    newCode = int(f"1050{code % 10000}")
                    name = epicFragments.get(newCode)
                    if name:
                        treasure["name"] = name
                        canUpgradeTreasureList.append(treasure)
            else:
                if code in [10504013, 10504014, 10504015]:
                    newCode = int(f"1050{code % 10000}")
                    name = epicFragments.get(newCode)
                    if name:
                        treasure["name"] = name
                        canUpgradeTreasureList.append(treasure)

        return canUpgradeTreasureList

    def tryUpgradeTreasure(self, treasureId, index, count=15):
        """尝试升级宝物"""
        if treasureId is None or index is None:
            return 0

        self.bugCount = 0
        tasks = [self.treasureUpgradeA(treasureId, index) for _ in range(count)]
        self.runTasks(tasks)
        self.log(f"{treasureId}使用成功{self.bugCount}次")
        return self.bugCount

    def tryAutoBuyCaravan(self, resourceAll=False):
        """尝试自动购买海盗船"""
        t = time.time()
        if t - self.caravaTime < 60 * 60:
            return

        caravanList = self.importantCaravanList(needResourceItem=True)
        if caravanList:
            self.log(f"扫描商店列表 有价值商品数:{len(caravanList)}")
            self.caravaTime = t
            for caravan in caravanList:
                code = caravan.get("code")
                caravanItemId = caravan.get("caravanItemId")
                # saleRate = caravan.get("saleRate")
                itemTitle = caravan.get("itemTitle")
                isCrystalItem = caravan.get("isCrystalItem")

                if itemTitle.find("VIP") >= 0:
                    # vip17不买VIP经验
                    if self.vip == 17 or self.isBug:
                        continue

                # 不买碎片
                if code // 10000 == 1060 and not resourceAll:
                    continue

                # 砖石商品但是不是目标商品
                if isCrystalItem and code not in crystalItems:
                    continue

                # if code <=10103004 and code >=10102001:
                #     continue
                if self.caravanBuy(caravanItemId):
                    text = isCrystalItem and "砖石" or "资源"
                    self.log(f"{itemTitle}购买成功 {text}")
                self.randomSleep(3, 5)

    def tryClaimVipWithCount(self, count=30):
        """尝试领取vip"""
        self.bugCount = 0
        tasks = [self.vipClaimA() for _ in range(count)]
        self.runTasks(tasks)
        self.log(f"领取vip成功{self.bugCount}次")
        return self.bugCount

    def webNftItemList(self):
        """web nft列表"""
        itemCodes = [
            10101019,
            10101020,
            10101021,
            Enum.ITEM_CODE_FOOD_50M,
            Enum.ITEM_CODE_FOOD_100M,  # 粮食
            10101028,
            10101029,
            10101030,
            Enum.ITEM_CODE_LUMBER_50M,
            Enum.ITEM_CODE_LUMBER_100M,  # 木头
            10101037,
            10101038,
            10101039,
            Enum.ITEM_CODE_STONE_50M,
            Enum.ITEM_CODE_STONE_100M,  # 石头
            10101046,
            10101047,
            10101048,
            Enum.ITEM_CODE_GOLD_50M,
            Enum.ITEM_CODE_GOLD_100M,  # 黄金
        ]
        itemNames = ["粮食", "木头", "石头", "黄金"]
        itemUnits = ["1M", "5M", "10M", "50M", "100M"]

        res = self.nftItemList()
        if res:
            publicKey = res.get("publicKey")
            nfts = res.get("items")
            items = {}
            items["publicKey"] = publicKey
            for nft in nfts:
                itemCode = nft.get("itemCode")
                status = nft.get("status")
                statusName = "异常"
                if status == 0:
                    statusName = "未发行"
                elif status == 4:
                    statusName = "已发行"

                if itemCode not in itemCodes:
                    itemTitle = ""
                    continue
                index = itemCodes.index(itemCode)
                itemName = itemNames[index // 5]
                itemUnit = itemUnits[index % 5]
                itemTitle = f"{itemUnit}{itemName} {statusName}"
                if items.get(itemTitle) is None:
                    items[itemTitle] = 1
                else:
                    items[itemTitle] += 1
            return items

        return res

    def webNftDragoList(self):
        """web nft龙列表"""
        res = self.nftDragoList(onlyDrago=False)
        if res:
            publicKey = res.get("publicKey")
            dragos = res.get("dragos")
            items = {}
            items["publicKey"] = publicKey
            for drago in dragos:
                tokenId = drago.get("tokenId")
                dragoId = drago.get("_id")
                # status = drago.get("status")
                items[str(tokenId)] = dragoId
            return items

        return res

    def tryUseAccelerationItem(self):
        """使用加速道具"""
        itemKey = f"{self.key}_10102020"
        item = redisHelper.get(itemKey)
        if item:
            self.accelerationItemTime = item
            return False
        else:
            if not self.accelerationItemTime:
                self.accelerationItemTime = time.time()
            if time.time() - self.accelerationItemTime < 60 * 60 * 2:
                return False
            self.itemUse(10102020, 1)
            self.log("道具加速")
            redisHelper.set(itemKey, int(time.time()), 60 * 60 * 2)
            return True

    def autoTreatment(self, wounded=None, clear=False):
        """自动治疗"""

        if wounded is None:
            wounded = self.wounded()
        if not wounded:
            return True
        else:
            self.troopRecover()

        woundTime = 0
        for woundedInfo in wounded:
            startTime = None
            startT = 0
            needTime = 0
            for info in woundedInfo:
                if startTime is None:
                    startTime = info.get("startTime")
                    if not startTime:
                        continue
                    startT = self.compareDateTimeWithNow(startTime)
                woundedTime = info.get("time")
                needTime += woundedTime
            if startT < 0:
                needTime += startT
            if needTime <= 0:
                continue
            woundTime += needTime

        if woundTime > 0:
            lastWoundedInfo = wounded[-1]
            if lastWoundedInfo:
                if lastWoundedInfo[0].get("startTime") is None:
                    for info in lastWoundedInfo:
                        woundedTime = info.get("time")
                        woundTime += woundedTime

            t = math.ceil(woundTime / 60)
            if t < 60 * 24 and not clear:
                return True
            self.log(f"本次需要治疗总时长:{secondToDate(woundTime)}")
            speedItems = [
                10103050,
                10103049,
                10103048,
                10103047,
                10103046,
                10103045,
                10103044,
                10103043,
                10103042,
            ]
            times = [4320, 1440, 480, 180, 60, 30, 10, 5, 1]
            itemList = self.itemList()
            if not itemList:
                return False

            for i in range(len(speedItems)):
                costTime = times[i]
                itemCode = speedItems[i]
                if t >= costTime:
                    count = self.itemCount(itemCode)
                    n = t // costTime
                    if costTime == 1 and n * costTime < t and (n + 1) * costTime > t:
                        n += 1
                    if n > count:
                        n = count
                    if n > 0:
                        wounded = self.speedupHeal(itemCode, n)
                        if wounded is False or wounded is None:
                            self.troopRecover()
                            return False
                        successTime = n * costTime
                        self.woundFinishTime += successTime
                        self.randomSleep(1, msg=f"治疗成功{successTime}分钟 / 总治疗{self.woundFinishTime}分钟")

                        return self.autoTreatment(wounded)
                    else:
                        continue

                else:
                    continue
            return False
        else:
            return True

    def checkMailInfo(self):
        res = self.mailList(category=1)
        if res:
            mails = res.get("mails")
            if mails:
                for mail in mails:
                    like = mail.get("like")
                    if not like:
                        type = mail.get("type")
                        needLike = False
                        if type == 21:
                            self.attacked += 1
                            needLike = True
                        elif type == 28:
                            self.spyed += 1
                            needLike = True

                        if needLike:
                            self.mailLike(mail.get("_id"))

    def searchCrystalWithZoneSize(
        self, loc, zoneSize=324, zones=None, n=6, callBack=None, codes=None
    ):
        """搜索水晶"""
        if zones is None:
            zones = returnAdjacentCrystalLands(loc, maxCount=zoneSize)

        if codes is None:
            codes = [20100105, 20100106]

        def nextCallback(obj, code):
            if callable(callBack):
                callBack(obj)

        return self.searchFieldWithZoneSize(
            loc, zoneSize=zoneSize, zones=zones, n=n, codes=codes, callBack=nextCallback
        )

    def searchFieldWithZoneSize(
        self,
        loc,
        zoneSize=MaxSixThreadZoneSize,
        zones=None,
        n=6,
        codes=None,
        callBack=None,
    ):
        """多线程搜索"""
        if len(loc) == 3:
            return self.searchFieldWithZoneSize(
                loc[1:],
                zoneSize=zoneSize,
                zones=zones,
                n=n,
                codes=codes,
                callBack=callBack,
            )

        if zones is None:
            power = math.ceil((math.ceil(math.sqrt(zoneSize)) + 1) / 2)
            zones = self.coordinateTransformation([self.worldId] + loc, power)

        size = len(zones)
        div = math.ceil(size / n)
        if n == 6:
            n = math.ceil(size / MaxSingleZoneSize)
            div = MaxSingleZoneSize
        # threads = []
        # crystalList = {}
        fields = {}

        def fieldCallBack(objects, code):
            if fields.get(code) is None:
                fields[code] = {}
            for object in objects:
                loc = object.get("loc")
                fields[code][f"{loc[1]}_{loc[2]}"] = object
                if callable(callBack):
                    callBack(object, code)

        def searchZone(zone):
            maxLen = int(math.ceil(len(zone) / MaxSingleZoneSize))
            for i in range(maxLen):
                z = [self.worldId] + zone[
                    i * MaxSingleZoneSize : (i + 1) * MaxSingleZoneSize
                ]
                o = self.wsGetFields([self.worldId] + loc, zone=z, more=True)
                if o:
                    allCodes = o.keys()
                    if codes:
                        allCodes = codes
                    for code in allCodes:
                        objects = o.get(code)
                        if objects:
                            fieldCallBack(objects, code)

        res = {}
        with ThreadPoolExecutor(
            max_workers=1, thread_name_prefix=f"searchFieldWithZoneSize_{self.key}"
        ) as pool:
            for index in range(n):
                zone = zones[div * index : div * (index + 1)]
                pool.submit(searchZone, zone)

            pool.shutdown(wait=True)

            for code in fields:
                tmpFields = fields[code]
                res[code] = [tmpFields[key] for key in tmpFields]

        return res

    def tryDeleteResourceMail(self, monster=True):
        """删除资源邮件"""
        result = self.mailList(1)
        if result:
            mails = result.get("mails")
            dels = []
            if mails:
                for mail in mails:
                    receiveDate = mail["receiveDate"]
                    type = mail["type"]
                    if type == 22:
                        if -self.compareDateTimeWithNow(receiveDate) > 3600:
                            dels.append(mail["_id"])
                    elif type == 23 and monster:
                        if -self.compareDateTimeWithNow(receiveDate) > 3600:
                            dels.append(mail["_id"])

            if dels:
                # for id in dels:
                #     self.mailRead(id)
                    # self.deleteMails([id])
                    # self.randomSleep(1)
                self.deleteMails(dels)
                self.randomSleep(3)
                return True
        return False

    def tryAllianceGiftClaimAll(self):
        """尝试领取联盟礼物"""
        if not self.allianceId:
            return
        result = self.allianceGiftList()
        if result:
            needClaim = False
            for v in result:
                claimed = v.get("claimed", True)
                if not claimed:
                    needClaim = True
                    break

            if needClaim:
                self.allianceGiftClaimAll()

    def autoJoinBattle(
        self,
        troopNum=1,
        minLevel=2,
        mustList=None,
        minGroupCount=1200000,
        detailKillList=None,
        autoEnergy=True,
        warJoin=False,
    ):
        if self.actionPoint < 40:
            if autoEnergy:
                if redisHelper.getStockEnergy(self.email) < 1000 or self.chargeEnergy(200) == 0:
                    return False
            else:
                return False

        res = self.joinAllianceBattle(
            troopNum=troopNum,
            minLevel=minLevel,
            mustList=mustList,
            minGroupCount=minGroupCount,
            detailKillList=detailKillList,
            warJoin=warJoin,
        )
        if res is None:
            if autoEnergy:
                energy = self.chargeEnergy(200)
                if energy > 0:
                    return self.autoJoinBattle(
                        troopNum=troopNum,
                        minLevel=minLevel,
                        mustList=mustList,
                        minGroupCount=minGroupCount,
                        detailKillList=detailKillList,
                        autoEnergy=autoEnergy,
                        warJoin=warJoin,
                    )

            if self.actionPoint == 0:
                self.log("没有能量了")
                return False
        return True

    def tryUseBuffer(self):
        flag = redisHelper.getBufferUse(self.key)
        if flag:
            return
        else:
            self.log("吃药了")
            buffers = [
                ITEM_CODE_GATHERING_BOOST_1D,
                ITEM_CODE_FOOD_BOOST_1D,
                ITEM_CODE_LUMBER_BOOST_1D,
                ITEM_CODE_STONE_BOOST_1D,
                ITEM_CODE_GOLD_BOOST_1D,
            ]
            for code in buffers:
                self.itemUse(code, 1)
                self.randomSleep(2, 3)
            redisHelper.setBufferUse(self.key)

    def tryRepairWall2(self):
        rate = self.wallInfo()
        if rate and rate < 1:
            self.repairWall()
            self.log("修理城墙")
        elif rate and rate == 1:
            self.log("墙好的")
            return True
        return False

    def tryUseUpSkillToUse(self, kingdomId, count=2):
        skillList = self.unUseSkillList()
        useCodes = [10004, 10005, 10006, 10025, 10026, 10027]
        for skill in skillList:
            code = skill["code"]
            if code in useCodes:
                name = skill["codeName"]
                self.log(f"准备使用技能{name} {count}次")
                self.tryUseSkill(code, count=count, targetId=kingdomId)
                self.randomSleep(1, 2)

    def getInfoAndCheck(self, loc, wsUser=None):
        """并发获取数据判断抢线问题"""
        r = Result(loc)
        if wsUser is None:
            wsUser = self

        def wsGet():
            def marchCallBack(objects):
                for object in objects:
                    if object.get("fromLoc") == r.loc or object.get("toLoc") == r.loc:
                        r.hasOccupied = True
                        break

            values = wsUser.wsGetFields(
                zone=wsUser.coordinateTransformation(r.loc, 0),
                marchCallBack=marchCallBack,
                waitMarch=True,
            )
            if not r.hasOccupied:
                for _, value in values.items():
                    for filed in value:
                        if filed.get("loc") == r.loc:
                            if filed.get("occupied"):
                                r.hasOccupied = True
                            break
            else:
                self.debuglog(f"getInfoAndCheck: 有线拦截{r.loc}")

        def locInfo():
            try:
                r.value = self.info(r.loc)
            except Exception as e:
                self.errorLog(f"locInfo:{e}")

        th1 = threading.Thread(target=wsGet, daemon=True)
        th2 = threading.Thread(target=locInfo, daemon=True)
        th1.start()
        th2.start()

        th1.join(timeout=30)
        th2.join(timeout=30)
        if r.hasOccupied:
            return None
        return r.value

    def tryGarrison(self, toLoc, onlyMelee = True, troopSum = 300000, rates = None):
        """尝试驻防"""
        if rates is None:
            rates = redisHelper.getGarrisonRates()
            if rates is None:
                rates = [4, 4, 2]
        if self.isInvalid:
            return False
        if len(toLoc) != 3:
            return False
        if self.worldId != toLoc[0]:
            return False

        info = self.info(toLoc)
        if info:
            distance = info.get("distance",9999)
            if distance > 500 or distance < 0:
                self.log(f"驻防距离过远{distance} 放弃")
                return False
            fo = info.get("fo",{})
            filedCode = fo.get("code", 0)
            occupied = fo.get("occupied",{})
            worldId = occupied.get("worldId")
            if (worldId and worldId == self.realWorld) or filedCode in Enum.OBJECT_SHRINE_LIST:
                numMaxTroops = info.get("numMaxTroops",0)
                if filedCode in Enum.OBJECT_CVC_LIST:
                    if onlyMelee:
                        if distance > 30:
                            self.log(f"cvc驻防距离过远{distance} 放弃")
                            return False
                    else:
                        if distance < 30:
                            self.log(f"cvc驻防距离过近{distance} 放弃")
                            return False
                    numMaxTroops = 9999999

                if troopSum < numMaxTroops:
                    troops = info.get("troops",[])
                    marchTroops = []
                    rateSum = sum(rates)
                    for troop in troops:
                        code = troop.get("code")
                        amount = troop.get("amount")
                        if code % 10 == 6:
                            index = code // 100 % 10
                            needAmount = troopSum / rateSum * rates[index - 1]
                            if amount > needAmount:
                                marchTroops.append(self.buildMarchTroop(code, int(needAmount)))

                    if len(marchTroops) == 3:
                        marchTroops = sorted(marchTroops,key=lambda x:x["code"])
                        if self.startGarrison(toLoc, marchTroops=marchTroops):
                            self.addBotWorkLog(f"驻防{toLoc}成功")
                            return True
                    else:
                        self.addBotWorkLog(f"驻防{toLoc}失败 驻防部队不足")
        return False