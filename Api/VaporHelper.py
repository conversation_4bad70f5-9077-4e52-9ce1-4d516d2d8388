#!/usr/bin/python3
# -*- coding: utf-8 -*-

import threading
import time

import requests

from Unit.Logger import logger

"""
功能描述：Vaporapi
编写人：darkedge
编写日期：2023年04月27日

"""
# vaporUrl = 'https://vapor-drago.fly.dev/'
vaporUrl = "https://mine.darkedge.cn/"


def requestSaveDragoMines(worldId, mines, mineType=1, relay=0):
    """
    写入龙矿
    """
    if relay > 3:
        logger.error("写入龙矿失败 超过重试次数")
        return
    data = {
        "worldId": str(worldId),
        "mines": mines,
        "mineType": str(mineType),  # 0 水晶矿 1 龙矿
    }
    # logger.info(f'requestSuperGo: {data}')
    name = mineType == 1 and "龙矿" or "水晶矿"
    try:
        res = requests.post(vaporUrl + "api/saveMines", json=data, timeout=20)
        if res.status_code == 200:
            logger.info(f"写入{name}成功")
        else:
            logger.error(f"写入{name}失败 状态码错误:{res.status_code} {res.text}")
            requestSaveDragoMines(worldId, mines, mineType, relay + 1)
    except Exception as e:
        logger.error(f"requestSaveDragoMines:{e}")
        time.sleep(5)
        requestSaveDragoMines(worldId, mines, mineType, relay + 1)


def saveDragoMinesInThread(worldId, mines, mineType=1) -> threading.Thread:
    """
    写入龙矿
    """
    t = threading.Thread(
        target=requestSaveDragoMines,
        args=(
            worldId,
            mines,
            mineType,
        ),
        name="saveDragoMinesInThread",
        daemon=True,
    )
    t.start()
    return t
