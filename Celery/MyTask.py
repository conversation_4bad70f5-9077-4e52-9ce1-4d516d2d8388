from .app import app


class MyTask(app.Task):
    """
    自定义一个类，继承自celery.Task
    exc: 失败时的错误的类型；
    task_id: 任务的id；
    args: 任务函数的位置参数；
    kwargs: 任务函数的关键字参数；
    einfo: 失败时的异常详细信息；
    retval: 任务成功执行的返回值；
    """
    email = None
    userInfo = None

    def on_failure(self, exc, task_id, args, kwargs, einfo):
        """任务失败时执行"""
        if self.userInfo:
            self.userInfo.log('on_failure')

    def on_success(self, retval, task_id, args, kwargs):
        """任务成功时执行"""
        print("任务执行成功")
        if self.userInfo:
            self.userInfo.log('on_success')


    def on_retry(self, exc, task_id, args, kwargs, einfo):
        """任务重试时执行"""
        pass
        if self.userInfo:
            self.userInfo.log('on_retry')
