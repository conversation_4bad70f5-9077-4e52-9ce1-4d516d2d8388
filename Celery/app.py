# /bin/env python3
# -*- coding: UTF-8 -*-
"""
功能描述：celery分布式任务配置
编写人：darkedge
编写日期：2022年2月22日
   
"""

import platform

from celery import Celery
from celery.result import AsyncResult
from FlaskApp import app as flask_app
setting = {
    "result_expires": 60 * 60 * 24 * 60,
    "task_track_started": True,
    "result_extended": True,
}

host = "localhost"
if platform.system() == "Linux":
    host = "redis"

redisUrl = f"redis://{host}:6379"
app = Celery(
    "celery",
    broker=f"{redisUrl}/1",
    backend=f"{redisUrl}/2",
)
app.config_from_object(setting)
app.conf.update(flask_app.config)

# 解决上下文问题
class ContextTask(app.Task):
    def __call__(self, *args, **kwargs):
        with flask_app.app_context():
            return self.run(*args, **kwargs)

app.Task = ContextTask
def resultWithTaskId(taskId):
    if taskId:
        result = AsyncResult(taskId, app=app)
        return result
    return None
