# trunk-ignore(ruff/F401)
from Unit.Redis import <PERSON>isHelper, RedisMgr, logger, tzDate,MONTHNUM,HOURNUM


class RedisCeleryHelper(RedisHelper):
    def __init__(self):
        super().__init__(RedisMgr(db=5))

    def setRallyMonsterList(self, worldId, monsterList):
        key = f"monsters_rally_{worldId}"
        if self.enable:
            self.removeKey(key)
            if len(monsterList):
                try:
                    self.sadd(key, *monsterList)
                except Exception as e:
                    logger.error(f"setRallyMonsterList : {key} {monsterList} {e}")

    def getRallyMonsterList(self, worldId):
        key = f"monsters_rally_{worldId}"
        return self.smembers(key)

    def removeRallyMonsterListWithMember(self, worldId, value):
        key = f"monsters_rally_{worldId}"
        if self.enable:
            return self.srem(key, value)
        return False

    @property
    def rejectMonsterKey(self):
        return "reject_monster_%s"

    def setRejectMonster(self, monster):
        """设置拒绝的怪物"""
        if not monster:
            return
        key = self.rejectMonsterKey % monster
        self.set(key, 1, logMsg=f"setRejectMonster {monster}", ex=60 * 7)

    def setRejectMonsters(self, monsters):
        """设置拒绝的怪物列表"""
        if not monsters:
            return
        keys = [self.rejectMonsterKey % monster for monster in monsters]
        datas = {}
        for key in keys:
            datas[key] = 1
        self.batch_set_with_ex(datas, 60 * 6)

    def checkRejectMonster(self, monster) -> bool:
        """检查怪物是否在拒绝列表中"""
        key = self.rejectMonsterKey % monster
        if self.get(key):
            return True
        return False

    def checkRejectMonsters(self, monsters) -> list:
        """检查怪物列表是否在拒绝列表中"""
        if not monsters:
            return []
        keys = [self.rejectMonsterKey % monster for monster in monsters]
        values_dict = self.batch_get(keys)
         # 返回不在拒绝列表中的怪物
        return [monster for monster in monsters if not values_dict.get(self.rejectMonsterKey % monster)]

    def setCrystalList(self, worldId, crystalList):
        key = f"crystals_{worldId}"
        if self.enable:
            self.removeKey(key)
            if len(crystalList):
                try:
                    self.sadd(key, *crystalList)
                except Exception as e:
                    logger.error(f"setCrystalList : {key} {crystalList} {e}")

    def getCrystalList(self, worldId):
        key = f"crystals_{worldId}"
        return self.smembers(key)

    def removeCrystalListWithMember(self, worldId, value):
        key = f"crystals_{worldId}"
        if self.enable:
            return self.srem(key, value)
        return False

    def setDragoMineList(self, worldId, dragoMineList):
        key = f"dragoMines_{worldId}"
        if self.enable:
            self.removeKey(key)
            if dragoMineList:
                self.sadd(key, *dragoMineList)
            else:
                logger.error("setDragoMineList dragoMineList is None")

    def getDragoMineList(self, worldId):
        key = f"dragoMines_{worldId}"
        return self.smembers(key)

    def removeDragoMineListWithMember(self, worldId, value):
        key = f"dragoMines_{worldId}"
        return self.srem(key, value)

    def setDragoListenWorldIds(self, worldIds: list):
        key = "dragoListenWorldIds"
        self.setJson(key, worldIds)

    def getDragoListenWorldIds(self):
        key = "dragoListenWorldIds"
        return self.getJson(key) or []

    def setAutoTaskInterval(self, worldId: str, interval: int):
        values = self.getAutoTaskIntervals()
        values[str(worldId)] = int(interval)
        self.setAutoTaskIntervals(values)

    def getAutoTaskIntervals(self):
        key = "autoTaskDragoSearchIntervals"
        value = self.getJson(key)
        if not value:
            value = {}
        return value

    def setAutoTaskIntervals(self, value):
        key = "autoTaskDragoSearchIntervals"
        self.setJson(key, value)

    def setMaxMonsterDistance(self, value):
        key = "maxMonsterDistance"
        self.set(key, value)

    def getMaxMonsterDistance(self):
        key = "maxMonsterDistance"
        value = self.get(key)
        if value:
            return int(value)
        return 0

    def getCVCSearchPoints(self):
        """获取cvc搜索点"""
        key = "cvcSearchPoints"
        values = self.getJson(key)
        if values:
            return values
        return []

    def setCVCSearchPoints(self, values):
        """设置cvc搜索点"""
        key = "cvcSearchPoints"
        self.setJson(key, values)

    def getCVCBlockSearchPoints(self):
        """获取cvc封锁搜索点"""
        key = "cvcBlockSearchPoints"
        values = self.getJson(key)
        if values:
            return values
        return []

    def setCVCBlockSearchPoints(self, values):
        """设置cvc封锁搜索点"""
        key = "cvcBlockSearchPoints"
        self.setJson(key, values)

    def getSpartoiLevel(self) -> int:
        key = "SPARTOILevel"
        value = self.get(key)
        if value:
            return int(value)
        return 4

    def setSpartoiLevel(self, value):
        key = "SPARTOILevel"
        self.set(key, int(value))

    def getBattleTimes(self):
        key = "battleTimes"
        values = self.getJson(key)
        if values:
            return values
        return list(range(24))

    def setBattleTimes(self,values):
        key = "battleTimes"
        self.setJson(key, values)

    @property
    def cvcBlockAutoKickKey(self):
        return "cvcBlockAutoKickKey_20"

    def getCvcBlockAutoKick(self):
        key = self.cvcBlockAutoKickKey
        value = self.get(key)
        if value and bool(int(value)):
            return True
        return False

    def setCvcBlockAutoKick(self, value):
        key = self.cvcBlockAutoKickKey
        self.set(key, value)

    @property
    def cvcBlockKey(self):
        return "cvcBlockKey_20"

    def getCvcBlockUsers(self):
        key = self.cvcBlockKey
        values = self.getJson(key)
        if values:
            return values
        return []

    def addCvcBlockUsers(self,kingdomId):
        key = self.cvcBlockKey
        values = self.getCvcBlockUsers()
        if kingdomId not in values:
            values.append(kingdomId)
        self.setJson(key, values)

    def removeCvcBlockUsers(self,kingdomId):
        key = self.cvcBlockKey
        values = self.getCvcBlockUsers()
        if kingdomId in values:
            values.remove(kingdomId)
            self.setJson(key, values)

    @property
    def cvcKingdomInfoKey(self):
        return "cvcKingdomInfo_%s"

    def setCvcKingdomInfo(self, kingdomId, info:dict):
        """设置cvc王国信息
        kingdomId: 王国id
        info: 王国信息 {
            "name": 王国名称,
            "worldId": 王国世界id,
        }
        """
        key = self.cvcKingdomInfoKey % kingdomId
        value = self.getCvcKingdomInfo(kingdomId)
        if value:
            info = value | info
        self.setJson(key, info)

    def getCvcKingdomInfo(self, kingdomId):
        key = self.cvcKingdomInfoKey % kingdomId
        return self.getJson(key) or {}

    def setCvcKingdomInfos(self, infos:dict):
        """设置cvc王国信息列表"""
        datas = {}
        for kingdomId, info in infos.items():
            key = self.cvcKingdomInfoKey % kingdomId
            datas[key] = info
        self.batch_set_with_ex(datas)

    @property
    def cvcFoIdInfoKey(self):
        return "cvcFoIdInfo_%s"

    def setCvcFoIdInfo(self, foId, info:dict):
        """设置cvc王国信息
        foId: 王国id
        info: 王国信息 {
            "name": 王国名称,
            "worldId": 王国世界id,
            "userId": 王国id(kingdomId)
        }
        """
        key = self.cvcFoIdInfoKey % foId
        value = self.getCvcFoIdInfo(foId)
        if value and value.get("name"):
            info = value | info
        self.setJson(key, info)

    def getCvcFoIdInfo(self, foId):
        key = self.cvcFoIdInfoKey % foId
        return self.getJson(key) or {}

    def setCvcFoIdInfos(self, infos:dict):
        """设置cvc王国信息列表"""
        datas = {}
        for foId, info in infos.items():
            key = self.cvcFoIdInfoKey % foId
            datas[key] = info
        self.batch_set_with_ex(datas)

    @property
    def cvcRankSwitchKey(self):
        return "cvc_rank_switch_%s"

    def getCvcRankSwitch(self, worldId = 20):
        key = self.cvcRankSwitchKey % worldId
        value = self.get(key)
        if value and int(value) == 1:
            return True
        return False

    def setCvcRankSwitch(self, value, worldId = 20):
        key = self.cvcRankSwitchKey % worldId
        self.set(key, value, ex=60 * 60 * 24 * 14)

    @property
    def shieldRallySwitchKey(self):
        # 破盾跟团开关
        return "shieldRallySwitch_%s"

    def getShieldRallySwitch(self):
        key = self.shieldRallySwitchKey
        value = self.get(key)
        if value and int(value) == 1:
            return True
        return False

    def setShieldRallySwitch(self, value):
        key = self.shieldRallySwitchKey
        self.set(key, value)

    @property
    def botWorkLogKey(self):
        return "BotWorkLog"

    def addToBotWorkLog(self, value):
        """使用pipeline原子方式添加单个值到机器人工作日志列表

        Args:
            value: 要添加的日志内容
        Returns:
            bool: 是否添加成功
        """
        key = self.botWorkLogKey
        if self.enable:
            try:
                pipe = self.redisMgr.pipeline()

                # 检查key是否存在，不存在则设置过期时间
                pipe.exists(key)
                pipe.llen(key)
                exists, length = pipe.execute()

                pipe = self.redisMgr.pipeline()
                if not exists:
                    pipe.expire(key, 3600)  # 1小时过期

                # 如果超过最大长度，移除最早的元素
                if length >= 100:
                    pipe.lpop(key)

                # 添加新元素到列表末尾
                pipe.rpush(key, value)
                pipe.execute()
                return True
            except Exception as e:
                logger.error(f"addToBotWorkLog error: {e}")
        return False

    def getBotWorkLog(self):
        """获取机器人工作日志列表内容

        Returns:
            list: 日志列表内容，如果出错返回空列表
        """
        key = self.botWorkLogKey
        if self.enable:
            try:
                values = self.redisMgr.lrange(key, 0, -1)
                return [v.decode() for v in values] if values else []
            except Exception as e:
                logger.error(f"getBotWorkLog error: {e}")
        return []

    @property
    def cvcRankLockPointKey(self):
        return "cvcRankLockPoint"

    def getCvcRankLockPoint(self):
        key = self.cvcRankLockPointKey
        value = self.get(key)
        if value:
            return int(value)
        return 500_0000

    def setCvcRankLockPoint(self, value:int):
        key = self.cvcRankLockPointKey
        self.set(key, value)

    @property
    def cvcFoIdKey(self):
        return "cvcFoId_%s"

    def setCvcFoId(self, kingdomId, foId):
        key = self.cvcFoIdKey % kingdomId
        self.set(key, foId, ex = 60 * 3)

    def getCvcFoId(self, kingdomId):
        key = self.cvcFoIdKey % kingdomId
        return self.get(key)

    def removeCvcFoId(self, kingdomId):
        key = self.cvcFoIdKey % kingdomId
        self.removeKey(key)

    def setCvcFoIdList(self, values:dict):
        datas = {}
        for kingdomId, foId in values.items():
            key = self.cvcFoIdKey % kingdomId
            datas[key] = foId
        self.batch_set_with_ex(datas, 60 * 3)

    def setWarJoinCountKey(self, worldId):
        """设置战争跟团数量key"""
        return f"warJoinCount_{worldId}"

    def getWarJoinCount(self, worldId: int) -> int:
        """获取战争跟团数量"""
        key = self.setWarJoinCountKey(worldId)
        value = self.get(key)
        if value:
            return int(value)
        return 0

    def setWarJoinCount(self, worldId: int, count:int):
        """设置战争跟团数量"""
        key = self.setWarJoinCountKey(worldId)
        self.set(key, count)

    def allWarJoinCount(self):
        """获取所有战争跟团数量"""
        keys = self.keys("warJoinCount_*")
        values = {}
        for key in keys:
            key = key.decode()
            worldId = int(key.split("_")[1])
            values[worldId] = self.getWarJoinCount(int(worldId))
        return values

redisCeleryHelper = RedisCeleryHelper()
