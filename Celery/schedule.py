# /bin/env python3
# -*- coding: UTF-8 -*-
"""
功能描述：celery定时任务配置
编写人：darkedge
编写日期：2023年1月13日
   
"""

from celery import Celery
from celery.schedules import crontab


from Celery.scheduleTask import (
    autoTestLocalSocks5,
    autoTestLocalSocks5Speed,
    list20CrystalTask,
    listDragoMinesTask_drago,
    # trunk-ignore(ruff/F401)
    listCrystalTask,
    listCVCMonster,
    listCVCBlock,
    listDragoMinesTask,
    autoCvcRankMonitor,
)

from .app import app


@app.on_after_configure.connect
def setup_periodic_tasks(sender: Celery, **kwargs):
    # Calls test('hello') every 10 seconds.
    # sender.add_periodic_task(10.0 * 60.0, listCrystalTask.s(20), name='crystal task')

    # sender.add_periodic_task(3.0, test.s('hello'))
    # # Calls test('world') every 30 seconds
    # sender.add_periodic_task(30.0, test.s('world'), expires=10)

    # Executes every Monday morning at 7:30 a.m.
    # sender.add_periodic_task(
    #     crontab(minute="30,40,50",hour="*/12"),
    #     listCrystalTask.s(20),
    #     name='crystal crontab task 20'
    # )

    # sender.add_periodic_task(crontab(minute="*"),
    # testHello.s(),
    # name="test hello")
    # sender.add_periodic_task(
    #     crontab(minute="*/10",hour="1-5,7-11,13-17,19-23"),
    #     listCrystalTask.s(20),
    #     name="listCrystalTask crontab task"
    #     )

    sender.add_periodic_task(
        crontab(minute="*"),
        listDragoMinesTask.s(),
        name="listDragoMinesTask crontab task",
    )
    sender.add_periodic_task(
        crontab(minute="23", hour="*"),
        list20CrystalTask.s(),
        name="listCrystalTask crontab task",
    )
    sender.add_periodic_task(
        crontab(hour="*/8", minute="0"), autoTestLocalSocks5.s(), name="socks task"
    )
    sender.add_periodic_task(
        crontab(minute="7,22,37,52"),
        autoTestLocalSocks5Speed.s(),
        name="socks speed task",
    )
    sender.add_periodic_task(
        crontab(minute="*/2"), listCVCMonster.s(), name="cvc search task"
    )

    sender.add_periodic_task(
        crontab(minute="*/2"), listCVCBlock.s(), name="cvc block search task"
    )

    sender.add_periodic_task(
        crontab(minute="*"), autoCvcRankMonitor.s(), name="cvc rank monitor task"
    )

    sender.add_periodic_task(
        crontab(minute="25"), listDragoMinesTask_drago.s(), name="drago search task"
    )
    print("start task")


@app.task
def test(arg):
    print(arg)


@app.task
def add(x, y):
    z = x + y
    print(z)


@app.task
def testHello():
    print("hello")
