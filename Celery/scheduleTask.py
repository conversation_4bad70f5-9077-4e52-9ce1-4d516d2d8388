#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
功能描述：celery分布式定时任务
作者：darkedge
编写日期：2022年12月28日
"""
import random
import sys
import threading
import time

from Unit.Logger import logger

from .app import app

isDebug = True if sys.gettrace() else False

QueueKey = "schedule"


@app.task(queue=QueueKey)
def test_b():
    from Unit.Logger import logger

    logger.info("test_b")
    return "b"


def searchAllDragoMines(
    worldId, vaporNeed=False, maxLevel=9, without=False, all=False, tokens=None
):
    from Api.FlaskHelper import requestTokens
    from Api.UserInfo import Enum, UserInfo
    from Unit.LandZoneUnit import crystalLands
    from Unit.Logger import logger
    from Unit.UserEncrypt import refreshNodKey
    from Unit.UserInfoHelper import searchAll

    refreshNodKey()
    user = UserInfo()
    user.loc = [worldId, 1024, 1024]

    zones = None
    if without:
        zones = []
        for i in range(1, 4096):
            if i not in crystalLands:
                zones.append(i)
    # tasks = user.runTasks([requestTokenA() for v in range(8)])
    if all:
        zones = [i for i in range(1, 4096)]

    try:
        if tokens is None:
            tokenCount = without and 12 or 8
            if all:
                tokenCount = 20
            tokens = requestTokens(tokenCount)
            logger.info("未传入token，自己获取")
        if tokens:
            t1 = time.time()
            codes = None
            if all:
                codes = [
                    Enum.OBJECT_CODE_CRYSTAL_MINE,
                    Enum.OBJECT_CODE_DRAGON_SOUL_CAVERN,
                    Enum.OBJECT_CODE_DEATHKAR,
                    Enum.OBJECT_CODE_RED_DRAGON,
                ]
            data = searchAll(tokens, [worldId, 1024, 1024], codes=codes, zones=zones)

            for k, v in data.items():
                data[k] = user.returnSortFieldsWithLevelDes(
                    v, clearOccupied=True, clearHidden=True, maxLevel=maxLevel
                )

            dragoMines = data.get(Enum.OBJECT_CODE_DRAGON_SOUL_CAVERN, [])
            if dragoMines:
                dragoMines = [
                    f'{dragoMine["level"]}_{dragoMine["loc"][1]}_{dragoMine["loc"][2]}'
                    for dragoMine in dragoMines
                ]
                if vaporNeed:
                    from Api.VaporHelper import saveDragoMinesInThread

                    t = saveDragoMinesInThread(worldId, dragoMines, mineType=1)
                    t.join()

            crystalMines = data.get(Enum.OBJECT_CODE_CRYSTAL_MINE, [])
            if crystalMines:
                crystalMines = [
                    f'{crystalMine["level"]}_{crystalMine["loc"][1]}_{crystalMine["loc"][2]}'
                    for crystalMine in crystalMines
                ]
                if vaporNeed:
                    from Api.VaporHelper import saveDragoMinesInThread

                    t = saveDragoMinesInThread(worldId, crystalMines, mineType=0)
                    t.join()
            logger.info(
                f"获取有效数据: 大陆{worldId} 龙矿{len(dragoMines)} 水晶矿{len(crystalMines)} 耗时:{round(time.time()-t1,2)}"
            )

            return data
    except Exception as e:
        logger.error(f"异常错误 : {e}", exc_info=True)


@app.task(retry=False, ignore_result=True, queue=QueueKey)
def listDragoMinesTask_drago():
    from Api.FlaskHelper import requestTokens
    # from Celery.redis import redisCeleryHelper, tzDate
    from Unit.Logger import logger

    worldIds = [24, 17, 20] #redisCeleryHelper.getDragoListenWorldIds()

    # minute = tzDate().minute
    # hour = tzDate().hour
    # if hour % 12 == 8 and minute == 28:
    if worldIds:
        tokens = requestTokens(8, isGoogle=True)
        if not tokens:
            logger.error("监听矿区【龙矿】 获取tokens失败")
            return

        logger.info(f"监听矿区【龙矿】的世界ID {worldIds} ")
        for worldId in worldIds:
            searchAllDragoMines(worldId, vaporNeed=False, tokens=tokens)
            time.sleep(10)


@app.task(retry=False, ignore_result=True, queue=QueueKey)
def listDragoMinesTask():
    from Api.FlaskHelper import requestTokens
    from Celery.redis import redisCeleryHelper, tzDate
    from Unit.Logger import logger
    from Api.FlaskHelper import requestMakeGlobalCrystalInfo

    worldIds = redisCeleryHelper.getDragoListenWorldIds()
    taskWorldIds = []

    minute = tzDate().minute
    if worldIds:
        taskIntervals = redisCeleryHelper.getAutoTaskIntervals()
        for worldId in worldIds:
            interval = taskIntervals.get(str(worldId), 0)
            if minute % 10 == interval:
                taskWorldIds.append(worldId)

    tokens = None
    if taskWorldIds:
        tokens = requestTokens(8 * len(taskWorldIds))
        if not tokens:
            logger.error("获取tokens失败")
            return

    def work(worldId, tokens):
        searchAllDragoMines(worldId, vaporNeed=True, tokens=tokens)
        time.sleep(5)
        requestMakeGlobalCrystalInfo(worldId=worldId)

    logger.info(f"监听矿区的世界ID minute:{minute} : {taskWorldIds} ")
    ths = []
    for index in range(len(taskWorldIds)):
        worldId = taskWorldIds[index]
        currentTokens = tokens[index * 8 : (index + 1) * 8]
        th = threading.Thread(
            target=work,
            args=(worldId, currentTokens),
            daemon=True,
            name=f"listDragoMinesTask_{worldId}",
        )
        th.start()
        ths.append(th)

    [th.join() for th in ths]


@app.task(retry=False, ignore_result=True, queue=QueueKey)
def listCrystalTask(worldId, maxLevel=9, vaporNeed=False):
    from Unit.Logger import logger
    from Api.FlaskHelper import requestMakeGlobalCrystalInfo

    logger.info(f"监听水晶的世界ID : {worldId} vaporNeed:{vaporNeed}")
    searchAllDragoMines(worldId, vaporNeed=vaporNeed, maxLevel=maxLevel)
    requestMakeGlobalCrystalInfo(worldId=worldId)


@app.task(retry=False, ignore_result=True, queue=QueueKey)
def list20CrystalTask():
    # trunk-ignore(ruff/F401)
    import Unit.enum as Enum
    from Celery.redis import redisCeleryHelper
    from Api.FlaskHelper import requestMakeGlobalCrystalInfo

    datas = None
    # if currentHour() % 12 in [9,10,11]:
    #     logger.info('监听20区技能水晶')
    # datas = searchAllDragoMines(20, vaporNeed=False,without=True)

    # datas2 = searchAllDragoMines(20, vaporNeed=False)
    # if datas and datas.get(Enum.OBJECT_CODE_CRYSTAL_MINE):
    #     crystals_1 = datas.get(Enum.OBJECT_CODE_CRYSTAL_MINE,[])
    #     crystals_2 = datas2.get(Enum.OBJECT_CODE_CRYSTAL_MINE,[])
    #     oldLocs = [x['loc'] for x in crystals_2]
    #     for crystal in [crystal for crystal in crystals_1 if crystal['loc'] not in oldLocs]:
    #         crystals_2.append(crystal)
    #     crystals = [
    #         f'{crystal["level"]}_{crystal["loc"][1]}_{crystal["loc"][2]}'
    #         for crystal in crystals_2
    #     ]
    #     redisCeleryHelper.setCrystalList(20, crystals)
    #     logger.info(f"20区增量水晶:{len(crystals_2)} - {len(crystals_1)}")
    datas = searchAllDragoMines(20, vaporNeed=False, all=True)
    if datas:
        crystals = datas.get(Enum.OBJECT_CODE_CRYSTAL_MINE, [])
        dks = datas.get(Enum.OBJECT_CODE_DEATHKAR, [])
        redDragons = datas.get(Enum.OBJECT_CODE_RED_DRAGON, [])
        goldDragons = datas.get(Enum.OBJECT_CODE_GOLD_DRAGON, [])
        redDragons += goldDragons

        values = []
        if redDragons:
            monsters = list(filter(lambda x: x["level"] >= 1, redDragons))
            monsters = [
                f'{monster["level"]}_{monster["loc"][1]}_{monster["loc"][2]}'
                for monster in monsters
            ]
            values += monsters

        if dks:
            monsters = list(filter(lambda x: x["level"] >= 4, dks))
            monsters = [
                f'{monster["level"]}_{monster["loc"][1]}_{monster["loc"][2]}'
                for monster in monsters
            ]
            values += monsters

        redisCeleryHelper.setRallyMonsterList(20, values)
        logger.info(f"20区水晶:{len(crystals)} 红龙:{len(redDragons)} dk:{len(dks)}")
    requestMakeGlobalCrystalInfo(worldId=20)


@app.task(retry=False, ignore_result=True, queue=QueueKey)
def listCVCMonster():
    # trunk-ignore(ruff/F401)
    import Unit.enum as Enum
    from Api.FlaskHelper import requestTokens
    from Celery.redis import redisCeleryHelper
    from Unit.LandZoneUnit import AllZoneOfPolygon, NewPolygon, isPointOnPolygonEdge
    from Unit.UserInfoHelper import searchAll

    spLevel = redisCeleryHelper.getSpartoiLevel()
    worldId = 100002
    values = []

    if spLevel == 11:
        logger.info("停止搜索 不获取")
        redisCeleryHelper.setRallyMonsterList(worldId, values)
        redisCeleryHelper.setRallyMonsterList(worldId * 10, values)
        return

    points = redisCeleryHelper.getCVCSearchPoints()
    if len(points) < 3:
        logger.error("listCVCMonster 点位不够")
        return

    polygon = NewPolygon(points)
    zones = AllZoneOfPolygon(polygon)

    tokens = requestTokens(len(zones) > 216 and 8 or 4)

    datas = searchAll(
        tokens,
        [worldId, 1024, 1024],
        codes=Enum.OBJECT_RALLY_MONSTER_CODE_LIST + Enum.OBJECT_MONSTER_CODE_LIST,
        zones=zones,
    )
    if datas:
        dks = datas.get(Enum.OBJECT_CODE_SPARTOI, [])
        redDragons = datas.get(Enum.OBJECT_CODE_MAGODA, [])
        otherMonsters = [monster for code in Enum.OBJECT_MONSTER_CODE_LIST for monster in datas.get(code, [])]

        if redDragons:
            monsters = list(
                filter(
                    lambda x: x["level"] >= 1
                    and isPointOnPolygonEdge(x["loc"][1:], polygon),
                    redDragons,
                )
            )
            monsters = [
                f'{monster["level"]}_{monster["loc"][1]}_{monster["loc"][2]}'
                for monster in monsters
            ]
            values += monsters

        if dks:
            monsters = list(
                filter(
                    lambda x: x["level"] >= spLevel
                    and isPointOnPolygonEdge(x["loc"][1:], polygon),
                    dks,
                )
            )
            monsters = [
                f'{monster["level"]}_{monster["loc"][1]}_{monster["loc"][2]}'
                for monster in monsters
            ]
            values += monsters

        if otherMonsters:
            monsters = list(
                filter(
                    lambda x: isPointOnPolygonEdge(x["loc"][1:], polygon),
                    otherMonsters,
                )
            )
            monsters = [
                f'{monster["level"]}_{monster["loc"][1]}_{monster["loc"][2]}'
                for monster in monsters
            ]
            otherMonsters = monsters

        oldValueCount = len(values)
        values = redisCeleryHelper.checkRejectMonsters(values)
        otherMonsters = redisCeleryHelper.checkRejectMonsters(otherMonsters)
        redisCeleryHelper.setRallyMonsterList(worldId, values)
        # 使用*10来代表野怪
        redisCeleryHelper.setRallyMonsterList(worldId * 10, otherMonsters)
        logger.info(
            f"{worldId} 野怪:{len(otherMonsters)} 龙:{len(redDragons)} sp:{len(dks)} rate:{len(values)}/{len(dks) + len(redDragons)} reject:{oldValueCount-len(values)}"
        )


@app.task(retry=False, ignore_result=True, queue=QueueKey)
def listCVCBlock():
    # trunk-ignore(ruff/F401)
    import Unit.enum as Enum
    from Api.FlaskHelper import requestTokens
    from Celery.redis import redisCeleryHelper
    from Unit.LandZoneUnit import AllZoneOfPolygon, NewPolygon
    # from Unit.Redis import currentHour
    from Unit.UserInfoHelper import searchAll
    from Unit.ZeroUnit import ZeroSend

    worldId = 100002

    blockUsers = redisCeleryHelper.getCvcBlockUsers()
    points = redisCeleryHelper.getCVCBlockSearchPoints()
    # points = [[800, 800], [800, 2000], [2000, 2000], [2000, 800]]
    #points = [[0,0], [0 , 800], [1200, 800], [1200, 0], [800, 0], [800, 400], [400, 400], [400, 0]]
    if len(points) < 3:
        logger.error("listCVCBlock 点位不够")
        return

    if len(blockUsers) == 0:
        logger.info("listCVCBlock blockUsers 0")
        return

    if not redisCeleryHelper.getCvcBlockAutoKick():
        logger.info("listCVCBlock 未开启自动踢人")
        return

    polygon = NewPolygon(points)
    zones = AllZoneOfPolygon(polygon)

    tokens = requestTokens(len(zones) > 216 and 8 or 4)

    datas = searchAll(
        tokens, [worldId, 1024, 1024], codes=[Enum.OBJECT_CODE_KINGDOM], zones=zones
    )
    if datas:
        kingdoms = datas.get(Enum.OBJECT_CODE_KINGDOM, [])

        foIds = {}
        kingdomIds = {}
        kickList = []

        for kingdom in kingdoms:
            foId = kingdom.get("_id")
            occupied = kingdom.get("occupied", {})
            if not occupied:
                continue
            kingdomId = occupied.get("id")
            allianceTag = occupied.get("allianceTag","")
            worldId = occupied.get("worldId")
            slimKingdom = {"foId":foId, "kingdomId":kingdomId, "worldId":worldId} | kingdom
            foIds[foId] = slimKingdom
            kingdomIds[kingdomId] = slimKingdom

            # 踢出列表
            if kingdomId in blockUsers or allianceTag in ["UN24"]:
                kickList.append(slimKingdom)

        redisCeleryHelper.setCvcFoIdInfos(foIds)
        redisCeleryHelper.setCvcKingdomInfos(kingdomIds)
        logger.info(f'listCVCBlock testkingdomIds:{len(kingdomIds)}')
        logger.info(f'listCVCBlock testfoIds:{len(foIds)}')

        if kickList:
            foIds = [kingdom.get("foId") for kingdom in kickList]
            for kingdom in kickList:
                name = kingdom.get("name")
                foId = kingdom.get("foId")
                kingdomId = kingdom.get("kingdomId")
                if kingdomId == '63560352d338816f1b1b1372':
                    logger.info('listCVCBlock 免疫德勒')
                    continue
                worldId = kingdom.get("worldId")

                logger.info(f"listCVCBlock kingdom:{kingdom}")
                logger.info(f"listCVCBlock 广播踢出: {name}[{worldId}] {kingdomId} {foId}")
            # 踢出前4个
            ZeroSend.tryCvcKingKick(foIds[:5])

    logger.info("listCVCBlock 结束")


@app.task(retry=False, ignore_result=True, queue=QueueKey)
def autoTestLocalSocks5():
    from Service.ToolUnit import testLocalSocks5

    try:
        fails, localIPs = testLocalSocks5()
        fails2, localIPs2 = testLocalSocks5("./socks5_web.txt")
        fails += fails2
        localIPs.update(localIPs2)
        if len(fails) > 0:
            from Api.BarkApi import barkSendMsg

            failsResult = []
            for fail in fails:
                res = fail.split("@")
                failsResult.append(f"{res[1]}:{res[0][2:]}")
            barkSendMsg(
                "sosks异常", "\n".join(failsResult), "bAzCab9gbvpDza7YYsRjgM", "王国"
            )
    except Exception as e:
        logger.error(e, exc_info=True)


@app.task(retry=False, ignore_result=True, queue=QueueKey)
def autoTestLocalSocks5Speed(path=None):
    from Service.ToolUnit import testLocalSocks5Speed
    from Unit.Redis import redisHelper

    try:
        fails, delays = testLocalSocks5Speed(path=path, max_workers=50)
        redisHelper.removeSocks5Timeout()
        if len(delays) > 70:
            # 获取平均数
            # 获取 delays 中所有元素的第一个值的平均数
            average = sum(k[0] for k in delays) / len(delays)
            logger.debug(f"第70个延时为{delays[70][0]} 平均数:{average}")
            if delays[70][0] < average:
                for k in delays:
                    if k[0] > average:
                        fails.append(k[1])
            else:
                fails += [k[1] for k in delays[70:]]

            if len(fails) > 0:
                redisHelper.setSocks5TimeoutList(fails)
                # 循环fails 使用index
                for i in range(len(fails)):
                    backup = delays[i % 70][1]
                    value = fails[i]
                    redisHelper.setSocks5TimeoutBackup(value, backup)

    except Exception as e:
        logger.error(e, exc_info=True)


@app.task(retry=False, ignore_result=True, queue=QueueKey)
def autoCvcRankMonitor(userKey = None, sendNoti = True):
    from Api.UserInfo import UserInfo
    from Celery.redis import redisCeleryHelper
    from Unit.FileTool import loadS5List
    from Unit.ZeroUnit import ZeroSend

    autoKill = redisCeleryHelper.getCvcBlockAutoKick()
    rankSwitch = redisCeleryHelper.getCvcRankSwitch()
    if not rankSwitch and not autoKill:
        UserInfo.cls_saveCVCRankList([])
        logger.info("CVC排名监控未开启")
        return

    if not userKey:
        userKey = "5b8d11e9-db40-197f-534a-3714e6887b01"

    user = UserInfo(
        userKey=userKey,
        socks5=random.choice(loadS5List()),
        saveSocks5=True,
    )
    user.useBarkNoti = True
    user.name = "CVCMonitor"
    user.token = user.loadRedisToken()
    user.initLog(needTestS5=False)
    if not user.token:
        user.login()
    kickList = user.tryCvcRankMonitor(autoKill= rankSwitch and autoKill, sendNoti= sendNoti)
    if kickList:
        ZeroSend.tryCvcKingKick(kickList)
