# /bin/env python3
# -*- coding: UTF-8 -*-
"""
功能描述：celery分布式任务
编写人：darkedge
编写日期：2022年12月28日
   
"""
import json
import secrets
import sys
import platform

from FlaskApp.Models.MiningWorkMan import MiningWorkMan
from Model.UserError import UserError
from Unit.FileTool import loadS5List
from Unit.Redis import redisHelper

from .app import app

isDebug = True if sys.gettrace() else False
__all__ = ["revokeTask", "collectCrystalsTask", "test_a"]


def revokeTask(task_id):
    return app.control.revoke(task_id, terminate=True, signal="SIGKILL")


@app.task(name="test_a", ignore_result=True)
def test_a():
    from Unit.Logger import logger

    logger.info("test")


@app.task(
    name="Mining",
    bind=True,
    soft_time_limit=10 * 24 * 60 * 60,
    time_limit=10 * 24 * 60 * 60,
    default_retry_delay=99999999999,
    retry=False,
    ignore_result=True,
)
def collectCrystalsTask(self, config: dict):
    s5List = None
    try:
        s5List = loadS5List("./socks5_web.txt")
    except Exception:
        from Unit.Logger import logger

        logger.error("socks5_web异常")
        return
    from Api.User.Request import checkB64ApiList
    from Api.UserInfo import UserInfo, refreshNodKey
    from Unit.UserInfoHelper import MiningHelper

    if not self.request.called_directly:
        self.update_state(state="RUN")
    print(config)
    try:
        email = config.get("email")
        pwd = config.get("pwd")
        token = config.get("token")
        socks5 = config.get("socks5")
        params = config.get("params")

        if isinstance(params, str):
            params = json.loads(params)

        refreshNodKey()
        checkB64ApiList()
        user = UserInfo(
            email, pwd, token=token, socks5=socks5 or secrets.choice(s5List)
        )
        if user.email is None:
            user.loadEmailWithToken()

        user.userBarkKey = params.get("barkKey")
        user.addRedisHander()
        if not user.login():
            user.log("登入失败")
            return

        if isDebug:
            user.debuglog = user.log
        user.wsWithKingdomApp(isDebug=isDebug)

        model: MiningWorkMan = MiningWorkMan.query.filter_by(email=user.email).first()
        if platform.system() == "Darwin":
            # Code for macOS
            if not user.kingdomId:
                user.loadKingdomId()

            helper = MiningHelper(user)
            helper.updateWithDict(params)
            helper.run()
            return
        
        model.name = user.name
        if token != user.token:
            model.token = user.token
        if not user.kingdomId:
            user.loadKingdomId()
        model.kingdom_id = user.kingdomId
        model.save()

        helper = MiningHelper(user)
        helper.updateWithDict(params)
        helper.run()

    except SystemExit as e:
        user.log(f"系统退出: {e}")

    except UserError as e:
        if e.errorCode == -4:
            user.clearSelf()
            user.log("登录失败,token异常")
            # self.userId
        else:
            user.log(f"异常{e}")
    except BaseException as e:
        if user:
            user.errorLog(f"未捕获异常{e}", exc_info=True)
        else:
            UserInfo().errorLog(f"未捕获异常{e}", exc_info=True)

    finally:
        user.invalidStatu = True
        redisHelper.removeCeleryTaskId(email)
