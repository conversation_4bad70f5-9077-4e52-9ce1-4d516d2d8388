# 构建阶段
FROM python:3.11.8-slim as builder

# 设置环境变量
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    # 设置 pip 进度显示
    PIP_PROGRESS_BAR=on \
    TZ=Asia/Shanghai

# 设置工作目录
WORKDIR /build

# 设置pip源
RUN pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple

# 安装编译依赖
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        gcc=4:12.2.0-3 \
        libpython3-dev=3.11.2-1+b1 && \
    rm -rf /var/lib/apt/lists/*

# 安装 Python 依赖，添加 -v 参数显示详细输出
COPY requirements.txt .
RUN pip install -r requirements.txt -v

# 最终阶段
FROM python:3.11.8-slim

# 设置环境变量
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    TZ=Asia/Shanghai

# 安装运行时依赖
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        vim-nox=2:9.0.1378-2+deb12u2 \
        cron-daemon-common=3.0pl1-162 \
        curl=7.88.1-10+deb12u12 \
        zip=3.0-13 \
        unzip=6.0-28 && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/* && \
    # 设置时区
    ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && \
    echo $TZ > /etc/timezone

# 从构建阶段复制 Python 包
COPY --from=builder /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=builder /usr/local/bin /usr/local/bin

# 设置工作目录
WORKDIR /league

# 暴露端口
EXPOSE 9999

# 使用 ENTRYPOINT 结合 CMD
ENTRYPOINT ["/bin/bash"]
CMD ["/league/start.sh"]
