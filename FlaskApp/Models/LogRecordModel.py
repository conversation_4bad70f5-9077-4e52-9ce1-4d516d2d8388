#!/usr/bin/python3
# -*- coding: utf-8 -*-

"""
LogRecordModel
编写人：darkedge
编写日期：2023年07月27日

"""
import datetime

import FlaskApp

from .HelperModel import HelpModel

db = FlaskApp.db

typeList = ["默认", "系统", "告警", "资源"]


class LogRecordModel(db.Model, HelpModel):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(40), nullable=True)
    type = db.Column(db.Integer, nullable=True)
    msg = db.Column(db.String(1024), nullable=True)
    created_at = db.Column(db.DateTime(), default=datetime.datetime.now)
    active = db.Column(db.Boolean(), default=True)

    @classmethod
    def typeOptions(cls):
        return [(i, typeList[i]) for i in range(len(typeList))]

    @property
    def typeString(self):
        if self.type is not None and self.type >= 0 and self.type < len(typeList):
            return typeList[self.type]
        return ""
