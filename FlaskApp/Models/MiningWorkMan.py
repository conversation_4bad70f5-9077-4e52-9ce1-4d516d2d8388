#!/usr/bin/python3
# -*- coding: utf-8 -*-

"""
MiningWorkMan
编写人：darkedge
编写日期：2022年12月25日

"""
from typing import Dict, List, Optional
import FlaskApp
import datetime
import json
from .HelperModel import HelpModel
from Unit.Redis import redisHelper
from Unit.Logger import logger
# from Celery.redis import redisCeleryHelper

db = FlaskApp.db


class MiningWorkMan(db.Model, HelpModel):
    id = db.Column(db.Integer, primary_key=True)
    email = db.Column(db.String(120), unique=True, nullable=True)
    pwd = db.Column(db.String(120), nullable=True)
    name = db.Column(db.String(40), unique=True, nullable=True)
    userKey = db.Column(db.String(36), unique=True, nullable=True)
    kingdom_id = db.Column(db.String(40), nullable=True)
    token = db.Column(db.String(2048), nullable=True)
    socks5 = db.Column(db.String(200), unique=True, nullable=True)
    params = db.Column(db.String(2048), nullable=True)

    active = db.Column(db.Boolean(), default=True)
    confirmed_at = db.Column(
        db.DateTime(), default=datetime.datetime.now, onupdate=datetime.datetime.now)

    crystalLevel = ''
    autoJoinAllianceBattle = ''
    barkKey = ''

    def __str__(self):
        return self.name

    def celeryTaskId(self):
        if self.email and len(self.email) > 0:
            taskId = redisHelper.getCeleryTaskId(self.email)
            if taskId:
                return taskId
        return None

    def buildConfig(self) -> Dict:
        return {
            "email": self.email,
            "pwd": self.pwd,
            "token": self.token,
            "socks5": self.socks5,
            "params": self.params,
        }

    def startTask(self) -> str:
        """启动挂机任务并返回任务ID"""
        from Celery.task import collectCrystalsTask
        result = collectCrystalsTask.delay(self.buildConfig())
        task_id = result.task_id
        redisHelper.setCeleryTaskId(self.email, task_id)
        return task_id

    @property
    def emailSub(self) -> Optional[str]:
        if self.email:
            return self.email[:3] + "****" + self.email[-7:]
        return None
    
    @property
    def workStatu(self):
        from Celery.app import resultWithTaskId
        taskId = self.celeryTaskId()
        if taskId:
            result = resultWithTaskId(taskId)
            if result.state == 'RUN':
                return True
        return False

    @property
    def playHiddenStr(self):
        if self.workStatu:
            return "hidden"
        return ""
    
    @property
    def stopHiddenStr(self):
        if not self.workStatu:
            return "hidden"
        return ""
    
    @property
    def energy(self):
        return self.numberUpString(redisHelper.getStockEnergy(self.email))
    
    @property
    def crystal(self):
        return self.numberUpString(redisHelper.getCrystalItem(self.email))
    
    @property
    def socks5Port(self):
        if self.socks5:
            print(self.socks5)
            print(type(self.socks5))
            arr = self.socks5.split(":")
            if len(arr) >= 2:
                port = arr[-1]
                ip = arr[-2].split(".")
                return f'{ip[-1]}:{port}'
            return "NaN"

        return ""

    @property
    def barkUse(self) -> bool:
        return self.barkKey is not None and len(self.barkKey) > 0
    
    @property
    def tokenTime(self) -> str:
        if self.token:
            from Unit.Redis import loadJWTInfo,secondToDate
            import time
            info = loadJWTInfo(self.token)
            if isinstance(info, dict):
                exp = info.get('exp')
                diff = exp - time.time()
                if diff > 60 * 60 * 24 * 6:
                    return "6d+"
                return secondToDate(diff)
        return ""
    
    @property
    def workDragosStr(self):
        dragos = []
        if self.params:
            data = json.loads(self.params)
            dragos = data.get("workDragos", [])
        if dragos:
            return dragos
        return "无"

    @property
    def getKingdomTodayCrystalCount(self) -> int:
        if self.token:
            from Unit.Redis import loadJWTInfo
            from Api.UserInfo import UserInfo
            info = loadJWTInfo(self.token)
            if info:
                kingdomId = info.get('kingdomId')
                if kingdomId:
                    userInfo = UserInfo()
                    userInfo.kingdomId = kingdomId
                    return userInfo.getKingdomTodayCrystalCount()
            return 0
        return 0

    @property
    def getDiffRecordCrystal(self) -> int:
        if self.name:
            from Api.UserInfo import UserInfo
            userInfo = UserInfo()
            userInfo.name = self.name
            return userInfo.getDiffRecordCrystal()
        return 0
        
    def clearParams(self, params):
        members = ['id','email', 'pwd','token', 'name', 'userKey','socks5', 'params']
        for member in members:
            if member in params.keys():
                del params[member]
        params = {k: v for k, v in params.items() if v is not None}
        return params

    def loadParams(self):
        if self.params:
            data = json.loads(self.params)
            data = self.clearParams(data)
            self.__dict__.update(data)
            s = f'({self.getKingdomTodayCrystalCount}=>{self.getDiffRecordCrystal})'
            self.crystalLevel = f'{data.get("minCrystalLevel",0)}-{data.get("maxCrystalLevel",0)}{s}'
            if data.get('isJoinAllianceBattle'):
                battleTroopNum = data.get('battleTroopNum',0)
                self.autoJoinAllianceBattle = f'{self.numberUpString(battleTroopNum)}'
            else:
                self.autoJoinAllianceBattle = '关'

    def numberUpString(self, num) -> str:
        num = int(num)
        if num >= 1000:
            return f'{num//1000}k'
        return f'{num}'
    
    @classmethod
    def paramsKeyMap(cls):
        return {
            'name': "名称",
            'emailSub': "email",
            'socks5Port': "代理",
            'tokenTime': "时间",
            "energy": "能量",
            "crystal": "水晶",
            "maxTroopNum": "出兵数",
            "crystalLevel": "水晶等级",
            "autoJoinAllianceBattle": "自动跟团",
            "workDragosStr": "挖矿龙",
            "autoJoinAllianceTag": "自动加盟",
            "autoPackage": "自动打包",
            # "autoSkill": "自动技能",
            # "autoEat": "自动吃药",
            # "autoBuy": "自动商店",
            "autoLevelUp": "自动升级",
            "autoMinGame": "消消乐",
            "autoGlobalCrystal": "闲时水晶",
            "autoStartRally": "自动开团",
            "onlyMonster": "只打怪",
            # "autoReLogin": "每日登录",
            # "barkUse": "推送",
        }

    @classmethod
    def paramsKeys(cls):
        return list(cls.paramsKeyMap().keys())

    @classmethod
    def findToken(cls, token) -> 'MiningWorkMan':
        from Unit.Redis import loadJWTInfo
        info = loadJWTInfo(token)
        if isinstance(info, dict):
            kingdomId = info.get('kingdomId')
            model = cls.query.filter_by(kingdom_id=kingdomId).first()
            return model

    @classmethod
    def findAndUpdateToken(cls,token):
        model = cls.findToken(token)
        if model:
            logger.debug(f'findAndUpdateToken: {model.email} {model.token} -> {token}')
            model.token = token
            model.save()
            return True
        else:
            logger.debug(f'findAndUpdateToken: {token} not found')
        return False
    
    @classmethod
    def stopAll(cls):
        from Celery.task import revokeTask
        mans:List[MiningWorkMan] = cls.getAll()
        for model in mans:
            task_id = redisHelper.getCeleryTaskId(model.email)
            revokeTask(task_id)
            redisHelper.removeCeleryTaskId(model.email)

    @classmethod
    def updateToken(cls, email, token):
        model: MiningWorkMan = cls.query.filter_by(email=email).first()
        if model:
            model.token = token
            model.save()
            return True
        return False
    
    @classmethod
    def usedSocks(cls):
        models = cls.query.all()
        return [model.socks5 for model in models if model.socks5]

    @classmethod
    def getAll(cls):
        return cls.query.filter_by(active=True).all()

    @classmethod
    def recycleAppleSubIds(cls) -> List[str]:
        """返回没有使用的AppleSubIds"""
        keys = redisHelper.keys("*_helper_apple")
        kingdomIds = [key.decode("utf-8")[:-13] for key in keys]
        currentKingdomIds = [model.kingdom_id for model in cls.getAll()]
        recycleKingdomIds = set(kingdomIds) - set(currentKingdomIds)
        return list(recycleKingdomIds)