
#!/usr/bin/python3
# -*- coding: utf-8 -*-

"""
TokenWorkMan
编写人：darkedge
编写日期：2022年05月08日

"""
import FlaskApp
import datetime
from .HelperModel import HelpModel
db = FlaskApp.db

class TokenWorkMan(db.Model,HelpModel):
    id = db.Column(db.Integer, primary_key=True)
    email = db.Column(db.String(120), unique=True, nullable=True)
    pwd = db.Column(db.String(120), nullable=True)
    name = db.Column(db.String(40), unique=True, nullable=True)
    userKey = db.Column(db.String(36), unique=True, nullable=True)
    appleSubId = db.Column(db.String(32), unique=True, nullable=True)
    kingdom_id = db.Column(db.String(40),nullable=True)
    token = db.Column(db.String(2048), nullable=True)
    active = db.Column(db.<PERSON>an(), default=True)
    confirmed_at = db.Column(db.DateTime(),default=datetime.datetime.now,onupdate=datetime.datetime.now)
    created_at = db.Column(db.DateTime(),default=datetime.datetime.now)
    
    def __str__(self):
        return self.name

    def loadToken(self):
        if self.token:
            from Unit.Redis import loadJWTInfo,redisHelper
            jwt = loadJWTInfo(self.token)
            if jwt:
                self.kingdom_id = jwt.get("kingdomId")
                redisHelper.saveToken(self.token)

    @classmethod
    def findToken(cls, token):
        from Unit.Redis import loadJWTInfo
        info = loadJWTInfo(token)
        if isinstance(info, dict):
            kingdomId = info.get('kingdomId')
            model = cls.query.filter_by(kingdom_id=kingdomId).first()
            return model