#!/usr/bin/python3
# -*- coding: utf-8 -*-

"""
功能描述：User模型
编写人：darkedge
编写日期：2022年05月08日

"""
import datetime

from flask_security import UserMixin

import FlaskApp

db = FlaskApp.db

"""关联"""
roles_users = db.Table(
    "roles_users",
    db.<PERSON>umn("user_id", db.Integer(), db.<PERSON>("user.id")),
    db.<PERSON>("role_id", db.<PERSON><PERSON><PERSON>(), db.<PERSON>("role.id")),
)


class User(db.Model, UserMixin):
    """用户模型"""

    id = db.Column(db.Integer, primary_key=True)
    email = db.Column(db.String(255), unique=True)
    password = db.Column(db.String(255))
    kingdom_id = db.Column(db.String(40), nullable=True)
    # remark = db.Column(db.String(255),nullable=True)
    active = db.Column(db.<PERSON><PERSON>an())
    end_at = db.Column(db.DateTime(), nullable=True)
    confirmed_at = db.Column(
        db.DateTime(), default=datetime.datetime.now, onupdate=datetime.datetime.now
    )
    roles = db.relationship(
        "Role", secondary=roles_users, backref=db.backref("users", lazy="dynamic")
    )

    fs_uniquifier = db.Column(db.String(64), unique=True, nullable=False)

    helpToken = None
    helpTokens = None

    def __str__(self):
        return self.email

    def has_role(self, role):
        has = super().has_role(role)
        if not has:
            if "superuser" in self.roles:
                return True
        return has
