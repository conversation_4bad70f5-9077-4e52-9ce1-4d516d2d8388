#!/usr/bin/python3
# -*- coding: utf-8 -*-

"""
功能描述：处理线程
编写人：darkedge
编写日期：2022年06月24日

"""

import threading
# trunk-ignore(ruff/F401)
import time
import random
from Api.UserInfo import UserInfo,redisHelper

handleWorkThreads = []

class HandleWorkThread(threading.Thread):
    """自动撤回池"""
    def __init__(self,kingdomId,loc,level=30):
        super(HandleWorkThread,self).__init__(daemon=True,name=f'HandleWorkThread-{kingdomId}')
        self.kingdomId = kingdomId
        self.user = UserInfo(f"{kingdomId}")
        self.user.kingdomId = kingdomId
        self.user.level = level
        self.user.loc = loc
        self.user.initLog(needTestS5=False)

        self.stop = False
        self.start()

    def run(self):
        from FlaskApp.Units import workManGoBack
        self.user.log(f"开始自动返回 {self.kingdomId}")
        def goback(token,kingdomId,moId,count=25):
            resultCount = 0
            try:
                resultCount = workManGoBack(moId,count=count)
            except Exception as e:
                self.user.errorLog(f'撤回异常 {e}')
            t = 0.1
            if resultCount > 0:
                t = random.randint(15,30)
            self.user.randomSleep(t,msg=f'web自动多倍撤回{resultCount}次')
            return resultCount

        retryCount = 0
        while not self.stop:
            try:
                fieldTasks = redisHelper.getFieldTasks(self.kingdomId)
                if len(fieldTasks) > 0:
                    self.user.fieldTasks = fieldTasks

                fieldTasks = redisHelper.getFieldTaskAll(self.kingdomId)
                if len(fieldTasks) > 0:
                    [self.user.kingdomAppWithTaskUpdate(task) for task in fieldTasks]

                self.user.gobackOnlyOne(timeout=300,domain=self.user.level == 30, needRefresh=False,gobackCallBack=goback,hourLimit=False)
                t = 0
                if self.user.lastMoTime > 600 or len(self.user.fieldTasks) == 0:
                    t = 60
                    self.user.debuglog("休息等待任务")
                if len(self.user.fieldTasks) == 0:
                    retryCount += 1
                    if retryCount > 10:
                        self.stopThread()
                        break
                else:
                    retryCount = 0
                self.user.randomSleep(10,c=t)

            except Exception as e:
                self.user.errorLog(f"自动翻倍进程异常:{e}",exc_info=True)

    def stopThread(self):
        self.stop = True
        self.kingdomId = None
        self.user = None

def addHandleWork(kingdomId,loc,level=30):
    """添加处理线程"""

    for thread in handleWorkThreads:
        if thread.kingdomId == kingdomId:
            thread.user.loc = loc
            return
    
    dels = []
    for th in handleWorkThreads:
        if not th.kingdomId:
            dels.append(th)
    
    for d in dels:
        handleWorkThreads.remove(d)

    th = HandleWorkThread(kingdomId,loc,level=level)
    handleWorkThreads.append(th)
