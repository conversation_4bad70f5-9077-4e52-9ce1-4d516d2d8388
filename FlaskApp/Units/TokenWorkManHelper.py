#!/usr/bin/python3
# -*- coding: utf-8 -*-

"""
功能描述：TokenWorkMan工具
编写人：darkedge
编写日期：2022年03月11日

"""
import secrets
import random
import threading
import time
from concurrent.futures import ThreadPoolExecutor
from typing import List

from Api.UserInfo import UserInfo, redisHelper
from FlaskApp.Models import TokenWorkMan
from Model.UserError import UserError
from Unit.FileTool import loadS5List
from Unit.Logger import logger

workManUsers: List[UserInfo] = []
threadsLock = threading.BoundedSemaphore(20)
cc = None
guestLock = threading.Lock()
MaxWorkManCount = 20
tokenLock = threading.Lock()
tokenExpireMap = {}


def registerGuest(count):
    value = redisHelper.getTodayRegisterCount()
    if value > 100:
        logger.info(f"今日注册上限 {value}")
        return

    logger.info(f"准备注册 {count}个账号")
    from guestRegister import clearUserKeys, registerWithDynamicProxy

    clearUserKeys()
    registerWithDynamicProxy(
        isV2=True, maxCount=count, autoExitIfChangeEncrypt=False, fackIP=True
    )


def loadGusetUser():
    return
    with guestLock:
        try:
            models = TokenWorkMan.query.filter_by(active=True).all()
            if len(models) < MaxWorkManCount:
                neddCount = MaxWorkManCount - len(models)
                userKeys = redisHelper.allUserKey()
                if len(userKeys) > 0:
                    tmpUserKeys = userKeys[:neddCount]
                    for userKey in tmpUserKeys:
                        redisHelper.removeToken(userKey=userKey)
                        redisHelper.removeUserKey(userKey)
                        TokenWorkMan(userKey=userKey).save()
                        neddCount -= 1
                if neddCount > 0:
                    th = threading.Thread(
                        target=registerGuest, args=(2,), name="register", daemon=True
                    )
                    th.start()

        except Exception as e:
            logger.error(f"loadGusetUser error:{e}", exc_info=True)


def randomDelayLoadAllTokenWorkMan(cache):
    global workManUsers, cc
    cc = cache
    # workManUsersCache = cache.get("workManUsers")
    # if workManUsersCache is None:
    #     cache.set("workManUsers",workManUsers)
    # else:
    #     workManUsers = workManUsersCache

    # def load():
    #     sleepT = random.randint(1,3)
    #     print(f"休息{sleepT}秒")
    #     time.sleep(sleepT)
    #     workManUsersLoad = cache.get("workManUsersLoad")
    #     workManUsersCache = cache.get("workManUsers")
    #     print(f"workManUsersLoad:{workManUsersLoad}")
    #     if workManUsersLoad is None:
    #         cache.set("workManUsersLoad",1)
    #         cache.set("workManUsers",workManUsers)
    #         loadAllTokenWorkMan()
    #     else:
    #         global workManUsers
    #         workManUsers = workManUsersCache

    # th = threading.Thread(target=load,daemon=True,name="loadAllTokenWorkMan")
    # th.start()


def loadAllTokenWorkMan(active=True):
    """加载所有token用户"""
    print("读取配置")
    # return
    tokenWorkMans: List[TokenWorkMan] = TokenWorkMan.query.filter_by(
        active=active
    ).all()
    s5List = loadS5List()
    if len(tokenWorkMans):
        print(f"读取到token用户数量:{len(tokenWorkMans)}")
    for tokenWorkMan in tokenWorkMans:
        u1 = UserInfo(
            tokenWorkMan.email,
            tokenWorkMan.pwd,
            userKey=tokenWorkMan.userKey,
            appleSubId=tokenWorkMan.appleSubId,
            socks5=secrets.choice(s5List),
            saveSocks5=True,
        )
        u1.noWSUser = True
        initWorkManWithUserInfo(u1)


def loadTokenWorkMan(ids):
    """加载工作线程中的用户"""
    s5List = loadS5List()
    for id in ids:
        model: TokenWorkMan = TokenWorkMan.query.filter_by(id=id).first()
        if model:
            u1 = UserInfo(
                model.email,
                model.pwd,
                userKey=model.userKey,
                appleSubId=model.appleSubId,
                socks5=secrets.choice(s5List),
                saveSocks5=True,
            )
            u1.noWSUser = True
            initWorkManWithUserInfo(u1)


def saveWorkManWithList(accounts):
    """保存工作线程中的用户"""
    for account in accounts:
        if not account:
            continue
        model = TokenWorkMan.query.filter_by(userKey=account).first()
        if model:
            model.active = True
            model.save()
            # print("保存账号成功1")
        else:
            TokenWorkMan(userKey=account).save()
            # print("保存账号成功2")


def initWorkManWithUserInfo(user: UserInfo):
    """初始化工作线程"""
    removeUserInfoInWorkMan(user)
    user.autoExitIfChangeEncrypt = False
    th = threading.Thread(
        target=threadWorkMan, args=(user,), daemon=True, name=f"workManinit_{user.key}"
    )
    th.start()


def removeUserInfoInWorkMan(user: UserInfo):
    """从工作线程中移除用户"""
    for userInfo in workManUsers:
        if userInfo.key == user.key:
            workManUsers.remove(userInfo)
            break


def threadWorkMan(u1: UserInfo):
    """工作线程"""
    from FlaskApp import app  # 在函数开始处导入 Flask app

    threadsLock.acquire()
    u1.randomSleep(1, 20, msg="启动等待")
    try:
        u1.invalidStatu = False
        u1.isWebDevice = True
        model: TokenWorkMan
        with app.app_context():  # 使用应用上下文
            if u1.userKey:
                model = TokenWorkMan.query.filter_by(userKey=u1.userKey).first()
            elif u1.appleSubId:
                model = TokenWorkMan.query.filter_by(appleSubId=u1.appleSubId).first()
            else:
                model = TokenWorkMan.query.filter_by(email=u1.email).first()

            token = redisHelper.getToken(
                u1.email, u1.userKey, kingdomId=u1.kingdomId, appleSubId=u1.appleSubId
            )
            if token:
                u1.token = token
                u1.initLog()
                methods = [u1.wallInfo, u1.questMain, u1.taskAll, u1.kingdomProfileMy]
                secrets.choice(methods)()

            else:
                u1.token = None
                u1.login()
                u1.wsWithKingdomApp()

            if not u1.isInvalid:
                workManUsers.append(u1)
                tokenExpireMap[u1.token] = time.time()
                if model:
                    if model.token != u1.token or not model.active:
                        model.token = u1.token
                        model.active = True
                        model.save()
            else:
                if model:
                    model.active = False
                    model.token = None
                    model.save()

    except UserError as e:
        errorCode = e.errorCode
        codes = [
            41,
            6,
            31,
        ]
        if errorCode in codes:
            u1.log("号没了")
            u1.invalidStatu = True
            u1.clearSelf()
            twm: TokenWorkMan = model
            # if u1.email:
            #     twm = TokenWorkMan.query.filter_by(email=u1.email).first()
            # elif u1.userKey:
            #     twm = TokenWorkMan.query.filter_by(userKey=u1.userKey).first()
            if twm:
                twm.active = False
                twm.token = None
                twm.save()
                twm.deleteSelf()
                u1.log(f"禁用账号:{u1.key}")
            else:
                u1.log("没有找到数据？？")
            # 查找/获取缓存号
            loadGusetUser()
        elif errorCode == -4:
            u1.clearSelf()
            u1.invalidStatu = True
            initWorkManWithUserInfo(u1)
        # elif errorCode == 42:
        #     pass
        else:
            u1.errorLog(f"threadWorkMan 错误:{e.message}")
    except Exception as e:
        logger.error(f"threadWorkMan 错误:{e}", exc_info=True)
    finally:
        threadsLock.release()


def clearInvalidUsers():
    """清除工作线程中的无效用户"""
    for user in workManUsers:
        if user.isInvalid:
            removeUserInfoInWorkMan(user)


def findUserWithToken(token):
    for user in workManUsers:
        if user.token == token:
            return user
    return None


def returnWorkManUserWithCount(count, isGoogle=False) -> List[UserInfo]:
    """返回工作线程中的用户"""
    clearInvalidUsers()
    tokenWorkMans: List[TokenWorkMan] = None
    if isGoogle:
        tokenWorkMans = TokenWorkMan.query.filter(
            # trunk-ignore(ruff/E712)
            # trunk-ignore(ruff/E711)
            TokenWorkMan.active == True, TokenWorkMan.appleSubId == None
        ).all()
    else:
        tokenWorkMans = TokenWorkMan.query.filter(
            # trunk-ignore(ruff/E712)
            # trunk-ignore(ruff/E711)
            TokenWorkMan.active == True, TokenWorkMan.appleSubId != None
        ).all()
        if len(tokenWorkMans) == 0:
            # 没有apple账号，使用游客号
            tokenWorkMans = TokenWorkMan.query.filter(
                # trunk-ignore(ruff/E712)
                # trunk-ignore(ruff/E711)
                TokenWorkMan.active == True, TokenWorkMan.userKey != None
            ).all()

    if len(tokenWorkMans) < count:
        if len(tokenWorkMans) > 0 and len(workManUsers) > count / 2:
            count = len(workManUsers)
        else:
            print(f"{len(tokenWorkMans)} 不满足需要数量{count}")
            # loadAllTokenWorkMan()
            loadGusetUser()
            return None
    sampleMans = random.sample(tokenWorkMans, count)
    users = []
    s5List = loadS5List()
    for user in sampleMans:
        token = (
            redisHelper.getToken(
                user.email,
                user.userKey,
                kingdomId=user.kingdom_id,
                appleSubId=user.appleSubId,
            )
            or user.token
        )
        u = UserInfo(
            user.email,
            user.pwd,
            userKey=user.userKey,
            appleSubId=user.appleSubId,
            socks5=secrets.choice(s5List),
            saveSocks5=True,
            token=token,
        )
        if user.kingdom_id:
            u.kingdomId = user.kingdom_id
        u.autoExitIfChangeEncrypt = False
        u.isWebDevice = True
        if not token:
            initWorkManWithUserInfo(u)
            continue
            # u.login()
        else:
            u1 = findUserWithToken(token)
            if u1:
                u = u1
            else:
                u.initLog(needTestS5=False)
        if u.checkWSWithKingdomApp():
            u.wsWithKingdomApp()
        users.append(u)
    return users


def workManGoBack(moId, count=25):
    users = returnWorkManUserWithCount(count)
    if users:
        u1 = users[0]
        tasks = [user.kingdomSupportReturnA(moId) for user in users]
        results = u1.runTasks(tasks)
        bugCount = 0
        needCheck = False
        for result in results[0]:
            try:
                if result.result():
                    bugCount += 1
            except UserError as e:
                if e.errorCode == -4:
                    needCheck = True
                else:
                    logger.error(e)
            except Exception as e:
                logger.error(e, exc_info=True)
        if needCheck:
            for user in users:
                if user.isInvalid:
                    user.clearSelf()
                    initWorkManWithUserInfo(user)
            if bugCount == 0:
                bugCount = -1
        return bugCount
    else:
        return -1


def workManClaimPkg(pkgId, count=5):
    users = returnWorkManUserWithCount(count)
    if users:
        u1 = users[0]
        tasks = [user.pkgDailyFreeClaimA(pkgId) for user in users]
        results = u1.runTasks(tasks)
        bugCount = 0
        needCheck = False
        for result in results[0]:
            try:
                if result.result():
                    bugCount += 1
            except UserError as e:
                if e.errorCode == -4:
                    needCheck = True
                else:
                    logger.error(e)
            except Exception as e:
                logger.error(e, exc_info=True)
        if needCheck:
            for user in users:
                if user.isInvalid:
                    user.clearSelf()
                    initWorkManWithUserInfo(user)
            if bugCount == 0:
                bugCount = -1
        return bugCount
    else:
        return -1


def workManCanUseToken() -> str:
    """工作线程中是否有可用token"""
    tokens = workManCanUseTokenWithCount(1)
    if tokens:
        return tokens[0]
    return None


def workManCanUseTokenWithCount(count, isGoogle=False) -> List[str]:
    """工作线程中是否有可用token"""
    users = returnWorkManUserWithCount(count, isGoogle=isGoogle)

    def tryCheckUser(user: UserInfo):
        try:
            token = user.token
            t = tokenExpireMap.get(token)
            if t:
                if time.time() - t < 60 * 30:
                    logger.info("小于间隔,跳过")
                    return token
            tokenExpireMap[token] = time.time()

            # [user.wallInfo,user.taskAll,user.dragoLairList,user.itemList]
            methods = [user.taskAll]
            secrets.choice(methods)()
        except UserError as e:
            if e.errorCode == -4:
                user.clearSelf()
                initWorkManWithUserInfo(user)
                return None
        if not user.isInvalid:
            return user.token

    if users:
        with tokenLock:
            with ThreadPoolExecutor(
                max_workers=count, thread_name_prefix="workManCanUseTokenWithCountCheck"
            ) as executor:
                results = executor.map(tryCheckUser, users)
                return list(filter(None, results))
    loadGusetUser()
    return None


def refreshUseKey(userKey):
    """刷新用户key"""
    s5List = loadS5List()
    u1 = UserInfo(userKey=userKey, socks5=secrets.choice(s5List))
    initWorkManWithUserInfo(u1)


def loadAppleAccount(count):
    """加载苹果账号"""
    accounts = redisHelper.getAllAppleSubId()
    if len(accounts) < count:
        return False
    for _i in range(count):
        u = redisHelper.getOneAppleSubId()
        if u:
            TokenWorkMan(appleSubId=u).save()
    return True
