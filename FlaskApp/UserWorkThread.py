#!/usr/bin/python3
# -*- coding: utf-8 -*-

"""
功能描述：用户端线程
编写人：darkedge
编写日期：2022年03月11日

"""

import threading
# import time
# import sys
import random
# import os.path as op

# SCRIPT_DIR = op.dirname(op.abspath(__file__))
# sys.path.append(op.dirname(SCRIPT_DIR))

from Api.UserInfo import UserInfo
from Unit.Redis import redisHelper
from Unit.FileTool import loadS5List
from flask_caching import Cache
from typing import List
from .Models import MiningWorkMan

s5List = loadS5List()
cache:Cache = None
class WebWorkThread(threading.Thread):
    def __init__(self,user:UserInfo,userId:str,token=None,email=None,password=None,isDebug=False):
        threading.Thread.__init__(self)
        self.daemon = True
        self.user:UserInfo = user
        self.userId = userId
        self.token = token
        self.email = email
        self.password = password
        self.isDebug = isDebug
        self.workStatus = False
        self.workType = 0
        self.target = None
        self.args = None
        self.start()

    @property
    def workName(self):
        works = ["无","自动跟团","自动驻守","战争跟团","自动侦查","1骑骚扰","战争检测",f"一骑当千 - {len(self.user.fieldTasks)}"]
        return works[self.workType]

    def run(self):
        from Model.UserError import UserError
        canRun = True
        try:
            while not self.user.isInvalid and canRun:
                if callable(self.target):
                    self.workStatus = True
                    canRun = False
                    self.target(*self.args)
                else:
                    self.user.randomSleep(1)
        except UserError as e:
            self.user.log(e)
        except Exception as e:
            self.user.errorLog(e,exc_info=True)

userThreads:List[WebWorkThread] = []
tokenUsers:List[UserInfo] = {}
def setCache(c):
    from FlaskApp.Units.TokenWorkManHelper import randomDelayLoadAllTokenWorkMan
    global cache,userThreads,tokenUsers
    cache = c
    userThreadsCache = cache.get("userThreads")
    if userThreadsCache is None:
        cache.set("userThreads",userThreads)
    else:
        userThreads = userThreadsCache

    tokenUsersCache = cache.get("tokenUsers")
    if tokenUsersCache is None:
        cache.set("tokenUserThreads",tokenUsers)
    else:
        tokenUsers = tokenUsersCache

    randomDelayLoadAllTokenWorkMan(cache)
    

def findTokenUser(token:str) -> UserInfo:
    for t in tokenUsers:
        if t == token:
            return tokenUsers[t]
    return None    

def findUserThread(userId:str) -> WebWorkThread:
    for t in userThreads:
        if t.userId == userId:
            if t.user.isInvalid or t.user.loc is None:
                userThreads.remove(t)
                return None
            return t
    return None

def changeSocks5(user:UserInfo):
    socks5=random.choice(s5List)
    user.hasInit = False
    user.socks5 = socks5
    user.initLog()

def logoutUser(userId:str):
    for t in userThreads:
        if t.userId == userId:
            t.user.invalidStatu = True
            userThreads.remove(t)
            return

def startWorkInThread(th:WebWorkThread,workType=1,args=()):
    if th is None or th.user.isInvalid:
        return False
    target = None
    # enableAutoEnergy()
    if workType == 1:
        target = tryAutoJoinBattle
    elif workType == 2:
        target = tryAutoGarrison
    elif workType == 3:
        target = tryAutoJoinWarBattle
    elif workType == 4:
        target = tryAutoHarassment
    elif workType == 5:
        target = tryAutoHarassmentAttack
    elif workType == 6:
        target = tryCacheAllianceBattleList
    elif workType == 7:
        target = tryAutoGoBack

    if not target:
        return False
    th.workType = workType
    th.target = target
    th.args = (th.user,) + args
    # th.run()
    return True

def buildUser(userId,token=None,email=None,password=None,isDebug=False,userEmail=None) -> UserInfo:
    if len(token) == 0:
        token = None
    else:
        user = buildTokenUser(token)
        if user:
            userThread = WebWorkThread(user, userId,token=token,email=email,password=password)
            userThreads.append(userThread)
            user.log(f'用户{userEmail} 登录账号 {user.key}')
        return user
    if len(email) < 10:
        email = None
    
    user = UserInfo(token=token,email=email,pwd=password,socks5=random.choice(s5List))
    if user.email is None:
        user.email = f'{random.randint(1111111, 99999999)}@webuser.com'
    try:
        if user.isLogin:
            user.noWSUser = True
            # user.initLog()
            # user.kingdomProfileMy()
        if user.login():
            if not user.noWSUser:
                user.wsWithKingdomApp(isDebug=True)
            if user.isBug:
                user.wsGetFields(power=1)
        # user.kingdomProfileMy()
    except Exception:
        pass
    finally:
        if not user.isInvalid and user.loc is not None:
            userThread = WebWorkThread(user, userId,token=token,email=email,password=password)
            userThreads.append(userThread)
            user.log(f'用户{userEmail} 登录账号 {user.name}')
            # redisHelper.saveWebUser(userId, user)
        else:
            user = None

    return user

def buildTokenUser(token:str,wokrnUser=None) -> UserInfo:
    """生成token用户"""
    user = tokenUsers.get(token)
    if not user and wokrnUser:
        token = wokrnUser["token"]
        user = tokenUsers.get(token)
        if not user:
            model = MiningWorkMan.findToken(token)
            if model:
                user = UserInfo(model.email, token=token, socks5=model.socks5)
                user.loadKingdomId()
            else:
                user = UserInfo(wokrnUser["name"]+"@gmail.com",token=wokrnUser["token"],socks5=wokrnUser["socks5"])
                user.loadEmailWithToken()
    else:
        if len(token) == 0:
            return None
        user = UserInfo(f'{random.randint(1111111, 99999999)}@token.com',token=token,socks5=random.choice(s5List))
        user.loadEmailWithToken()
    user.noWSUser = True
    try:
        user.initLog(needTestS5=False)
        if user.marchSize == 0:
            user.getTroops()
    except Exception as e:
        print(e)
        pass
    finally:
        if not user.isInvalid:
            kingdomInfo = redisHelper.getKingdomInfo(user.tokenWithKingomId())
            if kingdomInfo:
                user.setKingdomInfo(kingdomInfo)
                # user.loc = [20,638,1403]
            if user.token:
                tokenUsers[user.token] = user
            workDragos = redisHelper.getWorkDragos(user.kingdomId)
            if workDragos:
                user.workDragos = [int(i) for i in workDragos]
            user.log(f'token:{user.name or user.key} 登录成功')
        else:
            user = None
    return user

def tryAutoJoinBattle(user:UserInfo,followInfo):
    troopNum = int(followInfo[2])
    detailKillList = {}
    checkList = [int(v) for v in followInfo[0]]
    minList = [int(v) for v in followInfo[1]]

    anyLevel = 9
    if checkList[0] == 1:
        anyLevel = minList[0]
    
    detailKillList["anyLevel"] = anyLevel

    greenLv = 9
    if checkList[1] == 1:
        greenLv = minList[1]
    detailKillList["20200202"] = greenLv

    redLv = 9
    if checkList[2] == 1:
        redLv = minList[2]
    detailKillList["20200203"] = redLv

    goldLv = 9
    if checkList[3] == 1:
        goldLv = minList[3]
    detailKillList["20200204"] = goldLv

    user.log(f"开启自动跟团 出兵:{troopNum} {detailKillList}")
    t = 0
    while not user.isInvalid:
        t += 1
        if t % 10 == 0:
            user.troopRecover()
        if user.checkWSWithKingdomApp():
            if not user.noWSUser:
                user.trySetDeviceInfo()
            user.wsWithKingdomApp()

        if user.autoJoinBattle(troopNum=troopNum,detailKillList=detailKillList,autoEnergy=True):
            user.randomSleep(30)
            if t % 2 == 0:
                user.tryHelpAll()
            elif t % 120 == 0:
                user.tryHarvestAll()

        else:
            user.invalidStatu = True
            user.log("自动跟团结束")
            break

def tryAutoGarrison(user:UserInfo,garrisonInfo):
    locx = int(garrisonInfo["locx"])
    locy = int(garrisonInfo["locy"])
    mainTroop = int(garrisonInfo["troopGroup"])
    troopNum = int(garrisonInfo["num"])
    user.log(f"开启自动驻防 出兵:{troopNum} {locx},{locy} {mainTroop}")
    loc = [locx,locy]

    while not user.isInvalid:
        if user.checkWSWithKingdomApp():
            if not user.noWSUser:
                user.trySetDeviceInfo()
            user.wsWithKingdomApp()
        
        if user.startGarrisonWithMainTroopAndNum(loc,mainTroop,troopNum):
            user.randomSleep(5)
        else:
            user.randomSleep(10,msg="自动驻防失败")

def tryAutoJoinWarBattle(user:UserInfo,followInfo):
    leagueKingdomName = followInfo["leagueKingdomName"]
    troopNum = int(followInfo["num"])

    user.log(f"开启战争自动跟团 出兵:{troopNum} 团长:{leagueKingdomName}")
    t = 0
    while not user.isInvalid:
        t += 1
        if t % 10 == 0:
            user.troopRecover()
        if user.checkWSWithKingdomApp():
            if not user.noWSUser:
                user.trySetDeviceInfo()
            user.wsWithKingdomApp()

        if user.joinWarfareAllianceBattle(troopNum,leagueKingdomName):
            user.randomSleep(10)

        else:
            user.randomSleep(20)

def tryAutoHarassment(user:UserInfo,followInfo):
    locx = int(followInfo["locx"])
    locy = int(followInfo["locy"])

    user.log(f"开启战争自动侦查{locx},{locy}")

    user.getTroops()
    while not user.isInvalid:
        user.taskAll()
        if len(user.fieldTasks) < user.marchLimit:
            if user.startInvestigation([locx,locy]):
                user.randomSleep(3)
            else:
                user.randomSleep(10,msg="自动侦查失败")

def tryAutoHarassmentAttack(user:UserInfo,followInfo):
    locx = int(followInfo["locx"])
    locy = int(followInfo["locy"])

    user.log(f"开启战争自动骚扰{locx},{locy}")

    user.getTroops()
    while not user.isInvalid:
        user.taskAll()
        if len(user.fieldTasks) < user.marchLimit:
            if user.harassmentAttack([locx,locy]):
                user.randomSleep(3)
            else:
                user.randomSleep(10,msg="自动攻击失败")

def tryCacheAllianceBattleList(user:UserInfo):
    user.log("开始缓存联盟战争列表")
    while not user.isInvalid:
        user.cacheAllianceBattleList()
        user.randomSleep(10)

def tryAutoGoBack(user:UserInfo):
    from FlaskApp.Units import workManGoBack
    user.log("开始自动返回")
    def goback(token,kingdomId,moId):
        count = workManGoBack(moId)
        t = 0.1
        if count > 0:
            t = random.randint(10,15)
        user.randomSleep(t,msg=f'web自动多倍撤回{count}次')
        return count

    while not user.isInvalid:
        try:
            user.gobackOnlyOne(domain=True, needRefresh=False,gobackCallBack=goback,hourLimit=False)
        except Exception as e:
            user.errorLog(e,exc_info=True)
        t = 0
        if user.lastMoTime > 600:
            t = 60
        user.randomSleep(10,c=t)

