#!/usr/bin/python3
# -*- coding: utf-8 -*-

"""
AdminUserModelView
编写人：darkedge
编写日期：2022年05月08日

"""

from .MyModelView import Models, MyModelView, db
import uuid


class AdminUserModelView(MyModelView):
    column_exclude_list = [
        "password",
        "fs_uniquifier",
        "kingdom_id"
    ]
    form_excluded_columns = (
        "password",
        "fs_uniquifier",
        "kingdom_id"
    )
    column_searchable_list = ["email"]
    
    def __init__(
        self,
        model=None,
        session=None,
        name=None,
        category=None,
        endpoint=None,
        url=None,
        static_folder=None,
        menu_class_name=None,
        menu_icon_type=None,
        menu_icon_value=None,
    ):
        if not model:
            model = Models.User

        if not session:
            session = db.session

        super(AdminUserModelView, self).__init__(
            model,
            session,
            name=name,
            category=category,
            endpoint=endpoint,
            url=url,
            static_folder=static_folder,
            menu_class_name=menu_class_name,
            menu_icon_type=menu_icon_type,
            menu_icon_value=menu_icon_value,
        )
    
    def on_model_change(self, form, model, is_created):
        """
        在新建用户时自动生成 fs_uniquifier
        """
        if is_created:
            model.fs_uniquifier = uuid.uuid4().hex
        super(AdminUserModelView, self).on_model_change(form, model, is_created)
