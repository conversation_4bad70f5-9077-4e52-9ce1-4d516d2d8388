 #!/usr/bin/python3
# -*- coding: utf-8 -*-

'''
建筑
编写人：darkedge
编写日期：2022年05月08日

'''

from .WorkView import WorkView
from flask_security import current_user
from flask import url_for, redirect, render_template, request, abort,jsonify
from flask_admin import expose
from FlaskApp.UserWorkThread import UserInfo,WebWorkThread,userThreads,findUserThread,UserInfo,buildUser,buildTokenUser,startWorkInThread,logoutUser,setCache,changeSocks5,buildTokenUser,findTokenUser
import Unit.enum as Enum
import json
import time

class BuildView(WorkView):
    def __init__(self):
        super(BuildView, self).__init__()
        self.endpoint = 'buildlist'
        self.indexHtml = 'buildlist.html'
        self.name = "5号技师"
        self.needRole = "vipuser"
        locations = [v for v in range(2,11)] + [v for v in range(100,121)]
        builds = {}

        for k in Enum.BUILDING_CODE_MAP:
            v = Enum.BUILDING_CODE_MAP[k]
            name = Enum.BUILDING_CODE_NAME_MAP[k]
            builds[name] = v

        self.otherInfo = {
            'locations': locations,
            'builds': builds
        }
        
    # @expose('/treasurelist/', methods=('GET', 'POST'))
    # def treasurelist_api(self):
    #     userThread = findUserThread(userId=current_user.id)
    #     if userThread:
    #         user = userThread.user
    #         res = user.canUpgradeTreasureList(isSuper=current_user.has_role('superuser'))
    #         if res is not None:
    #             return jsonify({"code":200,"data":res})
    #     return jsonify({"code":201 ,"msg":"异常"})

    # @expose('/treasure_upgrade/', methods=('GET', 'POST'))
    # def treasure_upgrade_api(self):
    #     userThread = findUserThread(userId=current_user.id)
    #     if userThread:
    #         user = userThread.user
    #         treasureId = request.form.get('treasureId')
    #         index = int(request.form.get('index'))

    #         res = user.tryUpgradeTreasure(treasureId, index)
    #         return jsonify({"code":200,"count":res})
    #     return jsonify({"code":201 ,"msg":"异常"})

    # @expose('/treasure_generate/', methods=('GET', 'POST'))
    # def treasure_generate_api(self):
    #     userThread = findUserThread(userId=current_user.id)
    #     if userThread:
    #         user = userThread.user
    #         code = request.form.get('code')
    #         if code:
    #             res = user.treasureOpen(itemCode=code)
    #             return jsonify({"code":200,"count":res})
    #     return jsonify({"code":201 ,"msg":"异常"})

    @expose('/rebuild/', methods=('GET', 'POST'))
    def rebuild_api(self):
        userThread = findUserThread(userId=current_user.id)
        if userThread:
            user = userThread.user
            buildId = request.form.get('buildId')
            localId = request.form.get('localId')
            if buildId and localId:
                user.build(localId,buildId,False,1)
                time.sleep(5)
                if user.build(localId,buildId,False,1):
                    return jsonify({"code":200,"msg":"ok"})

        return jsonify({"code":201 ,"msg":"异常"})

    @expose('/levelup/', methods=('GET', 'POST'))
    def levelup_api(self):
        # userThread = findUserThread(userId=current_user.id)
        # if userThread:
        #     user = userThread.user
        #     buildId = request.form.get('buildId')
        #     localId = request.form.get('localId')
        #     if buildId and localId:
        #         user.build(localId,buildId,False,1)
        #         time.sleep(5)
        #         if user.build(localId,buildId,False,1):
        #             return jsonify({"code":200,"msg":"ok"})

        return jsonify({"code":201 ,"msg":"异常"})

    