#!/usr/bin/python3
# -*- coding: utf-8 -*-

"""
CVC黑名单列表
编写人：darkedge
编写日期：2022年05月08日

"""

# trunk-ignore(ruff/F401)
from flask import abort, jsonify, redirect, render_template, request, url_for, flash

import FlaskApp
import FlaskApp.Views as Views
from Celery.redis import redisCeleryHelper

from flask_admin.babel import lazy_gettext, ngettext
from flask_admin.actions import action

db = FlaskApp.db


class CVCBlockModel(db.Model):
    id = db.Column(db.String, primary_key=True)
    kingdomId = db.Column(db.String(24), nullable=True)
    name = db.Column(db.String(24), nullable=True)
    worldId = db.Column(db.Integer, nullable=True)
    @classmethod
    def getWithKingdomId(cls, kingdomId, name=None, worldId=None):
        model = cls()
        model.id = kingdomId
        model.kingdomId = kingdomId
        kingdomInfo = redisCeleryHelper.getCvcKingdomInfo(kingdomId)
        if not kingdomInfo:
            kingdomInfo = {}
        model.name = name or kingdomInfo.get("name")
        model.worldId = worldId or kingdomInfo.get("worldId")
        return model


class CVCBlockListView(Views.MyModelView):
    def __init__(
        self,
        model=None,
        session=None,
        name=None,
        category=None,
        endpoint=None,
        url=None,
        static_folder=None,
        menu_class_name=None,
        menu_icon_type=None,
        menu_icon_value=None,
    ):

        super(CVCBlockListView, self).__init__(
            CVCBlockModel,
            session,
            name=name,
            category=category,
            endpoint=endpoint,
            url=url,
            static_folder=static_folder,
            menu_class_name=menu_class_name,
            menu_icon_type=menu_icon_type,
            menu_icon_value=menu_icon_value,
        )
        self.endpoint = "cvcblock"
        self.indexHtml = "cvcblock.html"
        self.name = "黑名单"
        self.category = "独享"
        self.needRole = "superuser"

    page_size = 200

    def get_list(
        self,
        page,
        sort_column,
        sort_desc,
        search,
        filters,
        execute=True,
        page_size=None,
    ):
        datas = redisCeleryHelper.getCvcBlockUsers()
        models = []
        for data in datas:
            model = CVCBlockModel.getWithKingdomId(data)
            models.append(model)

        return len(models), models

    # def validate_form(self, form):
    #     return True

    def get_one(self, id):
        model = CVCBlockModel.getWithKingdomId(id)

        return model

    def on_model_change(self, form, model, is_created):
        if not is_created:
            redisCeleryHelper.setCvcKingdomInfo(model.kingdomId,{"name":model.name, "worldId":model.worldId})

    def create_model(self, form):
        kingdomId = form.data.get("kingdomId")
        name = form.data.get("name")
        worldId = form.data.get("worldId")
        redisCeleryHelper.setCvcKingdomInfo(kingdomId, {"name":name, "worldId":worldId})
        model = CVCBlockModel.getWithKingdomId(kingdomId, name, worldId)
        redisCeleryHelper.addCvcBlockUsers(model.kingdomId)

        return model

    def delete_model(self, model):
        redisCeleryHelper.removeCvcBlockUsers(model.kingdomId)
        return True

    @action('delete',
            lazy_gettext('Delete'),
            lazy_gettext('Are you sure you want to delete selected records?'))
    def action_delete(self, ids):
        count = len(ids)
        for id in ids:
            model = self.get_one(id)
            self.delete_model(model)
        flash(ngettext('Record was successfully deleted.',
                           '%(count)s records were successfully deleted.',
                           count,
                           count=count), 'success')
