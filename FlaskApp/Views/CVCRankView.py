#!/usr/bin/python3
# -*- coding: utf-8 -*-

"""
CVC排名View
编写人：darkedge
编写日期：2025年01月07日
"""

from .WorkView import WorkView
from flask import request, jsonify, Response
from flask_admin import expose
from FlaskApp.UserWorkThread import UserInfo
import datetime
from Celery.redis import redisCeleryHelper


class CVCRankView(WorkView):
    def __init__(self):
        super(CVCRankView, self).__init__()
        self.endpoint = 'cvcrank'
        self.indexHtml = 'admin/cvcrank.html'
        self.name = "CVC排名"
        self.category = "独享"
        self.needRole = "superuser"
        self.startTime = datetime.datetime.now()

    @expose('/', methods=('GET', 'POST'))
    def index_view(self):
        u1 = UserInfo()
        ranks = u1.getCVCRankList()
        white_list = u1.cvcRankSafeKingdoms()
        black_list = redisCeleryHelper.getCvcBlockUsers()
        return self.render(self.indexHtml,
                         ranks=ranks,
                         white_list=white_list,
                         black_list=black_list)

    @expose('/black_list/', methods=['POST'])
    def black_list_api(self) -> Response:
        """处理拉黑玩家请求。"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({
                    'success': False,
                    'message': '无效的请求数据'
                }), 400
            # 获取必要参数
            kingdomId = data.get('_id')
            name = data.get('name')
            worldId = data.get('worldId')
            if not all([kingdomId, name, worldId]):
                return jsonify({
                    'success': False,
                    'message': '缺少必要参数'
                }), 400
            # 先移除白名单
            u1 = UserInfo()
            u1.cvcRankSafeKingdomRemove(kingdomId)
            # 再添加黑名单
            redisCeleryHelper.setCvcKingdomInfo(kingdomId, {"name":name, "worldId":worldId})
            redisCeleryHelper.addCvcBlockUsers(kingdomId)
            return jsonify({
                'success': True,
                'message': f'成功拉黑玩家 {name}'
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'服务器错误: {str(e)}'
            }), 500

    @expose('/white_list/', methods=['POST'])
    def white_list_api(self) -> Response:
        """处理添加白名单玩家请求。"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({
                    'success': False,
                    'message': '无效的请求数据'
                }), 400
            # 获取必要参数
            kingdomId = data.get('_id')
            name = data.get('name')
            worldId = data.get('worldId')
            if not all([kingdomId, name, worldId]):
                return jsonify({
                    'success': False,
                    'message': '缺少必要参数'
                }), 400
            # 先移除黑名单
            redisCeleryHelper.removeCvcBlockUsers(kingdomId)
            # 再添加白名单
            redisCeleryHelper.setCvcKingdomInfo(kingdomId, {"name":name, "worldId":worldId})
            UserInfo().cvcRankSafeKingdom(kingdomId)
            return jsonify({
                'success': True,
                'message': f'成功将玩家 {name} 加入白名单'
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'服务器错误: {str(e)}'
            }), 500
