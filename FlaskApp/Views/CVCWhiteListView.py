#!/usr/bin/python3
# -*- coding: utf-8 -*-

"""
CVC白名单列表
编写人：AI Assistant
编写日期：2024年

"""

from flask import flash

import FlaskApp
import FlaskApp.Views as Views
from Api.UserInfo import UserInfo
from Celery.redis import redisCeleryHelper
from FlaskApp.Views.CVCBlockListView import CVCBlockModel

from flask_admin.babel import lazy_gettext, ngettext
from flask_admin.actions import action

db = FlaskApp.db


class CVCWhiteListView(Views.MyModelView):
    def __init__(
        self,
        model=None,
        session=None,
        name=None,
        category=None,
        endpoint=None,
        url=None,
        static_folder=None,
        menu_class_name=None,
        menu_icon_type=None,
        menu_icon_value=None,
    ):
        super(CVCWhiteListView, self).__init__(
            CVCBlockModel,
            session,
            name=name,
            category=category,
            endpoint=endpoint,
            url=url,
            static_folder=static_folder,
            menu_class_name=menu_class_name,
            menu_icon_type=menu_icon_type,
            menu_icon_value=menu_icon_value,
        )
        self.endpoint = "cvcwhitelist"
        self.indexHtml = "cvcwhitelist.html"
        self.name = "白名单"
        self.category = "独享"
        self.needRole = "superuser"

    page_size = 200

    def get_list(
        self,
        page,
        sort_column,
        sort_desc,
        search,
        filters,
        execute=True,
        page_size=None,
    ):
        user = UserInfo()
        datas = user.cvcRankSafeKingdoms()
        models = []
        for data in datas:
            model = CVCBlockModel.getWithKingdomId(data)
            models.append(model)

        return len(models), models

    def get_one(self, id):
        model = CVCBlockModel.getWithKingdomId(id)
        return model

    def create_model(self, form):
        kingdomId = form.data.get("kingdomId")
        name = form.data.get("name")
        worldId = form.data.get("worldId")
        redisCeleryHelper.setCvcKingdomInfo(kingdomId, {"name":name, "worldId":worldId})
        model = CVCBlockModel.getWithKingdomId(kingdomId, name, worldId)
        UserInfo().cvcRankSafeKingdom(kingdomId)
        return model

    def delete_model(self, model):
        UserInfo().cvcRankSafeKingdomRemove(model.kingdomId)
        return True
    
    @action('delete',
            lazy_gettext('Delete'),
            lazy_gettext('Are you sure you want to delete selected records?'))
    def action_delete(self, ids):
        count = len(ids)
        for id in ids:
            model = self.get_one(id)
            self.delete_model(model)
        flash(ngettext('Record was successfully deleted.',
                      '%(count)s records were successfully deleted.',
                      count,
                      count=count), 'success') 