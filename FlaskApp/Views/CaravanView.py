#!/usr/bin/python3
# -*- coding: utf-8 -*-

'''
海盗船
编写人：darkedge
编写日期：2022年05月08日

'''

from .WorkView import WorkView
from flask_security import current_user
from flask import url_for, redirect, render_template, request, abort,jsonify
from flask_admin import expose
from FlaskApp.UserWorkThread import UserInfo,WebWorkThread,userThreads,findUserThread,UserInfo,buildUser,buildTokenUser,startWorkInThread,logoutUser,setCache,changeSocks5,buildTokenUser,findTokenUser

class CaravanView(WorkView):
    def __init__(self):
        super(CaravanView, self).__init__()
        self.endpoint = 'caravan'
        self.indexHtml = 'caravan.html'
        self.name = "2号技师"
        self.needRole = "handupuser"
    
    @expose('/caravan/', methods=('GET', 'POST'))
    def caravan_api(self):
        userThread = findUserThread(userId=current_user.id)
        if userThread:
            user = userThread.user
            res = user.importantCaravanList(current_user.has_role('superuser'))
            if res is not None:
                return jsonify({"code":200,"data":res})
        return jsonify({"code":201 ,"msg":"异常"})

    @expose('/caravan_buy/', methods=('GET', 'POST'))
    def caravan_buy_api(self):
        userThread = findUserThread(userId=current_user.id)
        if userThread:
            user = userThread.user
            caravanItemId = request.form.get('caravanItemId')
            count = int(request.form.get('count'))
            if count > 50:
                if not current_user.has_role('vipuser') and not current_user.has_role('superuser'):
                    count = 50
            
            res = user.tryBuyNormalShop(caravanItemId,count)
            if res == 0:
                logoutUser(userId=current_user.id)
            return jsonify({"code":200,"count":res})
        return jsonify({"code":201 ,"msg":"异常"})