#!/usr/bin/python3
# -*- coding: utf-8 -*-

"""
公共水晶页面
编写人：darkedge
编写日期：2023年01月13日

"""

from flask import abort, jsonify, redirect, render_template, request, url_for
from flask_admin import BaseView, expose
from flask_security import current_user

from Celery.redis import redisCeleryHelper
from FlaskApp.UserWorkThread import (
    UserInfo,
    WebWorkThread,
    buildTokenUser,
    buildUser,
    changeSocks5,
    findTokenUser,
    findUserThread,
    logoutUser,
    setCache,
    startWorkInThread,
    userThreads,
)


class GlobalCrystalView(BaseView):
    def is_accessible(self):
        return True

    def __init__(self):
        super(GlobalCrystalView, self).__init__()
        self.endpoint = "global_crystal"
        self.indexHtml = "global_crystal.html"
        self.static_url_path = "hello"
        self.name = "全球水晶"
        # self.category = "服务"

    def _handle_view(self, name, **kwargs):
        pass

    def _get_view_url(self, admin, url):
        return "/hello"

    @expose("/", methods=("GET", "POST"))
    def index_view(self):
        worldId = request.args.get("worldId", 20)
        return self.render(self.indexHtml, worldId=worldId)

    @expose("/get_crystal/", methods=("POST", "OPTIONS"))
    def get_crystal(self):
        jsonValue = request.form
        worldId = jsonValue.get("worldId", 20)
        data = {
            "crystals": redisCeleryHelper.getCrystalList(worldId),
            "dragoMines": redisCeleryHelper.getDragoMineList(worldId),
        }
        return jsonify({"code": 200, "data": data})

    @expose("/receive/", methods=("POST", "OPTIONS"))
    def receive(self):
        jsonValue = request.form
        worldId = jsonValue.get("worldId", 20)
        key = jsonValue.get("key")
        typeValue = jsonValue.get("type")
        if worldId and key and typeValue is not None:
            typeValue = int(typeValue)
            if typeValue == 0:
                redisCeleryHelper.removeCrystalListWithMember(worldId, key)
            elif typeValue == 1:
                redisCeleryHelper.removeDragoMineListWithMember(worldId, key)

            return jsonify({"code": 200})
        return jsonify({"code": 201, "msg": "异常"})
