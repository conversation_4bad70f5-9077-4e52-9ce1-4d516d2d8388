#!/usr/bin/python3
# -*- coding: utf-8 -*-

'''
撤回列表
编写人：darkedge
编写日期：2022年05月08日

'''

from .WorkView import WorkView
from flask_security import current_user
from flask import url_for, redirect, render_template, request, abort,jsonify
from flask_admin import expose
from FlaskApp.UserWorkThread import UserInfo,WebWorkThread,userThreads,findUserThread,UserInfo,buildUser,buildTokenUser,startWorkInThread,logoutUser,setCache,changeSocks5,buildTokenUser,findTokenUser
from FlaskApp.Units import workManGoBack

class GoBackListView(WorkView):
    def __init__(self):
        super(GoBackListView, self).__init__()
        self.endpoint = 'gobacklist'
        self.indexHtml = 'gobacklist.html'
        self.name = "4号技师"
        self.needRole = "vipuser"
        
    @expose('/gobacklist/', methods=('GET', 'POST'))
    def gobacklist_api(self):
        userThread = findUserThread(userId=current_user.id)
        if userThread:
            user = userThread.user
            res = user.canGobackList()
            if res is not None:
                return jsonify({"code":200,"data":res})
        return jsonify({"code":201 ,"msg":"异常"})

    @expose('/goback_use/', methods=('GET', 'POST'))
    def goback_use_api(self):
        userThread = findUserThread(userId=current_user.id)
        if userThread:
            user = userThread.user
            moId = request.form.get('moId')
            # if not current_user.has_role('superuser'):
            #     return jsonify({"code":200,"count":0})
            
            maxCount = 10
            if current_user.has_role('superuser'):
                maxCount = 26
            count = workManGoBack(moId,count=maxCount)
            user.log(f'手动多倍撤回{count}次')
            return jsonify({"code":200,"count":count})
            
        return jsonify({"code":201 ,"msg":"异常"})

    @expose('/goback_auto/', methods=('GET', 'POST'))
    def goback_auto_api(self):
        userThread = findUserThread(userId=current_user.id)
        if userThread:
            user = userThread.user
            res = startWorkInThread(userThread,workType=7)
            if res:
                return jsonify({"code":200})
        return jsonify({"code":201 ,"msg":"some error"})
