#!/usr/bin/python3
# -*- coding: utf-8 -*-

"""
日志管理View
编写人：darkedge
编写日期：2023年07月27日

"""

import FlaskApp
import FlaskApp.Models as Models
import FlaskApp.Views as Views
db = FlaskApp.db


class LogRecordView(Views.MyModelView):
    """日志管理"""

    def __init__(self):
        super(LogRecordView, self).__init__(model=Models.LogRecordModel)
        self.name = "日志管理"
        self.category = "独享"

    can_edit = False
    can_delete = False
    column_display_actions = False
    # 使用 created_at 倒叙排列
    column_default_sort = ("created_at", True)
    # type字段使用Options过滤
    column_filters = ["type"]
    # type字段使用Choices过滤
    column_choices = {"type": Models.LogRecordModel.typeOptions()}

    column_exclude_list = ["active"]

    # column_formatters = dict(type = lambda v,c,m,p: m.typeString)
    form_choices = {"type": Models.LogRecordModel.typeOptions()}
