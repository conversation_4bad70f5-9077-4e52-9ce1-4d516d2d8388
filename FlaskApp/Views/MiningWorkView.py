#!/usr/bin/python3
# -*- coding: utf-8 -*-

"""
MiningWorkView
编写人：darkedge
编写日期：2022年05月13日

"""
# trunk-ignore-all(ruff/F401)

from typing import List
from flask_admin.actions import action, ActionsMixin
from flask_admin.model.template import EndpointLinkRowAction, ViewPopupRowAction, TemplateLinkRowAction
from flask_admin.babel import gettext, lazy_gettext, ngettext
from flask_admin import expose
from flask_admin.helpers import get_form_data, validate_form_on_submit, get_redirect_target, flash_errors, is_form_submitted,flash
from flask_security import current_user
from flask import url_for, redirect, render_template, request, abort, jsonify, Response, stream_with_context
from wtforms.validators import Email, NumberRange, Length
from wtforms.fields import StringField, HiddenField, BooleanField, IntegerField
import FlaskApp.Models as Models
import FlaskApp.Views as Views
import FlaskApp
import json
import random
import time

from Unit.Redis import redisHelper
from Celery.task import collectCrystalsTask, revokeTask
from Celery.redis import redisCeleryHelper
from Unit.Logger import shellColorToWeb, logger
from Unit.ZeroUnit import ZeroSend

db = FlaskApp.db


def loadS5ListMap():
    from Unit.FileTool import loadS5List
    try:
        s5List = loadS5List("./socks5_web.txt")
        if len(s5List):
            listMap = [(v, v) for v in s5List]
            return listMap
    except FileNotFoundError:
        pass
    return []


class MiningWorkView(Views.MyModelView, ActionsMixin):
    """临时token管理"""

    def __init__(self, model=None, session=None, name=None, category=None, endpoint=None, url=None, static_folder=None, menu_class_name=None, menu_icon_type=None, menu_icon_value=None):
        model = Models.MiningWorkMan
        super(MiningWorkView, self).__init__(model, session, name=name, category=category, endpoint=endpoint, url=url,
                                             static_folder=static_folder, menu_class_name=menu_class_name, menu_icon_type=menu_icon_type, menu_icon_value=menu_icon_value)
        # self.endpoint = 'tokenwork'
        # self.indexHtml = 'tokenwork.html'
        self.name = "挂机管理"
        self.category = "独享"

    list_template = 'admin/miningworkman/list.html'
    edit_modal_template = 'admin/miningworkman/edit.html'

    create_modal = True
    edit_modal = True
    column_list = Models.MiningWorkMan.paramsKeys()
    column_labels = Models.MiningWorkMan.paramsKeyMap()
    column_searchable_list = ['email', 'name']
    column_editable_list = ['token']
    page_size = 100
    
    form_choices = {
        'socks5': loadS5ListMap(),
    }

    form_excluded_columns = ('kingdom_id',
                             'userKey', 'active', 'confirmed_at')

    form_args = dict(
        email=dict(label='邮箱', validators=[Email()]),
        pwd={"label": "密码"},
        # name={"render_kw": {
        #     "readonly": True
        # }}
    )

    form_extra_fields = {
        "params": HiddenField(),
        "barkKey": StringField("Bark推送Key",default=""),
        "maxTroopNum": IntegerField("出兵数量", default=2000, validators=[NumberRange(1, 400000, "出兵数量")]),
        "maxCrystalLevel": IntegerField("最大水晶等级", default=9, validators=[NumberRange(0, 9)]),
        "minCrystalLevel": IntegerField("最小水晶等级", default=2, validators=[NumberRange(0, 9)]),
        "isJoinAllianceBattle": BooleanField("自动跟团", default=False),
        "battleTroopNum": IntegerField("跟团数量", default=30000, validators=[NumberRange(1, 400000, "跟团数量")]),
        "workDragos": StringField("工作龙ID列表(用逗号分隔)", default=""),
        "autoJoinAllianceTag": StringField("自动加盟标签", default="", description="联盟TAG", validators=[Length(max=4)]),
        "autoPackage": BooleanField("自动打包", default=True),
        "autoSkill": BooleanField("自动技能", default=True),
        "autoEat": BooleanField("自动吃药", default=True),
        "autoBuy": BooleanField("自动商店", default=True),
        "autoLevelUp": BooleanField("自动升级", default=False),
        "autoMinGame": BooleanField("消消乐", default=False),
        "autoGlobalCrystal": BooleanField("闲时水晶", default=False),
        "autoStartRally": BooleanField("自动开团", default=False),
        "onlyMonster": BooleanField("只打怪", default=False),
        # "autoReLogin": BooleanField("每日登录", default=False),
    }

    column_extra_row_actions = [
        # EndpointLinkRowAction('fa fa-cog', 'miningworkman.show_log'),
        TemplateLinkRowAction("view_row_log_popup", "日志"),
        TemplateLinkRowAction("view_row_run", "启动"),
        TemplateLinkRowAction("view_row_stop", "停止"),
    ]

    def get_one(self, id):
        model = super().get_one(id)
        model.loadParams()
        return model

    def get_list(self, page, sort_column, sort_desc, search, filters, execute=True, page_size=None):
        count, data = super().get_list(page, sort_column, sort_desc, search,
                                       filters, execute=execute, page_size=page_size)
        if data and len(data):
            [model.loadParams() for model in data]
        return count, data

    @expose('/show_log/', methods=('GET', 'POST'))
    def show_log(self):
        id = request.args.get("id")
        model: Models.MiningWorkMan = self.get_one(id)
        from Unit.Logger import RedisHandler
        r = RedisHandler(model.email)
        ZeroSend.tryPrintResource(model.kingdom_id)

        return self.render("admin/miningworkman/log.html", logs=r.getLogs())

    @expose('/start/', methods=('GET', 'POST'))
    def start(self):
        id = request.args.get("id")
        model: Models.MiningWorkMan = self.get_one(id)
        task_id = model.startTask()
        return self.render("succeed.html", task_id=task_id)

    @expose('/stop/', methods=('GET', 'POST'))
    def stop(self):
        id = request.args.get("id")
        model: Models.MiningWorkMan = self.get_one(id)
        task_id = redisHelper.getCeleryTaskId(model.email)
        revokeTask(task_id)
        redisHelper.removeCeleryTaskId(model.email)
        return self.render("succeed.html")

    # 一键启动
    @expose('/start_all/', methods=('GET', 'POST'))
    def start_all(self):
        FlaskApp.Units.loadAllTokenWorkMan()
        time.sleep(3)
        mans:List[Models.MiningWorkMan] = Models.MiningWorkMan.query.filter_by(active=True).all()
        for model in mans:
            if not model.workStatu:
                _task_id = model.startTask()
        return jsonify({"code":200,"msg":"启动成功"})
    
    def on_model_change(self, form, model: Models.MiningWorkMan, is_created):
        data = form.data
        if data.get("token"):
            data['token'] = data['token'].strip()
            model.token = data['token']
        print(f"data:{data}")
        data = model.clearParams(data)
        print(f"data2:{data}")
        paramsStr = json.dumps(data)
        print(f"data3:{paramsStr}")
        model.params = paramsStr
        if not model.email or len(model.email) == 0:
            if model.token:
                from Unit.Redis import loadJWTInfo
                info = loadJWTInfo(model.token)
                if isinstance(info, dict):
                    kingdomId = info.get('kingdomId')
                    model.kingdom_id = kingdomId
                    model.email = f'{kingdomId}@gmail.com'
    
    @action('batch_stop', '停止', '确定停止吗？')
    def action_active(self, ids):
        count = len(ids)
        for id in ids:
            model: Models.MiningWorkMan = self.get_one(id)
            task_id = redisHelper.getCeleryTaskId(model.email)
            revokeTask(task_id)
            redisHelper.removeCeleryTaskId(model.email)
        flash(ngettext('停止成功',
                    '%(count)s 停止成功',
                    count,
                    count=count), 'success')

    @action('batch_start', '启动', '确定要启动选中的任务吗？')
    def action_start(self, ids):
        """批量启动选中的挂机任务"""
        count = len(ids)
        for id in ids:
            model: Models.MiningWorkMan = self.get_one(id)
            if not model.workStatu:
                _task_id = model.startTask()
        flash(ngettext('启动成功',
                    '%(count)s 启动成功',
                    count,
                    count=count), 'success')

    def validate_form(self, form):
        """
            Validate the form on submit.

            :param form:
                Form to validate
        """
        return validate_form_on_submit(form)

    def create_form(self, obj=None):
        """
            Instantiate model creation form and return it.

            Override to implement custom behavior.
        """
        form = self._create_form_class(get_form_data(), obj=obj)

        unUsedSocks = self.returnUnUsedSocks() or []
        form.socks5.choices = unUsedSocks

        return form

    def edit_form(self, obj=None):
        """
            Instantiate model editing form and return it.

            Override to implement custom behavior.
        """

        form = self._edit_form_class(get_form_data(), obj=obj)
        # form.name.render_kw = {'readonly':True}

        if not is_form_submitted():
            paramsStr = form.params.data
            if paramsStr and len(paramsStr) > 0:
                data = json.loads(paramsStr)
                for key in data:
                    field = form._fields.get(key)
                    if field:
                        field.data = data[key]
        unUsedSocks = self.returnUnUsedSocks() or []
        if form.socks5.data:
            unUsedSocks.append((form.socks5.data,form.socks5.data))
        form.socks5.choices = unUsedSocks

        return form

    def returnUnUsedSocks(self):
        allSocks5 = loadS5ListMap()
        socks5 = Models.MiningWorkMan.usedSocks()
        return list(filter(lambda x: x[0] not in socks5, allSocks5))

    @expose('/stream_bot_work_log')
    def stream_bot_work_log(self):
        """使用SSE推送机器人工作日志"""
        import base64
        last_msg = request.args.get('last_msg', '')
        print(f"last_msg:{last_msg}")
        try:
            # 解码客户端传来的last_msg
            last_msg = base64.b64decode(last_msg).decode('utf-8') if last_msg else ''
        except Exception:
            last_msg = ''
        print(f"last_msg:{last_msg}")
        def generate():
            nonlocal last_msg
            first = False
            while True:
                curr_logs = redisCeleryHelper.getBotWorkLog()
                curr_logs = [shellColorToWeb(log) for log in curr_logs]
                curr_logs.reverse()

                new_logs = []
                for log in curr_logs:
                    if log != last_msg:
                        new_logs.append(log)
                    else:
                        break

                if new_logs:
                    last_msg = new_logs[0]
                    new_logs.reverse()
                    # 对每条日志进行base64编码
                    encoded_logs = [base64.b64encode(log.encode('utf-8')).decode('utf-8') for log in new_logs]
                    data = json.dumps({'logs': encoded_logs})
                    yield f"data: {data}\n\n"
                elif not first:
                    first = True
                    data = json.dumps({'logs': []})
                    yield f"data: {data}\n\n"

                time.sleep(1)

        return Response(
            stream_with_context(generate()),
            mimetype='text/event-stream',
            headers={
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive'
            }
        )

    @expose('/try_print_resource', methods=['POST'])
    def try_print_resource(self):
        """打印资源信息"""
        ZeroSend.tryPrintResource()
        return jsonify({
            'code': 200,
            'msg': '资源信息请求已发送'
        })

    @expose('/can_recycle_apple_sub_ids', methods=['GET'])
    def can_recycle_apple_sub_ids(self):
        """检查是否可以回收AppleSubIds"""
        recycleKingdomIds = Models.MiningWorkMan.recycleAppleSubIds()
        if len(recycleKingdomIds) > 0:
            return jsonify({
                'code': 200,
                'data': recycleKingdomIds
            })
        return jsonify({
            'code': 201,
            'msg': '没有可以回收的AppleSubIds'
        })

    @expose('/recycle_apple_sub_ids', methods=['POST'])
    def recycle_apple_sub_ids(self):
        """回收AppleSubIds"""
        recycleKingdomIds = Models.MiningWorkMan.recycleAppleSubIds()
        for kingdomId in recycleKingdomIds:
            appleSubId = redisHelper.get(f"{kingdomId}_helper_apple")
            if appleSubId:
                logger.info(f"回收{kingdomId}的AppleSubId:{appleSubId}")
                redisHelper.saveAppleSubId(appleSubId)
                redisHelper.removeKey(f"{kingdomId}_helper_apple")
            else:
                logger.error(f"没有找到{kingdomId}_helper_apple")
        return jsonify({
            'code': 200,
            'msg': '执行成功'
        })