#!/usr/bin/python3
# -*- coding: utf-8 -*-

'''
监控列表
编写人：darkedge
编写日期：2022年05月08日

'''

from .WorkView import WorkView
from flask_security import current_user
from flask import url_for, redirect, render_template, request, abort,jsonify
from flask_admin import expose
from FlaskApp.UserWorkThread import UserInfo,WebWorkThread,userThreads,findUserThread,UserInfo,buildUser,buildTokenUser,startWorkInThread,logoutUser,setCache,changeSocks5,buildTokenUser,findTokenUser


class MonitoringAllianceBattleView(WorkView):
    def __init__(self):
        super(MonitoringAllianceBattleView, self).__init__()
        self.endpoint = 'monitoringalliancebattle'
        self.indexHtml = 'monitoringalliancebattle.html'
        self.name = "7号技师"
        self.needRole = "vip4user"
        
    @expose('/monitoringalliancebattle/', methods=('GET', 'POST'))
    def monitoringalliancebattle_api(self):
        userThread = findUserThread(userId=current_user.id)
        if userThread:
            user = userThread.user
            res = user.monitoringAllianceBattle()
            if res is not None:
                return jsonify({"code":200,"data":res})
        return jsonify({"code":201 ,"msg":"异常"})
