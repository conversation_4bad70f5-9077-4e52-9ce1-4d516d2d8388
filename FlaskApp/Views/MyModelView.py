#!/usr/bin/python3
# -*- coding: utf-8 -*-

"""
功能描述：MyModelView
编写人：darkedge
编写日期：2022年05月08日
"""

from flask_admin.contrib import sqla
from flask_security import current_user
from flask import url_for, redirect, request, abort
import FlaskApp.Models as Models
import FlaskApp

db = FlaskApp.db

class MyModelView(sqla.ModelView):
    def __init__(self, model=None, session=None,name=None, category=None, endpoint=None, url=None, static_folder=None,menu_class_name=None, menu_icon_type=None, menu_icon_value=None):
        if not model:
            model = Models.Role
        
        if not session:
            session = FlaskApp.db.session

        super(MyModelView, self).__init__(model, session,name=name, category=category, endpoint=endpoint, url=url, static_folder=static_folder,menu_class_name=menu_class_name, menu_icon_type=menu_icon_type, menu_icon_value=menu_icon_value)

    def is_accessible(self):
        return (current_user.is_active and
                current_user.is_authenticated and
                current_user.has_role('superuser')
        )

    def _handle_view(self, name, **kwargs):
        """
        Override builtin _handle_view in order to redirect users when a view is not accessible.
        """
        if not self.is_accessible():
            if current_user.is_authenticated:
                # permission denied
                abort(403)
            else:
                # login
                return redirect(url_for('security.login', next=request.url))