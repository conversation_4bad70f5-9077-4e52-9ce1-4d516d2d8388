#!/usr/bin/python3
# -*- coding: utf-8 -*-

"""
NFT公公页
编写人：darkedge
编写日期：2022年05月08日

"""
from typing import List
from .WorkView import WorkView
from flask_security import current_user
# trunk-ignore(ruff/F401)
from flask import url_for, redirect, render_template, request, abort,jsonify
from flask_admin import expose
# trunk-ignore(ruff/F401)
from FlaskApp.UserWorkThread import UserInfo,WebWorkThread,userThreads,findUserThread,buildUser,buildTokenUser,startWorkInThread,logoutUser,setCache,changeSocks5,findTokenUser
from Unit.EthUnit import EthService
from Unit.Logger import logger

class NFTView(WorkView):
    def __init__(self):
        super(NFTView, self).__init__()
        self.endpoint = 'nft'
        self.indexHtml = 'nft.html'
        self.name = "NFT"
        self.category = "独享"
        self.needRole = "superuser"
    
    @expose('/', methods=('GET', 'POST'))
    def index_view(self):
        workUsers = []
        if current_user.has_role("superuser"):
            from FlaskApp.Models import MiningWorkMan

            mans: List[MiningWorkMan] = MiningWorkMan.query.filter_by(active=True).all()
            index = 1
            for man in mans:
                if man.workStatu:
                    try:
                        _name = f"{man.name}({man.getKingdomTodayCrystalCount})"
                        workUsers.append(
                            {
                                "name": _name,
                                "token": man.token,
                                "socks5": man.socks5,
                                "index": index,
                            }
                        )
                    except Exception as e:
                        logger.error(f"获取工人信息失败:{e}\nemail:{man.email}")
                    index += 1
        return self.render(self.indexHtml,workUsers=workUsers)

    @expose('/getnftlist/', methods=('GET', 'POST'))
    def getnftlist_api(self):
        workUser = self.getWorkUser()
        if workUser:
            user = workUser
        else:
            token = request.form.get('token')
            user = buildTokenUser(token)
        if user:
            res = user.webNftItemList()
            if res is False:
                return jsonify({"code":201 ,"msg":"没有绑定钱包"})
            if res is None:
                return jsonify({"code":201 ,"msg":"some error"})
            return jsonify({"code":200,"data":res})
        return jsonify({"code":201,"data":"token异常"})

    @expose('/getnftdragolist/', methods=('GET', 'POST'))
    def getnftdragolist_api(self):
        workUser = self.getWorkUser()
        if workUser:
            user = workUser
        else:
            token = request.form.get('token')
            user = buildTokenUser(token)
        if user:
            res = user.webNftDragoList()
            if res is False:
                return jsonify({"code":201 ,"msg":"没有绑定钱包"})
            if res is None:
                return jsonify({"code":201 ,"msg":"some error"})
            return jsonify({"code":200,"data":res})
        
    @expose('/bindpkey/', methods=('GET', 'POST'))
    def bindpkey_api(self):
        privateKey = request.form.get('pkey')
        if len(privateKey) != 66 or privateKey[0] != '0':
            return jsonify({"code":201 ,"msg":"私钥格式错误"})

        user = self.getWorkUser()
        if user is None:
            token = request.form.get('token')
            user = buildTokenUser(token)
        ethService = EthService(privateKey)
        if user:
            res = user.webNftItemList()
            if res:
                publicKey = res.get("publicKey")
                if publicKey == ethService.address:
                    return jsonify({"code":200,"data":res})
            if user.tryLinkWallet(privateKey):
                res = user.webNftItemList()
                return jsonify({"code":200,"data":res})
            return jsonify({"code":201 ,"msg":"绑定异常"})

    @expose('/unbindpkey/', methods=('GET', 'POST'))
    def unbindpkey_api(self):
        user = self.getWorkUser()
        if user is None:
            token = request.form.get('token')
            user = buildTokenUser(token)
        if user:
            res = user.unlinkWallet()
            if res:
                return jsonify({"code":200,"data":res})
        return jsonify({"code":201 ,"msg":"异常"})

    @expose('/mint/', methods=('GET', 'POST'))
    def mint_api(self):
        nftItemId = request.form.get('nftItemId')
        print(f"发行NFT：{nftItemId}")
        user = self.getWorkUser()
        if user is None:
            userThread = findUserThread(userId=current_user.id)
            if userThread:
                user = userThread.user
        if user:
            txData = user.mintNft(nftItemId)
            if txData:
                print(f"发行数据 {txData}")
                return jsonify({"code":200,"data":txData})
        return jsonify({"code":201 ,"msg":"异常"})

    @expose('/mint_tx/', methods=('GET', 'POST'))
    def mint_tx_api(self):
        txHash = request.form.get('tx')
        mintCode = request.form.get('mintCode')
        print(f"发行NFT tx：{txHash}")
        user = self.getWorkUser()
        if user is None:
            userThread = findUserThread(userId=current_user.id)
            if userThread:
                user = userThread.user
        if user:
            res = user.mintTx(mintCode,txHash)
            if res:
                return jsonify({"code":200})
        return jsonify({"code":201 ,"msg":"异常"})

    @expose("/kick_drago/", methods=('GET', 'POST'))
    def kick_drago_api(self):
        """踢回城"""

        token = request.form.get('token')
        dragoId = request.form.get('dragoId')
        user = self.getWorkUser()
        if user is None:
            token = request.form.get('token')
            user = buildTokenUser(token)
        if user:
            res = user.dragoLairLeave(dragoId)
            if res is False:
                return jsonify({"code":201 ,"msg":"执行失败"})
            if res is None:
                return jsonify({"code":201 ,"msg":"some error"})
            return jsonify({"code":200,"data":res})
        return jsonify({"code":201,"data":"token异常"})