#!/usr/bin/python3
# -*- coding: utf-8 -*-

"""
在线管理View
编写人：darkedge
编写日期：2023年08月91日

"""

# import datetime
import json

# trunk-ignore(ruff/F401)
from flask import abort, jsonify, redirect, render_template, request, url_for
from flask_admin import expose

# trunk-ignore(ruff/F401)
import FlaskApp
import FlaskApp.Models as Models
from Celery.redis import redisCeleryHelper
# from Celery.task import app, revokeTask
from FlaskApp.UserWorkThread import (
    UserInfo,
    # WebWorkThread,
    # buildTokenUser,
    # buildUser,
    # changeSocks5,
    # findTokenUser,
    # findUserThread,
    # logoutUser,
    # setCache,
    # startWorkInThread,
    # userThreads,
)
from Unit.ZeroUnit import ZeroSend

from .WorkView import WorkView

MiningWorkMan = Models.MiningWorkMan


class OnlineView(WorkView):
    def __init__(self):
        super(OnlineView, self).__init__()
        self.endpoint = "online"
        self.indexHtml = "online.html"
        self.name = "在线管理"
        self.category = "独享"
        self.needRole = "superuser"

    @expose("/", methods=["GET", "POST"])
    def index_view(self):
        maxMonsterDistance = redisCeleryHelper.getMaxMonsterDistance()
        cvcPoints = redisCeleryHelper.getCVCSearchPoints()
        cvcBlockPoints = redisCeleryHelper.getCVCBlockSearchPoints()
        spartoiLevel = redisCeleryHelper.getSpartoiLevel()
        battleTimes = redisCeleryHelper.getBattleTimes()
        cvcRankSwitch = redisCeleryHelper.getCvcRankSwitch()
        cvcBlockAutoKick = redisCeleryHelper.getCvcBlockAutoKick()
        shieldRallySwitch = redisCeleryHelper.getShieldRallySwitch()
        cvcRankLockPoint = redisCeleryHelper.getCvcRankLockPoint()
        user = UserInfo()
        todayRedDragon = user.getPutDragonDailyQuota()
        todayMonster = user.getTodayMonster()
        rallyMonsters = redisCeleryHelper.getRallyMonsterList(100002)
        rallyMonsterNum = len(rallyMonsters)
        return self.render(
            self.indexHtml,
            maxMonsterDistance=maxMonsterDistance,
            cvcPoints=cvcPoints,
            cvcBlockPoints=cvcBlockPoints,
            spartoiLevel=spartoiLevel,
            battleTimes=battleTimes,
            cvcRankSwitch=cvcRankSwitch,
            cvcBlockAutoKick=cvcBlockAutoKick,
            shieldRallySwitch=shieldRallySwitch,
            cvcRankLockPoint=cvcRankLockPoint,
            todayRedDragon=todayRedDragon,
            todayMonster=todayMonster,
            rallyMonsterNum=rallyMonsterNum
        )

    @expose("/update_cvcblockpoints/", methods=["POST"])
    def update_cvc_block_points_api(self):
        cvcBlockPoints = request.form.get("cvcBlockPoints")
        points = json.loads(cvcBlockPoints)
        points = list(map(lambda x: (x[0], x[1]), points))
        redisCeleryHelper.setCVCBlockSearchPoints(points)
        return jsonify({"code": 200})

    @expose("/update_battletimes/", methods=["POST"])
    def update_battle_times_api(self):
        battleTimes = request.form.get("battleTimes")
        battleTimes = json.loads(battleTimes)
        battleTimes = [int(v) for v in battleTimes]
        redisCeleryHelper.setBattleTimes(battleTimes)

        return jsonify({"code": 200})

    @expose("/update_cvcpoints/", methods=["POST"])
    def update_cvc_points_api(self):
        cvcpoints = request.form.get("cvcpoints")
        spartoiLevel = int(request.form.get("spartoiLevel"))

        points = json.loads(cvcpoints)
        points = list(map(lambda x: (x[0], x[1]), points))
        redisCeleryHelper.setCVCSearchPoints(points)
        redisCeleryHelper.setSpartoiLevel(spartoiLevel)
        return jsonify({"code": 200})

    @expose("/update_distance/", methods=["POST"])
    def update_distance_api(self):
        distance = int(request.form.get("distance"))
        redisCeleryHelper.setMaxMonsterDistance(distance)
        return jsonify({"code": 200})

    @expose("/global_crystal", methods=["POST"])
    def global_crystal_api(self):
        ZeroSend.makeGlobalCrystalInfo()
        return jsonify({"code": 200})

    @expose("/garrison/", methods=["POST"])
    def garrison_api(self):
        loc = request.form.get("loc")
        onlyMelee = request.form.get("onlyMelee")
        quantity = request.form.get("quantity", "300000")
        ratio = request.form.get("ratio", "[4,4,2]")
        quantity = int(quantity)
        ratio = json.loads(ratio)
        if onlyMelee == "false":
            onlyMelee = False
        else:
            onlyMelee = True
        if isinstance(loc, str):
            loc = loc.split(",")
            loc = [int(x) for x in loc]

        ZeroSend.tryGarrison(loc, not onlyMelee, quantity, ratio)
        return jsonify({"code": 200})

    @expose("/treasure_page/", methods=["POST"])
    def treasure_page_api(self):
        page = request.form.get("page")
        ZeroSend.tryChangeTreasure(int(page))
        return jsonify({"code": 200})

    @expose("/get_teleport_targets/", methods=["GET"])
    def get_teleport_targets_api(self):
        targets = [
        ]
        # 从MiningWorkMan获取所有数据
        workmen = MiningWorkMan.getAll()
        for workman in workmen:
            if workman.kingdom_id:
                loc = UserInfo.getUserLoc(workman.kingdom_id) or [0,0,0]
                targets.append({
                    "name": workman.name,
                    "kingdomId": workman.kingdom_id,
                    "loc": loc
                })
        return jsonify({"code": 200, "targets": targets})

    @expose("/teleport/", methods=["POST"])
    def teleport_api(self):
        kingdomId = request.form.get("kingdomId")
        loc = request.form.get("loc")
        loc = [int(x) for x in json.loads(loc)]
        ZeroSend.tryTeleport(kingdomId, loc)
        print(f"传送王国 {kingdomId} 到 {loc}")
        return jsonify({"code": 200})

    @expose("/cvc_move/", methods=["POST"])
    def cvc_move_api(self):
        kingdomId = request.form.get("kingdomId")
        enter = request.form.get("enter")
        enter = bool(int(enter))
        ZeroSend.tryCvcMove(kingdomId, enter)
        title = enter and "进入" or "离开"
        print(f"{title} cvc 王国 {kingdomId}")
        return jsonify({"code": 200})

    @expose("/cvc_rank_switch/", methods=["POST"])
    def cvc_rank_switch_api(self):
        value = request.form.get("value")
        value = int(value)
        redisCeleryHelper.setCvcRankSwitch(value)
        return jsonify({"code": 200})

    @expose("/cvc_block_auto_kick/", methods=["POST"])
    def cvc_block_auto_kick_api(self):
        value = request.form.get("value")
        value = int(value)
        redisCeleryHelper.setCvcBlockAutoKick(value)
        return jsonify({"code": 200})

    @expose("/use_item/", methods=["POST"])
    def use_item_api(self):
        code = int(request.form.get("itemId"))
        count = request.form.get("count", 1)
        kingdomId = request.form.get("kingdomId", None)
        ZeroSend.tryUseItem(code, count, kingdomId)
        return jsonify({"code": 200})

    @expose("/shield_rally_switch", methods=["POST"])
    def shield_rally_switch_api(self):
        """
        开启/关闭破盾跟团
        """
        value = request.form.get("value", type=int)
        if value not in [0, 1]:
            return jsonify({"code": 400, "msg": "参数错误"})
        redisCeleryHelper.setShieldRallySwitch(value)
        return jsonify({"code": 200})

    @expose("/update_cvc_lock_point", methods=["POST"])
    def update_cvc_lock_point_api(self):
        """
        更新CVC锁分值
        """
        lockPoint = request.form.get("lockPoint", type=int)
        if not lockPoint or lockPoint <= 0:
            return jsonify({"code": 400, "msg": "参数错误"})
        redisCeleryHelper.setCvcRankLockPoint(lockPoint)
        return jsonify({"code": 200})

    @expose("/use_king_power/", methods=["POST"])
    def use_king_power_api(self):
        """
        使用国王技能
        powerId:
            101 - 采集加速
            102 - 治疗加速
            103 - 训练加速
            104 - 水晶加速
        """
        power_id = request.form.get("powerId", type=int)
        if power_id not in [101, 102, 103, 104]:
            return jsonify({"code": 400, "msg": "无效的技能ID"})

        ZeroSend.tryUseKingSkill(power_id)
        return jsonify({"code": 200})

    @expose("/return_support/", methods=["POST"])
    def return_support_api(self):
        """
        撤回支援部队
        """
        ZeroSend.tryReturnAllSupport()
        return jsonify({"code": 200})

    @expose("/garrison_temple/", methods=["POST"])
    def garrison_temple_api(self):
        """
        神殿驻军
        """
        ZeroSend.tryGarrisonTemple()
        return jsonify({"code": 200})

    @expose("/update_war_join_count/", methods=["POST"])
    def update_war_join_count_api(self):
        """
        更新战争跟团数量
        """
        worldId = request.form.get("worldId", type=int)
        count = request.form.get("count", type=int)
        redisCeleryHelper.setWarJoinCount(worldId, count)
        return jsonify({"code": 200})

    @expose("/get_all_war_join_count/", methods=["GET"])
    def get_all_war_join_count_api(self):
        """
        获取所有战争跟团数量
        """
        return jsonify({"code": 200, "data": redisCeleryHelper.allWarJoinCount()})
