#!/usr/bin/python3
# -*- coding: utf-8 -*-

"""
记录页
编写人：darkedge
编写日期：2024年07月23日

"""
import json

# trunk-ignore(ruff/F401)
from flask import abort, jsonify, redirect, render_template, request, url_for
from flask_admin import expose


# from FlaskApp.UserWorkThread import (
#     UserInfo,
#     WebWorkThread,
#     buildTokenUser,
#     buildUser,
#     changeSocks5,
#     findTokenUser,
#     findUserThread,
#     logoutUser,
#     setCache,
#     startWorkInThread,
#     userThreads,
# )
from Unit.Redis import redisHelper
from Unit.UserInfoHelper import Enum, getRecordCrystals

from .WorkView import WorkView


class RecordView(WorkView):
    def __init__(self):
        super(RecordView, self).__init__()
        self.endpoint = "record"
        self.indexHtml = "record.html"
        self.name = "砖石记录"
        self.category = "独享"
        self.needRole = "superuser"

    @expose("/", methods=("GET", "POST"))
    def index_view(self):
        records = getRecordCrystals()
        if records:
            records = json.dumps(records, ensure_ascii=False)
        else:
            records = "[]"
        dragoSouls = self.getDragoSoul()
        dragoSouls = json.dumps(dragoSouls, ensure_ascii=False)
        return self.render(self.indexHtml, records=records, dragoSouls=dragoSouls)

    def getDragoSoul(self):
        itemLists = redisHelper.getItemLists()
        dragoMaps = []

        def findCode(items, code):
            for item in items:
                if code == item["code"]:
                    return item["amount"]
            return 0

        for k, v in itemLists.items():
            dragoMaps.append({"name": k, "amount": findCode(v, Enum.ITEM_CODE_DRAGO_SOUL)})
        return sorted(dragoMaps, key=lambda x: x["amount"], reverse=True)
