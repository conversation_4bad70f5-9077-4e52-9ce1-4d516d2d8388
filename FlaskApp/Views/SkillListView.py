#!/usr/bin/python3
# -*- coding: utf-8 -*-

'''
技能
编写人：darkedge
编写日期：2022年05月08日

'''

from .WorkView import WorkView
from flask_security import current_user
from flask import url_for, redirect, render_template, request, abort,jsonify
from flask_admin import expose
from FlaskApp.UserWorkThread import UserInfo,WebWorkThread,userThreads,findUserThread,UserInfo,buildUser,buildTokenUser,startWorkInThread,logoutUser,setCache,changeSocks5,buildTokenUser,findTokenUser

class SkillListView(WorkView):
    def __init__(self):
        super(SkillListView, self).__init__()
        self.endpoint = 'skilllist'
        self.indexHtml = 'skilllist.html'
        self.name = "6号技师"
        self.needRole = "vipuser"
        
    @expose('/skilllist/', methods=('GET', 'POST'))
    def skilllist_api(self):
        userThread = findUserThread(userId=current_user.id)
        if userThread:
            user = userThread.user
            res = user.unUseSkillList(current_user.has_role('superuser'))
            if res is not None:
                return jsonify({"code":200,"data":res})
        return jsonify({"code":201 ,"msg":"异常"})

    @expose('/skill_use/', methods=('GET', 'POST'))
    def skill_use_api(self):
        userThread = findUserThread(userId=current_user.id)
        if userThread:
            user = userThread.user
            code = request.form.get('code')
            count = int(request.form.get('count'))
            # return jsonify({"code":200,"count":code})
            res = user.tryUseSkill(code,count,isSuper=current_user.has_role('superuser'))
            return jsonify({"code":200,"count":res})
        return jsonify({"code":201 ,"msg":"异常"})