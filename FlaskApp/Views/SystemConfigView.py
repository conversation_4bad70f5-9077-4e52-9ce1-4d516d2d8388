#!/usr/bin/python3
# -*- coding: utf-8 -*-

"""
系统参数管理View
编写人：darkedge
编写日期：2023年04月24日

"""

import datetime

from flask import  jsonify, request
from flask_admin import expose

from Celery.redis import redisCeleryHelper

from .WorkView import WorkView


class SystemConfigView(WorkView):
    def __init__(self):
        super(SystemConfigView, self).__init__()
        self.endpoint = "systemconfig"
        self.indexHtml = "systemconfig.html"
        self.name = "参数管理"
        self.category = "独享"
        self.needRole = "superuser"
        self.startTime = datetime.datetime.now()

    @expose("/", methods=("GET", "POST"))
    def index_view(self):
        worldIds = redisCeleryHelper.getDragoListenWorldIds()
        worldIdsStr = [str(v) for v in worldIds]
        intervals = redisCeleryHelper.getAutoTaskIntervals()
        intervalsStr = str(intervals)
        return self.render(
            self.indexHtml,
            worldIds=worldIds,
            worldIdsStr=worldIdsStr,
            intervals=intervals,
            intervalsStr=intervalsStr,
        )

    @expose("/add/", methods=("GET", "POST"))
    def add_api(self):
        worldId = int(request.form.get("worldId"))
        worldIds = redisCeleryHelper.getDragoListenWorldIds()
        if worldId not in worldIds:
            worldIds.append(worldId)
            redisCeleryHelper.setDragoListenWorldIds(worldIds)
            return jsonify({"code": 200})
        else:
            return jsonify({"code": 201, "msg": "已存在"})
        return jsonify({"code": 201, "msg": "unknow"})

    @expose("/delete/", methods=("GET", "POST"))
    def delete_api(self):
        worldId = int(request.form.get("worldId"))
        worldIds = redisCeleryHelper.getDragoListenWorldIds()
        if worldId in worldIds:
            worldIds.remove(worldId)
            redisCeleryHelper.setDragoListenWorldIds(worldIds)
            return jsonify({"code": 200})
        else:
            return jsonify({"code": 201, "msg": "不存在"})
        return jsonify({"code": 201, "msg": "unknow"})

    @expose("/set/", methods=("GET", "POST"))
    def set_api(self):
        worldId = request.form.get("worldId")
        value = int(request.form.get("value"))
        redisCeleryHelper.setAutoTaskInterval(worldId, value)
        return jsonify({"code": 200})
