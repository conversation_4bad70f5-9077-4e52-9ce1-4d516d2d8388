#!/usr/bin/python3
# -*- coding: utf-8 -*-

"""
系统管理View
编写人：darkedge
编写日期：2023年04月24日

"""

import datetime
import threading

from flask import abort, jsonify, redirect, render_template, request, url_for
from flask_admin import expose

import FlaskApp
from Celery.task import  revokeTask,app
import FlaskApp.Models as Models
from celery.app.control import Inspect
from FlaskApp.UserWorkThread import (
    UserInfo,
    WebWorkThread,
    buildTokenUser,
    buildUser,
    changeSocks5,
    findTokenUser,
    findUserThread,
    logoutUser,
    setCache,
    startWorkInThread,
    userThreads,
)
from Unit.DeviceInfo import version as gameVersion
from Unit.System import execute_command, execute_commandSplit

from .WorkView import WorkView

MiningWorkMan = Models.MiningWorkMan


class SystemView(WorkView):
    def __init__(self):
        super(SystemView, self).__init__()
        self.endpoint = "system"
        self.indexHtml = "system.html"
        self.name = "系统管理"
        self.category = "独享"
        self.needRole = "superuser"
        self.startTime = datetime.datetime.now()

    @expose("/", methods=("GET", "POST"))
    def index_view(self):
        inspect: Inspect = app.control.inspect()
        B64ApiList = UserInfo().returnB64ApiList()
        activeList = []
        active = inspect.active()
        if active:
            for key in active:
                value = active[key]
                for v in value:
                    activeList.append(f'{v["name"]} {v["id"]}')
        return self.render(
            self.indexHtml,
            startTime=self.startTime,
            nodKey=UserInfo().nodKey,
            active=activeList,
            B64ApiList=B64ApiList,
            gameVersion=gameVersion,
        )

    @expose("/rebootweb/", methods=("GET", "POST"))
    def rebootweb_api(self):
        command = "`echo r > /tmp/fifo0`"
        result = execute_command(command)
        return jsonify({"code": 200, "msg": result})

    @expose("/rebootbot/", methods=("GET", "POST"))
    def rebootbot_api(self):
        MiningWorkMan.stopAll()

        command = "celery -A celeryTask multi restart workman -c 50"
        result = execute_commandSplit(command)
        print(result)
        return jsonify({"code": 200})

    @expose("/refreshnodkey/", methods=("GET", "POST"))
    def refreshnodkey_api(self):
        th = threading.Thread(target=self.refreshnodkey, daemon=True)
        th.start()
        return jsonify({"code": 200})

    def refreshnodkey(self):
        from FlaskApp.Units.TokenWorkManHelper import loadGusetUser, registerGuest
        from Unit.UserEncrypt import refreshNodKey

        u1 = UserInfo()
        u1.log(f"refreshnodkey old:{u1.nodKey}")
        registerGuest(1)
        refreshNodKey()
        u1.log(f"refreshnodkey new:{u1.nodKey}")
        loadGusetUser()
