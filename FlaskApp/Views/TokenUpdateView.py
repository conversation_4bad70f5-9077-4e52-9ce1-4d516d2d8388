#!/usr/bin/python3
# -*- coding: utf-8 -*-

"""
更新token管理View
编写人：darkedge
编写日期：2023年04月24日

"""

import datetime

from flask import  jsonify, request
from flask_admin import expose
from flask_security import current_user

import FlaskApp.Models as Models
from Celery.task import collectCrystalsTask, revokeTask
from FlaskApp.UserWorkThread import (
    UserInfo,
)
from Unit.Logger import logger
from Unit.Redis import redisHelper

from .WorkView import WorkView

MiningWorkMan = Models.MiningWorkMan
TokenWorkMan = Models.TokenWorkMan


class TokenUpdateView(WorkView):
    def __init__(self):
        super(TokenUpdateView, self).__init__()
        self.endpoint = "tokenupdate"
        self.indexHtml = "tokenupdate.html"
        self.name = "token更新管理"
        self.category = "服务"
        self.needRole = "user"
        self.startTime = datetime.datetime.now()

    def is_accessible(self):
        return current_user.is_active and current_user.is_authenticated

    @expose("/update_token/", methods=["POST"])
    def update_token_api(self):
        token = request.form.get("token")
        token = token.strip()
        model = self.checkToken(token)
        if isinstance(model, MiningWorkMan):
            task_id = redisHelper.getCeleryTaskId(model.email)
            revokeTask(task_id)
            redisHelper.removeCeleryTaskId(model.email)

            model.token = token
            model.save()
            logger.info(
                f"update_token 用户:{current_user.email} 触发刷新 {model.name} token:{token}"
            )

            result = collectCrystalsTask.delay(model.buildConfig())
            task_id = result.task_id
            redisHelper.setCeleryTaskId(model.email, task_id)
            return jsonify({"code": 200})
        elif isinstance(model, TokenWorkMan):
            return jsonify({"code": 201, "msg": "未支持"})
        logger.info(f"update_token 用户:{current_user.email} 无效 token:{token}")
        return model

    @expose("/query_token/", methods=["POST"])
    def query_token_api(self):
        # return jsonify({"code":200, "logs":["未支持aslkdjaslkdjalksjdklasjdalskd","asd"] })
        token = request.form.get("token")
        token = token.strip()
        model = self.checkToken(token)

        if isinstance(model, MiningWorkMan):
            logger.info(
                f"query_token 用户:{current_user.email} 查询日志 {model.name} token:{token}"
            )
            from Unit.Logger import RedisHandler

            r = RedisHandler(model.email)
            return jsonify({"code": 200, "logs": r.getLogs()})
        else:
            logger.info(
                f"query_token 用户:{current_user.email} 查询日志 异常 token:{token}"
            )
        return jsonify({"code": 201, "msg": "token无效"})

    def checkToken(self, token):
        user = UserInfo(token=token)
        if token and user.checkTokenExp():
            return jsonify({"code": 201, "msg": "token异常"})

        user.loadEmailWithToken()
        if user.kingdomId:
            model = MiningWorkMan.findToken(token)
            if model:
                return model
            else:
                model = TokenWorkMan.findToken(token)
                return model
