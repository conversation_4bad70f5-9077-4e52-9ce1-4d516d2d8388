#!/usr/bin/python3
# -*- coding: utf-8 -*-

"""
TokenWorkView
编写人：darkedge
编写日期：2022年05月13日

"""


# trunk-ignore(ruff/F401)
from flask import abort, jsonify, redirect, render_template, request, url_for
from flask_admin import expose
from flask_admin.actions import ActionsMixin, action
from flask_admin.model.template import EndpointLinkRowAction
# trunk-ignore(ruff/F401)
from flask_security import current_user

import FlaskApp
import FlaskApp.Models as Models
import FlaskApp.Views as Views

db = FlaskApp.db


class TokenWorkView(Views.MyModelView, ActionsMixin):
    """临时token管理"""

    def __init__(
        self,
        model=None,
        session=None,
        name=None,
        category=None,
        endpoint=None,
        url=None,
        static_folder=None,
        menu_class_name=None,
        menu_icon_type=None,
        menu_icon_value=None,
    ):
        model = Models.TokenWorkMan
        super(TokenWorkView, self).__init__(
            model,
            session,
            name=name,
            category=category,
            endpoint=endpoint,
            url=url,
            static_folder=static_folder,
            menu_class_name=menu_class_name,
            menu_icon_type=menu_icon_type,
            menu_icon_value=menu_icon_value,
        )
        # self.endpoint = 'tokenwork'
        # self.indexHtml = 'tokenwork.html'
        self.name = "Token管理"
        self.category = "独享"

    column_exclude_list = ["name", "pwd", "kingdom_id", "token"]
    column_searchable_list = ["email", "userKey"]
    column_extra_row_actions = [
        EndpointLinkRowAction("fa fa-refresh", "tokenworkman.active"),
    ]
    page_size = 100

    def on_model_change(self, form, model: Models.TokenWorkMan, is_created):
        if model.token:
            model.loadToken()
        return super().on_model_change(form, model, is_created)

    @action("active", "激活", "确定激活吗？")
    def action_active(self, ids):
        print(ids)
        FlaskApp.Units.loadTokenWorkMan(ids)
        return None

    @action("activeall", "激活所有", "确定激活所有吗？")
    def action_activeall(self, ids):
        FlaskApp.Units.loadAllTokenWorkMan()
        return None

    @expose("/active/", methods=("GET", "POST"))
    def active(self):
        id = request.args.get("id")
        FlaskApp.Units.loadTokenWorkMan([id])
        return self.index_view()
