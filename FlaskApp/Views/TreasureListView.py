 #!/usr/bin/python3
# -*- coding: utf-8 -*-

'''
装备
编写人：darkedge
编写日期：2022年05月08日

'''

from .WorkView import WorkView
from flask_security import current_user
from flask import url_for, redirect, render_template, request, abort,jsonify
from flask_admin import expose
from FlaskApp.UserWorkThread import UserInfo,WebWorkThread,userThreads,findUserThread,UserInfo,buildUser,buildTokenUser,startWorkInThread,logoutUser,setCache,changeSocks5,buildTokenUser,findTokenUser


class TreasureListView(WorkView):
    def __init__(self):
        super(TreasureListView, self).__init__()
        self.endpoint = 'treasurelist'
        self.indexHtml = 'treasurelist.html'
        self.name = "3号技师"
        self.needRole = "vipuser"
        
    @expose('/treasurelist/', methods=('GET', 'POST'))
    def treasurelist_api(self):
        user = self.getWorkUser()
        if user is None:
            userThread = findUserThread(userId=current_user.id)
            if userThread:
                user = userThread.user
        if user:
            res = user.canUpgradeTreasureList(isSuper=current_user.has_role('superuser'))
            if res is not None:
                return jsonify({"code":200,"data":res})
        return jsonify({"code":201 ,"msg":"异常"})

    @expose('/treasure_upgrade/', methods=('GET', 'POST'))
    def treasure_upgrade_api(self):
        user = self.getWorkUser()
        if user is None:
            userThread = findUserThread(userId=current_user.id)
            if userThread:
                user = userThread.user
        if user:
            treasureId = request.form.get('treasureId')
            index = int(request.form.get('index'))

            res = user.tryUpgradeTreasure(treasureId, index)
            return jsonify({"code":200,"count":res})
        return jsonify({"code":201 ,"msg":"异常"})

    @expose('/treasure_generate/', methods=('GET', 'POST'))
    def treasure_generate_api(self):
        user = self.getWorkUser()
        if user is None:
            userThread = findUserThread(userId=current_user.id)
            if userThread:
                user = userThread.user
        if user:
            code = request.form.get('code')
            if code:
                res = user.treasureOpen(itemCode=code)
                return jsonify({"code":200,"count":res})
        return jsonify({"code":201 ,"msg":"异常"})

    @expose('/treasure_equip/', methods=('GET', 'POST'))
    def treasure_equip_api(self):
        user = self.getWorkUser()
        if user is None:
            userThread = findUserThread(userId=current_user.id)
            if userThread:
                user = userThread.user
        if user:
            treasureId = request.form.get('treasureId')
            if treasureId:
                user.runTasks([user.treasureEquipA(treasureId, True)])
                return jsonify({"code":200,"msg":"ok"})

        return jsonify({"code":201 ,"msg":"异常"})
