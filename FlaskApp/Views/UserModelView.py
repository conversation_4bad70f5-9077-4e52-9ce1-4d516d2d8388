#!/usr/bin/python3
# -*- coding: utf-8 -*-

"""
功能描述：MyModelView
编写人：darkedge
编写日期：2022年05月08日
"""

from flask_admin import expose,BaseView
from flask_security import current_user
from flask import url_for, redirect, render_template, request, abort,jsonify

class UserModelView(BaseView):
    def is_accessible(self):
        return (current_user.is_active and
                current_user.is_authenticated and
                current_user.has_role('handupuser')
        )
    
    def _handle_view(self, name, **kwargs):
        if not self.is_accessible():
            if current_user.is_authenticated:
                # permission denied
                abort(403)
            else:
                # login
                return redirect(url_for('security.login', next=request.url))

def apiCache(func):
    def wrapper(*args, **kwargs):
        ret = func(*args, **kwargs)
        return ret
    return wrapper