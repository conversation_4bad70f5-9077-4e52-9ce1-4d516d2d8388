#!/usr/bin/python3
# -*- coding: utf-8 -*-

'''
vip商店
编写人：darkedge
编写日期：2022年05月08日

'''

from .WorkView import WorkView
from flask_security import current_user
from flask import url_for, redirect, render_template, request, abort,jsonify
from flask_admin import expose
from FlaskApp.UserWorkThread import UserInfo,WebWorkThread,userThreads,findUserThread,UserInfo,buildUser,buildTokenUser,startWorkInThread,logoutUser,setCache,changeSocks5,buildTokenUser,findTokenUser

class VipListView(WorkView):
    def __init__(self):
        super(VipListView, self).__init__()
        self.endpoint = 'viplist'
        self.indexHtml = 'viplist.html'
        self.name = "8号技师"
        self.needRole = "handupuser"
        

    @expose('/viplist/', methods=('GET', 'POST'))
    def viplist_api(self):
        userThread = findUserThread(userId=current_user.id)
        if userThread:
            user = userThread.user
            res = user.importantVipList()
            if res is not None:
                return jsonify({"code":200,"data":res})
        return jsonify({"code":201 ,"msg":"异常"})

    @expose('/viplist_buy/', methods=('GET', 'POST'))
    def viplist_buy_api(self):
        userThread = findUserThread(userId=current_user.id)
        if userThread:
            user = userThread.user
            code = request.form.get('code')
            amount = request.form.get('amount')
            count = int(request.form.get('count'))
            if count > 100:
                if not current_user.has_role('superuser'):
                    count = 100
            res = user.tryBuyVipShopWeb(code, amount, count,isSuper=current_user.has_role('superuser'))
            if res == -1:
                return jsonify({"code":201,"msg":"30秒只能使用一次"})
            return jsonify({"code":200,"msg":"预约成功，请在游戏中查看结果"})
        return jsonify({"code":201 ,"msg":"异常"})