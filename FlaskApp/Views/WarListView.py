#!/usr/bin/python3
# -*- coding: utf-8 -*-

"""
战争列表
编写人：darkedge
编写日期：2022年05月08日

"""

# trunk-ignore(ruff/F401)
from flask import abort, jsonify, redirect, render_template, request, url_for
from flask_admin import expose
from flask_security import current_user

from FlaskApp.UserWorkThread import (
    findUserThread,
    startWorkInThread,
)

from .WorkView import WorkView


class WarListView(WorkView):
    def __init__(self):
        super(WarListView, self).__init__()
        self.endpoint = "warlist"
        self.indexHtml = "warlist.html"
        self.name = "9号技师"
        self.needRole = "vip4user"

    @expose("/warlist/", methods=("GET", "POST"))
    def warlist_api(self):
        userThread = findUserThread(userId=current_user.id)
        if userThread:
            user = userThread.user
            res = user.cacheGetAllianceBattleList()
            if res is not None:
                return jsonify({"code": 200, "data": res})
        return jsonify({"code": 201, "msg": "异常"})

    @expose("/warget/", methods=("GET", "POST"))
    def warget_api(self):
        userThread = findUserThread(userId=current_user.id)
        if userThread:
            user = userThread.user
            res = startWorkInThread(userThread, workType=6)
            if res:
                return jsonify({"code": 200})
        return jsonify({"code": 201, "msg": "异常"})

    @expose("/warselfget/", methods=("GET", "POST"))
    def warselfget_api(self):
        userThread = findUserThread(userId=current_user.id)
        if userThread:
            user = userThread.user
            res = user.cacheAllianceBattleListWithTasks()
            if res:
                return jsonify({"code": 200})
            else:
                return jsonify({"code": 201, "msg": "没有有效的队列"})

        return jsonify({"code": 201, "msg": "异常"})

    @expose("/warjoin/", methods=("GET", "POST"))
    def warjoin_api(self):
        userThread = findUserThread(userId=current_user.id)
        if userThread:
            user = userThread.user
            rallyMoId = request.form.get("rallyMoId")
            troopNum = int(request.form.get("troopNum"))
            mainTroop = int(request.form.get("mainTroop"))

            res = user.tryJoinAttackAllianceBattle(rallyMoId, troopNum, mainTroop)
            return jsonify({"code": 200})

        return jsonify({"code": 201, "msg": "异常"})
