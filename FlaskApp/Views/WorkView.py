#!/usr/bin/python3
# -*- coding: utf-8 -*-

"""
WorkView 基类
编写人：darkedge
编写日期：2022年05月08日

"""

import json
# trunk-ignore(ruff/F401)
import platform
import random
import time
from concurrent.futures import Thread<PERSON>oolExecutor
from typing import List

# trunk-ignore(ruff/F401)
from flask import abort, jsonify, redirect, render_template, request, url_for
from flask_admin import expose
from flask_security import current_user

import FlaskApp.Units.TokenWorkManHelper as TokenWorkManHelper
from FlaskApp.UserWorkThread import (
    UserInfo,
    # trunk-ignore(ruff/F401)
    WebWorkThread,
    buildTokenUser,
    buildUser,
    changeSocks5,
    # trunk-ignore(ruff/F401)
    findTokenUser,
    findUserThread,
    logoutUser,
    # trunk-ignore(ruff/F401)
    setCache,
    startWorkInThread,
    # trunk-ignore(ruff/F401)
    userThreads,
)
from Unit.FileTool import loadS5List
from Unit.Logger import logger
from Unit.Redis import redisHelper
from Unit.UserInfoHelper import searchAll,Enum
from Unit.LandZoneUnit import crystalLands,AllZoneOfPolygon,PolygonWithPoint,isPointOnPolygonEdge

from .UserModelView import UserModelView

s5List = loadS5List()


class WorkView(UserModelView):
    def __init__(self):
        super(WorkView, self).__init__()
        self.endpoint = "work"
        self.indexHtml = "work.html"
        self.name = "1号技师"
        self.category = "服务"
        self.indexa = 0
        self.needRole = "user"
        self.otherInfo = {}

    def is_accessible(self):
        return (
            current_user.is_active
            and current_user.is_authenticated
            and current_user.has_role(self.needRole)
        )

    @expose("/logout/", methods=("GET", "POST"))
    def logout_api(self):
        logoutUser(current_user.id)

        return jsonify({"code": 200})

    @expose("/stop/", methods=("GET", "POST"))
    def stop_api(self):
        userThread = findUserThread(userId=current_user.id)
        if userThread:
            user = userThread.user
            token = user.token
            email = user.email
            password = user.pwd
            logoutUser(current_user.id)
            user = buildUser(
                current_user.id,
                token=token,
                email=email,
                password=password,
                userEmail=current_user.email,
            )
            if user:
                return jsonify({"code": 200})
            return jsonify({"code": 201, "msg": "连接异常"})

    @expose("/changesocks/", methods=("GET", "POST"))
    def changesocks_api(self):
        userThread = findUserThread(userId=current_user.id)
        if userThread:
            user = userThread.user
            changeSocks5(user)

        return jsonify({"code": 200})

    @expose("/", methods=("GET", "POST"))
    def index_view(self):
        isConnect = False
        workStatus = False
        workName = None
        name = "未登录"
        locX = 1024
        locY = 1024
        locW = 20
        userThread = findUserThread(userId=current_user.id)
        self.indexa += 1
        if userThread:
            user = userThread.user
            isConnect = True
            name = user.name
            workStatus = userThread.workStatus
            workName = userThread.workName
            locW = user.loc[0]
            locX = user.loc[1]
            locY = user.loc[2]
        # elif platform.system() == "Darwin":
        #     isConnect = True
        #     name = "test"

        workUsers = []
        if current_user.has_role("superuser"):
            from FlaskApp.Models import MiningWorkMan

            mans: List[MiningWorkMan] = MiningWorkMan.query.filter_by(active=True).all()
            index = 1
            for man in mans:
                if man.workStatu:
                    try:
                        _name = f"{man.name}({man.getKingdomTodayCrystalCount})"
                        workUsers.append(
                            {
                                "name": _name,
                                "token": man.token,
                                "socks5": man.socks5,
                                "index": index,
                            }
                        )
                    except Exception as e:
                        logger.error(f"获取工人信息失败:{e}\nemail:{man.email}")
                    index += 1
            # workUsers.append({"name":"luoluo","token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJfaWQiOiI2MWI4M2MyZDM3MWJmYTEzZDk0OTAyYzYiLCJraW5nZG9tSWQiOiI2MWI4NDFjOGJiZTRhZjdmNjZhYzNmNDkiLCJ3b3JsZElkIjoyMCwidmVyc2lvbiI6MTYxMSwiYnVpbGQiOjAsInBsYXRmb3JtIjoid2ViIiwidGltZSI6MTY4MDc5ODE5ODQzNywiY2xpZW50WG9yIjoiMCIsImlhdCI6MTY4MDc5ODE5OCwiZXhwIjoxNjgxNDAyOTk4LCJpc3MiOiJub2RnYW1lcy5jb20iLCJzdWIiOiJ1c2VySW5mbyJ9.YD0nibTBVDBi35VoMgta2F-ddBwuKuTZl4VkpBwPoJ4","socks5":"jj:<EMAIL>:36038","index":index})
        return self.render(
            self.indexHtml,
            connect=isConnect,
            name=name,
            workStatus=workStatus,
            workName=workName,
            locW=locW,
            locX=locX,
            locY=locY,
            endpoint=self.endpoint,
            workUsers=workUsers,
            otherInfo=self.otherInfo,
        )

    @expose("/connect/", methods=("GET", "POST"))
    def connect_api(self):
        token = request.form.get("token")
        email = request.form.get("email")
        password = request.form.get("password")

        user = buildUser(
            current_user.id,
            token=token,
            email=email,
            password=password,
            userEmail=current_user.email,
        )
        if user:
            if current_user.kingdom_id is None:
                current_user.kingdom_id = user.kingdomId
                current_user.query.session.commit()
            else:
                if user.kingdomId != current_user.kingdom_id:
                    if not current_user.has_role("superuser"):
                        user.log(
                            f"当前token kingdomId:{user.kingdomId} 不等于当前用户 kingdomId:{current_user.kingdom_id}"
                        )
                        logoutUser(current_user.id)
                        return jsonify({"code": 201, "msg": "不是绑定的游戏账号"})

            return jsonify({"code": 200})
        return jsonify({"code": 201, "msg": "连接异常"})

    @expose("/getfields/", methods=("GET", "POST"))
    def getfields_api(self):
        t1 = time.time()

        userThread = findUserThread(userId=current_user.id)

        t2 = time.time()
        power = 3
        searchpower = request.form.get("searchpower")
        if searchpower:
            power = int(searchpower)
        userLoc = None
        logInfo = logger.info
        if userThread:
            user = userThread.user
            logInfo = user.log
            userLoc = userLoc
        loc = None
        if request.form.get("loc"):
            loc = [int(v) for v in request.form.get("loc").split(",")]

        t3 = time.time()
        logInfo(f"request.form{request.form}")
        token = current_user.helpToken
        if not token:
            token = TokenWorkManHelper.workManCanUseToken()
            current_user.helpToken = token
        if token:
            u1 = UserInfo(
                f"{random.randint(1, 999999)}@search.com",
                token=token,
                socks5=random.choice(s5List),
                saveSocks5=True,
            )
            u1.loadEmailWithToken()
            u1.initLog(needTestS5=False)
            u1.loc = userLoc or loc
            data = u1.wsGetFields(loc=loc, more=True, power=power)
            if data:
                for k, v in data.items():
                    data[k] = u1.returnSortFieldsWithLevelDes(v)
            t4 = time.time()
            logInfo(f"总耗时:{round(t4-t1,2)},获取地图数据:{round(t3-t2,2)},获取地图数据耗时:{round(t4-t3,2)}")
            if not data:
                current_user.helpToken = None
                return jsonify({"code": 201, "msg": "小号异常，稍后再试"})
            return jsonify({"code": 200, "data": data})
        else:
            return jsonify({"code": 201, "msg": "没有可用的小号，稍后再试"})
        return jsonify({"code": 201, "msg": "some error"})

    @expose("/getfieldscvcblock/", methods=("GET", "POST"))
    def getfields_cvc_block_api(self):
        t1 = time.time()

        userThread = findUserThread(userId=current_user.id)

        t2 = time.time()
        userLoc = None
        logInfo = logger.info
        if userThread:
            user = userThread.user
            logInfo = user.log
            userLoc = userLoc
        loc = None
        if request.form.get("loc"):
            loc = [int(v) for v in request.form.get("loc").split(",")]

        t3 = time.time()
        logInfo(f"getfields_cvc_block_api request.form{request.form}")
        tokens = TokenWorkManHelper.workManCanUseTokenWithCount(2)
        if tokens:
            polygon = PolygonWithPoint(loc[1],loc[2])
            zones = AllZoneOfPolygon(polygon)
            codes = Enum.OBJECT_MAP_FIELD_LIST
            data = searchAll(tokens, loc,codes= codes, zones=zones)
            t4 = time.time()
            realData = {}
            # 删除资源
            for code in [20100101,20100102,20100103,20100104,20700601,20700602,20700603,20700604]:
                if code in data:
                    del data[code]
            for code in codes:
                datas = data.get(code)
                if datas:
                    realData[code] = []
                    for obj in datas:
                        try:
                            if isPointOnPolygonEdge(obj["loc"][-2:],polygon):
                                realData[code].append(obj)
                        except Exception as e:
                            logger.error(e,exc_info=True)
            t5 = time.time()
            logInfo(f"cvc块总耗时:{round(t4-t1,2)},计算区域:{round(t5 - t4,2)},获取地图数据:{round(t3-t2,2)},获取地图数据耗时:{round(t4-t3,2)}")
            if not data:
                current_user.helpToken = None
                return jsonify({"code": 201, "msg": "小号异常，稍后再试"})
            return jsonify({"code": 200, "data": realData})
        else:
            return jsonify({"code": 201, "msg": "没有可用的小号，稍后再试"})
        return jsonify({"code": 201, "msg": "some error"})

    @expose("/getdkall/", methods=("GET", "POST"))
    def getdkall_api(self):
        logger.info(f"getdkall 用户:{current_user.email} 触发刷新")
        userThread = findUserThread(userId=current_user.id)
        logInfo = logger.info
        userLoc = None
        user = None
        if userThread:
            user = userThread.user
            logInfo = user.log
            userLoc = user.loc
        loc = None
        if request.form.get("loc"):
            loc = [int(v) for v in request.form.get("loc").split(",")]
        t1 = time.time()

        tokens = current_user.helpTokens
        if not tokens:
            tokens = TokenWorkManHelper.workManCanUseTokenWithCount(7, isGoogle=True)
            current_user.helpTokens = tokens
        t2 = time.time()
        if tokens:
            zones = [v for v in range(4096)]
            users = [
                UserInfo(
                    f"dk_{random.randint(1, 9999)}@search",
                    token=token,
                    socks5=random.choice(s5List),
                    saveSocks5=True,
                )
                for token in tokens
            ]
            data = {}

            def searchDk(user: UserInfo, zones):
                datas = {}
                try:
                    datas = user.searchFieldWithZoneSize(
                        user.loc,
                        zones=zones,
                        codes=[
                            20200104,
                            20200201,
                            20200202,
                            20200203,
                            20200204,
                            20700505,
                            20700506,
                            20200206,
                            20200207,
                        ],
                    )
                except Exception as e:
                    user.log(f"searchDk error:{e}")
                return datas

            with ThreadPoolExecutor(max_workers=len(users)) as executor:
                splitNum = len(zones) // len(users)
                tasks = []
                for index in range(len(users)):
                    user = users[index]
                    user.loadEmailWithToken()
                    user.initLog(needTestS5=False)
                    user.loc = userLoc or loc
                    subZones = zones[index * splitNum : (index + 1) * splitNum]
                    tasks.append(executor.submit(searchDk, user, subZones))
                executor.shutdown(wait=True)
                for task in tasks:
                    datas = task.result()
                    for k, v in datas.items():
                        if k in data:
                            data[k] += v
                        else:
                            data[k] = v
                for code in [20200201, 20700506]:
                    if code in data:
                        data[code] = [
                            v
                            for v in filter(
                                lambda obj: obj.get("level", 0) > 4, data[code]
                            )
                        ]

            t3 = time.time()
            user = user or users[0]
            for k, v in data.items():
                data[k] = user.returnSortFieldsWithLevelDes(v)
            t4 = time.time()
            logInfo(f"dk总耗时:{round(t4-t1,2)},获取地图数据:{round(t3-t2,2)},获取地图数据耗时:{round(t4-t3,2)}")
            # user.log(data)
            if not data:
                current_user.helpTokens = None
                return jsonify({"code": 201, "msg": "小号异常，稍后再试"})
            return jsonify({"code": 200, "data": data})
        else:
            return jsonify({"code": 201, "msg": "没有可用的小号，稍后再试"})
        return jsonify({"code": 201, "msg": "some error"})

    @expose("/getcrystalall/", methods=("GET", "POST"))
    def getcrystalall_api(self):
        logger.info(f"getcrystalall 用户:{current_user.email} 触发刷新")
        userThread = findUserThread(userId=current_user.id)
        logInfo = logger.info
        userLoc = None
        user = None
        if userThread:
            user = userThread.user
            logInfo = user.log
            userLoc = user.loc
        loc = None
        if request.form.get("loc"):
            loc = [int(v) for v in request.form.get("loc").split(",")]

        key = f"getcrystalall_{loc[0]}"
        timeKey = f"getcrystalall_api_time_{loc[0]}"

        if redisHelper.get(timeKey):
            data = redisHelper.getJson(key)
            if data:
                user = UserInfo()
                user.loc = loc
                for k, v in data.items():
                    data[k] = user.returnSortFieldsWithLevelDes(v)
                logger.info(f"getcrystalall 用户:{current_user.email} 使用缓存")
                return jsonify({"code": 200, "data": data})

        redisHelper.set(timeKey, 1, ex=60)

        tokens = current_user.helpTokens
        if not tokens:
            tokens = TokenWorkManHelper.workManCanUseTokenWithCount(8, isGoogle=True)
            if not tokens:
                tokens =  TokenWorkManHelper.workManCanUseTokenWithCount(8, isGoogle=False)
            current_user.helpTokens = tokens
        if tokens:
            data = searchAll(tokens, userLoc or loc, logInfo=logInfo)
            # user.log(data)
            if not data:
                current_user.helpTokens = None
                return jsonify({"code": 201, "msg": "小号异常，稍后再试"})
            redisHelper.setJson(key, data, ex=60 * 10)
            return jsonify({"code": 200, "data": data})
        else:
            return jsonify({"code": 201, "msg": "没有可用的小号，稍后再试"})
        return jsonify({"code": 201, "msg": "some error"})

    @expose("/getcrystalall2/", methods=("GET", "POST"))
    def getcrystalall2_api(self):
        logger.info(f"getcrystalall2 用户:{current_user.email} 触发刷新")
        userThread = findUserThread(userId=current_user.id)
        logInfo = logger.info
        userLoc = None
        user = None
        if userThread:
            user = userThread.user
            logInfo = user.log
            userLoc = user.loc
        loc = None
        if request.form.get("loc"):
            loc = [int(v) for v in request.form.get("loc").split(",")]

        key = f"getcrystalall2_{loc[0]}"
        timeKey = f"getcrystalall2_api_time_{loc[0]}"

        if redisHelper.get(timeKey):
            data = redisHelper.getJson(key)
            if data:
                user = UserInfo()
                user.loc = loc
                for k, v in data.items():
                    data[k] = user.returnSortFieldsWithLevelDes(v)
                logger.info(f"getcrystalall2 用户:{current_user.email} 使用缓存")
                return jsonify({"code": 200, "data": data})

        redisHelper.set(timeKey, 1, ex=60)

        tokens = current_user.helpTokens
        if not tokens:
            tokens = TokenWorkManHelper.workManCanUseTokenWithCount(14)
            current_user.helpTokens = tokens
        if tokens:
            zones = []
            for i in range(1, 4096):
                if i not in crystalLands:
                    zones.append(i)
            data = searchAll(tokens, userLoc or loc, logInfo=logInfo, zones = zones)
            # user.log(data)
            if not data:
                current_user.helpTokens = None
                return jsonify({"code": 201, "msg": "没有数据"})
            redisHelper.setJson(key, data, ex=60 * 10)
            return jsonify({"code": 200, "data": data})
        else:
            return jsonify({"code": 201, "msg": "没有可用的小号，稍后再试"})
        return jsonify({"code": 201, "msg": "some error"})

    @expose("/getcrystal/", methods=("GET", "POST"))
    def getcrystal_api(self):
        t1 = time.time()

        userThread = findUserThread(userId=current_user.id)
        logInfo = logger.info
        userLoc = None
        if userThread:
            user = userThread.user
            logInfo = user.log
            userLoc = user.loc
        loc = None
        if request.form.get("loc"):
            loc = [int(v) for v in request.form.get("loc").split(",")]

        token = current_user.helpToken
        if not token:
            token = TokenWorkManHelper.workManCanUseToken()
            current_user.helpToken = token
        t2 = time.time()
        if token:
            u1 = UserInfo(
                f"{random.randint(1, 999999)}@search.com",
                token=token,
                socks5=random.choice(s5List),
                saveSocks5=True,
            )
            u1.loadEmailWithToken()
            u1.initLog(needTestS5=False)
            u1.loc = userLoc or loc
            data = u1.searchCrystalWithZoneSize(loc=loc)
            t3 = time.time()
            if data:
                data = {20100105: u1.returnSortFieldsWithLevelDes(data[20100105])}
            else:
                data = {}
            t4 = time.time()
            logInfo(f"总耗时:{round(t4-t1,2)},获取地图数据:{round(t3-t2,2)},获取地图数据耗时:{round(t4-t3,2)}")
            # user.log(data)
            if not data:
                current_user.helpToken = None
                return jsonify({"code": 201, "msg": "小号异常，稍后再试"})
            return jsonify({"code": 200, "data": data})
        else:
            return jsonify({"code": 201, "msg": "没有可用的小号，稍后再试"})
        return jsonify({"code": 201, "msg": "some error"})

    @expose("/collection/", methods=("GET", "POST"))
    def collection_api(self):
        user = None
        workUser = self.getWorkUser()
        if workUser:
            user = workUser
        else:
            userThread = findUserThread(userId=current_user.id)
            if userThread:
                user = userThread.user
        if user:
            loc = json.loads(request.form.get("loc"))
            troopNum = int(request.form.get("troopNum"))
            isCrystal = bool(int(request.form.get("isCrystal")))
            res = user.collectionResource(
                loc, minCount=1, maxCount=troopNum, isCrystal=isCrystal
            )
            return jsonify({"code": 200, "data": res.helperString})
        return jsonify({"code": 201, "msg": "some error"})

    @expose("/attackMonster/", methods=("GET", "POST"))
    def attackMonster_api(self):
        userThread = findUserThread(userId=current_user.id)
        if userThread:
            user = userThread.user
            loc = json.loads(request.form.get("loc"))
            troopNum = int(request.form.get("troopNum"))
            isTreasure = False
            if request.form.get("mainTroop"):
                mainTroop = request.form.get("mainTroop")
                isTreasure = mainTroop == "3"

            res = user.attackMonster(
                loc, isTreasure=isTreasure, isWeb=True, uTroopNum=troopNum
            )
            if res is None:
                return jsonify({"code": 202, "msg": "活力不足或队列满了"})
            return jsonify({"code": 200})
        return jsonify({"code": 201, "msg": "some error"})

    @expose("/startRally/", methods=("GET", "POST"))
    def startRally_api(self):
        user = None
        workUser = self.getWorkUser()
        if workUser:
            user = workUser
        else:
            userThread = findUserThread(userId=current_user.id)
            if userThread:
                user = userThread.user
        if user:
            marchType = 5
            loc = json.loads(request.form.get("loc"))
            troopNum = int(request.form.get("troopNum"))
            mainTroop = int(request.form.get("mainTroop"))
            if request.form.get("marchType"):
                marchType = int(request.form.get("marchType"))
            res = user.startRally(loc, mainTroop, troopNum, marchType=marchType)
            if res:
                return jsonify({"code": 200})
            elif res is None:
                return jsonify({"code": 202, "msg": "体力不足"})
        return jsonify({"code": 201, "msg": "some error"})

    @expose("/autoJoinBattle/", methods=("GET", "POST"))
    def autoJoinBattle_api(self):
        userThread = findUserThread(userId=current_user.id)
        if userThread:
            # user = userThread.user
            followInfo = json.loads(request.form.get("followInfo"))
            res = startWorkInThread(userThread, workType=1, args=(followInfo,))
            if res:
                return jsonify({"code": 200})
        return jsonify({"code": 201, "msg": "some error"})

    @expose("/autoJoinWarfareBattle/", methods=("GET", "POST"))
    def autoJoinWarfareBattle_api(self):
        userThread = findUserThread(userId=current_user.id)
        if userThread:
            # user = userThread.user
            warInfo = json.loads(request.form.get("warInfo"))
            res = startWorkInThread(userThread, workType=3, args=(warInfo,))
            if res:
                return jsonify({"code": 200})
        return jsonify({"code": 201, "msg": "some error"})

    @expose("/autogarrison/", methods=("GET", "POST"))
    def autogarrison_api(self):
        userThread = findUserThread(userId=current_user.id)
        if userThread:
            # user = userThread.user
            garrisonInfo = json.loads(request.form.get("garrisonInfo"))
            res = startWorkInThread(userThread, workType=2, args=(garrisonInfo,))
            if res:
                return jsonify({"code": 200})
        return jsonify({"code": 201, "msg": "some error"})

    @expose("/autoHarassment/", methods=("GET", "POST"))
    def autoHarassment_api(self):
        userThread = findUserThread(userId=current_user.id)
        if userThread:
            # user = userThread.user
            harassmentInfo = json.loads(request.form.get("harassmentInfo"))
            res = startWorkInThread(userThread, workType=4, args=(harassmentInfo,))
            if res:
                return jsonify({"code": 200})
        return jsonify({"code": 201, "msg": "some error"})

    @expose("/autoHarassmentAttack/", methods=("GET", "POST"))
    def autoHarassment_attack_api(self):
        userThread = findUserThread(userId=current_user.id)
        if userThread:
            # user = userThread.user
            harassmentInfo = json.loads(request.form.get("harassmentInfo"))
            res = startWorkInThread(userThread, workType=5, args=(harassmentInfo,))
            if res:
                return jsonify({"code": 200})
        return jsonify({"code": 201, "msg": "some error"})

    @expose("/goback_all/", methods=("GET", "POST"))
    def goback_all_api(self):
        userThread = findUserThread(userId=current_user.id)
        if userThread:
            user = userThread.user
            if user.getTroops():
                for field in user.troopFields:
                    user.kingdomId = field.get("kingdomId")
                    moId = field.get("_id")
                    # marchType = field.get("marchType")
                    # state = field.get("state")
                    endTime = field.get("endTime")
                    t = user.compareDateTimeWithNow(endTime)

                    if t < 0:
                        if user.fieldMarchReturn(moId):
                            user.log("撤回卡死队列成功")

                return jsonify({"code": 200, "msg": "完成"})

        return jsonify({"code": 201, "msg": "异常"})

    @expose("/share/", methods=("GET", "POST"))
    def share_api(self):
        userThread = findUserThread(userId=current_user.id)
        if userThread:
            user = userThread.user
            info = json.loads(request.form.get("info"))
            res = user.sendChat(info)
            if res:
                return jsonify({"code": 200})
        return jsonify({"code": 201, "msg": "some error"})

    @expose("/energy/", methods=("GET", "POST"))
    def energy_api(self):
        userThread = findUserThread(userId=current_user.id)
        if userThread:
            user = userThread.user
            res = user.chargeEnergy()
            if res > 0:
                return jsonify({"code": 200})
            else:
                return jsonify({"code": 201, "msg": "活力道具不足"})
        return jsonify({"code": 201, "msg": "some error"})

    @expose("/getMyFields/", methods=("GET", "POST"))
    def getMyFields_api(self):
        userThread = findUserThread(userId=current_user.id)
        if userThread:
            user = userThread.user
            res = user.taskAll()
            if res:
                return jsonify({"code": 200, "data": user.fieldTasks})
        return jsonify({"code": 201, "msg": "some error"})

    @expose("/kick/", methods=("GET", "POST"))
    def kick_api(self):
        """踢回城"""
        # userThread = findUserThread(userId=current_user.id)

        token = current_user.helpToken
        if not token:
            token = TokenWorkManHelper.workManCanUseToken()
            current_user.helpToken = token
        if token:
            u1 = UserInfo(
                f"{random.randint(1, 999999)}@search.com",
                token=token,
                socks5=random.choice(s5List),
                saveSocks5=True,
            )
            u1.initLog(needTestS5=False)
            moId = request.form.get("moId")
            u1.kingdomSupportReturn(moId)
            return jsonify({"code": 200})
        else:
            return jsonify({"code": 201, "msg": "没有可用的小号，稍后再试"})
        return jsonify({"code": 201, "msg": "some error"})

    @expose("/cvckick/", methods=("GET", "POST"))
    def cvckick_api(self):
        """cvc踢回大陆"""
        userId = request.form.get("userId")
        name = request.form.get("name")
        foId = request.form.get("foId")
        worldId = request.form.get("worldId")
        from Celery.redis import redisCeleryHelper
        from Unit.ZeroUnit import ZeroSend
        redisCeleryHelper.addCvcBlockUsers(userId)
        redisCeleryHelper.setCvcKingdomInfo(userId, {"name":name, "worldId":worldId})
        redisCeleryHelper.setCvcFoIdInfo(foId, {"name":name, "worldId":worldId, "userId":userId})
        ZeroSend.tryCvcKingKick([foId])
        return jsonify({"code": 200})

    @expose("/dragoPointGet/", methods=("GET", "POST"))
    def dragoPointGet_api(self):
        user = None
        workUser = self.getWorkUser()
        if workUser:
            user = workUser
        else:
            userThread = findUserThread(userId=current_user.id)
            if userThread:
                user = userThread.user
        if user:
            res = user.kingdomProfileMy()
            if res:
                point = 0
                dragoActionPoint = res.get("dragoActionPoint")
                if dragoActionPoint:
                    point = dragoActionPoint.get("value")

                return jsonify({"code": 200, "data": point})
        else:
            logger.info("dragoPointGet_api 没有user")
        return jsonify({"code": 201, "msg": "some error"})

    @expose("/dragoIdGet/", methods=("GET", "POST"))
    def dragoIdGet_api(self):
        user = None
        workUser = self.getWorkUser()
        if workUser:
            user = workUser
        else:
            userThread = findUserThread(userId=current_user.id)
            if userThread:
                user = userThread.user
        if user:
            dragos = user.dragoLairList()
            realDragos = []
            if dragos:
                realDragos = [drago.get("tokenId") for drago in dragos]

            return jsonify({"code": 200, "data": realDragos})
        return jsonify({"code": 201, "msg": "some error"})

    @expose("/insertHidden/", methods=("GET", "POST"))
    def insertHidden_api(self):
        datas = request.form.get("mines")
        from Celery.redis import redisCeleryHelper
        if datas:
            mines = json.loads(datas)
            worldId = mines[0].get("loc")[0]
            crystals = [
                f'{crystalMine["level"]}_{crystalMine["loc"][1]}_{crystalMine["loc"][2]}'
                for crystalMine in mines
            ]
            oldDatas = redisCeleryHelper.getCrystalList(worldId)
            for crystal in crystals:
                if crystal not in oldDatas:
                    oldDatas.append(crystal)

            redisCeleryHelper.setCrystalList(worldId, oldDatas)
            return jsonify({"code": 200})
        return jsonify({"code": 201, "msg": "some error"})

    def getWorkUser(self):
        # 获取superquanx的work用户
        workUser = request.form.get("workUser")
        if workUser:
            # trunk-ignore(bandit/B307)
            user = buildTokenUser(None, eval(workUser))
            return user
        return None