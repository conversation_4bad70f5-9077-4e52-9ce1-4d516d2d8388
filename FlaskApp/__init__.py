#!/usr/bin/python3
# -*- coding: utf-8 -*-

"""
功能描述：web服务端初始化配置
编写人：darkedge
编写日期：2022年05月08日

"""

from flask import Flask,request,session
from flask_caching import Cache
from flask_sqlalchemy import SQLAlchemy
from flask_babel import Babel
import flask_admin
from flask_admin.base import Bootstrap4Theme
import platform
import sys

print(sys.argv)
configPath = 'config.pyi'
template_folder = 'templates'
static_folder = 'static'

def get_locale():
    if request.args.get('lang'):
        session['lang'] = request.args.get('lang')
    lang = session.get('lang', 'zh_Hans_CN')
    print(f"lang: {lang}")
    return lang

app = Flask("FlaskApp",template_folder=template_folder,static_folder=static_folder)
babel = Babel(app, locale_selector=get_locale)

# Create admin
admin = flask_admin.Admin(
    app,
    '洗脚城',
    theme=Bootstrap4Theme(base_template='my_master.html')

)

try:
    app.config.from_pyfile(configPath)
    app.config["DATABASE_FILE"] = app.config["DATABASE_FILE"][3:]
    app.config["SQLALCHEMY_DATABASE_URI"] = 'sqlite:///' + app.config["DATABASE_FILE"]
except FileNotFoundError as _e:
    app.config.from_pyfile(f"../{configPath}")

if 'uwsgi' not in sys.argv or platform.system() == "Darwin":
    app.config["CACHE_TYPE"] = "SimpleCache"

cache = Cache(app)
db = SQLAlchemy(app)
try:
    from .UserWorkThread import setCache
    setCache(cache)
except ImportError as _e:
    print("载入setCache失败 跳过")


try:
    from flask_migrate import Migrate
    migrate = Migrate(app,db)
except ImportError:
    print("没有安装flask_migrate")