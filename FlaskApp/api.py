#!/usr/bin/python3
# -*- coding: utf-8 -*-

"""
功能描述：api
编写人：darkedge
编写日期：2022年05月08日

"""
import random
import time

from flask import Blueprint
# trunk-ignore(ruff/F401)
from flask import abort, jsonify, redirect, render_template, request, url_for
# trunk-ignore(ruff/F401)
from flask_admin import expose
from flask_cors import cross_origin
# trunk-ignore(ruff/F401)
from flask_security import current_user

from Celery.redis import redisCeleryHelper
from Celery.schedule import listCrystalTask
from FlaskApp.Models import MiningWorkMan,TokenWorkMan,LogRecordModel
from FlaskApp.Units import (
    addHandleWork,
    loadAppleAccount,
    workManCanUseToken,
    workManCanUseTokenWithCount,
    workManClaimPkg,
    workManGoBack,
)
from FlaskApp.UserWorkThread import (
    UserInfo,
    # WebWorkThread,
    buildTokenUser,
    # buildUser,
    # changeSocks5,
    findTokenUser,
    # findUserThread,
    # logoutUser,
    # setCache,
    # startWorkInThread,
    # userThreads,
)
from Unit.FileTool import loadS5List
from Unit.LandZoneUnit import MaxSixThreadZoneSize
from Unit.Logger import logger
from Unit.Redis import redisHelper
from Unit.ZeroUnit import ZeroSend
s5List = loadS5List()

api = Blueprint("api", __name__, url_prefix="/api")
normalApi = Blueprint("normalApi", __name__, url_prefix="/normal")

@normalApi.route("/gettoken", methods=["POST", "OPTIONS"])
def getToken():
    """获取一个小号token"""
    token = workManCanUseToken()
    if token:
        return jsonify({"code": 200, "token": token})
    return jsonify({"code": 201, "msg": "没有可用的token"})


@normalApi.route("/gettokens", methods=["POST", "OPTIONS"])
def getTokens():
    """获取一个小号token"""
    jsonValue = request.get_json()
    count = jsonValue.get("count")
    isGoogle = jsonValue.get("isGoogle", 0)
    isGoogle = bool(isGoogle)
    logger.info(f"gettokens {count}")
    tokens = workManCanUseTokenWithCount(int(count), isGoogle=isGoogle)
    if tokens:
        return jsonify({"code": 200, "tokens": tokens})
    return jsonify({"code": 201, "msg": "没有可用的token"})


@normalApi.route("/supergo", methods=["POST", "OPTIONS"])
@cross_origin()
def supergo():
    jsonValue = request.get_json()
    if jsonValue:
        moId = jsonValue.get("moId")
        if moId:
            count = jsonValue.get("count")
            if count and not isinstance(count, int) and count.isdigit():
                count = int(count)
            else:
                count = 25
            bugCount = workManGoBack(moId, count=count)
            logger.info(f"api触发多倍撤回{bugCount}次")
            if bugCount == -1:
                return jsonify({"code": 201, "msg": "异常数据??"})
            return jsonify({"code": 200, "count": bugCount})

    return jsonify({"code": 201, "msg": "数据异常"})


@normalApi.route("/claim", methods=["POST", "OPTIONS"])
@cross_origin()
def superclaim():
    jsonValue = request.get_json()
    if jsonValue:
        pkgId = jsonValue.get("pkgId")
        if pkgId:
            count = jsonValue.get("count")
            if count and not isinstance(count, int) and count.isdigit():
                count = int(count)
            else:
                count = 1
            bugCount = workManClaimPkg(pkgId, count=count)
            logger.info(f"api触发多倍领取{bugCount}次")
            if bugCount == -1:
                return jsonify({"code": 201, "msg": "异常数据??"})
            return jsonify({"code": 200, "count": bugCount})

    return jsonify({"code": 201, "msg": "数据异常"})


@normalApi.route("/getcrystals", methods=["POST", "OPTIONS"])
@cross_origin()
def getcrystals():
    jsonValue = request.get_json()
    loc = [int(v) for v in jsonValue["loc"].split(",")]
    size = jsonValue.get("size")
    if not size:
        size = MaxSixThreadZoneSize
    else:
        size = int(size)

    t1 = time.time()
    token = workManCanUseToken()
    if token:
        u1 = UserInfo(
            f"{random.randint(1, 999999)}@search.com",
            token=token,
            socks5=random.choice(s5List),
        )
        u1.loc = loc
        t2 = time.time()
        data = u1.searchCrystalWithZoneSize(loc=loc, zoneSize=size)
        if data:
            data = {
                20100105: u1.returnSortFieldsWithLevelDes(
                    data[20100105], clearOccupied=True
                )
            }
        else:
            data = {}
        t3 = time.time()
        t4 = time.time()
        u1.log(f"总耗时:{round(t4-t1,2)},获取地图数据:{round(t3-t2,2)},获取地图数据耗时:{round(t4-t3,2)}")

        if not data:
            return jsonify({"code": 201, "msg": "小号异常，稍后再试"})
        return jsonify({"code": 200, "data": data})
    else:
        return jsonify({"code": 201, "msg": "没有可用的小号，稍后再试"})


@normalApi.route("/savefieldtasks", methods=["POST", "OPTIONS"])
@cross_origin()
def saveFieldTasks():
    jsonValue = request.get_json()
    if jsonValue:
        kingdomId = jsonValue.get("kingdomId")
        level = int(jsonValue.get("level"))
        fieldTasks = jsonValue.get("fieldTasks")
        loc = jsonValue.get("loc")
        if kingdomId:
            if len(fieldTasks) > 0:
                redisHelper.saveFieldTasks(kingdomId, fieldTasks)
                addHandleWork(kingdomId, loc, level=level)
            return jsonify({"code": 200})
    return jsonify({"code": 201, "msg": "数据异常"})


@normalApi.route("/savefieldtask", methods=["POST", "OPTIONS"])
@cross_origin()
def saveFieldTask():
    jsonValue = request.get_json()
    if jsonValue:
        kingdomId = jsonValue.get("kingdomId")
        fieldTask = jsonValue.get("fieldTask")
        if kingdomId:
            if fieldTask:
                redisHelper.saveFieldTask(kingdomId, fieldTask)
            return jsonify({"code": 200})
    return jsonify({"code": 201, "msg": "数据异常"})


@normalApi.route("/saveuser", methods=["POST", "OPTIONS"])
@cross_origin()
def saveUser():
    jsonValue = request.get_json()
    if jsonValue:
        users = jsonValue.get("users")
        if users:
            for u in users:
                if len(u.split("-")) > 1:
                    TokenWorkMan(userKey=u).save()
                else:
                    TokenWorkMan(appleSubId=u).save()

            return jsonify({"code": 200})
    return jsonify({"code": 201, "msg": "数据异常"})


@normalApi.route("/addworkuser", methods=["POST", "OPTIONS"])
@cross_origin()
def addWorkUser():
    jsonValue = request.get_json()
    if jsonValue:
        count = jsonValue.get("count")
        if count:
            if loadAppleAccount(int(count)):
                return jsonify({"code": 200})
            else:
                return jsonify({"code": 201, "msg": "数量不足"})
    return jsonify({"code": 201, "msg": "数据异常"})


@normalApi.route("/test", methods=["POST", "OPTIONS"])
@cross_origin()
def testapi():
    jsonValue = request.get_json()
    if jsonValue:
        return jsonify({"code": 200, "data": jsonValue})
    return jsonify({"code": 201, "msg": "数据异常"})


@normalApi.route("/timeout", methods=["POST", "OPTIONS"])
@cross_origin()
def timeout_api():
    jsonValue = request.get_json()
    if jsonValue:
        t = jsonValue.get("t")
        time.sleep(int(t))
        return jsonify({"code": 200})
    return jsonify({"code": 201, "msg": "数据异常"})


@normalApi.route("/supergo2", methods=["POST"])
def supergo2():
    jsonValue = request.get_json()
    if jsonValue:
        token = jsonValue.get("token")
        kingdomId = jsonValue.get("kingdomId")
        moId = jsonValue.get("moId")
        user = findTokenUser(token)
        if not user:
            user = buildTokenUser(token)
            user.kingdomId = kingdomId
        if not user:
            return jsonify({"code": 201, "msg": "无效token?"})

        bugCount = user.gobackMoId(moId, isSuper=True)
        if bugCount == -1:
            return jsonify({"code": 201, "msg": "异常数据??"})
        return jsonify({"code": 200, "count": bugCount})

    return jsonify({"code": 201, "msg": "数据异常"})


@api.route("/getcrystals", methods=["GET", "OPTIONS"])
@cross_origin()
def getcrystalsWithWorldId():
    jsonValue = request.get_json()
    worldId = jsonValue.get("worldId", 20)

    data = {
        "crystals": redisCeleryHelper.getCrystalList(worldId),
        "dragoMines": redisCeleryHelper.getDragoMineList(worldId),
    }
    return jsonify({"code": 200, "data": data})


@api.route("/gets5", methods=["GET", "OPTIONS"])
def getOneS5():
    from Api.ProxyApi import allocateIP

    s5Key = "RDWYELQ1"
    s5Pwd = "D0609F88737F"
    jsonValue = request.get_json()

    try:
        s5ip = allocateIP(s5Key, jsonValue)
        arr = s5ip.split(":")
        arr.append(s5Key)
        arr.append(s5Pwd)
        return "|".join(arr)
    except Exception as e:
        logger.error(e, exc_info=True)
        return ""


@api.route("/refresh/mine", methods=["GET", "POST", "OPTIONS"])
def refreshMine():
    jsonValue = request.get_json()
    if jsonValue:
        worldId = jsonValue.get("worldId")
        if worldId:
            listCrystalTask.delay(worldId, vaporNeed=True)
            return jsonify({"code": 200})
    else:
        logger.error("/api/refresh/mine 异常请求")
    return jsonify({"code": 201})


@api.route("/mining/updatetoken", methods=["GET", "POST", "OPTIONS"])
def updateToken():
    jsonValue = request.get_json()
    if jsonValue:
        token = jsonValue.get("token")
        if token:
            if MiningWorkMan.findAndUpdateToken(token):
                return jsonify({"code": 200})
            else:
                return jsonify({"code": 202})
    else:
        logger.error("/api/mining/updatetoken 异常请求")
    return jsonify({"code": 201})


@api.route("/user/save", methods=["POST", "OPTIONS"])
def userSave():
    jsonValue = request.get_json()
    if jsonValue:
        appleSubId = jsonValue.get("appleSubId")
        deviceInfo = jsonValue.get("deviceInfo")
        if appleSubId and deviceInfo:
            redisHelper.saveAppleSubId(appleSubId)
            redisHelper.saveDeviceInfo(deviceInfo, appleSubId=appleSubId)
            fakeIP = jsonValue.get("fakeIP")
            if fakeIP:
                redisHelper.saveFakeIp(appleSubId, fakeIp=fakeIP)
            return jsonify({"code": 200})
        return jsonify({"code": 201})
    return jsonify({"code": 202})


@api.route("/log/write", methods=["POST", "OPTIONS"])
def logWrite():
    jsonValue = request.get_json()
    if jsonValue:
        name = jsonValue.get("name")
        typeValue = jsonValue.get("type")
        msg = jsonValue.get("msg")
        if name and typeValue is not None and msg:
            model = LogRecordModel(
                name=name, type=int(typeValue), msg=msg
            )
            model.save()
            return jsonify({"code": 200})
        return jsonify({"code": 201})
    return jsonify({"code": 202})

@api.route("/zero/makeGlobalCrystalInfo", methods=["GET", "POST", "OPTIONS"])
def zeroMakeGlobalCrystalInfo():
    jsonValue = request.get_json()
    if jsonValue:
        worldId = jsonValue.get("worldId")
        if worldId:
            worldId = int(worldId)
        else:
            worldId = None
        ZeroSend.makeGlobalCrystalInfo(worldId=worldId)
        return jsonify({"code": 200})
    return jsonify({"code": 201})
