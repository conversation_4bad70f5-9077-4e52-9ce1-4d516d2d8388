{% macro bot_work_log_modal() %}
<style>
    /* 日志模态框样式 */
    .log-modal .modal-content {
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .log-modal .modal-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #eee;
        padding: 1rem 1.5rem;
    }

    .log-modal .modal-title {
        color: #2c3e50;
        font-weight: 600;
    }

    .log-modal .modal-body {
        padding: 1.5rem;
        background-color: #fff;
    }

    /* 日志内容样式 */
    #logContent {
        height: 70vh;
        overflow-y: auto;
        padding: 10px;
        background-color: #f8f9fa;
        border-radius: 6px;
        border: 1px solid #eee;
    }

    .log-item {
        padding: 8px 12px;
        margin-bottom: 8px;
        background-color: #fff;
        border-left: 4px solid #3498db;
        border-radius: 4px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        font-family: "Consolas", monospace;
        font-size: 14px;
        line-height: 1.5;
    }

    /* 滚动条样式 */
    #logContent::-webkit-scrollbar {
        width: 8px;
    }

    #logContent::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
    }

    #logContent::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 4px;
    }

    #logContent::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
    }

    /* 添加模态框尺寸样式 */
    .log-modal .modal-dialog {
        max-width: 80%;
        width: 80%;
        margin: 1.75rem auto;
    }

    /* 确保在小屏幕上也有合适的边距 */
    @media (max-width: 768px) {
        .log-modal .modal-dialog {
            max-width: 95%;
            width: 95%;
            margin: 1rem auto;
        }
    }
</style>

<div class="modal fade log-modal" id="logModal" tabindex="-1" role="dialog" aria-labelledby="logModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title mr-3" id="logModalLabel">关键日志</h5>
                <button type="button" class="btn btn-primary mr-2" id="resourceInfoBtn">
                    <i class="fa fa-database"></i>
                    资源信息
                </button>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="logContent"></div>
            </div>
        </div>
    </div>
</div>

<script>
    let eventSource;
    let reconnectAttempts = 0;
    const MAX_RECONNECT_ATTEMPTS = 5;
    const RECONNECT_TIMEOUT = 5000;
    let lastMsg = '';  // 记录最后一条日志

    // 添加资源信息按钮点击事件处理
    $("#resourceInfoBtn").click(function() {
        let $btn = $(this);
        $btn.prop('disabled', true);
        $.ajax({
            url: "{{ url_for('.try_print_resource') }}",
            type: "POST",
            success: function(response) {
                console.log("资源信息请求成功");
            },
            error: function(xhr, status, error) {
                console.error("资源信息请求失败:", error);
            },
            complete: function() {
                $btn.prop('disabled', false);
            }
        });
    });

    // Base64编解码函数
    function base64Encode(str) {
        return btoa(encodeURIComponent(str).replace(/%([0-9A-F]{2})/g,
            function toSolidBytes(match, p1) {
                return String.fromCharCode('0x' + p1);
            }));
    }

    function base64Decode(str) {
        try {
            return decodeURIComponent(atob(str).split('').map(function(c) {
                return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
            }).join(''));
        } catch (e) {
            console.error('Base64解码错误:', e);
            return '';
        }
    }

    $("#showLog").click(function() {
        $('#logModal').modal('show');
        $("#logContent").empty();
        reconnectAttempts = 0;
        lastMsg = '';  // 清空最后一条日志记录
        connectEventSource();
    });

    $('#logModal').on('hidden.bs.modal', function () {
        if (eventSource) {
            eventSource.close();
            eventSource = null;
        }
        reconnectAttempts = 0;
    });

    function connectEventSource() {
        if (eventSource) {
            eventSource.close();
        }

        try {
            const url = new URL("{{ url_for('.stream_bot_work_log') }}", window.location.origin);
            if (lastMsg) {
                url.searchParams.append('last_msg', lastMsg);  // lastMsg已经是base64编码
            }
            eventSource = new EventSource(url.toString());

            eventSource.onopen = function() {
                console.log('SSE连接已建立');
                reconnectAttempts = 0;
                if (!lastMsg) {
                    $("#logContent").prepend('<div class="log-item" style="color: green;">日志连接已建立</div>');
                } else {
                    $("#logContent").prepend('<div class="log-item" style="color: green;">日志重连成功</div>');
                }
            };

            eventSource.onmessage = function(event) {
                const data = JSON.parse(event.data);
                if (data.logs && data.logs.length > 0) {
                    // 解码并显示日志
                    const logDiv = $("#logContent");
                    data.logs.forEach(encodedLog => {
                        const log = base64Decode(encodedLog);
                        logDiv.prepend('<div class="log-item">' + log + '</div>');
                    });
                    // 保存最后一条日志的编码形式
                    lastMsg = data.logs[data.logs.length - 1];  // 已经是base64编码
                }
            };

            eventSource.onerror = function(error) {
                if (eventSource) {
                    eventSource.close();
                    eventSource = null;
                }
                if (reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
                    reconnectAttempts++;
                    $("#logContent").prepend(
                        '<div class="log-item" style="color: orange;">' +
                        `连接断开，${RECONNECT_TIMEOUT/1000}秒后第${reconnectAttempts}次重试...</div>`
                    );
                    setTimeout(connectEventSource, RECONNECT_TIMEOUT);
                } else {
                    $("#logContent").prepend(
                        '<div class="log-item" style="color: red;">' +
                        '重连次数超过限制，请刷新页面重试</div>'
                    );
                }
            };
        } catch (error) {
            console.error("创建SSE连接失败:", error);
            $("#logContent").prepend(
                '<div class="log-item" style="color: red;">' +
                '创建日志连接失败，请刷新页面重试</div>'
            );
        }
    }
</script>
{% endmacro %} 