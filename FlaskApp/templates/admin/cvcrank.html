{% extends "admin/master.html" %}
{% block body %}
<div id="messageBox" class="alert" style="display: none; position: fixed; top: 20px; right: 20px; z-index: 1000;"></div>

<div class="container">
    <h2 class="text-center mb-4">CVC 排名</h2>
    
    <div class="table-responsive">
        <table class="table table-striped table-hover">
            <thead class="thead-dark">
                <tr>
                    <th>排名</th>
                    <th>玩家名称</th>
                    <th>联盟</th>
                    <th>积分</th>
                    <th>等级</th>
                    <th>战力</th>
                    <th>领主等级</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                {% for rank in ranks %}
                <tr>
                    <td>{{ rank.rank }}</td>
                    <td>{{ rank.kingdom.name }}</td>
                    <td>{{ rank.kingdom.alliance.tag if rank.kingdom.alliance else '' }}</td>
                    <td>{{ "{:,}".format(rank.point) }}</td>
                    <td>{{ rank.kingdom.level }}</td>
                    <td>{{ "{:,}".format(rank.kingdom.power) }}</td>
                    <td>{{ rank.kingdom.lord.level }}</td>
                    <td>
                        <button class="btn btn-danger btn-sm blacklist-btn" 
                                data-id="{{ rank.kingdom._id }}"
                                data-name="{{ rank.kingdom.name }}"
                                data-worldid="{{ rank.kingdom.worldId }}"
                                {% if rank.kingdom._id in black_list %}disabled{% endif %}>
                            {% if rank.kingdom._id in black_list %}已拉黑{% else %}拉黑{% endif %}
                        </button>
                        <button class="btn btn-success btn-sm whitelist-btn"
                                data-id="{{ rank.kingdom._id }}"
                                data-name="{{ rank.kingdom.name }}"
                                data-worldid="{{ rank.kingdom.worldId }}"
                                {% if rank.kingdom._id in white_list %}disabled{% endif %}>
                            {% if rank.kingdom._id in white_list %}已加入白名单
                            {% elif rank.kingdom._id in black_list %}加入白名单
                            {% else %}白名单{% endif %}
                        </button>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>

<style>
.container {
    padding: 20px;
    max-width: 1200px;
}

.table {
    font-size: 14px;
}

.table th {
    background-color: #343a40;
    color: white;
    border: none;
}

.table td {
    vertical-align: middle;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0,0,0,.05);
}

.table-hover tbody tr:hover {
    background-color: rgba(0,0,0,.075);
}

.text-center {
    text-align: center;
}

.mb-4 {
    margin-bottom: 1.5rem;
}

.btn-danger {
    padding: 0.25rem 0.5rem;
}

.alert {
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 20px;
}

.alert-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const messageBox = document.getElementById('messageBox');
    
    function showMessage(message, type) {
        messageBox.textContent = message;
        messageBox.className = `alert alert-${type}`;
        messageBox.style.display = 'block';
        
        setTimeout(() => {
            messageBox.style.display = 'none';
        }, 3000);
    }
    
    document.querySelectorAll('.blacklist-btn').forEach(button => {
        button.addEventListener('click', function() {
            if (this.disabled) {
                return;
            }
            const id = this.getAttribute('data-id');
            const name = this.getAttribute('data-name');
            const worldId = this.getAttribute('data-worldid');
            
            if (!confirm(`确定要拉黑玩家 ${name} 吗？`)) {
                return;
            }
            
            fetch('{{ url_for("cvcrank.black_list_api") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    _id: id,
                    name: name,
                    worldId: worldId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage(`已成功拉黑玩家 ${name}`, 'success');
                    this.disabled = true;
                    this.textContent = '已拉黑';
                    // 启用白名单按钮
                    const whitelistBtn = this.nextElementSibling;
                    if (whitelistBtn) {
                        whitelistBtn.disabled = false;
                        whitelistBtn.textContent = '加入白名单';
                    }
                } else {
                    showMessage(data.message || '操作失败', 'danger');
                }
            })
            .catch(error => {
                showMessage('操作失败：' + error.message, 'danger');
            });
        });
    });

    document.querySelectorAll('.whitelist-btn').forEach(button => {
        button.addEventListener('click', function() {
            if (this.disabled) {
                return;
            }
            const id = this.getAttribute('data-id');
            const name = this.getAttribute('data-name');
            const worldId = this.getAttribute('data-worldid');
            if (!confirm(`确定要将玩家 ${name} 加入白名单吗？`)) {
                return;
            }
            fetch('{{ url_for("cvcrank.white_list_api") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    _id: id,
                    name: name,
                    worldId: worldId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage(`已成功将玩家 ${name} 加入白名单`, 'success');
                    this.disabled = true;
                    this.textContent = '已加入白名单';
                    // 启用拉黑按钮
                    const blacklistBtn = this.previousElementSibling;
                    if (blacklistBtn) {
                        blacklistBtn.disabled = false;
                        blacklistBtn.textContent = '拉黑';
                    }
                } else {
                    showMessage(data.message || '操作失败', 'danger');
                }
            })
            .catch(error => {
                showMessage('操作失败：' + error.message, 'danger');
            });
        });
    });
});
</script>
{% endblock %} 