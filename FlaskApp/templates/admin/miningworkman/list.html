{% extends 'admin/model/list.html' %}
{% import 'admin/lib.html' as lib with context %}
{% import 'admin/static.html' as admin_static with context%}
{% import 'admin/model/layout.html' as model_layout with context %}
{% import 'admin/actions.html' as actionlib with context %}
{% import 'admin/model/row_actions.html' as row_actions with context %}
{% import 'admin/components/bot_work_log_modal.html' as components %}

{% macro view_row_log_popup(action, row_id, row) %}
{{ lib.add_modal_button(url=get_url('.show_log', id=row_id, url=return_url, modal=True), title=action.title,
content='<span class="fa fa-list"></span>') }}
{% endmacro %}

{% macro view_row_run(action, row_id, row) %}
{{ lib.add_modal_button(url=get_url('.start', id=row_id, url=return_url), title=action.title,
content='<span class="fa fa-play" '+ row.playHiddenStr +' ></span>') }}
{% endmacro %}

{% macro view_row_stop(action, row_id, row) %}
{{ lib.add_modal_button(url=get_url('.stop', id=row_id, url=return_url), title=action.title,
content="<span class='fa fa-stop' " + row.stopHiddenStr +"></span>") }}
{% endmacro %}

{% block model_menu_bar_after_filters %}
<li>
    {# 一键启动按钮，主要色 #}
    <button id="allStart" class="btn btn-primary my-2 my-sm-0 ml-2" type="submit">一键启动</button>
    {# 关键日志按钮 #}
    <button id="showLog" class="btn btn-info my-2 my-sm-0 ml-2" type="button">关键日志</button>
    {# 回收按钮 #}
    <button id="recycleBtn" class="btn btn-warning my-2 my-sm-0 ml-2" type="button">回收搜索</button>
</li>


{% endblock %}

{% block tail %}
{{ super() }}
{{ components.bot_work_log_modal() }}

<!-- 回收确认模态框 -->
<div class="modal fade" id="recycleModal" tabindex="-1" role="dialog" aria-labelledby="recycleModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="recycleModalLabel">回收确认</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="recycleContent"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirmRecycle">确认</button>
            </div>
        </div>
    </div>
</div>

<script>
    $("#allStart").click(function () {
        $.ajax({
            url: "{{ url_for('.start_all') }}",
            type: "POST",
            dataType: "json",
            success: function (data) {
                if (data.code == 200) {
                    alert("启动成功");
                } else {
                    alert("启动失败");
                }
            }
        });
    });

    // 回收按钮点击事件
    $("#recycleBtn").click(function() {
        $.ajax({
            url: "{{ url_for('.can_recycle_apple_sub_ids') }}",
            type: "GET",
            dataType: "json",
            success: function(response) {
                if (response.code === 201) {
                    // 显示错误信息
                    $("#recycleContent").html('<div class="alert alert-warning">' + response.msg + '</div>');
                    $("#confirmRecycle").hide();
                } else if (response.code === 200) {
                    // 显示可回收的ID列表
                    let content = '<p>以下ID可以回收：</p><ul>';
                    response.data.forEach(function(id) {
                        content += '<li>' + id + '</li>';
                    });
                    content += '</ul>';
                    $("#recycleContent").html(content);
                    $("#confirmRecycle").show();
                }
                $("#recycleModal").modal('show');
            }
        });
    });

    // 确认回收按钮点击事件
    $("#confirmRecycle").click(function() {
        $.ajax({
            url: "{{ url_for('.recycle_apple_sub_ids') }}",
            type: "POST",
            dataType: "json",
            success: function(response) {
                if (response.code === 200) {
                    alert("回收成功");
                    location.reload();
                } else {
                    alert("回收失败：" + response.msg);
                }
                $("#recycleModal").modal('hide');
            }
        });
    });
</script>
{% endblock %}
