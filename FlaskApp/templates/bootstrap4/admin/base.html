{% import 'admin/layout.html' as layout with context -%}
{% import 'admin/static.html' as admin_static with context %}
<!DOCTYPE html>
<html>
  <head>
    <title>{% block title %}{% if admin_view.category %}{{ admin_view.category }} - {% endif %}{{ admin_view.name }} - {{ admin_view.admin.name }}{% endblock %}</title>
    {% block head_meta %}
        <meta charset="UTF-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta name="description" content="">
        <meta name="author" content="">
    {% endblock %}
    {% block head_css %}
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/4.3.1/css/bootstrap.min.css" />
        <link href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/css/toastr.min.css" rel="stylesheet" />
        <link href="{{ admin_static.url(filename='admin/css/bootstrap4/admin.css', v='1.1.1') }}" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.css" rel="stylesheet">
        {% if admin_view.extra_css %}
          {% for css_url in admin_view.extra_css %}
            <link href="{{ css_url }}" rel="stylesheet">
          {% endfor %}
        {% endif %}
        <style>
            .hide {
                display: none;
            }
        </style>
    {% endblock %}
    {% block head %}
    {% endblock %}
    {% block head_tail %}
    {% endblock %}
  </head>
<body>
{% block page_body %}
    <div class="container{% if config.get('FLASK_ADMIN_FLUID_LAYOUT', False) %}-fluid{% endif %}">
        <nav class="navbar navbar-expand-lg navbar-dark bg-dark mb-2" role="navigation">
            <!-- Brand and toggle get grouped for better mobile display -->
            <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#admin-navbar-collapse"
                    aria-controls="admin-navbar-collapse" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <!-- navbar content -->
            <div class="collapse navbar-collapse" id="admin-navbar-collapse">
            {% block brand %}
                <a class="navbar-brand" href="{{ admin_view.admin.url }}">{{ admin_view.admin.name }}</a>
            {% endblock %}
            {% block main_menu %}
                <ul class="navbar-nav mr-auto">
                    {{ layout.menu() }}
                </ul>
            {% endblock %}

                {% block menu_links %}
                <ul class="nav navbar-nav navbar-right">
                    {{ layout.menu_links() }}
                </ul>
                {% endblock %}
            {% block access_control %}
            {% endblock %}
            </div>
        </nav>

        {% block messages %}
            {{ layout.messages() }}
        {% endblock %}

        {# store the jinja2 context for form_rules rendering logic #}
        {% set render_ctx = h.resolve_ctx() %}

        {% block body %}{% endblock %}
    </div>
{% endblock %}

{% block tail_js %}
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.5.1/jquery.min.js" type="text/javascript"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.16.1/umd/popper.min.js" crossorigin="anonymous"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/4.6.1/js/bootstrap.min.js" crossorigin="anonymous"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/js/toastr.min.js" crossorigin="anonymous"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.9.0/moment.min.js" type="text/javascript"></script>
    <script src="{{ admin_static.url(filename='vendor/bootstrap4/util.js', v='4.3.1') }}" type="text/javascript"></script>
    <script src="{{ admin_static.url(filename='vendor/bootstrap4/dropdown.js', v='4.3.1') }}" type="text/javascript"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/select2/3.5.2/select2.min.js"
            type="text/javascript"></script>
    <script src="{{ admin_static.url(filename='vendor/multi-level-dropdowns-bootstrap/bootstrap4-dropdown-ml-hack.js') }}" type="text/javascript"></script>
    <script src="{{ admin_static.url(filename='admin/js/helpers.js', v='1.0.0') }}" type="text/javascript"></script>
    {% if admin_view.extra_js %}
        {% for js_url in admin_view.extra_js %}
            <script src="{{ js_url }}" type="text/javascript"></script>
        {% endfor %}
    {% endif %}
{% endblock %}

    {% block tail %}
    {% endblock %}
  </body>
</html>
