{% import 'admin/static.html' as admin_static with context %}
{% import 'admin/lib.html' as lib with context %}

{% block body %}
  {# content added to modal-content #}
  <div class="modal-header">
    {% block header %}<h3>{{ header_text }}</h3>{% endblock %}
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
  </div>
  <div class="modal-body">
    {% block fa_form %}
      {{ lib.render_form(form, dir_url, action=request.url, is_modal=True) }}
    {% endblock %}
  </div>
{% endblock %}

{% block tail %}
  <script src="{{ admin_static.url(filename='admin/js/bs4_modal.js', v='1.0.0') }}"></script>
{% endblock %}
