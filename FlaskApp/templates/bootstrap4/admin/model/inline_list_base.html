{% macro render_inline_fields(field, template, render, check=None) %}
<div class="inline-field" id="{{ field.id }}">
    {# existing inline form fields #}
    <div class="inline-field-list">
        {% for subfield in field %}
        <div id="{{ subfield.id }}" class="inline-field card card-body bg-light">
            {%- if not check or check(subfield) %}
            <legend>
                <small>
                    {{ field.label.text }} #{{ loop.index }}
                    <div class="pull-right">
                        {% if subfield.get_pk and subfield.get_pk() %}
                        <input type="checkbox" name="del-{{ subfield.id }}" id="del-{{ subfield.id }}" />
                        <label for="del-{{ subfield.id }}" style="display: inline">{{ _gettext('Delete?') }}</label>
                        {% else %}
                        <a href="javascript:void(0)" value="{{ _gettext('Are you sure you want to delete this record?') }}" class="inline-remove-field"><i class="fa fa-times glyphicon glyphicon-remove"></i></a>
                        {% endif %}
                    </div>
                </small>
            </legend>
            <div class='clearfix'></div>
            {%- endif -%}
            {{ render(subfield) }}
        </div>
        {% endfor %}
    </div>

    {# template for new inline form fields #}
    <div class="inline-field-template hide">
        {% filter forceescape %}
        <div class="inline-field card card-body bg-light">
            <legend>
                <small>{{ _gettext('New') }} {{ field.label.text }}</small>
                <div class="pull-right">
                    <a href="javascript:void(0)" value="{{ _gettext('Are you sure you want to delete this record?') }}" class="inline-remove-field"><span class="fa fa-times glyphicon glyphicon-remove"></span></a>
                </div>
            </legend>
            <div class='clearfix'></div>
            {{ render(template) }}
        </div>
        {% endfilter %}
    </div>
    <a id="{{ field.id }}-button" href="javascript:void(0)" class="btn btn-primary" role="button" onclick="faForm.addInlineField(this, '{{ field.id }}');">{{ _gettext('Add') }} {{ field.label.text }}</a>
</div>
{% endmacro %}
