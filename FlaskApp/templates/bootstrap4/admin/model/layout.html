{% macro filter_options(btn_class='dropdown-toggle') %}
    <a class="nav-link {{ btn_class }}" data-toggle="dropdown" href="javascript:void(0)">{{ _gettext('Add Filter') }}<b class="caret"></b></a>
    <div class="dropdown-menu field-filters">
        {% for k in filter_groups %}
            <a href="javascript:void(0)" class="dropdown-item filter" onclick="return false;">{{ k }}</a>
        {% endfor %}
    </div>
{% endmacro %}

{% macro export_options(btn_class='dropdown-toggle') %}
    {% if admin_view.export_types|length > 1 %}
        <li class="dropdown">
            <a class="nav-link {{ btn_class }}" data-toggle="dropdown" href="javascript:void(0)" role="button"
               aria-haspopup="true" aria-expanded="false">{{ _gettext('Export') }}<b class="caret"></b></a>
            <div class="dropdown-menu">
                {% for export_type in admin_view.export_types %}
                    <a class="dropdown-item"
                       href="{{ get_url('.export', export_type=export_type, **request.args) }}"
                       title="{{ _gettext('Export') }}">{{ _gettext('Export') + ' ' + export_type|upper }}</a>
                {% endfor %}
            </div>
        </li>
    {% else %}
        <li>
            <a class="nav-link" href="{{ get_url('.export', export_type=admin_view.export_types[0], **request.args) }}"
               title="{{ _gettext('Export') }}">{{ _gettext('Export') }}</a>
        </li>
    {% endif %}
{% endmacro %}

{% macro filter_form() %}
    <form id="filter_form" method="GET" action="{{ return_url }}">
        {% if sort_column is not none %}
            <input type="hidden" name="sort" value="{{ sort_column }}">
        {% endif %}
        {% if sort_desc %}
            <input type="hidden" name="desc" value="{{ sort_desc }}">
        {% endif %}
        {% if search %}
            <input type="hidden" name="search" value="{{ search }}">
        {% endif %}
        {% if page_size != default_page_size %}
            <input type="hidden" name="page_size" value="{{ page_size }}">
        {% endif %}
        <div class="pull-right">
            <button type="submit" class="btn btn-primary" style="display: none">{{ _gettext('Apply') }}</button>
            {% if active_filters %}
                <a href="{{ clear_search_url }}" class="btn btn-secondary">{{ _gettext('Reset Filters') }}</a>
            {% endif %}
        </div>

        <table class="filters"></table>
    </form>
    <div class="clearfix"></div>
{% endmacro %}

{% macro search_form(input_class="col-auto") %}
    <form method="GET" action="{{ return_url }}" class="form-inline my-2 my-lg-0" role="search">
        {% for flt_name, flt_value in filter_args.items() %}
            <input type="hidden" name="{{ flt_name }}" value="{{ flt_value }}">
        {% endfor %}
        {% if page_size != default_page_size %}
            <input type="hidden" name="page_size" value="{{ page_size }}">
        {% endif %}
        {% for arg_name, arg_value in extra_args.items() %}
        <input type="hidden" name="{{ arg_name }}" value="{{ arg_value }}">
        {% endfor %}
        {% if sort_column is not none %}
            <input type="hidden" name="sort" value="{{ sort_column }}">
        {% endif %}
        {% if sort_desc %}
            <input type="hidden" name="desc" value="{{ sort_desc }}">
        {% endif %}
        {% if search %}
            <div class="form-inline input-group">
              <input  class="form-control {{ input_class }}" size="30" type="text" name="search" value="{{ search }}"
                       placeholder="{{ _gettext('%(placeholder)s', placeholder=search_placeholder) }}">
                <div class="input-group-append">
                  <span class="input-group-text">
                    <a href="{{ clear_search_url }}" class="align-middle">
                      <span class="fa fa-times glyphicon glyphicon-remove"></span>
                    </a>
                  </span>
                </div>
                <button class="btn btn-secondary my-2 my-sm-0 ml-2" type="submit">{{ _gettext('Search') }}</button>
            </div>
        {% else %}
            <div class="form-inline">
              <input class="form-control {{ input_class }}" size="30" type="text" name="search" value=""
                       placeholder="{{ _gettext('%(placeholder)s', placeholder=search_placeholder) }}">
                <button class="btn btn-secondary my-2 my-sm-0 ml-2" type="submit">{{ _gettext('Search') }}</button>
            </div>
        {% endif %}
    </form>
{% endmacro %}

{% macro page_size_form(generator, btn_class='nav-link dropdown-toggle') %}
    <a class="{{ btn_class }}" data-toggle="dropdown" href="javascript:void(0)">
        {{ page_size }} {{ _gettext('items') }}<b class="caret"></b>
    </a>
    <div class="dropdown-menu">
      <a class="dropdown-item{% if page_size == 20 %} active{% endif %}" href="{{ generator(20) }}">20 {{ _gettext('items') }}</a>
      <a class="dropdown-item{% if page_size == 50 %} active{% endif %}" href="{{ generator(50) }}">50 {{ _gettext('items') }}</a>
      <a class="dropdown-item{% if page_size == 100 %} active{% endif %}" href="{{ generator(100) }}">100 {{ _gettext('items') }}</a>
    </div>
{% endmacro %}
