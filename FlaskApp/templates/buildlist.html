{% extends 'work_base.html' %} {% import 'admin/lib.html' as lib with context %}
{% block userinfo %} {% endblock %} {% block actions %}
<form>
  <div class="field">
    <label>位置ID</label>
    <select class="form-control" id="locationOptions">
      <optgroup label="位置ID">
        {% for value in otherInfo['locations'] %}
        <option value="{{value}}">{{value}}</option>
        {% endfor %}
      </optgroup>
    </select>
    <label>建筑ID</label>
    <select class="form-control" id="buildOptions">
      <optgroup label="建筑ID">
        {% for key,value in otherInfo['builds'].items() %}
        <option value="{{value}}">{{key}}</option>
        {% endfor %}
      </optgroup>
    </select>
    <button class="btn btn-primary" type="button" id="rebuild">重建</button>
    <button class="btn btn-primary" type="button" id="levelup">秒30</button>
  </div>
</form>

{% endblock %} {% block tail_work %}
<script>
    $("#rebuild").click(function (arg1) {
        let buildOptions = $("#buildOptions")[0].value
        let locationOptions = $("#locationOptions")[0].value
        console.log(buildOptions)
        console.log(locationOptions)

        btn = $(this);
        $.ajax({
            url: '{{ url_for("buildlist.rebuild_api") }}',
            type: 'POST',
            data: {
                buildId:buildOptions,
                localId:locationOptions
            },
            beforeSend: function () {
                btn.prop("disabled", true);
                console.log(btn);
            },
            success: function (data) {
                if (data.code == 200) {
                    toastr.success("重建成功");
                } else {
                    toastr.error(data.msg);
                }
            },
            error: function (data) {
                toastr.error(data.msg);
            },
        });
    })

    $("#levelup").click(function (arg1) {
        let buildOptions = $("#buildOptions")[0].value
        let locationOptions = $("#locationOptions")[0].value
        console.log(buildOptions)
        console.log(locationOptions)

        btn = $(this);
        $.ajax({
            url: '{{ url_for("buildlist.levelup_api") }}',
            type: 'POST',
            data: {
                buildId:buildOptions,
                localId:locationOptions
            },
            beforeSend: function () {
                btn.prop("disabled", true);
                console.log(btn);
            },
            success: function (data) {
                if (data.code == 200) {
                    toastr.success("重建成功");
                } else {
                    toastr.error(data.msg);
                }
            },
            error: function (data) {
                toastr.error(data.msg);
            },
        });
    })
    
</script>
{% endblock %}
