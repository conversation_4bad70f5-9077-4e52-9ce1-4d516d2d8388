{# 定义一个宏来封装模态框的初始化逻辑 #}
{% macro init_king_power_modal() %}
<style>
.king-power-btn {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  padding: 1rem;
  border: 2px solid #007bff;
}

.king-power-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0,123,255,0.3);
}

.king-power-btn i {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  display: block;
}

.king-power-btn .skill-name {
  font-size: 1rem;
  font-weight: 500;
}

.king-power-btn .skill-desc {
  font-size: 0.8rem;
  color: #6c757d;
  margin-top: 0.25rem;
}

.modal-content {
  border: none;
  border-radius: 1rem;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.modal-header {
  background: linear-gradient(45deg, #007bff, #0056b3);
  border-radius: 1rem 1rem 0 0;
  padding: 1.5rem;
}

.modal-header .modal-title {
  font-size: 1.5rem;
  font-weight: 600;
}

.modal-header .close {
  text-shadow: none;
  opacity: 1;
}

.modal-body {
  padding: 2rem;
  background-color: #f8f9fa;
}

.modal-footer {
  background-color: #f8f9fa;
  border-radius: 0 0 1rem 1rem;
  padding: 1.5rem;
}

.btn-close-modal {
  background-color: #6c757d;
  color: white;
  padding: 0.5rem 2rem;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
}

.btn-close-modal:hover {
  background-color: #5a6268;
  transform: translateY(-2px);
}
</style>

<div class="modal fade" id="kingPowerModal" tabindex="-1" role="dialog" aria-labelledby="kingPowerModalLabel">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title text-white" id="kingPowerModalLabel">
          <i class="fas fa-crown mr-2"></i>国王技能
        </h5>
        <button type="button" class="close text-white" data-dismiss="modal" aria-label="关闭">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col-md-6 mb-4">
            <button type="button" class="btn btn-outline-primary btn-block king-power-btn" data-power="101" aria-label="采集加速">
              <i class="fas fa-hand-paper"></i>
              <div class="skill-name">采集加速</div>
            </button>
          </div>
          <div class="col-md-6 mb-4">
            <button type="button" class="btn btn-outline-primary btn-block king-power-btn" data-power="102" aria-label="治疗加速">
              <div class="skill-name">治疗加速</div>
            </button>
          </div>
          <div class="col-md-6 mb-4">
            <button type="button" class="btn btn-outline-primary btn-block king-power-btn" data-power="103" aria-label="训练加速">
              <div class="skill-name">训练加速</div>
            </button>
          </div>
          <div class="col-md-6 mb-4">
            <button type="button" class="btn btn-outline-primary btn-block king-power-btn" data-power="104" aria-label="水晶加速">
              <div class="skill-name">水晶加速</div>
            </button>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-info" data-dismiss="modal" aria-label="关闭模态框">关闭</button>
      </div>
    </div>
  </div>
</div>

<script>
function initKingPowerModal() {
  const modal = $('#kingPowerModal');
  
  // 初始化模态框
  modal.modal({
    backdrop: 'static',
    keyboard: false,
    show: false
  });

  // 技能按钮点击处理
  $("button.king-power-btn").click(function() {
    const $btn = $(this);
    const powerId = $btn.data('power');
    
    // 添加加载状态
    $btn.prop('disabled', true);
    $btn.html('<i class="fas fa-spinner fa-spin"></i> 使用中...');
    
    $.ajax({
      url: '{{ url_for(".use_king_power_api") }}',
      type: "POST",
      data: {
        powerId: powerId
      },
      success: function(data) {
        toastr.success("技能使用成功");
      },
      error: function() {
        toastr.error("技能使用失败");
      },
      complete: function() {
        // 恢复按钮状态
        $btn.prop('disabled', false);
        const names = {
          '101': '采集加速',
          '102': '治疗加速',
          '103': '训练加速',
          '104': '水晶加速'
        };
        $btn.html(`
          <div class="skill-name">${names[powerId]}</div>
        `);
      }
    });
  });

  // 模态框显示时设置焦点
  modal.on('shown.bs.modal', function () {
    requestAnimationFrame(() => {
      $("button.king-power-btn:first").focus();
    });
  });

  // 模态框隐藏时移除焦点
  modal.on('hide.bs.modal', function () {
    if (document.activeElement) {
      document.activeElement.blur();
    }
  });
}

// 检查jQuery是否已加载
if (typeof jQuery === 'undefined') {
  window.addEventListener('load', function() {
    if (typeof jQuery !== 'undefined') {
      initKingPowerModal();
    }
  });
} else {
  initKingPowerModal();
}
</script>
{% endmacro %} 