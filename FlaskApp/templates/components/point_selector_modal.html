{% macro init_point_selector_modal() %}
<div class="modal fade point-selector-modal" id="pointSelectorModal" tabindex="-1" role="dialog">
  <div class="modal-dialog" role="document" style="min-width: 650px; min-height: 650px;">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">
          <i class="fas fa-map-marker-alt mr-2"></i>选择坐标点
        </h5>
        <button type="button" class="close text-white close-modal" data-dismiss="modal" aria-label="关闭">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="canvas-container" style="display: flex; justify-content: center; align-items: center;">
          <canvas id="pointSelectorCanvas"></canvas>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-danger clear-points">
          <i class="fas fa-trash-alt mr-1"></i>清空
        </button>
        <button type="button" class="btn btn-primary confirm-points" disabled>
          <i class="fas fa-check mr-1"></i>确认
        </button>
      </div>
    </div>
  </div>
</div>

<script>
// 全局变量
let currentCallback = null;
let selectedPoints = [];
let canvas = null;
let ctx = null;
let hoveredPoint = null;
let scale = 1;
let offsetX = 0;
let offsetY = 0;

// 画布配置
const CANVAS_SIZE = 2000;
const GRID_SIZE = 400;
const POINT_INTERVAL = 200;
const POINT_RADIUS = 5;
const SELECTED_POINT_RADIUS = 8;
const PADDING = 50;
const DRAW_SIZE = 540;

// 颜色配置
const COLORS = {
  grid: ['#eeeeee', '#dddddd'],
  point: '#007bff',
  selectedPoint: '#28a745',
  polygon: 'rgba(40, 167, 69, 0.2)',
  hover: '#ffc107'
};

// 绘制网格
function drawGrid() {
  for (let i = 0; i < CANVAS_SIZE; i += GRID_SIZE) {
    for (let j = 0; j < CANVAS_SIZE; j += GRID_SIZE) {
      const colorIndex = (Math.floor(i / GRID_SIZE) + Math.floor(j / GRID_SIZE)) % 2;
      ctx.fillStyle = COLORS.grid[colorIndex];
      ctx.fillRect(i, j, GRID_SIZE, GRID_SIZE);
    }
  }
}

// 绘制点
function drawPoints() {
  for (let x = 0; x <= CANVAS_SIZE; x += POINT_INTERVAL) {
    for (let y = 0; y <= CANVAS_SIZE; y += POINT_INTERVAL) {
      const point = [x, y];
      const isSelected = selectedPoints.some(p => p[0] === x && p[1] === y);
      const isHovered = hoveredPoint && hoveredPoint[0] === x && hoveredPoint[1] === y;

      ctx.beginPath();
      ctx.arc(x, y, isSelected ? SELECTED_POINT_RADIUS : POINT_RADIUS, 0, Math.PI * 2);
      ctx.fillStyle = isHovered ? COLORS.hover : (isSelected ? COLORS.selectedPoint : COLORS.point);
      ctx.fill();

      if (isSelected) {
        const index = selectedPoints.findIndex(p => p[0] === x && p[1] === y);
        ctx.save();
        ctx.fillStyle = 'white';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText((index + 1).toString(), x, y);
      }
    }
  }
}

// 绘制多边形
function drawPolygon() {
  if (selectedPoints.length < 3) return;
  
  ctx.beginPath();
  ctx.moveTo(selectedPoints[0][0], selectedPoints[0][1]);
  for (let i = 1; i < selectedPoints.length; i++) {
    ctx.lineTo(selectedPoints[i][0], selectedPoints[i][1]);
  }
  ctx.closePath();
  ctx.fillStyle = COLORS.polygon;
  ctx.fill();
}

// 初始化画布
function drawCanvas() {
  if (!canvas || !ctx) return;
  
  const container = canvas.parentElement;
  const containerWidth = DRAW_SIZE;
  const containerHeight = DRAW_SIZE;

  // 设置画布大小
  canvas.width = DRAW_SIZE;
  canvas.height = DRAW_SIZE;

  // 计算缩放比例
  scale = Math.min(
    (containerWidth - PADDING * 2) / CANVAS_SIZE,
    (containerHeight - PADDING * 2) / CANVAS_SIZE
  );

  // 计算偏移量
  offsetX = (containerWidth - CANVAS_SIZE * scale) / 2;
  offsetY = (containerHeight - CANVAS_SIZE * scale) / 2;

  // 保存变换状态
  ctx.save();

  // 设置变换
  ctx.translate(offsetX, containerHeight - offsetY);
  ctx.scale(scale, -scale);

  // 绘制网格
  drawGrid();

  // 绘制点
  drawPoints();

  // 绘制多边形
  if (selectedPoints.length >= 3) {
    drawPolygon();
  }

  // 恢复变换状态
  ctx.restore();
}

// 获取点击位置对应的坐标
function getPointFromEvent(event) {
  const rect = canvas.getBoundingClientRect();
  const x = (event.clientX - rect.left - offsetX) / scale;
  const y = CANVAS_SIZE - (event.clientY - rect.top - offsetY) / scale;

  // 找到最近的点
  const gridX = Math.round(x / POINT_INTERVAL) * POINT_INTERVAL;
  const gridY = Math.round(y / POINT_INTERVAL) * POINT_INTERVAL;

  // 确保坐标在有效范围内
  if (gridX < 0 || gridX > CANVAS_SIZE || gridY < 0 || gridY > CANVAS_SIZE) {
    return null;
  }

  return [gridX, gridY];
}

// 检查点是否在选中范围内
function isPointNearby(point, x, y) {
  const distance = Math.sqrt(Math.pow(point[0] - x, 2) + Math.pow(point[1] - y, 2));
  return distance <= POINT_RADIUS * 2;
}

function openPointSelector(initialPoints = [], callback = null) {
  selectedPoints = [...initialPoints];
  currentCallback = callback;
  
  // 更新确认按钮状态
  const confirmBtn = document.querySelector('#pointSelectorModal .confirm-points');
  confirmBtn.disabled = selectedPoints.length < 3;
  
  // 重绘画布
  drawCanvas();

  // 显示模态框
  $('#pointSelectorModal').modal('show');
}

function initPointSelector() {
  const modal = document.getElementById('pointSelectorModal');
  canvas = modal.querySelector('#pointSelectorCanvas');
  ctx = canvas.getContext('2d');
  const confirmBtn = modal.querySelector('.confirm-points');
  const clearBtn = modal.querySelector('.clear-points');
  const closeBtn = modal.querySelector('.close-modal');

  // 事件处理
  canvas.addEventListener('mousedown', (event) => {
    const point = getPointFromEvent(event);
    if (!point) return;

    const index = selectedPoints.findIndex(p => p[0] === point[0] && p[1] === point[1]);

    if (index === -1) {
      selectedPoints.push(point);
    } else {
      selectedPoints.splice(index, 1);
    }

    // 更新确认按钮状态
    confirmBtn.disabled = selectedPoints.length < 3;

    // 重绘画布
    drawCanvas();
  });

  canvas.addEventListener('mousemove', (event) => {
    const point = getPointFromEvent(event);
    if (!point) {
      hoveredPoint = null;
      drawCanvas();
      return;
    }

    const wasHovered = hoveredPoint;
    hoveredPoint = null;

    // 检查是否悬停在点上
    for (let x = 0; x <= CANVAS_SIZE; x += POINT_INTERVAL) {
      for (let y = 0; y <= CANVAS_SIZE; y += POINT_INTERVAL) {
        if (isPointNearby([x, y], point[0], point[1])) {
          hoveredPoint = [x, y];
          break;
        }
      }
      if (hoveredPoint) break;
    }

    // 如果悬停状态改变，重绘画布
    if (wasHovered !== hoveredPoint) {
      drawCanvas();
    }
  });

  canvas.addEventListener('mouseleave', () => {
    hoveredPoint = null;
    drawCanvas();
  });

  // 确认按钮点击事件
  confirmBtn.addEventListener('click', function() {
    if (selectedPoints.length >= 3 && currentCallback) {
      currentCallback(selectedPoints);
      $('#pointSelectorModal').modal('hide');
    }
  });

  // 清空按钮点击事件
  clearBtn.addEventListener('click', function() {
    selectedPoints = [];
    confirmBtn.disabled = true;
    drawCanvas();
  });

  // 关闭按钮点击事件
  closeBtn.addEventListener('click', function() {
    $('#pointSelectorModal').modal('hide');
  });

  // 窗口大小改变时重绘
  window.addEventListener('resize', drawCanvas);

  // 初始化
  drawCanvas();
}

// 等待DOM加载完成
document.addEventListener('DOMContentLoaded', function() {
  initPointSelector();
});
</script>
{% endmacro %} 