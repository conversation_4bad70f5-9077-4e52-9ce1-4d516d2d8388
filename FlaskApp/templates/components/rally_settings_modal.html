{% macro init_rally_settings_modal() %}
<div class="modal fade" id="rallySettingsModal" tabindex="-1" role="dialog" aria-labelledby="rallySettingsModalLabel">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title text-white" id="rallySettingsModalLabel">跟团设置</h5>
        <button type="button" class="close text-white" data-dismiss="modal" aria-label="关闭">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <h6>战争跟团数量设置</h6>
        <div class="form-group">
          <div class="form-row">
            <div class="col">
              <label for="warWorldId" class="sr-only">世界ID</label>
              <input type="number" class="form-control" id="warWorldId" placeholder="世界ID" aria-label="世界ID">
            </div>
            <div class="col">
              <label for="warCount" class="sr-only">跟团数量</label>
              <input type="number" class="form-control" id="warCount" placeholder="跟团数量" aria-label="跟团数量">
            </div>
            <div class="col-auto">
              <button type="button" class="btn btn-info" id="submitWarCount" aria-label="提交设置">提交</button>
            </div>
          </div>
        </div>

        <div class="table-responsive">
          <table class="table table-sm" role="grid" aria-label="跟团数量列表">
            <thead>
              <tr>
                <th scope="col">世界ID</th>
                <th scope="col">跟团数量</th>
              </tr>
            </thead>
            <tbody id="warCountList">
              <!-- 动态加载数据 -->
            </tbody>
          </table>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-info" data-dismiss="modal" aria-label="关闭模态框">关闭</button>
      </div>
    </div>
  </div>
</div>

<style>
#rallySettingsModal .btn-info {
  background-color: #17a2b8;
  border-color: #17a2b8;
  color: #fff;
}

#rallySettingsModal .btn-info:hover {
  background-color: #138496;
  border-color: #117a8b;
}

#rallySettingsModal .close {
  color: #17a2b8;
}

#rallySettingsModal .sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

#rallySettingsModal .modal-header {
  background-color: #007bff;
  padding: 1rem 1.5rem;
}
</style>

<script>
// 等待jQuery加载完成
function initRallySettingsModal() {
  console.log('Rally Settings Modal Script Loading...');
  
  // 检查模态框是否存在
  const modal = $('#rallySettingsModal');
  console.log('Modal element:', modal.length > 0 ? 'Found' : 'Not Found');

  // 初始化模态框
  modal.modal({
    backdrop: 'static',
    keyboard: false,
    show: false
  });

  // 加载战争跟团数量列表
  function loadWarCountList() {
    console.log('Loading war count list...');
    $.ajax({
      url: '{{ url_for(".get_all_war_join_count_api") }}',
      type: "GET",
      success: function(data) {
        console.log('War count API response:', data);
        if (data.code == 200) {
          const tbody = $("#warCountList");
          tbody.empty();
          Object.entries(data.data).forEach(([worldId, count]) => {
            tbody.append(`
              <tr>
                <td>${worldId}</td>
                <td>${count}</td>
              </tr>
            `);
          });
        }
      },
      error: function(xhr, status, error) {
        console.error('War count API error:', error);
        toastr.error("加载数据失败");
      }
    });
  }

  // 当模态框显示时加载数据
  modal.on('shown.bs.modal', function () {
    console.log('Modal shown event triggered');
    loadWarCountList();
    // 使用requestAnimationFrame确保在下一帧设置焦点
    requestAnimationFrame(() => {
      $('#warWorldId').focus();
    });
  });

  // 当模态框隐藏时移除焦点
  modal.on('hide.bs.modal', function () {
    if (document.activeElement) {
      document.activeElement.blur();
    }
  });

  // 提交战争跟团数量
  $(document).on('click', '#submitWarCount', function() {
    console.log('Submit button clicked');
    const worldId = parseInt($("#warWorldId").val());
    const count = parseInt($("#warCount").val());

    console.log('Form values:', { worldId, count });

    if (isNaN(worldId) || isNaN(count)) {
      toastr.error("请输入有效的数字");
      return;
    }

    $.ajax({
      url: '{{ url_for(".update_war_join_count_api") }}',
      type: "POST",
      data: {
        worldId: worldId,
        count: count
      },
      success: function(data) {
        console.log('Update API response:', data);
        if (data.code == 200) {
          toastr.success("设置保存成功");
          $("#warWorldId").val("");
          $("#warCount").val("");
          loadWarCountList();
          // 使用requestAnimationFrame确保在下一帧设置焦点
          requestAnimationFrame(() => {
            $('#warWorldId').focus();
          });
        }
      },
      error: function(xhr, status, error) {
        console.error('Update API error:', error);
        toastr.error("保存失败");
      }
    });
  });

  // 测试事件绑定
  console.log('Event bindings completed');
}

// 检查jQuery是否已加载
if (typeof jQuery === 'undefined') {
  // 如果jQuery未加载，等待加载完成
  window.addEventListener('load', function() {
    if (typeof jQuery !== 'undefined') {
      initRallySettingsModal();
    } else {
      console.error('jQuery not loaded after window load');
    }
  });
} else {
  // jQuery已加载，直接初始化
  initRallySettingsModal();
}
</script>
{% endmacro %} 