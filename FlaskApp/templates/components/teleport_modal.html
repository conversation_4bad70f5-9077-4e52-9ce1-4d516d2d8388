{# 定义一个宏来封装模态框的初始化逻辑 #}
{% macro init_teleport_modal(get_url, post_url, cvc_move_url) %}
<div class="modal fade" id="teleportModal" tabindex="-1" role="dialog">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header bg-primary text-white">
        <h5 class="modal-title">
          <i class="fas fa-map-marker-alt mr-2"></i>选择传送目标
        </h5>
        <button type="button" class="close text-white" data-dismiss="modal">
          <span>&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="card mb-3">
          <div class="card-body">
            <div class="form-row align-items-center">
              <div class="col-md-4 mb-2">
                <label for="teleportWorld" class="small text-muted mb-1">世界ID</label>
                <input type="number" class="form-control bg-light" id="teleportWorld" placeholder="世界ID" readonly>
              </div>
              <div class="col-md-4 mb-2">
                <label for="teleportX" class="small text-muted mb-1">X坐标</label>
                <input type="number" class="form-control" id="teleportX" placeholder="X坐标" min="0" max="2048" required 
                       oninput="if(value>2048)value=2048;if(value<0)value=0">
              </div>
              <div class="col-md-4 mb-2">
                <label for="teleportY" class="small text-muted mb-1">Y坐标</label>
                <input type="number" class="form-control" id="teleportY" placeholder="Y坐标" min="0" max="2048" required
                       oninput="if(value>2048)value=2048;if(value<0)value=0">
              </div>
            </div>
          </div>
        </div>


        <div class="form-group">
          <label for="teleportSelect" class="small text-muted mb-1">选择目标位置</label>
          <select class="form-control form-control-lg" id="teleportSelect">
          </select>
        </div>
        
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-warning" id="leaveCVC">
          <i class="fas fa-sign-out-alt mr-1"></i>离开CVC
        </button>
        <button type="button" class="btn btn-success" id="enterCVC">
          <i class="fas fa-sign-in-alt mr-1"></i>进入CVC
        </button>
        <button type="button" class="btn btn-info" id="shield">
          <i class="fas fa-shield-alt mr-1"></i>开盾
        </button>
        <button type="button" class="btn btn-primary" id="confirmTeleport">
          <i class="fas fa-check mr-1"></i>确认传送
        </button>
      </div>
    </div>
  </div>
</div>


<script>
function initTeleportModal(getUrl, postUrl, cvcMoveUrl) {
  $("button.teleport").click(function() {
    $.ajax({
      url: getUrl,
      type: "GET",
      success: function(data) {
        const select = $("#teleportSelect");
        select.empty();
        data.targets.forEach(item => {
          select.append(`<option value="${item.kingdomId}" data-loc='${JSON.stringify(item.loc)}'>${item.name}(${item.loc[0]},${item.loc[1]},${item.loc[2]})</option>`);
        });
        $("#teleportModal").modal('show');
        select.trigger('change');
      }
    });
  });

  $("#teleportSelect").change(function() {
    const selectedOption = $(this).find("option:selected");
    const loc = JSON.parse(selectedOption.attr('data-loc'));
    $("#teleportWorld").val(loc[0]);
    $("#teleportX").val(loc[1]);
    $("#teleportY").val(loc[2]);
  });

  $("#confirmTeleport").click(function() {
    const selectedValue = $("#teleportSelect").val();
    const worldId = $("#teleportWorld").val();
    const x = $("#teleportX").val();
    const y = $("#teleportY").val();
    
    $.ajax({
      url: postUrl,
      type: "POST",
      data: {
        kingdomId: selectedValue,
        loc: JSON.stringify([worldId,x, y]),
      },
      success: function(data) {
        toastr.success("传送成功");
      }
    });
  });

  $("#enterCVC").click(function() {
    const selectedValue = $("#teleportSelect").val();
    $.ajax({
      url: cvcMoveUrl,
      type: "POST",
      data: {
        kingdomId: selectedValue,
        enter: 1,
      },
      success: function(data) {
        toastr.success("进入CVC成功");
      }
    });
  });

  $("#leaveCVC").click(function() {
    const selectedValue = $("#teleportSelect").val();
    $.ajax({
      url: cvcMoveUrl,
      type: "POST",
      data: {
        kingdomId: selectedValue,
        enter: 0,
      },
      success: function(data) {
        toastr.success("离开CVC成功");
      }
    });
  });
  $("#shield").click(function() {
    const selectedValue = $("#teleportSelect").val();
    $.ajax({
      url: '{{ url_for(".use_item_api") }}',
      type: "POST",
      data: {
        itemId: 10102026,
        kingdomId: selectedValue,

      },
      success: function(data) {
        toastr.success("开盾广播成功");
      }
    });
  });
}
</script>
{% endmacro %}