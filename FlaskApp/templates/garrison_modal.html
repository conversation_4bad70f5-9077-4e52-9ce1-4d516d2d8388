<!-- Modal -->
<div class="modal fade" id="garrisonModal" tabindex="-1" aria-labelledby="garrisonModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="garrisonModalLabel">驻守设置</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <form>
          <div class="form-group row align-items-center">
            <label for="monster" class="col-sm-2 col-form-label">驻守坐标</label>
            <div class="col-sm-6">
              <div class="form-check form-check-inline">
                <div class="input-group-prepend">
                  <div class="input-group-text">x</div>
                  <input type="number" id="garrison_locx" class="form-control" placeholder="x坐标" aria-label="x"
                    aria-describedby="x">
                </div>
                <div class="input-group-prepend">
                  <div class="input-group-text">y</div>
                  <input type="number" id="garrison_locy" class="form-control" placeholder="y坐标" aria-label="y"
                    aria-describedby="y">
                </div>
              </div>

            </div>
          </div>
          <div class="form-group row">
            <label for="monsterLv" class="col-sm-2 col-form-label">驻守兵种</label>
            <div class="col-sm-10">
              <div class="form-row">
                <div class="form-group col-md-3 form-check-inline">
                  <select class="form-control" id="garrisonTroopGroup">
                    <option value="1">步兵</option>
                    <option value="2" selected>弓兵</option>
                    <option value="3">骑兵</option>
                    <option value="4">全兵种</option>
                  </select>
                </div>
              </div>
            </div>
          </div>

          <div class="form-group row">
            <label for="monsterLv" class="col-sm-2 col-form-label">驻守数量</label>
            <div class="col-sm-4">
              <div class="form-row">
                <input type="number" class="form-control" id="garrisonNum" value="300000">
              </div>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
        <button type="button" class="btn btn-primary" id="saveGarrison">保存</button>
      </div>
    </div>
  </div>
  <script>
    window.onload = function (arg1) {
      console.log(arg1);
      garrisonInfo = JSON.parse(localStorage.getItem("garrisonInfo"))
      if (garrisonInfo == undefined) {
        return
      }
      console.log(garrisonInfo);
      $("#garrison_locx").val(garrisonInfo.locx);
      $("#garrison_locy").val(garrisonInfo.locy);
      $("#garrisonTroopGroup").val(garrisonInfo.troopGroup);
      $("#garrisonNum").val(garrisonInfo.num);
    }

    $("#saveGarrison").click(function () {
      var locx = $('#garrison_locx').val();
      var locy = $('#garrison_locy').val();
      var troopGroup = $('#garrisonTroopGroup').val();
      var num = $('#garrisonNum').val();
      if (locx == "" || locy == "") {
        alert("请输入驻守坐标");
        return
      }

      if (num == undefined) {
        alert("请输入驻守数量");
        return
      }

      var garrisonInfo = {
        locx: locx,
        locy: locy,
        troopGroup: $('#garrisonTroopGroup').val(),
        num: num,
      }

      //保存到本地
      localStorage.setItem("garrisonInfo", JSON.stringify(garrisonInfo))
      //获取
      console.log(JSON.parse(localStorage.getItem("garrisonInfo")))
      $("#garrisonModal").modal("hide")
    });

    $("#autogarrison").click((arg1) => {
      garrisonInfo = localStorage.getItem("garrisonInfo");
      if (garrisonInfo == undefined) {
        alert("请先设置驻守信息");
        return;
      }

      btn = $(this);
      $.ajax({
        url: '{{ url_for("work.autogarrison_api") }}',
        type: 'POST',
        data: {
          garrisonInfo: garrisonInfo,
        },
        beforeSend: function () {
          btn.prop("disabled", true);
        },
        success: function (data) {
          $("#autogarrison-load").hide();
          $("#autogarrison-text").show();
          if (data.code == 200) {
            setTimeout(() => {
              window.location.reload();
            }, 3000);
          } else {
            btn.prop("disabled", false);
            toastr.error(data.msg);
          }
        },
        error: function (data) {
          btn.prop("disabled", false);
          toastr.error(data.msg);
        }
      });
    });
  </script>
</div>

{% macro garrisonButton() -%}
<div class="btn-group" role="group">
  <button id="autogarrison" type="button" class="btn btn-primary">
    <span id="autogarrison-text">开启自动驻守</span>
  </button>
  <button id="autogarrison-set" type="button" class="btn btn-secondary" data-toggle="modal"
    data-target="#garrisonModal">驻守设置</button>
</div>
{%- endmacro %}