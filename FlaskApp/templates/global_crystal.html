<html>
    <body>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.5.1/jquery.min.js" type="text/javascript"></script>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/4.3.1/css/bootstrap.min.css" />
        <link href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/css/toastr.min.css" rel="stylesheet" />
        <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/js/toastr.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/4.3.1/js/bootstrap.min.js"></script>

        <div class="container">
            <table class="table table-borderless">
                <thead>
                    <tr>
                        <th scope="col">类型</th>
                        <th scope="col">等级</th>
                        <th scope="col">坐标</th>
                        <th scope="col">操作</th>
                        <th scope="col" hidden>id</th>
                    </tr>
                </thead>
                <tbody>
                </tbody>
    
                </table>
    
        </div>

    </body>
    <script>

    function createRow(data,type) {
        // console.log(data)
        let values = data.split("_")
        let typeName = "水晶"
        if (type == 1) {
            typeName = "龙矿"
        }
        var row = $('<tr data-info=\''+data+'\' data-type=\''+type+'\'></tr>');
        row.append($(`<td scope="col">${typeName}</td>`));
        row.append($(`<td scope="col">${values[0]}</td>`));
        row.append($(`<td scope="col">${values[1]},${values[2]}</td>`));
        row.append($('<td><button type="button" class="btn btn-primary collection text-nowrap">领取</button></dh>'));
        row.append($('<td hidden>' + data + '</td>'));
        return row[0];
    }

    function createRows(datas) {
        console.log(`总数${datas.length}`);

        tb = $("tbody")
        tb.empty()
        crystals = datas.crystals
        crystals.sort().reverse()
        for (index in crystals) {
            tb.append(createRow(crystals[index],0))
        }
        dragoMines = datas.dragoMines
        dragoMines.sort().reverse()
        for (index in dragoMines) {
            tb.append(createRow(dragoMines[index],1))
        }
        addClickEvent();        
    }

    function addClickEvent() {
        collections = $("button.btn-primary.collection");
        collections.click(collectionResource);
    }

    function collectionResource(arg1) {
        let info = this.parentElement.parentElement.dataset.info;
        let type = this.parentElement.parentElement.dataset.type;
        
        self = $(this)
        self.prop('disabled', true);
        $.ajax({
            url: "{{ url_for('global_crystal.receive') }}",
            type: 'POST',
            data: {
                worldId:{{ worldId }},
                key:info,
                type:type,
            },
            success: function (data) {
                self.prop('disabled', false);
                if (data.code == 200) {
                    toastr.success('领取成功');
                    self.parent().parent().prop('hidden',true);
                } else {
                    toastr.error('领取失败');
                }
            },
            error: function (data) {
                self.prop('disabled', false);
                toastr.error('领取失败');
            }
        });
    }

    function loadDatas() {
        $.ajax({
            url: "{{ url_for('global_crystal.get_crystal') }}",
            type: 'POST',
            data: {
                worldId:{{ worldId }},
            },
            success: function(data) {
                if (data.code == 200) {
                    createRows(data.data)
                    // $(this).prop('hidden',true);
                } else {
                    // $(this).prop('disabled', false);
                    toastr.error('异常');
                }
            },
            error: function(data) {
                toastr.error('请求异常');
            }

        });
    }
    
    window.onload = function (arg1) {
        loadDatas();
    }

    </script>
</html>
