{% extends 'work_base.html' %}
{% import 'admin/lib.html' as lib with context %}
{% block userinfo %}

{% endblock %}

{% block actions %}
<div class="row">
    <div class="col-md-6 row">
        <div class="col-md-2">
            <button id="gobacklist" type="button" class="btn btn-primary text-nowrap">队列列表</button>
        </div>
        {% if not workStatus %}
            {% if current_user.has_role('vip3user') %}
            <div class="col-md-2">
                <button id="gobackAuto" type="button" class="btn btn-info text-nowrap">自动撤回</button>
            </div>

            {% endif %}
        {% endif %}
    </div>
</div>
<br />
<table class="table">
    <thead>
        <tr>
            <th scope="col">剩余时间</th>
            <th scope="col">总时间</th>
            <th scope="col">数量</th>
            <th scope="col">距离</th>
            <th scope="col">操作</th>
        </tr>
    </thead>
    <tbody id="gobacklist-tbody">
    </tbody>
</table>
{% endblock %}
{% block tail_work %}
<script>
    function createRow(data) {
        
        var row = $('<tr data-info=\''+JSON.stringify(data)+'\'></tr>');
        row.append($('<th scope="col">' + data.t + '秒</th>'));
        row.append($('<th scope="col">' + data.maxT + '秒</th>'));
        row.append($('<th scope="col">' + data.amount + '</th>'));
        row.append($('<th scope="col">' + data.d + '</th>'));
        row.append($('<td><button type="button" class="btn btn-danger gobacklist-use">翻倍</button></td>'));
        return row[0];
    }

    function createRows(datas) {
        tb = $('#gobacklist-tbody')[0];
        $(tb).empty()
        for (var i = 0; i < datas.length; i++) {
            tb.append(createRow(datas[i]));
        }

        addClickEvent();        
    }

    function addClickEvent() {
        uses = $('.gobacklist-use');
        uses.click(gobackUse);
    }

    function gobackUse(arg1) {
        var data = $(arg1.target).parent().parent().data('info');
        btn = $(arg1.currentTarget);
        uses = $('.gobacklist-use');
        uses.prop("disabled", true);
        $.ajax({
            url: '{{ url_for("gobacklist.goback_use_api") }}',
            type: 'POST',
            data: {
                moId:data.moId
            },
            beforeSend: function () {
                uses.prop('disabled', true);
                btn.prop("hidden", true);
            },
            complete: function () {
                uses.prop('disabled', false);
            },
            success: function (data) {
                if (data.code == 200) {
                    toastr.success("翻倍成功"+data.count);
                } else {
                    toastr.error("翻倍异常:"+data.msg);
                }
            },
            error: function (data) {
                toastr.error(data.msg);
            },
        });
    }

    $("#gobacklist").click((arg1) => {
        btn = $("#gobacklist");
        $.ajax({
            url: '{{ url_for("gobacklist.gobacklist_api") }}',
            type: 'POST',
            data: {

            },
            beforeSend: function () {
                btn.prop("disabled", true);
            },
            complete: function () {
                btn.prop("disabled", false);
            },
            success: function (data) {
                if (data.code == 200) {
                    toastr.success('刷新成功');
                    createRows(data.data);
                } else {
                    toastr.error(data.msg);
                }
            },
            error: function (data) {
                toastr.error(data.msg);
            }
        });
    });

    $("#gobackAuto").click((arg1) => {
        btn = $(arg1.currentTarget);
        console.log(btn);

        $.ajax({
            url: '{{ url_for("gobacklist.goback_auto_api") }}',
            type: 'POST',
            data: {

            },
            beforeSend: function () {
                btn.prop("disabled", true);
            },
            complete: function () {
                btn.prop("disabled", false);
            },
            success: function (data) {
                if (data.code == 200) {
                    setTimeout(() => {
                        window.location.reload();
                        }, 2000);
                } else {
                    toastr.error(data.msg);
                }
            },
            error: function (data) {
                toastr.error(data.msg);
            }
        });
    });
</script>
{% endblock %}
