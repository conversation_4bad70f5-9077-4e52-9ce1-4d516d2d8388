<!-- Modal -->
<div class="modal fade" id="followModal" tabindex="-1" aria-labelledby="followModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="followModalLabel">跟团设置</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <form>
          <div class="form-group row align-items-center">
            <label for="monster" class="col-sm-2 col-form-label">参团怪物</label>
            <div class="col-sm-10">
              <div class="form-check form-check-inline">
                <input class="form-check-input" name="monster" type="checkbox" id="anyMonster" value="1" checked>
                <label class="form-check-label" for="dk">非龙</label>
              </div>
              <div class="form-check form-check-inline">
                <input class="form-check-input" name="monster" type="checkbox" id="greenDragon" value="1" checked>
                <label class="form-check-label" for="greenDragon">绿龙</label>
              </div>
              <div class="form-check form-check-inline">
                <input class="form-check-input" name="monster" type="checkbox" id="redDragon" value="1" checked>
                <label class="form-check-label" for="redDragon">红龙</label>
              </div>
              <div class="form-check form-check-inline">
                <input class="form-check-input" name="monster" type="checkbox" id="goldDragon" value="1" checked>
                <label class="form-check-label" for="goldDragon">金龙</label>
              </div>
            </div>
          </div>
          <div class="form-group row">
            <label for="monsterLv" class="col-sm-2 col-form-label">怪物等级</label>
            <div class="col-sm-10">
              <div class="form-row">
                <div class="form-group col-md-3 form-check-inline">
                  <label for="anyMonsterLv" class="col-form-label">非龙</label>
                  <select id="anyMonsterLv" class="form-control col-md-8">
                    <option value="0">不限</option>
                    <option value="8">8</option>
                    <option value="7">7</option>
                    <option value="6">6</option>
                    <option value="5">5</option>
                    <option value="4">4</option>
                    <option value="3">3</option>
                    <option value="2" selected>2</option>
                    <option value="1">1</option>

                  </select>
                </div>
                <div class="form-group col-md-3 form-check-inline">
                  <label for="greenLV" class="col-form-label">绿龙</label>
                  <select id="greenLV" class="form-control col-md-8">
                    <option value="0">不限</option>
                    <option value="3">3</option>
                    <option value="2" selected>2</option>
                  </select>
                </div>
                <div class="form-group col-md-3 form-check-inline">
                  <label for="redLv" class="col-form-label">红龙</label>
                  <select id="redLv" class="form-control col-md-8">
                    <option value="0">不限</option>
                    <option value="3">3</option>
                    <option value="2" selected>2</option>
                  </select>
                </div>
                <div class="form-group col-md-3 form-check-inline">
                  <label for="goldLv" class="col-form-label">金龙</label>
                  <select id="goldLv" class="form-control col-md-8">
                    <option selected value="0">不限</option>
                    <option value="3">3</option>
                    <option value="2">2</option>
                  </select>
                </div>
              </div>
            </div>
          </div>

          <div class="form-group row">
            <label for="monsterLv" class="col-sm-2 col-form-label">参团数量</label>
            <div class="col-sm-10">
              <div class="form-row">
                <input type="number" class="form-control" id="followNum" value="300000">
              </div>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
        <button type="button" class="btn btn-primary" id="saveFollow">保存</button>
      </div>
    </div>
  </div>
  <script>
    window.onload = function (arg1) {
      console.log(arg1);
      followInfo = JSON.parse(localStorage.getItem("followInfo"))
      if (followInfo == undefined) {
        return
      }
      console.log(followInfo);
      var monster = followInfo[0];
      var i = 0;
      $('input[name="monster"]').each(function () {
        $(this).prop("checked",monster[i++]);
      });
      lvs = followInfo[1];
      items = ['anyMonsterLv', 'greenLV', 'redLV', 'goldLV'];
      for (var i = 0; i < items.length; i++) {
        $('#' + items[i]).val(lvs[i]);
      }

      $('#followNum').val(followInfo[2]);
    }

    $("#saveFollow").click(function () {
      //选中怪物
      var mSelect = [];
      $('input[name="monster"]').each(function () {
        mSelect.push($(this).prop("checked"));
      });
      //跟随等级
      var anyMonsterLv = $("#anyMonsterLv").val();
      var greenLv = $("#greenLV").val()
      var redLv = $("#redLv").val()
      var goldLv = $("#goldLv").val()
      //参团数量
      var followNum = $("#followNum").val()
      var setting = [mSelect, [anyMonsterLv, greenLv, redLv, goldLv], followNum]
      //保存到本地
      localStorage.setItem("followInfo", JSON.stringify(setting))
      //获取
      console.log(JSON.parse(localStorage.getItem("followInfo")))
      $("#followModal").modal("hide")
    });

    $("#autojoin").click((arg1) => {
      followInfo = localStorage.getItem("followInfo");
      if (followInfo == undefined) {
        alert("请先设置跟团信息");
        return;
      }
      // troopNum = parseInt($("#troopnum")[0].value);
      // if (troopNum < 200000) {
      //     troopNum = 200000;
      // }
      btn = $(this);
      $.ajax({
        url: '{{ url_for("work.autoJoinBattle_api") }}',
        type: 'POST',
        data: {
          // troopNum: troopNum,
          followInfo: followInfo,
        },
        beforeSend: function () {
          btn.prop("disabled", true);
        },
        success: function (data) {
          $("#autojoin-load").hide();
          $("#autojoin-text").show();
          if (data.code == 200) {
            setTimeout(() => {
              window.location.reload();
            }, 3000);
          } else {
            btn.prop("disabled", false);
            toastr.error(data.msg);
          }
        },
        error: function (data) {
          btn.prop("disabled", false);
          toastr.error(data.msg);
        }
      });
    });
  </script>
</div>

{% macro rallyButton() -%}
<div class="btn-group" role="group">
  <button id="autojoin" type="button" class="btn btn-primary">
    <span id="autojoin-text">开启自动跟团</span>
  </button>
  <button id="autojoin-set" type="button" class="btn btn-secondary" data-toggle="modal"
    data-target="#followModal">跟团设置</button>
</div>
{%- endmacro %}