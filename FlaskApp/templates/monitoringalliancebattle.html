{% extends 'work_base.html' %}
{% import 'admin/lib.html' as lib with context %}
{% block userinfo %}

{% endblock %}
{% block actions %}
<div class="row">
    <div class="input-group col-md-2">
        <div class="btn-group" role="group">
            <button id="monitoringalliancebattle" type="button" class="btn btn-primary">团战列表</button>
        </div>
    </div>

</div>
<br />
<table class="table">
    <thead>
        <tr>
            <th scope="col">团长</th>
            <th scope="col">目标</th>
            <th scope="col">兵力</th>
        </tr>
    </thead>
    <tbody id="monitoringalliancebattle-tbody">
    </tbody>
</table>
{% endblock %}
{% block tail_work %}
<script>
    function createRow(data) {
        
        var row = $('<tr data-info=\''+JSON.stringify(data)+'\'></tr>');
        row.append($('<th scope="col">' + data.leaderKingdomName + '</th>'));
        row.append($('<th scope="col">' + data.targetKingdomName + '</th>'));
        troopRunMap = data.troopRunMap;
        td = $('<td></td>');
        for (key in troopRunMap) {
            td.append($(`<i>${key}:${troopRunMap[key]}</i> <br />`));
        }
        row.append(td);
        return row[0];
    }

    function createRows(datas) {
        tb = $('#monitoringalliancebattle-tbody')[0];
        $(tb).empty()
        for (var i = 0; i < datas.length; i++) {
            tb.append(createRow(datas[i]));
        }
    }

    $("#monitoringalliancebattle").click((arg1,arg2) => {
        btn = $("#monitoringalliancebattle");
        $.ajax({
            url: '{{ url_for("monitoringalliancebattle.monitoringalliancebattle_api") }}',
            type: 'POST',
            data: {

            },
            beforeSend: function () {
                btn.prop("disabled", true);
            },
            complete: function () {
                btn.prop("disabled", false);
            },
            success: function (data) {
                if (data.code == 200) {
                    toastr.success('刷新成功');
                    createRows(data.data);
                } else {
                    toastr.error(data.msg);
                }
            },
            error: function (data) {
                toastr.error(data.msg);
            }
        });
    });
</script>
{% endblock %}
