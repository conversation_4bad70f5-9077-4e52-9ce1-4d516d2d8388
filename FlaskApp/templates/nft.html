{% extends 'work_base.html' %}
{% block body %}
<div class="container-fluid">
    <div class="row" style="height: 480px;">
        <div class="col-md-6 col-lg-6 offset-lg-0">
            {% if current_user.has_role('superuser') %}
            <div class="input-group mb-3">
                <div class="input-group-prepend">
                    <span class="input-group-text" style="width: 70px;">用户</span>
                </div>
                <select id="workUser" class="form-control">
                    <optgroup label="用户列表">
                        <option value="0" selected>请选择用户</option>
                        {% for user in workUsers %}
                            <option value='{{ user["token"] }}' data-info='{{ user }}'>{{ user["name"] }}</option>
                        {% endfor %}
                    </optgroup>
                </select>
            </div>
            {% endif %}
            <div class="form-group">
                <div class="input-group">
                    <div class="input-group-prepend"><span class="input-group-text" style="width: 70px;">Token</span>
                    </div><input id="token" class="form-control" type="text" />
                    <div class="input-group-append"><button id="getnftlist" class="btn btn-primary"
                            type="button">查询</button></div>
                    <div class="input-group-append"><button id="getnftdragolist" class="btn btn-primary"
                            type="button">查龙</button></div>
                </div>
            </div>
            <div class="input-group">
                <div class="input-group-prepend"><span class="input-group-text" style="width: 70px;">私钥</span></div>
                <input id="pkey" class="form-control" type="text" />
                <div class="input-group-append"><button id="bindpkey" class="btn btn-primary" type="button">绑定</button>
                <button id="unbindpkey" class="btn btn-danger" type="button">解绑</button>
                </div>
            </div>
        </div>
        <div class="col">
            <label>公钥:<label id="publicKey"></label></label>
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>种类</th>
                            <th>数量</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- <tr>
                            <td>any</td>
                            <td>0</td>
                            <td></td>
                        </tr> -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}
{% block tail_work %}
<script>

    function createRows(data, type) {
        var html = "";
        var publicKey = "";
        for (k in data) {
            var v = data[k];
            if (k == "publicKey") {
                publicKey = v;
                continue
            }
            if (type == 0) {
                html += "<tr><td>" + k + "</td><td>" + v + "</td><td></td></tr>";
            }else if (type == 1) {
                html += `<tr data-info=${v}><td>${k}</td><td>1</td><td><button type="button" class="btn btn-danger kick text-nowrap">滚</button></td></tr>`;
            }
            
        }
        $("tbody").html(html);
        $("#publicKey").html(publicKey);

        addClickEvent();
    }

    function addClickEvent() {
        kicks = $("button.btn-danger.kick");
        kicks.click(kick);
    }

    function kick(arg1) {
        var token = $("#token").val();
        if (token == "" && !isSelectUser()) {
            toastr.error("请输入token");
            return;
        }
        info = this.parentElement.parentElement.dataset.info;
        dragoId = info;
        
        let btn = $(this);

        $.ajax({
            url: "{{ url_for('nft.kick_drago_api') }}",
            type: 'POST',
            data: {
                token: token,
                dragoId: dragoId,
                workUser: isSelectUser() ? workUserInfo() : null,
            },
            beforeSend: function () {
                btn.prop('disabled', true);
            },
            complete: function () {
                btn.prop('disabled', false);
            },
            success: function (data) {
                if (data.code == 200) {
                    toastr.success("踢出成功");
                }else {
                    toastr.error('踢出失败' + data.msg);
                    btn.prop('disabled', false);
                }
            },
            error: function (data) {
                toastr.error('踢出失败');
                btn.prop('disabled', false);
            }
        })

    }

    $("#getnftlist").click(function () {
        var token = $("#token").val();
        if (token == "" && !isSelectUser()) {
            toastr.error("请输入token");
            return;
        }
        $.ajax({
            url: "{{ url_for('nft.getnftlist_api') }}",
            type: "POST",
            data: {
                token: token,
                workUser: isSelectUser() ? workUserInfo() : null,
            },
            success: function (data) {
                if (data.code == 200) {
                    toastr.success("绑定成功");
                    createRows(data.data,0);                    
                } else {
                    toastr.error(data.msg);
                }
            }
        });
    });

    $("#getnftdragolist").click(function () {
        var token = $("#token").val();
        if (token == "" && !isSelectUser()) {
            toastr.error("请输入token");
            return;
        }
        $.ajax({
            url: "{{ url_for('nft.getnftdragolist_api') }}",
            type: "POST",
            data: {
                token: token,
                workUser: isSelectUser() ? workUserInfo() : null,
            },
            success: function (data) {
                if (data.code == 200) {
                    toastr.success("绑定成功");
                    createRows(data.data,1);                    
                } else {
                    toastr.error(data.msg);
                }
            }
        });
    });

    $("#bindpkey").click(function () {
        var pkey = $("#pkey").val();
        if (pkey == "" || pkey.length != 66) {
            toastr.error("请输入有效私钥");
            return;
        }
        var token = $("#token").val();
        if (token == "" && !isSelectUser()) {
            toastr.error("请输入token");
            return;
        }
        $.ajax({
            url: "{{ url_for('nft.bindpkey_api') }}",
            type: "POST",
            data: {
                token: token,
                pkey: pkey,
                workUser: isSelectUser() ? workUserInfo() : null,
            },
            success: function (data) {
                if (data.code == 200) {
                    toastr.success("绑定成功");
                    createRows(data.data);
                } else {
                    toastr.error(data.msg);
                }
            }
        });
    });

    $("#unbindpkey").click(function () {
        var token = $("#token").val();
        if (token == "" && !isSelectUser()) {
            toastr.error("请输入token");
            return;
        }
        $.ajax({
            url: "{{ url_for('nft.unbindpkey_api') }}",
            type: "POST",
            data: {
                token: token,
                workUser: isSelectUser() ? workUserInfo() : null,
            },
            success: function (data) {
                if (data.code == 200) {
                    toastr.success("解绑成功");
                } else {
                    toastr.error(data.msg);
                }
            }
        });
    });
</script>
{% endblock %}
