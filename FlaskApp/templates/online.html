{% extends 'work_base.html' %}
{% from 'components/teleport_modal.html' import init_teleport_modal %}
{% from 'components/king_power_modal.html' import init_king_power_modal %}
{% from 'components/rally_settings_modal.html' import init_rally_settings_modal %}
{% from 'components/point_selector_modal.html' import init_point_selector_modal %}
{% block body %}
<style>
  </style>
<div class="row">
  <div class="col-sm-2">
      <div class="card">
        <div class="card-body">
          <h5 class="card-title">范围 <number class="range-value" id="range-value">{{ maxMonsterDistance }}</number> <span style="color: red;">红龙: {{ todayRedDragon }}</span> <span style="color: blue;">打怪: {{ todayMonster }}</span></h5>
          <div class="row" style="margin: auto;">
            <input type="range" class="form-control-range" style="width: 70%" min="0" max="100" step="1" id="progress-range" value="{{ maxMonsterDistance }}" oninput="updateControlValue()">
            <button type="button" class="btn btn-primary submit-range"  style="margin: auto;">修改</button>
        </div>
      </div>
    </div>
  </div>
  <div class="col-sm-4">
    <div class="card">
      <div class="card-body">
        <h5 class="card-title">广播</h5>
        <div class="row mb-2">
          <div class="col-md-3">
            <button type="button" class="btn btn-primary btn-block attack-treasure">攻击装</button>
          </div>
          <div class="col-md-3">
            <button type="button" class="btn btn-primary btn-block collect-treasure">采集装</button>
          </div>
          <div class="col-md-3">
            <button type="button" class="btn btn-primary btn-block shield">开盾</button>
          </div>
          <div class="col-md-3">
            <button type="button" class="btn btn-primary btn-block anti-detect">反侦查</button>
          </div>
        </div>
        <div class="row">
          <div class="col-md-3">
            <button type="button" class="btn btn-primary btn-block global-crystal">闲时水晶</button>
          </div>
          <div class="col-md-3">
            <button type="button" class="btn btn-primary btn-block teleport">传送</button>
          </div>
          <div class="col-md-3">
            <button type="button" class="btn btn-primary btn-block king-powers-btn" data-toggle="modal" data-target="#kingPowerModal">国王技能</button>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-sm-3">
    <div class="card">
      <div class="card-body">
        <h5 class="card-title">CVC锁分</h5>
        <div class="row" style="margin: auto;">
          <div class="d-flex align-items-center">
            <div style="margin-right: 10px;">
              <span>分数: </span>
              <select class="form-control form-control-sm" id="cvcScoreSelect" style="width: 120px; display: inline-block;">
                <option value="100000" {% if cvcRankLockPoint == 100000 %}selected{% endif %}>100,000</option>
                <option value="500000" {% if cvcRankLockPoint == 500000 %}selected{% endif %}>500,000</option>
                <option value="1000000" {% if cvcRankLockPoint == 1000000 %}selected{% endif %}>1,000,000</option>
                <option value="3000000" {% if cvcRankLockPoint == 3000000 %}selected{% endif %}>3,000,000</option>
                <option value="5000000" {% if cvcRankLockPoint == 5000000 %}selected{% endif %}>5,000,000</option>
              </select>
            </div>
            <button type="button" class="btn btn-sm btn-primary update-cvc-score" style="margin-right: 10px;">修改</button>
            <button type="button" class="btn btn-sm btn-info" data-toggle="modal" data-target="#cvcBlockPointSelectorModal">
              <i class="fas fa-map-marker-alt mr-1"></i>选点
            </button>
          </div>
          <div class="d-flex align-items-center mt-2">
            <div style="margin-right: 10px;">
              <span>锁分: </span>
              <span class="text-{% if cvcRankSwitch %}success{% else %}danger{% endif %}">
                {% if cvcRankSwitch %}开启{% else %}关闭{% endif %}
              </span>
            </div>
            <button type="button" class="btn btn-sm btn-success cvc-rank-on" style="margin-right: 10px;">开启</button>
            <button type="button" class="btn btn-sm btn-danger cvc-rank-off" style="margin-right: 10px;">关闭</button>

            <div style="margin-right: 10px;">
              <span>踢人: </span>
              <span class="text-{% if cvcBlockAutoKick %}success{% else %}danger{% endif %}">
                {% if cvcBlockAutoKick %}开启{% else %}关闭{% endif %}
              </span>
            </div>
            <button type="button" class="btn btn-sm btn-success cvc-block-on" style="margin-right: 10px;">开启</button>
            <button type="button" class="btn btn-sm btn-danger cvc-block-off" style="margin-right: 10px;">关闭</button>
          </div>
        </div>
      </div>
    </div>
  </div>


</div>

<br/>

<div class="row">
  <div class="col-sm-2">
    <div class="card">
      <div class="card-body">
        <h5 class="card-title">驻防</h5>
        <div>
          <div class="form-group">
              <label for="historyInput">目标 (格式: 世界,x,y):</label>
              <input
                  type="text"
                  class="form-control"
                  id="historyInput"
                  placeholder="20,1024,1024"
              />
              <div class="invalid-feedback">格式不正确，请输入 *,*,*</div>
          </div>
          <div class="form-check d-flex align-items-center mb-3">
            <input type="checkbox" class="form-check-input" id="remoteCheck">
            <label class="form-check-label mr-3" for="remoteCheck">远程</label>
            <div class="d-flex align-items-center">
              <input type="number" class="form-control form-control-sm mr-2" id="troopCount" value="300000" min="210000" max="500000" style="width: 90px;" title="军队数量">
              <select class="form-control form-control-sm" id="remoteRatio" style="width: 80px;">
                <option value="[4,4,2]">442</option>
                <option value="[4,2,4]">424</option>
                <option value="[1,1,1]">111</option>
              </select>
            </div>
          </div>

          <button type="button" class="btn btn-success garrison">驻防</button>
          <button type="button" class="btn btn-danger return-support">撤回</button>
          <button type="button" class="btn btn-primary garrison-temple">神殿</button>
        </div>
      </div>
    </div>
  </div>

  <div class="col-sm-3">
    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between align-items-center">
          <h5 class="card-title">跟团时间 <span class="text-{% if shieldRallySwitch %}success{% else %}danger{% endif %}">
             破盾跟团 {% if shieldRallySwitch %}开启{% else %}关闭{% endif %}
          </span></h5>
          <button type="button" class="btn btn-sm btn-primary" data-toggle="modal" data-target="#rallySettingsModal">跟团设置</button>
        </div>
        <div class="row" style="margin: auto;">
          <form id="checkboxForm">
            <div class="row">
                <!-- 生成1到24的复选框，每行6个 -->
                <script>
                    let battleTimes = {{ battleTimes|safe }}
                    for (let i = 0; i < 24; i++) {
                      const checked = battleTimes.includes(i) ? 'checked' : '';
                      document.write(`
                          <div class="col-md-2">
                              <div class="form-check">
                                  <input class="form-check-input" type="checkbox" value="${i}" id="checkbox${i}" ${checked}>
                                  <label class="form-check-label" for="checkbox${i}">${i}</label>
                              </div>
                          </div>
                      `);
                    }
                </script>
            </div>
            <button type="submit" class="btn btn-primary mt-3">提交</button>
            <button type="button" class="btn btn-success mt-3 shield-rally-on">开启破盾跟团</button>
            <button type="button" class="btn btn-danger mt-3 shield-rally-off">关闭破盾跟团</button>
          </form>
        </div>
      </div>
    </div>
  </div>
  <div class="col-sm-3">
    <div class="card">
      <div class="card-body">
        <h5 class="card-title">cvc搜索范围 <span style="color: blue;">有效数量: {{ rallyMonsterNum }}</span></h5>
        <div>
          <form id="recordForm" >
            <div class="form-row">
              <select class="form-control mb-2" id="cvcLevel">
                  <option value="4">4</option>
                  <option value="5">5</option>
                  <option value="6">6</option>
                  <option value="7">7</option>
                  <option value="8">8</option>
                  <option value="9">9</option>
                  <option value="10">10</option>
                  <option value="11">无</option>
              </select>
            </div>
            <div class="form-row">
                <div class="col">
                    <input
                        type="number"
                        class="form-control"
                        id="x"
                        placeholder="X"
                        required
                    />
                </div>
                <div class="col">
                    <input
                        type="number"
                        class="form-control"
                        id="y"
                        placeholder="Y"
                        required
                    />
                </div>
                <div class="col">
                    <button type="submit" class="btn btn-primary">
                        添加
                    </button>
                </div>
                <div class="col">
                    <button type="button" class="btn btn-info" data-toggle="modal" data-target="#pointSelectorModal">
                        <i class="fas fa-map-marker-alt mr-1"></i>选点
                    </button>
                </div>
            </div>
            <div id="error" class="text-danger mt-2" style="display: none">
                请确保X和Y都是整数。
            </div>
          </form>

          <!-- 记录列表 -->
          <ul class="list-group" id="recordList">
              <!-- 动态添加列表项 -->
          </ul>

          <div class="mt-2">
            <button type="button" class="btn btn-success cvcpoint" id="CVCPointSubmit">保存</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{{ init_teleport_modal() }}
{{ init_king_power_modal() }}
{{ init_rally_settings_modal() }}
{{ init_point_selector_modal() }}
{% endblock %}
{% block tail %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.12.1/jquery-ui.min.js"></script>
<link
            rel="stylesheet"
            href="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.12.1/themes/base/jquery-ui.min.css"
        />
<script>

  let rangeValue = $("#range-value")[0];

  function updateControlValue() {
      var range = $("#progress-range")[0];
      var value = range.value;
      rangeValue.innerText = value;
  }

  $("button.submit-range").click(function() {
      var value = rangeValue.innerText;
      console.log(value);
      $.ajax({
          url: '{{ url_for(".update_distance_api") }}',
          type: "POST",
          data: {
              distance: value
          },
          success: function(data) {
              toastr.success("修改成功");
          }
      });
  });

  $("button.global-crystal").click(function() {
      $.ajax({
          url: '{{ url_for(".global_crystal_api") }}',
          type: "POST",
          success: function(data) {
              toastr.success("广播成功");
          }
      });
  });

  $("button.attack-treasure").click(function() {
      $.ajax({
          url: '{{ url_for(".treasure_page_api") }}',
          type: "POST",
          data: {
            "page": 0,
          },
          success: function(data) {
              toastr.success("广播成功");
          }
      });
  });

  $("button.collect-treasure").click(function() {
      $.ajax({
          url: '{{ url_for(".treasure_page_api") }}',
          type: "POST",
          data: {
            "page": 1,
          },
          success: function(data) {
              toastr.success("广播成功");
          }
      });
  });

  const history = []; // 历史记录的例子

  // 创建可编辑的记录列表项
  function createEditableListItem(x, y) {
      // 列表项结构
      const listItem = $(
          '<li class="list d-flex align-items-center mt-2"></li>',
      );

      // 输入框容器
      const inputContainer = $('<div class="d-flex"></div>');

      // X 输入框
      const xInput = $(
          '<input type="number" class="form-control mr-2" style="width: 80px;">',
      ).val(x);

      // Y 输入框
      const yInput = $(
          '<input type="number" class="form-control mr-2" style="width: 80px;">',
      ).val(y);

      // 将输入框添加到输入容器
      inputContainer.append(xInput).append(yInput);

      // 删除按钮
      const deleteButton = $(
          '<button class="btn btn-danger btn-sm">删除</button>',
      ).click(function () {
          listItem.remove();
      });

      // 将输入容器和删除按钮添加到列表项
      listItem.append(inputContainer).append(deleteButton);

      return listItem;
  }

  $(document).ready(function () {
    initTeleportModal(
      '{{ url_for(".get_teleport_targets_api") }}',
      '{{ url_for(".teleport_api") }}',
      '{{ url_for(".cvc_move_api") }}'
    );
    initKingPowerModal();
    const cvcBlockPoints = {{ cvcBlockPoints|safe }}
    const cvcPoints = {{ cvcPoints|safe }}
    const spartoiLevel = {{ spartoiLevel|safe }}

    // 实现输入框自动补全
    $("#historyInput").autocomplete({
            source: history,
            minLength: 0, // 允许在没有输入时显示历史记录
        })
        .focus(function () {
            $(this).autocomplete("search", "");
        });

    // 添加记录功能
    $("#recordForm").submit(function (event) {
        event.preventDefault(); // 防止表单提交刷新页面

        const xValue = parseInt($("#x").val());
        const yValue = parseInt($("#y").val());

        // 校验 x 和 y 是否为整数
        if (isNaN(xValue) || isNaN(yValue)) {
            $("#error").show(); // 显示错误消息
            return;
        } else {
            $("#error").hide(); // 隐藏错误消息
        }

        // 创建新的列表项
        const listItem = createEditableListItem(xValue, yValue);

        // 将列表项添加到记录列表
        $("#recordList").append(listItem);

        // 清空输入框
        $("#x").val("");
        $("#y").val("");
    });

    cvcPoints.forEach( point => $("#recordList").append(createEditableListItem(point[0],point[1])));
    $("#cvcLevel").val(`${spartoiLevel}`);
  });

  $('#CVCPointSubmit').click(function() {
    const dataArray = [];

    // 遍历记录列表，获取每个记录的 x 和 y 值
    $('#recordList li').each(function() {
        const xValue = $(this).find('input').eq(0).val();
        const yValue = $(this).find('input').eq(1).val();

        dataArray.push([parseInt(xValue), parseInt(yValue)]);
    });
    cvcLevel = $("#cvcLevel").val()

    console.log(dataArray);
    $.ajax({
      url: '{{ url_for(".update_cvc_points_api") }}',
      type: "POST",
      data: {
        cvcpoints: JSON.stringify(dataArray),
        spartoiLevel: parseInt(cvcLevel),
      },
      success: function (data) {
          toastr.success("保存成功");
      }
  });
  });

  // 提交按钮点击事件
  $("button.garrison").click(function () {
      const inputVal = $("#historyInput").val();
      const pattern = /^\d+,\d+,\d+$/; // 验证格式 *.*.*

      if (pattern.test(inputVal)) {
          let remoteCheck = $("#remoteCheck").is(':checked')
          let loc = inputVal.split(",");
          let worldId = parseInt(loc[0]);
          if (worldId != undefined && (worldId < 100 || (worldId % 100000 > 0 && (parseInt(worldId / 100000) == 1)))) {
            $("#historyInput").removeClass("is-invalid");
            history.push(inputVal);

            // 验证军队数量
            const troopCount = parseInt($("#troopCount").val());
            if (troopCount < 210000 || troopCount > 500000) {
              toastr.error("军队数量必须在210000到500000之间");
              return;
            }

            $.ajax({
                url: '{{ url_for(".garrison_api") }}',
                type: "POST",
                data: {
                  loc: inputVal,
                  onlyMelee: remoteCheck,
                  quantity: troopCount,
                  ratio: $("#remoteRatio").val()
                },
                success: function (data) {
                    toastr.success("驻防成功");
                }
            });
          }else {
            toastr.error("请输入正确的世界ID");
          }
      } else {
          $("#historyInput").addClass("is-invalid");
      }
  });

  // 添加军队数量输入验证
  $("#troopCount").on('input', function() {
    const value = parseInt($(this).val());
    if (value < 210000) {
      $(this).val(210000);
    } else if (value > 500000) {
      $(this).val(500000);
    }
  });

  // 提交表单时的处理
  $('#checkboxForm').on('submit', function(e) {
      e.preventDefault();
      let selectedCheckboxes = [];
      $('input[type=checkbox]:checked').each(function() {
          selectedCheckboxes.push($(this).val());
      });

      $.ajax({
        url: '{{ url_for(".update_battle_times_api") }}',
        type: "POST",
        data: {
          battleTimes: JSON.stringify(selectedCheckboxes),
        },
        success: function (data) {
            toastr.success("更新成功");
        }
      });
  });

  $("button.cvc-rank-on").click(function() {
    $.ajax({
      url: '{{ url_for(".cvc_rank_switch_api") }}',
      type: "POST",
      data: { value: 1 },
      success: function (data) {
        if (data.code == 200) {
          toastr.success("开启成功");
          setTimeout(function() {
            location.reload();
          }, 2000);
        }
      }
    });
  });

  $("button.cvc-rank-off").click(function() {
    $.ajax({
      url: '{{ url_for(".cvc_rank_switch_api") }}',
      type: "POST",
      data: { value: 0 },
      success: function (data) {
        if (data.code == 200) {
          toastr.success("关闭成功");
          setTimeout(function() {
            location.reload();
          }, 2000);
        }
      }
    });
  });

  $("button.cvc-block-on").click(function() {
    $.ajax({
      url: '{{ url_for(".cvc_block_auto_kick_api") }}',
      type: "POST",
      data: { value: 1 },
      success: function (data) {
        if (data.code == 200) {
          toastr.success("开启成功");
          setTimeout(function() {
            location.reload();
          }, 2000);
        }
      }
    });
  });

  $("button.cvc-block-off").click(function() {
    $.ajax({
      url: '{{ url_for(".cvc_block_auto_kick_api") }}',
      type: "POST",
      data: { value: 0 },
      success: function (data) {
        if (data.code == 200) {
          toastr.success("关闭成功");
          setTimeout(function() {
            location.reload();
          }, 2000);
        }
      }
    });
  });

  $("button.shield").click(function() {
    $.ajax({
        url: '{{ url_for(".use_item_api") }}',
        type: "POST",
        data: {
            itemId: 10102026
        },
        success: function(data) {
            toastr.success("开盾广播成功");
        }
    });
  });

  $("button.anti-detect").click(function() {
    $.ajax({
        url: '{{ url_for(".use_item_api") }}',
        type: "POST",
        data: {
            itemId: 10102022
        },
        success: function(data) {
            toastr.success("反侦查广播成功");
        }
    });
  });

  $("button.shield-rally-on").click(function() {
    $.ajax({
      url: '{{ url_for(".shield_rally_switch_api") }}',
      type: "POST",
      data: { value: 1 },
      success: function (data) {
        if (data.code == 200) {
          toastr.success("开启成功");
          setTimeout(function() {
            location.reload();
          }, 2000);
        }
      }
    });
  });

  $("button.shield-rally-off").click(function() {
    $.ajax({
      url: '{{ url_for(".shield_rally_switch_api") }}',
      type: "POST",
      data: { value: 0 },
      success: function (data) {
        if (data.code == 200) {
          toastr.success("关闭成功");
          setTimeout(function() {
            location.reload();
          }, 2000);
        }
      }
    });
  });

  $("button.update-cvc-score").click(function() {
    const score = $("#cvcScoreSelect").val();
    $.ajax({
      url: '{{ url_for(".update_cvc_lock_point_api") }}',
      type: "POST",
      data: { lockPoint: parseInt(score) },
      success: function (data) {
        if (data.code == 200) {
          toastr.success("修改成功");
          setTimeout(function() {
            location.reload();
          }, 2000);
        }
      }
    });
  });

  // 撤回支援部队
  $("button.return-support").click(function() {
    $.ajax({
      url: '{{ url_for(".return_support_api") }}',
      type: "POST",
      success: function(data) {
        if (data.code == 200) {
          toastr.success("撤回支援成功");
        }
      }
    });
  });

  // 神殿驻军
  $("button.garrison-temple").click(function() {
    $.ajax({
      url: '{{ url_for(".garrison_temple_api") }}',
      type: "POST",
      success: function(data) {
        if (data.code == 200) {
          toastr.success("神殿驻军成功");
        }
      }
    });
  });

  // 在CVC搜索范围卡片中的选点按钮点击事件
  $("button[data-target='#pointSelectorModal']").click(function() {
    const points = [];
    $('#recordList li').each(function() {
      const xValue = $(this).find('input').eq(0).val();
      const yValue = $(this).find('input').eq(1).val();
      points.push([parseInt(xValue), parseInt(yValue)]);
    });

    openPointSelector(points, function(selectedPoints) {
      // 清空现有列表
      $('#recordList').empty();

      // 添加新的点
      selectedPoints.forEach(point => {
        $('#recordList').append(createEditableListItem(point[0], point[1]));
      });
    });
  });

  // CVC锁分卡片中的选点按钮点击事件
  $("button[data-target='#cvcBlockPointSelectorModal']").click(function() {
    const points = {{ cvcBlockPoints|safe }};
    openPointSelector(points, function(selectedPoints) {
      // 调用API保存数据
      $.ajax({
        url: '{{ url_for(".update_cvc_block_points_api") }}',
        type: "POST",
        data: {
           cvcBlockPoints: JSON.stringify(selectedPoints)
        },
        success: function(data) {
          if (data.code == 200) {
            toastr.success('保存成功');
            setTimeout(function() {
              location.reload();
            }, 2000);
          } else {
            toastr.error('保存失败');
          }
        },
        error: function() {
          toastr.error('保存失败');
        }
      });
    });
  });
</script>
{% endblock %}