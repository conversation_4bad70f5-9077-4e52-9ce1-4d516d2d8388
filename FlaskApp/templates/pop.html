<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>pop</title>
  <link rel="stylesheet" href="css/bootstrap.min.css">
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.5.1/jquery.slim.min.js" crossorigin="anonymous"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.16.1/umd/popper.min.js" crossorigin="anonymous"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/4.6.1/js/bootstrap.min.js" crossorigin="anonymous"></script>
</head>

<body>
  <!-- Button trigger modal -->
  <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#followModal">
    跟团设置
  </button>

  <!-- Modal -->
  <div class="modal fade" id="followModal" tabindex="-1" aria-labelledby="followModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="followModalLabel">跟团设置</h5>
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <form>
            <div class="form-group row align-items-center">
              <label for="monster" class="col-sm-2 col-form-label">参团怪物</label>
              <div class="col-sm-10">
                <div class="form-check form-check-inline">
                  <input class="form-check-input" name="monster" type="checkbox" id="dk" value="1" checked>
                  <label class="form-check-label" for="dk">死骑</label>
                </div>
                <div class="form-check form-check-inline">
                  <input class="form-check-input" name="monster" type="checkbox" id="greenDragon" value="2" checked>
                  <label class="form-check-label" for="greenDragon">绿龙</label>
                </div>
                <div class="form-check form-check-inline">
                  <input class="form-check-input" name="monster" type="checkbox" id="redDragon" value="3" checked>
                  <label class="form-check-label" for="redDragon">红龙</label>
                </div>
                <div class="form-check form-check-inline">
                  <input class="form-check-input" name="monster" type="checkbox" id="goldDragon" value="4" checked>
                  <label class="form-check-label" for="goldDragon">金龙</label>
                </div>
              </div>
            </div>
            <div class="form-group row">
              <label for="monsterLv" class="col-sm-2 col-form-label">怪物等级</label>
              <div class="col-sm-10">
                <div class="form-row">
                  <div class="form-group col-md-3 form-check-inline">
                    <label for="dkLv" class="col-form-label">死骑</label>
                    <select id="dkLv" class="form-control col-md-8">
                      <option selected value="0">不限</option>
                      <option value="8">8</option>
                      <option value="7">7</option>
                      <option value="6">6</option>
                      <option value="5">5</option>
                      <option value="4">4</option>
                      <option value="3">3</option>
                      <option value="2">2</option>
                      <option value="1">1</option>

                    </select>
                  </div>
                  <div class="form-group col-md-3 form-check-inline">
                    <label for="greenLV" class="col-form-label">绿龙</label>
                    <select id="greenLV" class="form-control col-md-8">
                      <option selected value="0">不限</option>
                      <option value="3">3</option>
                      <option value="2">2</option>
                      <option value="1">1</option>
                    </select>
                  </div>
                  <div class="form-group col-md-3 form-check-inline">
                    <label for="redLv" class="col-form-label">红龙</label>
                    <select id="redLv" class="form-control col-md-8">
                      <option selected value="0">不限</option>
                      <option value="3">3</option>
                      <option value="2">2</option>
                      <option value="1">1</option>
                    </select>
                  </div>
                  <div class="form-group col-md-3 form-check-inline">
                    <label for="goldLv" class="col-form-label">金龙</label>
                    <select id="goldLv" class="form-control col-md-8">
                      <option selected value="0">不限</option>
                      <option value="3">3</option>
                      <option value="2">2</option>
                      <option value="1">1</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
            <div class="form-group row">
              <label for="monsterLv" class="col-sm-2 col-form-label">参团兵种</label>
              <div class="col-sm-10">
                <div class="form-row">
                  <select id="followType" class="form-control col-md-8">
                    <option selected value="0">跟随团长</option>
                    <option value="1">步兵</option>
                    <option value="2">弓兵</option>
                    <option value="3">骑兵</option>
                  </select>
                </div>
              </div>
            </div>
            <div class="form-group row">
              <label for="monsterLv" class="col-sm-2 col-form-label">参团数量</label>
              <div class="col-sm-10">
                <div class="form-row">
                  <input type="number" class="form-control" id="followNum" value="60000">
                </div>
              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
          <button type="button" class="btn btn-primary" id="saveFollow">保存</button>
        </div>
      </div>
    </div>
  </div>
</body>
<script>
  $("#saveFollow").click(function () {
    //选中怪物
    var mSelect = [];
    $('input[name="monster"]:checked').each(function () {
      mSelect.push($(this).val());
    });
    //跟随等级
    var greenLv = $("#greenLV ").val()
    var redLv = $("#redLv ").val()
    var goldLv = $("#goldLv ").val()
    //参团兵种
    var followType = $("#followType").val()
    //参团数量
    var followNum = $("#followNum ").val()
    var setting = [mSelect,[greenLv,redLv,goldLv],followType,followNum]
    //保存到本地
    localStorage.setItem("fSetting",JSON.stringify(setting))
    //获取
    console.log(JSON.parse(localStorage.getItem("fSetting")))
  })
</script>

</html>