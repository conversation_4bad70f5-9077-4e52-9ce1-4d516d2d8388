{% extends 'work_base.html' %}
{% block head %}
<script src="https://cdn.jsdelivr.net/npm/echarts@latest/dist/echarts.min.js"></script>
{{ super() }}
{% endblock %}
{% block body %}
<div class="container-fluid">
  <div style="width: 100%; height: 200px" />
  <div id="dragosoul" style="width: 100%; height: 576px"></div>
  <div id="main" style="width: 100%; height: 576px"></div>
  <div id="main2" style="width: 100%; height: 576px"></div>
</div>
{% endblock %}
{% block tail_work %}
<script>
    function sumArray(arr) {
        return arr.reduce((sum, num) => sum + num, 0);
    }

    function loadDragoSoul() {
        let datas = {{ dragoSouls|safe }};
        datas = datas.filter( item => item.amount > 0 );
        // 获取名称和数量数组
        const labels = datas.map(data => data.name);
        const amounts = datas.map(data => data.amount);

        var myChart = echarts.init(document.getElementById("dragosoul"));

        // 指定图表的配置项和数据
        var option = {
            title: {
                text: '龙魂表'
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: labels,
                axisTick: {
                    alignWithLabel: true
                }
            },
            yAxis: {
                type: 'value'
            },
            series: [{
                name: '数量',
                type: 'bar',
                barWidth: '60%',
                data: amounts
            }]
        };

        // 使用刚指定的配置项和数据显示图表
        myChart.setOption(option);
    }

    function loadCrystalMap() {
        let datas = {{ records|safe }};

        // 提取日期和数据标签
        const labels = Object.keys(datas);
        const datasets = [];
        var dataKeys = Object.keys(datas[labels[0]]);
        for (let label of labels) {
            dataKeysf = Object.keys(datas[label]);
            dataKeysf.forEach((key) => {
                if (!dataKeys.includes(key)) {
                    dataKeys.push(key);
                }
            });
        }
        var series = {};
        for (let index = 1; index < labels.length; index++) {
            let lastDatas = datas[labels[index - 1]];
            let curDatas = datas[labels[index]];
            dataKeys.forEach((key) => {
                let oldData = lastDatas[key] || 0;
                let curData = curDatas[key] || 0;
                if (series[key] == undefined) {
                    series[key] = {
                        name: key,
                        type: "line",
                        data: [],
                    };
                }
                if (oldData != undefined && curData != undefined) {
                    series[key].data.push(
                        Math.max(Math.min(curData - oldData, 88888), -1),
                    );
                } else {
                    series[key].data.push(0);
                }
            });
        }
        series = Object.keys(series).map((key) => series[key]);
        series = series.filter((item) => {
            let data = item.data;
            if (
                sumArray(data) < 10000
            ) {
                let index = dataKeys.indexOf(item.name);
                if (index !== -1) {
                    dataKeys.splice(index, 1); // 如果找到了，就删除位置在 index 的元素
                }
                return false;
            }
            return true;
        });
        var realLabels = Array.from(labels);
        realLabels.pop();

        // 基于准备好的dom，初始化echarts实例
        var myChart = echarts.init(document.getElementById("main"));
        // 指定图表的配置项和数据
        var option = {
            // title: {
            //     text: "Sample Line Chart",
            // },
            tooltip: {
                trigger: "axis",
            },
            legend: {
                data: dataKeys,
            },
            xAxis: {
                type: "category",
                boundaryGap: false,
                data: realLabels,
            },
            yAxis: {
                type: "value",
            },
            series: series,
        };

        // 使用刚指定的配置项和数据显示图表
        myChart.setOption(option);

        var series2 = {};
        for (let index = 1; index < labels.length; index++) {
            let lastDatas = datas[labels[0]];
            let curDatas = datas[labels[index]];
            dataKeys.forEach((key) => {
                let oldData = lastDatas[key] || 0;
                let curData = curDatas[key] || 0;
                if (series2[key] == undefined) {
                    series2[key] = {
                        name: key,
                        type: "line",
                        data: [0],
                    };
                }
                if (oldData != undefined && curData != undefined) {
                    series2[key].data.push(curData - oldData);
                } else {
                    series2[key].data.push(0);
                }
            });
        }
        series2 = Object.keys(series2).map((key) => series2[key]);

        // 基于准备好的dom，初始化echarts实例
        var myChart = echarts.init(document.getElementById("main2"));
        var realLabels2 = Array.from(labels);
        realLabels2.pop();

        // 指定图表的配置项和数据
        var option = {
            // title: {
            //     text: "Sample Line Chart",
            // },
            tooltip: {
                trigger: "axis",
            },
            legend: {
                data: dataKeys,
            },
            xAxis: {
                type: "category",
                boundaryGap: false,
                data: realLabels2,
            },
            yAxis: {
                type: "value",
            },
            series: series2,
        };

        // 使用刚指定的配置项和数据显示图表
        myChart.setOption(option);
    }

  loadDragoSoul();
  loadCrystalMap();
</script>
{% endblock %}
