{% if current_user.has_role('superuser') %}
    {% set fieldNames = ["怪物","资源","团怪","buff","城堡","水晶","精灵","龙矿"] %}
{% else %}
    {% set fieldNames = ["怪物","资源","团怪","buff","城堡","水晶","精灵"] %}
{% endif %}

<style>
.table td button {
    margin-right: 0.5rem;
}
.table td button:last-child {
    margin-right: 0;
}
</style>

<div class="container-fluid">
    <div class="mb-3">
        <button id="search" type="button" class="btn btn-success me-2">搜索</button>
        <button id="searchcvc" type="button" class="btn btn-success me-2">cvc搜索</button>
        <button id="searchcrystal" type="button" class="btn btn-info me-2">水晶</button>
        <button id="searchallcrystal" type="button" class="btn btn-primary me-2">全图水晶</button>
        {% if current_user.has_role('superuser') %}
            <button id="searchallcrystal2" type="button" class="btn btn-primary me-2">下图水晶</button>
        {% endif %}
    </div>

    <div class="search-params">
        <div class="input-group">
            <div class="input-group-prepend">
                <label class="input-group-text" for="searchpower">区域</label>
                <select class="custom-select" id="searchpower">
                    {% for i in range(1, 5) %}
                    <option value="{{i}}"{% if i == 4 %} selected{% endif %}>{{i}}</option>
                    {% endfor %}
                </select>
            </div>

            <div class="input-group-prepend">
                <label class="input-group-text" for="showpower">等级</label>
                <select class="custom-select" id="showpower">
                    {% for i in range(1, 8) %}
                    <option value="{{i}}"{% if i == 5 %} selected{% endif %}>{{i}}</option>
                    {% endfor %}
                </select>
            </div>

            <div class="input-group-prepend">
                <span class="input-group-text">w</span>
                <input type="number" id="locw" class="form-control" placeholder="区" value="{{locW or 20}}" />
            </div>
            <div class="input-group-prepend">
                <span class="input-group-text">x</span>
                <input type="number" id="locx" class="form-control" placeholder="x坐标" value="{{locX or 1024}}" />
            </div>
            <div class="input-group-prepend">
                <span class="input-group-text">y</span>
                <input type="number" id="locy" class="form-control" placeholder="y坐标" value="{{locY or 1024}}" />
            </div>
        </div>

        <div class="mt-2">
            <button id="noempty" type="button" class="btn btn-info">非空</button>
            <button id="notempty" type="button" class="btn btn-danger">有人</button>
            {% if current_user.has_role('superuser') %}
                <button id="publishhidden" type="button" class="btn btn-dark">分发地下</button>
            {% endif %}
        </div>
    </div>
</div>

<div class="search-results mt-3">
    <ul class="nav nav-tabs" id="myTab" role="tablist">
        {% for fieldName in fieldNames %}
        <li class="nav-item" role="presentation">
            <a class="nav-link{% if loop.index == 1 %} active{% endif %}"
                id="{{fieldName}}-tab"
                data-toggle="tab"
                href="#{{fieldName}}"
                role="tab"
                aria-controls="nav-{{fieldName}}"
                aria-selected="{% if loop.index == 1 %}true{% else %}false{% endif %}">{{fieldName}}</a>
        </li>
        {% endfor %}
    </ul>

    <div class="tab-content" id="myTabContent">
        {% for fieldName in fieldNames %}
        <div class="tab-pane fade{% if loop.index == 1 %} show active{% endif %}"
            id="{{fieldName}}"
            role="tabpanel"
            aria-labelledby="nav-{{fieldName}}-tab">
            {% if fieldName == "buff" %}
            <div class="mb-3 mt-3">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-primary me-2" data-filter="all">全部</button>
                    <button type="button" class="btn btn-outline-primary me-2" data-filter="attack">攻击</button>
                    <button type="button" class="btn btn-outline-primary me-2" data-filter="defender">捍卫者</button>
                    <button type="button" class="btn btn-outline-primary me-2" data-filter="stamina">体力</button>
                    <button type="button" class="btn btn-outline-primary me-2" data-filter="speed">速度</button>
                    <button type="button" class="btn btn-outline-primary me-2" data-filter="research">研究</button>
                    <button type="button" class="btn btn-outline-primary me-2" data-filter="building">建设</button>
                    <button type="button" class="btn btn-outline-primary me-2" data-filter="training">训练</button>
                </div>
            </div>
            {% endif %}
            <table class="table">
                <thead>
                    <tr>
                        <th scope="col">名称</th>
                        <th scope="col">等级</th>
                        <th scope="col">有效值</th>
                        <th scope="col">坐标</th>
                        <th scope="col">距离</th>
                        <th scope="col">地下</th>
                        <th scope="col">持有人</th>
                        <th scope="col">操作</th>
                        <th scope="col" hidden>id</th>
                    </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
        {% endfor %}
    </div>
</div>

<script>
const fieldNames = [{% for fieldName in fieldNames %}"{{fieldName}}"{% if not loop.last %}, {% endif %}{% endfor %}];

{% if current_user.has_role('superuser') %}
function isSelectUser() {
    const select = $("#workUser")[0];
    if (select.selectedIndex == 0) {
        toastr.error("请选择用户");
        return false;
    }
    return true;
}

function workUserInfo() {
    const select = $("#workUser")[0];
    return select.options[select.selectedIndex].dataset.info;
}
{% else %}
function isSelectUser() {
    return true;
}

function workUserInfo() {
    return null;
}
{% endif %}

const CONSTANTS = {
    MONSTER_CODES: [20200101,20200102,20200103,20700405,20700406,20700407],
    RESOURCE_CODES: [20100101,20100102,20100103,20100104,20700601,20700602,20700603,20700604],
    GROUP_MONSTER_CODES: [20200201,20200202,20200203,20200204,20700505,20700506],
    BUFF_CODES: [20500102,20500103,20500104],
    CASTLE_CODES: [20300101],
    CRYSTAL_CODES: [20100105],
    ELF_CODES: [20200104],
    DRAGON_CODES: [20100106],
    CODE_GROUPS: [
        [20200101,20200102,20200103,20700405,20700406,20700407],
        [20100101,20100102,20100103,20100104,20700601,20700602,20700603,20700604],
        [20200201,20200202,20200203,20200204,20700505,20700506],
        [20500102,20500103,20500104],
        [20300101],
        [20100105],
        [20200104],
        [20100106],
    ]
};

const Utils = {
    createActionButtons(type) {
        {% if not connect and not workUsers %}return '';{% endif %}
        let buttons = '';
        if (type === 0) {
            buttons += '<button type="button" class="btn btn-danger attack text-nowrap">攻击</button>' +
                '<button type="button" class="btn btn-success attackgroup text-nowrap">开团</button>';
        } else {
            buttons += '<button type="button" class="btn btn-primary collection text-nowrap">采集</button>';
        }
        buttons += '<button type="button" class="btn btn-info share text-nowrap">分享</button>';
        return buttons;
    },

    createRow(data, type, showLevel) {
        const {name: originalName, code, level, param, occupied, occupiedName, hidden, loc, distance, _id} = data;
        const value = param?.value || '';
        const {allianceTag = '', shield = 0} = occupied || {};
        let name = originalName;
        if (allianceTag && code === 20300101) {
            name = `${name}[${allianceTag}]`;
        }
        const shieldStr = shield === 1 ? '<span class="badge badge-danger">盾</span>' : '';
        const undergroundStr = hidden ? '<span class="badge badge-danger">地下</span>' : '';
        const displayOccupiedName = occupiedName ? `${occupiedName}[${allianceTag}]` : '';
        const hiddenClass = (level < showLevel && code <= 20200201) ? 'hidden' : '';
        let buttons = this.createActionButtons(type);
        {% if current_user.has_role('superuser') %}
        if (occupiedName && [20100105,20100106].includes(code)) {
            buttons += '<button type="button" class="btn btn-danger kick text-nowrap">滚</button>';
        }
        if ([20300101].includes(code)) {
            buttons += '<button type="button" class="btn btn-danger cvckick text-nowrap">黑</button>';
        }
        {% endif %}
        return `<tr data-info="${encodeURIComponent(JSON.stringify(data))}" ${hiddenClass}>
            <td>${name}${shieldStr}</td>
            <td>${level}</td>
            <td>${value}</td>
            <td>${loc[1]},${loc[2]}</td>
            <td>${distance}</td>
            <td>${undergroundStr}</td>
            <td>${displayOccupiedName}</td>
            <td style="white-space: nowrap;">${buttons}</td>
            <td hidden>${_id}</td>
        </tr>`;
    }
};

const TableManager = {
    createRows(datas) {
        const showpower = parseInt($("#showpower").val());
        {% for fieldName in fieldNames %}
        $("#{{fieldName}} tbody").empty();
        {% endfor %}
        let buffItems = [];
        for (const [code, items] of Object.entries(datas)) {
            const numericCode = parseInt(code);
            if (CONSTANTS.BUFF_CODES.includes(numericCode)) {
                buffItems = buffItems.concat(items);
            }
        }
        if (buffItems.length > 0) {
            buffItems.sort((a, b) => b.level - a.level);
            buffItems.forEach(item => {
                $("#buff tbody").append(
                    Utils.createRow(item, 3, 0)
                );
            });
        }
        for (const [code, items] of Object.entries(datas)) {
            const numericCode = parseInt(code);
            CONSTANTS.CODE_GROUPS.forEach((codes, index) => {
                if (codes.includes(numericCode) && fieldNames[index] !== 'buff') {
                    items.forEach(item => {
                        $("#" + fieldNames[index] + " tbody").append(
                            Utils.createRow(item, index % 2, index < 3 ? showpower : 0)
                        );
                    });
                }
            });
        }

        {% for fieldName in fieldNames %}
        $("#{{fieldName}}-tab").text(
            "{{fieldName}}(" + $("#{{fieldName}} tbody tr").length + ")"
        );
        {% endfor %}

        this.addEventListeners();
    },

    filterBuffRows(type) {
        const $tbody = $("#buff tbody");
        if (type === 'all') {
            $tbody.find('tr').show();
        } else {
            $tbody.find('tr').each(function() {
                const info = JSON.parse(decodeURIComponent($(this).data('info')));
                const name = info.name.toLowerCase();
                if (type === 'attack' && name.includes('攻击')) {
                    $(this).show();
                } else if (type === 'defender' && name.includes('捍卫者')) {
                    $(this).show();
                } else if (type === 'speed' && name.includes('速度')) {
                    $(this).show();
                } else if (type === 'stamina' && name.includes('体力')) {
                    $(this).show();
                } else if (type === 'research' && name.includes('研究')) {
                    $(this).show();
                } else if (type === 'building' && name.includes('建设')) {
                    $(this).show();
                } else if (type === 'training' && name.includes('训练')) {
                    $(this).show();
                } else {
                    $(this).hide();
                }
            });
        }
        $("#buff-tab").text(
            `buff(${$tbody.find('tr:visible').length})`
        );
    },

    addEventListeners() {
        $('.collection').click(handleCollection);
        $('.attack').click(handleAttack);
        $('.attackgroup').click(handleAttackGroup);
        $('.share').click(handleShare);
        {% if current_user.has_role('superuser') %}
        $('.kick').click(handleKick);
        $('.cvckick').click(handleCVCKick);
        {% endif %}
        // 添加buff过滤按钮的事件监听
        $('[data-filter]').click(function() {
            const filterType = $(this).data('filter');
            $(this).addClass('active').siblings().removeClass('active');
            TableManager.filterBuffRows(filterType);
        });
    },

    filterEmptyRows(shouldBeEmpty) {
        [{% for fieldName in fieldNames %}"{{fieldName}}"{% if not loop.last %}, {% endif %}{% endfor %}].forEach(fieldName => {
            const $tbody = $(`#${fieldName} tbody`);
            $tbody.find('tr').each(function() {
                const info = JSON.parse(decodeURIComponent($(this).data('info')));
                const hasOccupied = !!info.occupied;
                if (shouldBeEmpty ? hasOccupied : !hasOccupied) {
                    $(this).remove();
                }
            });
            $(`#${fieldName}-tab`).text(
                `${fieldName}(${$tbody.find('tr').length})`
            );
        });
    }
};

const ApiCaller = {
    async makeRequest(url, data = {}, btn = null, disabledReset = true) {
        if (btn) btn.prop('disabled', true);
        try {
            if (!navigator.onLine) {
                throw new Error('网络连接已断开');
            }
            const response = await $.ajax({
                url,
                type: 'POST',
                data,
                timeout: 300000,
                beforeSend: function(xhr) {
                    if (!url) {
                        throw new Error('无效的API地址');
                    }
                }
            });
            if (!response) {
                throw new Error('服务器无响应');
            }
            return response;
        } catch (error) {
            console.error('API调用错误:', error);
            toastr.error(error.responseJSON?.msg || error.message || '操作失败');
            return null;
        } finally {
            if (btn && disabledReset) {
                setTimeout(() => {
                    btn.prop('disabled', false);
                }, 500);
            }
        }
    }
};

function checkConnection() {
    if (!navigator.onLine) {
        toastr.error('网络连接已断开，请检查网络后重试');
        return false;
    }
    return true;
}

async function handleSearch(e, apiUrl, isCVC = false) {
    if (!checkConnection()) return;
    const btn = $(e.currentTarget);
    const searchpower = $("#searchpower").val();
    const locw = $("#locw").val();
    const locx = $("#locx").val();
    const locy = $("#locy").val();
    if (!locx || !locy) {
        toastr.error('请输入坐标');
        return;
    }
    const loc = `${locw},${locx},${locy}`;
    try {
        const response = await ApiCaller.makeRequest(apiUrl, {
            searchpower,
            loc,
            ...(isCVC ? {} : {isCVC: 0})
        }, btn);
        if (response?.code === 200) {
            TableManager.createRows(response.data);
            toastr.success("搜索成功");
        }
    } catch (error) {
        console.error('搜索错误:', error);
        toastr.error('搜索失败，请稍后重试');
    }
}

async function handleCollection(e) {
    if (!isSelectUser()) return;
    const btn = $(e.currentTarget);
    const info = JSON.parse(decodeURIComponent(btn.closest('tr').data('info')));
    const code = parseInt(info.code);
    let troopNum = 500;
    let isCrystal = 0;
    if (code > 20100104 && code < 20700601) {
        isCrystal = 1;
        if (code === 20100106) {troopNum = 1050 * info.level;}
    } else {
        troopNum = $("#troopnum").val();
    }
    const response = await ApiCaller.makeRequest('{{ url_for("work.collection_api") }}', {
        loc: JSON.stringify(info.loc),
        troopNum,
        isCrystal,
        workUser: workUserInfo()
    }, btn, false);
    if (response?.code === 200) {
        toastr.success(response.data === "成功" ? '采集成功' : response.data);
    } else {
        toastr.error('采集失败');
        btn.prop('disabled', true);
    }
}

async function handleAttack(e) {
    if (!isSelectUser()) return;
    const btn = $(e.currentTarget);
    const info = JSON.parse(decodeURIComponent(btn.closest('tr').data('info')));
    const code = parseInt(info.code);
    if (code === 20300101) {
        toastr.error('未实现');
        return;
    }
    if (code > 20200200 && code !== 20700405 && code !== 20700406 && code !== 20700407) {
        toastr.error('需要开团');
        return;
    }
    const response = await ApiCaller.makeRequest('{{ url_for("work.attackMonster_api") }}', {
        loc: JSON.stringify(info.loc),
        troopNum: $("#troopnum").val(),
        mainTroop: $('#troopGroup').val(),
        workUser: workUserInfo()
    }, btn);
    if (response?.code === 200) {
        toastr.success('攻击成功');
    } else {
        toastr.error(response?.msg || '攻击失败');
    }
}

async function handleAttackGroup(e) {
    if (!isSelectUser()) return;
    const btn = $(e.currentTarget);
    const info = JSON.parse(decodeURIComponent(btn.closest('tr').data('info')));
    const code = parseInt(info.code);
    const response = await ApiCaller.makeRequest('{{ url_for("work.startRally_api") }}', {
        troopNum: 20000,
        mainTroop: 3,
        loc: JSON.stringify(info.loc),
        marchType: code === 20300101 ? 2 : 5,
        workUser: workUserInfo()
    }, btn);
    if (response?.code === 200) {
        toastr.success('攻击成功');
    } else {
        toastr.error(response?.msg || '攻击失败');
    }
}

async function handleShare(e) {
    if (!isSelectUser()) return;
    const info = $(e.currentTarget).closest('tr').data('info');
    const response = await ApiCaller.makeRequest('{{ url_for("work.share_api") }}', {
        info: decodeURIComponent(info),
        workUser: workUserInfo()
    });
    if (response?.code === 200) {
        toastr.success('分享成功');
    } else {
        toastr.error('分享失败');
    }
}

{% if current_user.has_role('superuser') %}
async function handleKick(e) {
    const btn = $(e.currentTarget);
    const info = JSON.parse(decodeURIComponent(btn.closest('tr').data('info')));
    const response = await ApiCaller.makeRequest('{{ url_for("work.kick_api") }}', {
        moId: info.occupied.moId
    }, btn);
    if (response?.code === 200) {
        toastr.success('踢出成功');
    } else {
        toastr.error(response?.msg || '踢出失败');
    }
}

async function handleCVCKick(e) {
    const btn = $(e.currentTarget);
    const info = JSON.parse(decodeURIComponent(btn.closest('tr').data('info')));
    const occupied = info.occupied;
    const response = await ApiCaller.makeRequest('{{ url_for("work.cvckick_api") }}', {
        userId: occupied.id,
        foId: info._id,
        name: occupied.name,
        worldId: occupied.worldId
    }, btn);
    if (response?.code === 200) {
        toastr.success(`黑名单添加成功:${occupied.name}`);
    } else {
        toastr.error(response?.msg || '添加失败');
    }
}

async function handlePublishHidden(e) {
    const btn = $(e.currentTarget);
    const tbody = $("#水晶 tbody");
    const datas = [];
    tbody.find('tr').each(function() {
        const info = JSON.parse(decodeURIComponent($(this).data('info')));
        if (!info.occupied) {
            datas.push({level: info.level, loc: info.loc});
        }
    });
    if (datas.length === 0) {
        toastr.error("没有地下数据");
        return;
    }
    const response = await ApiCaller.makeRequest('{{ url_for("work.insertHidden_api") }}', {
        mines: JSON.stringify(datas)
    }, btn);
    if (response?.code === 200) {
        toastr.success("分发成功");
    } else {
        toastr.error(response?.msg || '分发失败');
    }
}
{% endif %}

$(document).ready(() => {
    window.addEventListener('online', () => {
        toastr.success('网络已连接');
    });
    window.addEventListener('offline', () => {
        toastr.error('网络已断开');
    });
    $("#search").click(e => handleSearch(e, '{{ url_for("work.getfields_api") }}'));
    $("#searchcvc").click(e => handleSearch(e, '{{ url_for("work.getfields_cvc_block_api") }}', true));
    $("#searchcrystal").click(e => handleSearch(e, '{{ url_for("work.getcrystal_api") }}'));
    $("#searchallcrystal").click(e => handleSearch(e, '{{ url_for("work.getcrystalall_api") }}'));
    {% if current_user.has_role('superuser') %}
    $("#searchallcrystal2").click(e => handleSearch(e, '{{ url_for("work.getcrystalall2_api") }}'));
    $("#publishhidden").click(handlePublishHidden);
    {% endif %}
    $("#noempty").click(() => TableManager.filterEmptyRows(true));
    $("#notempty").click(() => TableManager.filterEmptyRows(false));
});
</script>
