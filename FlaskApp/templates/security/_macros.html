{% macro render_field_with_errors(field) %}

<div class="form-group">
    {{ field.label }} {{ field(class_='form-control', **kwargs)|safe }}
    {% if field.errors %}
    <ul>
        {% for error in field.errors %}
        <li>{{ error }}</li>
        {% endfor %}
    </ul>
    {% endif %}
</div>
{% endmacro %}

{% macro render_field(field) %}
<p>{{ field(class_='form-control', **kwargs)|safe }}</p>
{% endmacro %}

{% macro render_checkbox_field(field) -%}
<div class="form-group">
    <div class="checkbox">
        <label>
            {{ field(type='checkbox', **kwargs) }} {{ field.label }}
        </label>
    </div>
</div>
{%- endmacro %}