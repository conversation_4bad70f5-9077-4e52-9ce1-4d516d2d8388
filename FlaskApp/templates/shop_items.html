<div class="row">
    <div class="input-group col-md-2">
        <div class="btn-group" role="group">
            <button id="caravan" type="button" class="btn btn-primary">银河战舰</button>
        </div>
    </div>
    <div class="input-group col-md-2">
        <!-- <div class="input-group-prepend">
            <label class="input-group-text" for="inputGroupSelect01">次数</label>
        </div> -->
        {% if current_user.has_role('superuser') %}
        <input type="number" id="buycount" class="form-control" placeholder="次数" value="100">
        {% else %}
            {% if current_user.has_role('vipuser') %}
            <select class="custom-select" id="buycount">
                <option value="50" selected>50</option>
                <option value="100">100</option>
                <option value="200">200</option>
                <option value="300">300</option>
            </select>
            {% else %}
                <input type="number" id="buycount" class="form-control" value="50" disabled>

            {% endif %}
        {% endif %}
    </div>
</div>
<br />
<table class="table">
    <thead>
        <tr>
            <th scope="col">名称</th>
            <th scope="col">消耗</th>
            <th scope="col">操作</th>
        </tr>
    </thead>
    <tbody id="caravan-tbody">
    </tbody>
</table>
<script>
    function createRow(data) {
        
        var row = $('<tr data-info=\''+JSON.stringify(data)+'\'></tr>');
        
        th = $('<th scope="col">' + data.itemTitle + '</th>');
        if (data.isCrystalItem) {
            th.prop('class', 'text-danger');
        };
        row.append(th);
        row.append($('<th scope="col">' + data.cost + '</th>'));
        row.append($('<td><button type="button" class="btn btn-danger caravan-buy">预订</button></td>'));
        row.append($('<th hidden>' + data.caravanItemId + '</th>'));
        return row[0];
    }

    function createRows(datas) {
        tb = $('#caravan-tbody')[0];
        $(tb).empty()
        for (var i = 0; i < datas.length; i++) {
            tb.append(createRow(datas[i]));
        }

        addClickEvent();        
    }

    function addClickEvent() {
        buys = $('.caravan-buy');
        buys.click(caravanBuy);
    }

    function caravanBuy(arg1) {
        var data = $(arg1.target).parent().parent().data('info');
        var count = $('#buycount').val();
        console.log(this)
        btn = $(this);
        $.ajax({
            url: '{{ url_for("caravan.caravan_buy_api") }}',
            type: 'POST',
            data: {
                caravanItemId: data.caravanItemId,
                count:count,
                workUser: isSelectUser() ? workUserInfo() : null,
            },
            beforeSend: function () {
                $(".caravan-buy").prop('disabled', true);
                btn.prop("hidden", true);
                console.log(btn);
            },
            success: function (data) {
                $(".caravan-buy").prop('disabled', false);
                if (data.code == 200) {
                    if (data.count == 0) {
                        alert("token异常，请重新登录");
                        setTimeout(() => {
                            window.location.reload();
                        }, 2000);
                    }
                    toastr.success("购买成功"+data.count);
                } else {
                    btn.prop("hidden", false);
                    toastr.error("购买异常"+data.msg);
                }
            },
            error: function (data) {
                btn.prop("hidden", true);
                btn.prop("hidden", false);
                toastr.error(data.msg);
            },
        });
    }

    window.onload = function() {
        $("#caravan").click((arg1,arg2) => {
            btn = $("#caravan");
            $.ajax({
            url: '{{ url_for("caravan.caravan_api") }}',
            type: 'POST',
            data: {
                workUser: isSelectUser() ? workUserInfo() : null,
            },
            beforeSend: function () {
                btn.prop("disabled", true);
            },
            complete: function () {
                btn.prop("disabled", false);
            },
            success: function (data) {
                if (data.code == 200) {
                    toastr.success('刷新成功');
                    createRows(data.data);
                } else {
                    toastr.error(data.msg);
                }
            },
            error: function (data) {
                    toastr.error(data.msg);
                }
            });
        });
    }
</script>