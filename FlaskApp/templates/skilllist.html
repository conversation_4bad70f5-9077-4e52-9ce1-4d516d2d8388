{% extends 'work_base.html' %}
{% import 'admin/lib.html' as lib with context %}
{% block userinfo %}

{% endblock %}

{% block actions %}
<div class="row">
    <div class="input-group col-md-5">
        <div class="btn-group col-md-2" role="group">
            <button id="skilllist" type="button" class="btn btn-primary text-nowrap">技能列表</button>
        </div>
        <div class="col-md-3">
            {% if current_user.has_role('superuser') %}
            <input type="number" id="buycount" class="form-control" placeholder="次数" value="15">
            {% else %}
            <input type="number" id="buycount" class="form-control" placeholder="次数" value="15" hidden>        
        <!-- <div class="col-md-2"> -->
            {% endif %}
        </div>
    </div>
</div>
<br />
<table class="table">
    <thead>
        <tr>
            <th scope="col">名称</th>
            <th scope="col">操作</th>
        </tr>
    </thead>
    <tbody id="skilllist-tbody">
    </tbody>
</table>
{% endblock %}
{% block tail_work %}
<script>
    function createRow(data) {
        
        var row = $('<tr data-info=\''+JSON.stringify(data)+'\'></tr>');
        row.append($('<th scope="col">' + data.codeName + '</th>'));
        row.append($('<td><button type="button" class="btn btn-danger skilllist-use">使用</button></td>'));
        return row[0];
    }

    function createRows(datas) {
        tb = $('#skilllist-tbody')[0];
        $(tb).empty()
        for (var i = 0; i < datas.length; i++) {
            tb.append(createRow(datas[i]));
        }

        addClickEvent();        
    }

    function addClickEvent() {
        buys = $('.skilllist-use');
        buys.click(skillUse);
    }

    function skillUse(arg1) {
        var data = $(arg1.target).parent().parent().data('info');
        btn = $(this);
        var count = $('#buycount').val();
        $.ajax({
            url: '{{ url_for("skilllist.skill_use_api") }}',
            type: 'POST',
            data: {
                code: data.code,
                count: count,
            },
            beforeSend: function () {
                btn.prop("disabled", true);
                btn.prop("hidden", true);
                console.log(btn);
            },
            success: function (data) {
                if (data.code == 200) {
                    toastr.success("使用成功"+data.count);
                } else {
                    toastr.error("使用异常"+data.msg);
                }
            },
            error: function (data) {
                toastr.error(data.msg);
            },
        });
    }

    $("#skilllist").click((arg1,arg2) => {
        btn = $("#skilllist");
        $.ajax({
            url: '{{ url_for("skilllist.skilllist_api") }}',
            type: 'POST',
            data: {

            },
            beforeSend: function () {
                btn.prop("disabled", true);
            },
            complete: function () {
                btn.prop("disabled", false);
            },
            success: function (data) {
                if (data.code == 200) {
                    toastr.success('刷新成功');
                    createRows(data.data);
                } else {
                    toastr.error(data.msg);
                }
            },
            error: function (data) {
                toastr.error(data.msg);
            }
        });

    });
</script>
{% endblock %}
