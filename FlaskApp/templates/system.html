{% extends 'work_base.html' %}
{% block body %}
<div class="container-fluid">
    <div>
    <p>Start Time: {{ startTime }}</p>
    <p>version: {{ gameVersion }}</p>
    <p>nodKey: {{ nodKey }}</p>
    <p>B64ApiList: {{ B64ApiList }}</p>
    <p>beat: {{ active }}</p>
    </div>
    <div>
        <!-- 重启Web按钮 -->
        <button type="button" class="btn btn-primary" id="rebootweb">
        <i class="fa fa-refresh"></i> 重启Web
        </button>

        <!-- 重启Bot按钮 -->
        <button type="button" class="btn btn-danger" id="rebootbot">
        <i class="fa fa-power-off"></i> 重启Bot
        </button>

        <!-- 刷新加密按钮 -->
        <button type="button" class="btn btn-success" id="refreshnodkey">
        <i class="fa fa-lock"></i> 刷新加密
        </button>
    </div>
</div>
{% endblock %}
{% block tail_work %}
<script>
    $("#rebootweb").click(function () {
        $.ajax({
            url: "{{ url_for('system.rebootweb_api') }}",
            type: "POST",
            data: {
            },
            success: function (data) {
            },
            complete: function () {
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            },
        });
    });

    $("#rebootbot").click(function () {
        $.ajax({
            url: "{{ url_for('system.rebootbot_api') }}",
            type: "POST",
            data: {
            },
            success: function (data) {
                if (data.code == 200) {
                    toastr.success("等待刷新");
                } else {
                    toastr.error(data.msg);
                }
            }
        });
    });

    $("#refreshnodkey").click(function () {
        $.ajax({
            url: "{{ url_for('system.refreshnodkey_api') }}",
            type: "POST",
            data: {
            },
            success: function (data) {
                if (data.code == 200) {
                    toastr.success("等待刷新");
                } else {
                    toastr.error(data.msg);
                }
            }
        });
    });
</script>
{% endblock %}