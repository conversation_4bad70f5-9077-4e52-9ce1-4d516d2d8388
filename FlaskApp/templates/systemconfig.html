{% extends 'work_base.html' %}
{% block body %}
<div class="p-4">
    <div class="row g-4">
        <!-- 左侧控制面板 -->
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">控制面板</h5>
                </div>
                <div class="card-body">
                    <form>
                        <div class="mb-4">
                            <label class="form-label text-muted">世界ID</label>
                            <select class="form-select form-select-lg" id="number">
                            </select>
                        </div>
                        <div class="mb-4">
                            <label class="form-label text-muted">间隔值</label>
                            <select class="form-select form-select-lg" id="secondNumber">
                            </select>
                        </div>
                        <div class="d-flex" style="gap: 1rem;">
                            <button type="button" class="btn btn-primary btn-lg flex-grow-1" id="addBtn">增加</button>
                            <button type="button" class="btn btn-success btn-lg flex-grow-1" id="setBtn">设置</button>
                            <button type="button" class="btn btn-outline-danger btn-lg flex-grow-1" id="deleteBtn">删除</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 右侧数据表格 -->
        <div class="col-md-9">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">间隔配置列表</h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="bg-light">
                                <tr>
                                    <th class="border-0 px-4">世界ID</th>
                                    <th class="border-0 px-4">间隔值</th>
                                </tr>
                            </thead>
                            <tbody id="interval-tbody">
                            {% for worldId in worldIdsStr %}
                            <tr>
                                <td class="px-4">{{ worldId }}</td>
                                <td class="px-4">{{ intervals.get(worldId,0) }}</td>
                            </tr>
                            {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block tail_work %}
<style>
    .card {
        border-radius: 8px;
    }
    .form-select {
        border-radius: 6px;
        border-color: #e0e0e0;
    }
    .form-select:focus {
        border-color: #3b82f6;
        box-shadow: 0 0 0 0.25rem rgba(59, 130, 246, 0.1);
    }
    .btn {
        border-radius: 6px;
        padding: 0.75rem 1.5rem;
    }
    .btn-lg {
        font-size: 1rem;
    }
    .table {
        margin-bottom: 0;
    }
    .table th {
        font-weight: 500;
        color: #666;
    }
    .table td {
        vertical-align: middle;
    }
    .table-responsive {
        max-height: calc(100vh - 250px);
        overflow-y: auto;
    }
    .table thead th {
        position: sticky;
        top: 0;
        background: #f8f9fa;
        z-index: 1;
    }
</style>

<script>
// 生成第一个选择器的选项
    var select = document.getElementById("number");
    for (var i = 1; i <= 70; i++) {
      var option = document.createElement("option");
      option.value = i;
      option.text = i;
      select.appendChild(option);
    }

// 生成第二个选择器的选项（0-9）
    var secondSelect = document.getElementById("secondNumber");
    for (var i = 0; i <= 9; i++) {
      var option = document.createElement("option");
      option.value = i;
      option.text = i;
      secondSelect.appendChild(option);
    }
    
    // 添加加载动画函数
    function showLoading(btn) {
        const originalText = btn.html();
        btn.prop("disabled", true)
           .html('<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>处理中...');
        return originalText;
    }

    function hideLoading(btn, originalText) {
        btn.prop("disabled", false).html(originalText);
    }

    // 统一的Ajax处理函数
    function handleAjaxRequest(url, data, btn, successMessage) {
        const originalText = showLoading(btn);
        $.ajax({
            url: url,
            type: 'POST',
            data: data,
            success: function (data) {
                if (data.code == 200) {
                    toastr.options = {
                        "positionClass": "toast-top-center",
                        "timeOut": "1500"
                    };
                    toastr.success(successMessage);
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                } else {
                    toastr.error(data.msg);
                }
            },
            error: function (data) {
                toastr.error('操作失败，请重试');
            },
            complete: function () {
                hideLoading(btn, originalText);
            }
        });
    }

    // 事件处理程序
    $("#addBtn").click((e) => {
        const btn = $(e.target);
        handleAjaxRequest(
            '{{ url_for("systemconfig.add_api") }}',
            { "worldId": $("#number").val() },
            btn,
            '添加成功'
        );
    });

    $("#deleteBtn").click((e) => {
        const btn = $(e.target);
        if (confirm('确定要删除吗？')) {
            handleAjaxRequest(
                '{{ url_for("systemconfig.delete_api") }}',
                { "worldId": $("#number").val() },
                btn,
                '删除成功'
            );
        }
    });

    $("#setBtn").click((e) => {
        const btn = $(e.target);
        handleAjaxRequest(
            '{{ url_for("systemconfig.set_api") }}',
            {
                "worldId": $("#number").val(),
                "value": $("#secondNumber").val()
            },
            btn,
            '设置成功'
        );
    });

    // 初始化 toastr 配置
    toastr.options = {
        "closeButton": true,
        "progressBar": true,
        "positionClass": "toast-top-center",
        "timeOut": "1500"
    };
</script>
{% endblock %}