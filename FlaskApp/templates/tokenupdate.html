{% extends 'work_base.html' %} {% block body %}
<div class="container d-flex justify-content-center align-items-center vh-100">
  <div class="row align-items-center">
    <h3>Token</h3>
    <textarea
      class="form-control mb-3"
      style="min-width: 280px; min-height: 300px"
      placeholder="请输入游戏token"
    ></textarea>
    <div class="mt-3">
      <button class="btn btn-primary mr-2" id="submit">提交</button>
      <button class="btn btn-secondary" id="query">查询</button>
      <p></p>
    </div>
  </div>
</div>
{{ lib.add_modal_window() }} {% endblock %} {% block tail_work %}
<script>
  const textarea = document.querySelector("textarea");
  const submitButton = document.getElementById("submit");

  const queryButton = document.getElementById("query");

  submitButton.addEventListener("click", (arg1) => {
    let btn = $(arg1.target);
    const tokenValue = textarea.value;
    $.ajax({
      url: '{{ url_for("tokenupdate.update_token_api") }}',
      type: "POST",
      data: {
        token: tokenValue,
      },
      beforeSend: function () {
        btn.prop("disabled", true);
      },
      complete: function () {
        btn.prop("disabled", false);
      },
      success: function (data) {
        if (data.code == 200) {
          toastr.success("修改成功");
        } else {
          toastr.error(data.msg);
        }
      },
      error: function (data) {
        toastr.error(data.msg);
      },
    });
    console.log("提交按钮点击，Token 值为:", tokenValue);
  });

  queryButton.addEventListener("click", (arg1) => {
    let btn = $(arg1.target);
    const tokenValue = textarea.value;
    $.ajax({
      url: '{{ url_for("tokenupdate.query_token_api") }}',
      type: "POST",
      data: {
        token: tokenValue,
      },
      beforeSend: function () {
        btn.prop("disabled", true);
      },
      complete: function () {
        btn.prop("disabled", false);
      },
      success: function (data) {
        if (data.code == 200) {
            const logsDiv = createLogs(data.logs);
            $("#fa_modal_window .modal-content").empty();
            $("#fa_modal_window .modal-content").append(logsDiv);
            $("#fa_modal_window").modal("show");
        } else {
          toastr.error(data.msg);
        }
      },
      error: function (data) {
        toastr.error(data.msg);
      },
    });
    console.log("查询按钮点击，Token 值为:", tokenValue);
  });

  createLogs = (logs) => {
    const logsDiv = document.createElement("ul");
    logsDiv.classList.add("list-group");

    for (let i = 0; i < logs.length; i++) {
      const log = logs[i];
      const logDiv = document.createElement("li");
      logDiv.classList.add("list-group-item");
      const codeDiv = document.createElement("code");
      codeDiv.classList.add("overflow-auto");
      codeDiv.innerText = log;
      logDiv.appendChild(codeDiv);
      logsDiv.appendChild(logDiv);
    }
    return logsDiv;
  };
</script>

{% endblock %}
