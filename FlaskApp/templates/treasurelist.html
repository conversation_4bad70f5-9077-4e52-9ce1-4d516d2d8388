{% extends 'work_base.html' %}
{% import 'admin/lib.html' as lib with context %}
{% block userinfo %}

{% endblock %}

{% block actions %}
<div class="row">
    <div class="input-group col-md-2">
        <div class="btn-group" role="group">
            <button id="treasurelist" type="button" class="btn btn-primary">装备列表</button>
        </div>
    </div>
</div>
<br />
<table class="table">
    <thead>
        <tr>
            <th scope="col">名称</th>
            <th scope="col">升级槽</th>
        </tr>
    </thead>
    <tbody id="treasurelist-tbody">
    </tbody>
</table>
{% endblock %}
{% block tail_work %}
<script>
    function createRow(data) {
        var skillLevel = data.skillLevel;
        var code = data.code;
        var amount = data.amount;
        let maxCount = Math.min(parseInt(code % 10000 / 1000), 5);
        var row = $('<tr data-info=\''+JSON.stringify(data)+'\'></tr>');
        row.append($(`<th scope="col">${data.name} (${data.piece}) </th>`));
        td = $('<td></td>');
        if (amount == 0) {
            td.append($(`<button class="btn btn-primary treasureOpen">合成</button>`));
        }else {
            for(var i = 0;i < maxCount; i++ ) {
                currentLevel = skillLevel[i];
                enable = currentLevel == 5 ? 'disabled' : '';
                td.append($(`<button type="button" class="btn btn-success treasureUpgrade" value="${i}" ${enable}>${currentLevel}</button>`));

            }
        }
        var equipped = data.equipped;
        if (!equipped) {
            td.append($(`<button class="btn btn-primary treasureEquip">装备</button>`));
        }
        row.append(td);
        
        return row[0];
    }

    function createRows(datas) {
        tb = $('#treasurelist-tbody')[0];
        $(tb).empty()
        for (var i = 0; i < datas.length; i++) {
            tb.append(createRow(datas[i]));
        }

        addClickEvent();        
    }

    function addClickEvent() {
        treasureUpgrades = $('.treasureUpgrade');
        treasureUpgrades.click(treasureUpgrade);
        treasureOpens = $('.treasureOpen');
        treasureOpens.click(treasureOpen);
        treasureEquips = $('.treasureEquip');
        treasureEquips.click(treasureEquip);
    }

    function treasureOpen(arg1) {
        var data = $(arg1.target).parent().parent().data('info');
        index = $(arg1.target).val();
        btn = $(this);
        $.ajax({
            url: '{{ url_for("treasurelist.treasure_generate_api") }}',
            type: 'POST',
            data: {
                code: data.code,
                workUser: isSelectUser() ? workUserInfo() : null,
            },
            beforeSend: function () {
                btn.prop("disabled", true);
                console.log(btn);
            },
            success: function (data) {
                if (data.code == 200) {
                    toastr.success("装备成功");
                } else {
                    toastr.error("装备失败");
                }
            },
            error: function (data) {
                toastr.error(data.msg);
            },
        });
    }

    function treasureEquip(arg1) {
        var data = $(arg1.target).parent().parent().data('info');
        index = $(arg1.target).val();
        btn = $(this);
        $.ajax({
            url: '{{ url_for("treasurelist.treasure_equip_api") }}',
            type: 'POST',
            data: {
                treasureId: data._id,
                workUser: isSelectUser() ? workUserInfo() : null,
            },
            beforeSend: function () {
                btn.prop("disabled", true);
                console.log(btn);
            },
            success: function (data) {
                if (data.code == 200) {
                    toastr.success("装备成功");
                } else {
                    toastr.error("装备失败");
                }
            },
            error: function (data) {
                toastr.error(data.msg);
            },
        });
    }
    
    function treasureUpgrade(arg1) {
        var data = $(arg1.target).parent().parent().data('info');
        index = $(arg1.target).val();
        btn = $(this);
        $.ajax({
            url: '{{ url_for("treasurelist.treasure_upgrade_api") }}',
            type: 'POST',
            data: {
                treasureId: data._id,
                index: index,
                workUser: isSelectUser() ? workUserInfo() : null,
            },
            beforeSend: function () {
                btn.prop("disabled", true);
                console.log(btn);
            },
            success: function (data) {
                if (data.code == 200) {
                    toastr.success("升级成功"+data.count);
                } else {
                    toastr.error("升级异常"+data.msg);
                }
            },
            error: function (data) {
                toastr.error(data.msg);
            },
        });
    }

    $("#treasurelist").click((arg1,arg2) => {
        btn = $("#treasurelist");
        $.ajax({
            url: '{{ url_for("treasurelist.treasurelist_api") }}',
            type: 'POST',
            data: {
                workUser: isSelectUser() ? workUserInfo() : null,
            },
            beforeSend: function () {
                btn.prop("disabled", true);
            },
            complete: function () {
                btn.prop("disabled", false);
            },
            success: function (data) {
                if (data.code == 200) {
                    toastr.success('刷新成功');
                    createRows(data.data);
                } else {
                    toastr.error(data.msg);
                }
            },
            error: function (data) {
                toastr.error(data.msg);
            }
        });

    });
</script>
{% endblock %}
