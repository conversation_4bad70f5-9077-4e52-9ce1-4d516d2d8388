{% extends 'work_base.html' %}
{% import 'admin/lib.html' as lib with context %}
{% block userinfo %}

{% endblock %}

{% block actions %}
<div class="row">
    <div class="input-group col-md-2">
        <div class="btn-group" role="group">
            <button id="viplist" type="button" class="btn btn-primary">高端客户</button>
        </div>
    </div>
    <div class="input-group col-md-2">
        <!-- <div class="input-group-prepend">
            <label class="input-group-text" for="inputGroupSelect01">次数</label>
        </div> -->
        <input type="number" id="buycount" class="form-control" placeholder="次数" value="10">
    </div>
</div>
<br />
<table class="table">
    <thead>
        <tr>
            <th scope="col">名称</th>
            <th scope="col">操作</th>
        </tr>
    </thead>
    <tbody id="viplist-tbody">
    </tbody>
</table>
{% endblock %}
{% block tail_work %}
<script>
    function createRow(data) {
        
        var row = $('<tr data-info=\''+JSON.stringify(data)+'\'></tr>');
        row.append($('<th scope="col">' + data.itemTitle + '</th>'));
        row.append($('<td><button type="button" class="btn btn-danger viplist-buy">预约</button></td>'));
        return row[0];
    }

    function createRows(datas) {
        tb = $('#viplist-tbody')[0];
        $(tb).empty()
        for (var i = 0; i < datas.length; i++) {
            tb.append(createRow(datas[i]));
        }

        addClickEvent();        
    }

    function addClickEvent() {
        buys = $('.viplist-buy');
        buys.click(viplistBuy);
    }

    function viplistBuy(arg1) {
        var data = $(arg1.target).parent().parent().data('info');
        var count = $('#buycount').val();
        console.log(this)
        btn = $(this);
        $.ajax({
            url: '{{ url_for("viplist.viplist_buy_api") }}',
            type: 'POST',
            data: {
                code: data.code,
                amount: data.numRemain,
                count:count,
            },
            beforeSend: function () {
                btn.prop("disabled", true);
                btn.prop("hidden", true);
                console.log(btn);
            },
            success: function (data) {
                if (data.code == 200) {
                    toastr.success(data.msg);
                } else {
                    toastr.error("异常"+data.msg);
                }
            },
            error: function (data) {
                toastr.error(data.msg);
            },
        });
    }

    $("#viplist").click((arg1,arg2) => {
        btn = $("#viplist");
        $.ajax({
            url: '{{ url_for("viplist.viplist_api") }}',
            type: 'POST',
            data: {

            },
            beforeSend: function () {
                btn.prop("disabled", true);
            },
            complete: function () {
                btn.prop("disabled", false);
            },
            success: function (data) {
                if (data.code == 200) {
                    toastr.success('刷新成功');
                    createRows(data.data);
                } else {
                    toastr.error(data.msg);
                }
            },
            error: function (data) {
                toastr.error(data.msg);
            }
        });

    });
</script>
{% endblock %}
