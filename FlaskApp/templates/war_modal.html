<!-- Modal -->
<div class="modal fade" id="warModal" tabindex="-1" aria-labelledby="warModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="warModalLabel">战争设置</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <form>
          <div class="form-group row align-items-center">
            <label for="monster" class="col-sm-2 col-form-label">团长名字</label>
            <div class="col-sm-6">
              <div class="form-check form-check-inline">
                <div class="input-group-prepend">
                  <div class="input-group-text"></div>
                  <input type="string" id="war_name" class="form-control" placeholder="名字" value="不限">
                </div>
                
              </div>

            </div>
          </div>
          <!-- <div class="form-group row">
            <label for="monsterLv" class="col-sm-2 col-form-label">驻守兵种</label>
            <div class="col-sm-10">
              <div class="form-row">
                <div class="form-group col-md-3 form-check-inline">
                  <select class="form-control" id="warTroopGroup">
                    <option value="1">步兵</option>
                    <option value="2" selected>弓兵</option>
                    <option value="3">骑兵</option>
                  </select>
                </div>
              </div>
            </div>
          </div> -->

          <div class="form-group row">
            <label for="monsterLv" class="col-sm-2 col-form-label">跟团数量</label>
            <div class="col-sm-4">
              <div class="form-row">
                <input type="number" class="form-control" id="warNum" value="300000">
              </div>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
        <button type="button" class="btn btn-primary" id="savewar">保存</button>
      </div>
    </div>
  </div>
  <script>
    window.onload = function (arg1) {
      console.log(arg1);
      warInfo = JSON.parse(localStorage.getItem("warInfo"))
      if (warInfo == undefined) {
        return
      }
      console.log(warInfo);
      $("#war_name").val(warInfo.leagueKingdomName);
      // $("#warTroopGroup").val(warInfo.troopGroup);
      $("#warNum").val(warInfo.num);
    }

    $("#savewar").click(function () {
      var war_name = $('#war_name').val();
      // var troopGroup = $('#warTroopGroup').val();
      var num = $('#warNum').val();
      if (war_name == "") {
        alert("请输入团长名字");
        return
      }

      if (num == undefined) {
        alert("请输入驻守数量");
        return
      }

      var warInfo = {
        leagueKingdomName: war_name,
        // troopGroup: $('#warTroopGroup').val(),
        num: num,
      }

      //保存到本地
      localStorage.setItem("warInfo", JSON.stringify(warInfo))
      //获取
      console.log(JSON.parse(localStorage.getItem("warInfo")))
      $("#warModal").modal("hide")
    });

    $("#autowar").click((arg1) => {
      warInfo = localStorage.getItem("warInfo");
      if (warInfo == undefined) {
        alert("请先设置战争信息");
        return;
      }

      btn = $(this);
      $.ajax({
        url: '{{ url_for("work.autoJoinWarfareBattle_api") }}',
        type: 'POST',
        data: {
          warInfo: warInfo,
        },
        beforeSend: function () {
          btn.prop("disabled", true);
        },
        success: function (data) {
          $("#autowar-load").hide();
          $("#autowar-text").show();
          if (data.code == 200) {
            setTimeout(() => {
              window.location.reload();
            }, 3000);
          } else {
            btn.prop("disabled", false);
            toastr.error(data.msg);
          }
        },
        error: function (data) {
          btn.prop("disabled", false);
          toastr.error(data.msg);
        }
      });
    });
  </script>
</div>

{% macro warButton() -%}
<div class="btn-group" role="group">
  <button id="autowar" type="button" class="btn btn-primary">
    <span id="autowar-text">开启战争跟团</span>
  </button>
  <button id="autowar-set" type="button" class="btn btn-secondary" data-toggle="modal"
    data-target="#warModal">战争设置</button>
</div>
{%- endmacro %}