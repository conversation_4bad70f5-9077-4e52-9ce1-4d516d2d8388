<!-- Modal -->
<div class="modal fade" id="harassmentModal" tabindex="-1" aria-labelledby="harassmentModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="harassmentModalLabel">骚扰设置</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <form>
          <div class="form-group row align-items-center">
            <label for="monster" class="col-sm-2 col-form-label">目标坐标</label>
            <div class="col-sm-6">
              <div class="form-check form-check-inline">
                <div class="input-group-prepend">
                  <div class="input-group-text">x</div>
                  <input type="number" id="harassment_locx" class="form-control" placeholder="x坐标" aria-label="x"
                    aria-describedby="x">
                </div>
                <div class="input-group-prepend">
                  <div class="input-group-text">y</div>
                  <input type="number" id="harassment_locy" class="form-control" placeholder="y坐标" aria-label="y"
                    aria-describedby="y">
                </div>
              </div>
            </div>
          </div>
          <!-- <div class="form-group row">
            <label for="monsterLv" class="col-sm-2 col-form-label">驻守兵种</label>
            <div class="col-sm-10">
              <div class="form-row">
                <div class="form-group col-md-3 form-check-inline">
                  <select class="form-control" id="harassmentTroopGroup">
                    <option value="1">步兵</option>
                    <option value="2" selected>弓兵</option>
                    <option value="3">骑兵</option>
                  </select>
                </div>
              </div>
            </div>
          </div> -->
<!-- 
          <div class="form-group row">
            <label for="monsterLv" class="col-sm-2 col-form-label">驻守数量</label>
            <div class="col-sm-4">
              <div class="form-row">
                <input type="number" class="form-control" id="harassmentNum" value="300000">
              </div>
            </div>
          </div> -->
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
        <button type="button" class="btn btn-primary" id="saveharassment">保存</button>
      </div>
    </div>
  </div>
  <script>
    window.onload = function (arg1) {
      console.log(arg1);
      harassmentInfo = JSON.parse(localStorage.getItem("harassmentInfo"))
      if (harassmentInfo == undefined) {
        return
      }
      console.log(harassmentInfo);
      $("#harassment_locx").val(harassmentInfo.locx);
      $("#harassment_locy").val(harassmentInfo.locy);
      // $("#harassmentTroopGroup").val(harassmentInfo.troopGroup);
      // $("#harassmentNum").val(harassmentInfo.num);
    }

    $("#saveharassment").click(function () {
      var locx = $('#harassment_locx').val();
      var locy = $('#harassment_locy').val();
      if (locx == "" || locy == "") {
        alert("请输入骚扰坐标");
        return
      }


      var harassmentInfo = {
        locx: locx,
        locy: locy,
      }

      //保存到本地
      localStorage.setItem("harassmentInfo", JSON.stringify(harassmentInfo))
      //获取
      console.log(JSON.parse(localStorage.getItem("harassmentInfo")))
      $("#harassmentModal").modal("hide")
    });

    $("#autoharassment").click((arg1) => {
      harassmentInfo = localStorage.getItem("harassmentInfo");
      if (harassmentInfo == undefined) {
        alert("请先设置战争信息");
        return;
      }

      btn = $(this);
      $.ajax({
        url: '{{ url_for("work.autoHarassment_api") }}',
        type: 'POST',
        data: {
          harassmentInfo: harassmentInfo,
        },
        beforeSend: function () {
          btn.prop("disabled", true);
        },
        success: function (data) {
          $("#autoharassment-load").hide();
          $("#autoharassment-text").show();
          if (data.code == 200) {
            setTimeout(() => {
              window.location.reload();
            }, 3000);
          } else {
            btn.prop("disabled", false);
            toastr.error(data.msg);
          }
        },
        error: function (data) {
          btn.prop("disabled", false);
          toastr.error(data.msg);
        }
      });
    });
    $("#autoharassmentAttack").click((arg1) => {
      harassmentInfo = localStorage.getItem("harassmentInfo");
      if (harassmentInfo == undefined) {
        alert("请先设置战争信息");
        return;
      }

      btn = $(this);
      $.ajax({
        url: '{{ url_for("work.autoHarassment_attack_api") }}',
        type: 'POST',
        data: {
          harassmentInfo: harassmentInfo,
        },
        beforeSend: function () {
          btn.prop("disabled", true);
        },
        success: function (data) {
          $("#autoharassment-load").hide();
          $("#autoharassment-text").show();
          if (data.code == 200) {
            setTimeout(() => {
              window.location.reload();
            }, 3000);
          } else {
            btn.prop("disabled", false);
            toastr.error(data.msg);
          }
        },
        error: function (data) {
          btn.prop("disabled", false);
          toastr.error(data.msg);
        }
      });
    });
  </script>
</div>

{% macro harassmentButton() -%}
<div class="btn-group" role="group">
  <button id="autoharassment" type="button" class="btn btn-primary">
    <span id="autoharassment-text">开启侦查</span>
  </button>
  <button id="autoharassmentAttack" type="button" class="btn btn-primary">
    <span id="autoharassment-text">开启骚扰</span>
  </button>

  <button id="autoharassment-set" type="button" class="btn btn-secondary" data-toggle="modal"
    data-target="#harassmentModal">骚扰设置</button>
</div>
{%- endmacro %}