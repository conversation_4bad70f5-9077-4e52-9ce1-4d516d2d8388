{% extends 'work_base.html' %}
{% import 'admin/lib.html' as lib with context %}
{% block userinfo %}

{% endblock %}
{% block actions %}
<div class="row">
    <div class="input-group col-md-2">
        <div class="btn-group" role="group">
            <button id="warlist" type="button" class="btn btn-primary">团战列表</button>
        </div>
        <div class="btn-group" role="group">
            <button id="warselfget" type="button" class="btn btn-info">检测自身</button>
        </div>
    </div>
    <div class="input-group col-md-2">
        <div class="btn-group" role="group">
            <button id="warget" type="button" class="btn btn-danger">检测模式</button>
        </div>
       
    </div>
    <div class="input-group col-md-2">
        <div class="input-group col-md">
        <div class="input-group-prepend">
            <label class="input-group-text" for="inputGroupSelect01" hidden>跟团兵种</label>
        </div>
        <select class="custom-select" id="troopGroup" hidden>
            <option value="1">步兵</option>
            <option value="2" selected>弓兵</option>
            <option value="3">骑兵</option>
        </select>
        <div class="input-group-prepend">
            <div class="input-group-text" id="btnGroupAddon2">出兵数</div>
        </div>
        <input type="number" id="troopnum" class="form-control" placeholder="出兵数" aria-label="出兵数"
            aria-describedby="btnGroupAddon2" value="300000">
        </div>
    </div>
</div>
<br />
<table class="table">
    <thead>
        <tr>
            <th scope="col">团长</th>
            <th scope="col">目标</th>
            <th scope="col">兵力</th>
            <th scope="col">剩余时间</th>
            <th scope="col">操作</th>
        </tr>
    </thead>
    <tbody id="warlist-tbody">
    </tbody>
</table>
{% endblock %}
{% block tail_work %}
<script>
    function createRow(data) {
        
        var row = $('<tr data-info=\''+JSON.stringify(data)+'\'></tr>');
        row.append($('<th scope="col">' + data.leaderKingdomName + '</th>'));
        row.append($('<th scope="col">' + data.targetKingdomName + '</th>'));
        row.append($(`<th scope="col">${data.numTroops}/${data.maxTroops}</th>`));
        row.append($(`<th scope="col">${data.overTime}</th>`));
        td = $('<td></td>');
        if (data.isJoined == false && data.self && data.overTime > 0) {
            td.append($(`<button type="button" class="btn btn-success warlist-join">参团</button>`));
        }
        row.append(td);
        return row[0];
    }

    function createRows(datas) {
        tb = $('#warlist-tbody')[0];
        $(tb).empty()
        for (var i = 0; i < datas.length; i++) {
            tb.append(createRow(datas[i]));
        }

        addClickEvent();        
    }

    function addClickEvent() {
        buys = $('.warlist-join');
        buys.click(warlistJoin);
    }

    function warlistJoin(arg1) {
        var data = $(arg1.target).parent().parent().data('info');
        var troopNum = $("#troopnum")[0].value;
        var mainTroop = $('#troopGroup')[0].value;
        var rallyMoId = data._id;
        console.log(this)
        btn = $(this);
        $.ajax({
            url: '{{ url_for("warlist.warjoin_api") }}',
            type: 'POST',
            data: {
                troopNum: troopNum,
                mainTroop: mainTroop,
                rallyMoId:rallyMoId,
            },
            beforeSend: function () {
                btn.prop("disabled", true);
                btn.prop("hidden", true);
                console.log(btn);
            },
            success: function (data) {
                if (data.code == 200) {
                    toastr.success(data.msg);
                } else {
                    toastr.error("异常"+data.msg);
                }
            },
            error: function (data) {
                toastr.error(data.msg);
            },
        });
    }

    $("#warlist").click((arg1,arg2) => {
        btn = $("#warlist");
        $.ajax({
            url: '{{ url_for("warlist.warlist_api") }}',
            type: 'POST',
            data: {

            },
            beforeSend: function () {
                btn.prop("disabled", true);
            },
            complete: function () {
                btn.prop("disabled", false);
            },
            success: function (data) {
                if (data.code == 200) {
                    toastr.success('刷新成功');
                    createRows(data.data);
                } else {
                    toastr.error(data.msg);
                }
            },
            error: function (data) {
                toastr.error(data.msg);
            }
        });
    });

    $("#warselfget").click((arg1) => {
        btn = $("#warselfget");
        $.ajax({
            url: '{{ url_for("warlist.warselfget_api") }}',
            type: 'POST',
            data: {

            },
            beforeSend: function () {
                btn.prop("disabled", true);
            },
            complete: function () {
                btn.prop("disabled", false);
            },
            success: function (data) {
                if (data.code == 200) {
                    toastr.success('刷新成功');
                } else {
                    toastr.error(data.msg);
                }
            },
            error: function (data) {
                toastr.error(data.msg);
            }
        });
    });


    $("#warget").click((arg1,arg2) => {
        btn = $("#warget");
        $.ajax({
            url: '{{ url_for("warlist.warget_api") }}',
            type: 'POST',
            data: {

            },
            beforeSend: function () {
                btn.prop("disabled", true);
            },
            // complete: function () {
            //     btn.prop("disabled", false);
            // },
            success: function (data) {
                if (data.code == 200) {
                    toastr.success('刷新成功');
                } else {
                    toastr.error(data.msg);
                }
            },
            error: function (data) {
                toastr.error(data.msg);
            }
        });

    });
</script>
{% endblock %}
