{% extends 'work_base.html' %}

{% block work_some %}
<p class="col-sm-2 mb-auto">当前队列数:<p id="fieldp"></p>
{% endblock %}
{% block superuserblock %}
{{ super() }}
{% if current_user.has_role('superuser') %}
<button id="dragoPointGet" class="btn btn-primary" type="button">获取龙点</button>
<button id="dragoIdGet" class="btn btn-primary" type="button">获取龙ID</button>
{% endif %}
{% endblock %}
{% block actions %}
{% if connect %}
<div class="row">
    
        {% import 'group_modal.html' as group_modal %}
        {% if workStatus %}
        {% else %}
    <div class="input-group col-md-3">    
        {{ group_modal.rallyButton() }}
    </div>
        {% endif %}
     <div class="input-group col-md-5">
        <div class="input-group col-md">
        <div class="input-group-prepend">
            <label class="input-group-text" for="inputGroupSelect01">开团兵种</label>
        </div>
        <select class="custom-select" id="troopGroup">
            <option value="1">步兵</option>
            <option value="2" selected>弓兵</option>
            <option value="3">骑兵</option>
        </select>
        <div class="input-group-prepend">
            <div class="input-group-text" id="btnGroupAddon2">出兵数</div>
        </div>
        <input type="number" id="troopnum" class="form-control" placeholder="出兵数" aria-label="出兵数"
            aria-describedby="btnGroupAddon2" value="300000">
        </div>
    </div>
    <div class="btn-group" role="group">
        <button id="supplementEnergy" type="button" class="btn btn-success">
          <span id="supplementEnergy-text">回点能量</span>
        </button>
    </div>
    <div class="btn-group" role="group">
        <button id="refreshField" type="button" class="btn btn-primary">
          <span id="refreshField-text">刷新队列</span>
        </button>
    </div>
    <div class="btn-group" role="group">
        <button id="gobackNow" type="button" class="btn btn-secondary text-goback">卡兵撤回</button>
    </div>
</div>
<br />
<div class="row">
    {% import 'garrison_modal.html' as garrison_modal %}
    {% import 'war_modal.html' as war_modal %}
    {% import 'warevent_modal.html' as warevent_modal %}
    {% if workStatus %}
    {% else %}
    <div class="input-group col-md-3">    
        {{ garrison_modal.garrisonButton() }}
    </div>
    <div class="input-group col-md-3">    
        {{ war_modal.warButton() }}
    </div>
    <div class="input-group col-md-3">
        {{ warevent_modal.harassmentButton() }}
    </div>
    {% endif %}
</div>
{% endif %}
{% endblock %}
{% block tail_work %}
{% include 'search_fields.html' %}
<script>
    $("#dragoPointGet").click((arg1) => {
        if (!isSelectUser()) return;
        let btn = $(arg1.currentTarget);
        
        $.ajax({
            url: '{{ url_for("work.dragoPointGet_api") }}',
            type: 'POST',
            data: {
                workUser: workUserInfo(),
            },
            beforeSend: function (xhr) {
                btn.prop("disabled", true);
            },
            complete: function (xhr, status) {
                btn.prop("disabled", false);
            },
            success: function (data) {
                if (data.code == 200) {
                    toastr.success("刷新成功:"+data.data);
                }else {
                    toastr.error("刷新失败"+data.msg);
                }
            }
        });
    })

    $("#dragoIdGet").click((arg1) => {
        if (!isSelectUser()) return;
        let btn = $(arg1.currentTarget);
        
        $.ajax({
            url: '{{ url_for("work.dragoIdGet_api") }}',
            type: 'POST',
            data: {
                workUser: workUserInfo(),
            },
            beforeSend: function (xhr) {
                btn.prop("disabled", true);
            },
            complete: function (xhr, status) {
                btn.prop("disabled", false);
            },
            success: function (data) {
                if (data.code == 200) {
                    toastr.success("刷新成功:"+data.data);
                }else {
                    toastr.error("刷新失败"+data.msg);
                }
            }
        });
    })

    $("#refreshField").click((arg1) => {
        let btn = $(arg1.currentTarget);

        $.ajax({
            url: '{{ url_for("work.getMyFields_api") }}',
            type: 'POST',
            data: {
            },
            beforeSend: function (xhr) {
                btn.prop("disabled", true);
            },
            complete: function (xhr, status) {
                btn.prop("disabled", false);
            },
            success: function (data) {
                if (data.code == 200) {
                    toastr.success("刷新成功")
                    $("#fieldp").text(data.data.length);
                }else {
                    toastr.error("刷新失败"+data.msg)
                }
            }
        });
    });

    $("#supplementEnergy").click((arg1) => {
        let btn = $(arg1.currentTarget);

        $.ajax({
            url: "{{ url_for('work.energy_api') }}",
            type: "POST",
            data: {
            },
            beforeSend: function (xhr) {
                btn.prop("disabled", true);
            },
            complete: function (xhr, status) {
                btn.prop("disabled", false);
            },
            success: (data) => {
                if (data.code == 200) {
                    toastr.success("充能成功")
                }else {
                    toastr.error("充能失败"+data.msg)
                }
            }
        });

    });

    $("#gobackNow").click((arg1,arg2) => {
        btn = $("#gobackNow");
        $.ajax({
            url: '{{ url_for("work.goback_all_api") }}',
            type: 'POST',
            data: {

            },
            beforeSend: function () {
                btn.prop("disabled", true);
            },
            complete: function () {
                btn.prop("disabled", false);
            },
            success: function (data) {
                if (data.code == 200) {
                    toastr.success('刷新成功');
                } else {
                    toastr.error(data.msg);
                }
            },
            error: function (data) {
                toastr.error(data.msg);
            }
        });
    })


</script>
{% include 'group_modal.html' %}
{% include 'garrison_modal.html' %}
{% include 'war_modal.html' %}
{% include 'warevent_modal.html' %}
{% endblock %}

