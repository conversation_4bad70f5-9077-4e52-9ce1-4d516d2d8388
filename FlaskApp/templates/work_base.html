{% extends 'my_master.html' %}
{% import 'admin/lib.html' as lib with context %}
{% block head %}
{% endblock %}

{% block body %}

<div class="container-fluid">
    {% if not connect and (endpoint != 'work' and not current_user.has_role('superuser')) %}
    <div class="input-group flex-nowrap" style="padding: 10px !important;">
        <div class="input-group-prepend">
            <span class="input-group-text" id="addon-wrapping">token</span>
        </div>
        <input type="text" class="form-control" placeholder="token" aria-label="token" name="token"
            aria-describedby="addon-wrapping">
    </div>
    <div class="input-group flex-nowrap" style="padding: 10px !important;">
        <div class="input-group-prepend">
            <span class="input-group-text" id="addon-wrapping">email</span>
        </div>
        <input type="text" class="form-control" placeholder="email" aria-label="email" name="email"
            aria-describedby="addon-wrapping">
        <div class="input-group-prepend">
            <span class="input-group-text" id="addon-wrapping">password</span>
        </div>
        <input type="text" class="form-control" placeholder="password" aria-label="password" name="password"
            aria-describedby="addon-wrapping">
    </div>

    <div>
        <button id="connect" type="button" class="btn btn-primary btn-lg btn-block">
            <span id="connect-load" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"
                hidden="true"></span>
            <span id="connect-text">连接</span>
        </button>
    </div>
    {% else %}
    {% if connect %}
    <div class="row align-items-center">
        <div class="row col-md align-self-center">
            <p class="font-weight-bold mb-auto">昵称: {{ name }}</p>
        {% if workStatus %}
            <p>模式: {{ workName }}</p>
        {% endif %}
        
        {% block work_some %}
        
        {% endblock %}

        </div>
        <div class=" col-md-3">
            {% if workStatus %}
            <button id="stop" type="button" class="btn btn-danger">
                <span id="stop-load" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"
                    hidden="true"></span>
                <span id="stop-text">停止</span>
            </button>
            {% endif %}
            <button id="changesocks" type="button" class="btn btn-primary">
                <span id="changesocks-load" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"
                    hidden="true"></span>
                <span id="changesocks-text">更换代理</span>
            </button>
            <button id="logout" type="button" class="btn btn-danger">
                <span id="logout-load" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"
                    hidden="true"></span>
                <span id="logout-text">退出</span>
            </button>
        </div>
    </div>
    
    {% block userinfo %}

    {% endblock %}
    {% endif %}
    <div>
    {% block superuserblock %}
        {% if current_user.has_role('superuser') %}
            <select id="workUser">
                <optgroup label="用户列表">
                    <option value="0" selected>请选择用户</option>
                    {% for user in workUsers %}
                        <option value='{{ user["token"] }}' data-info='{{ user }}'>{{ user["name"] }}</option>
                    {% endfor %}
                </optgroup>
            </select>
        {% endif %}
    {% endblock %}
    </div>
    <br />
    <div>
        {% block actions %}
        {% endblock %}
    </div>
    {% endif %}
    {% block body_font %}

    {% endblock %}
</div>
{% endblock %}

{% block tail %}
<script>
    $("#stop").click((arg1) => {
        $.ajax({
            url: '{{ url_for("work.stop_api") }}',
            type: 'POST',
            data: {

            },
            success: function (data) {
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            },
            error: function (data) {
                toastr.error(data.msg);
            }
        })
    });

    // $SCRIPT_ROOT = {{ request.script_root|tojson }} ;
    $("#logout").click((arg1) => {
        $.ajax({
            url: '{{ url_for("work.logout_api") }}',
            type: 'POST',
            data: {

            },
            success: function (data) {
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            },
            error: function (data) {
                toastr.error(data.msg);
            }
        });
    });

    $("#changesocks").click((arg1) => {
        $.ajax({
            url: '{{ url_for("work.changesocks_api") }}',
            type: 'POST',
            data: {

            },
            success: function (data) {
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            },
            error: function (data) {
                toastr.error(data.msg);
            }
        });
    });

    $("#connect").click(function () {
        var token = $("input[name='token']").val();
        var email = $("input[name='email']").val();
        var password = $("input[name='password']").val();
        if (token == '' && (email == '' || password == '')) {
            alert('请输入token或者账号密码');
            return;
        }

        $("#connect").prop('disabled', true);
        $("connect-load").prop('hidden', false);

        $.ajax({
            url: "{{ url_for('work.connect_api') }}",
            type: 'POST',
            data: {
                token: token,
                email: email,
                password: password
            },
            success: function (data) {
                if (data.code == 200) {
                    window.location.href = window.location.href;
                } else {
                    alert(data.msg);
                }
                $("#connect").prop('disabled', false);
                $("connect-load").prop('hidden', true);

            },
            error: function (data) {
                alert('登录失败');
                $("#connect").prop('disabled', false);
                $("connect-load").prop('hidden', true);

            }
        });
    });

    {% if current_user.has_role('superuser') %}
    function isSelectUser() {
        var select = $("#workUser")[0];
        if (select.selectedIndex == 0) {
            toastr.error("请选择用户");
            return false;
        }else {
            return true;
        }
    }
    function workUserInfo() {
        var select = $("#workUser")[0];
        var user = select.options[select.selectedIndex].dataset.info;
        return user;
    }
    {% else %}
    function isSelectUser() {
        return false;
    }
    function workUserInfo() {
        return null;
    }
    {% endif %}
</script>
{% block tail_work %}{% endblock %}
{% endblock %}