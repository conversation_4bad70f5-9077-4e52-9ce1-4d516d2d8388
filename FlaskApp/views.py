

from . import admin,Views
# Add model views
admin.add_view(Views.MyModelView(name='权限'))
admin.add_view(Views.AdminUserModelView(name='用户'))
admin.add_view(Views.WorkView())
admin.add_view(Views.NFTView())
admin.add_view(Views.RecordView())
admin.add_view(Views.TokenWorkView())
admin.add_view(Views.MiningWorkView())
admin.add_view(Views.CaravanView())
admin.add_view(Views.TreasureListView())
admin.add_view(Views.GoBackListView())
admin.add_view(Views.BuildView())
admin.add_view(Views.SkillListView())
admin.add_view(Views.MonitoringAllianceBattleView())
admin.add_view(Views.VipListView())
admin.add_view(Views.WarListView())
admin.add_view(Views.GlobalCrystalView())
admin.add_view(Views.SystemView())
admin.add_view(Views.SystemConfigView())
admin.add_view(Views.LogRecordView())
admin.add_view(Views.TokenUpdateView())
admin.add_view(Views.OnlineView())
admin.add_view(Views.CVCBlockListView())
admin.add_view(Views.CVCWhiteListView())
admin.add_view(Views.CVCRankView())