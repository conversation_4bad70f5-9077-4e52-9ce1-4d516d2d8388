# /bin/env python3
# -*- coding: UTF-8 -*-
from Api.UserInfo import UserInfo

# 账号


class Account:
    def __init__(self, text, userKey=None, token=None, googleToken=None, socks5=None):
        self.ok = False
        self.email = None
        self.password = None
        self.userKey = userKey
        self.token = token
        self.googleToken = googleToken
        self.s5 = socks5
        self.text = text
        l = self.text.split("|")
        if l and len(l) < 3:
            l = self.text.split("----")
            if l and len(l) < 3:
                if not text:
                    if userKey:
                        self.text = f'{userKey}||'
                return

        if len(l[0]) == 36:
            self.userKey = l[0]
        else:
            self.email = l[0]
            self.password = l[1]

        if len(l) >= 3:
            l3 = l[2]
            if len(l) >= 4:
                l3 = l[3]

            if l3[:2] == 'ey':
                if len(l3) > 500:
                    self.googleToken = l3
                else:
                    self.token = l3
            else:
                self.s5 = l[2]

        self.ok = True

    def returnUserInfo(self, isMain=False, saveSocks5=True):
        return UserInfo(self.email, self.password, userKey=self.userKey, token=self.token, googleToken=self.googleToken, socks5=self.s5, isMain=isMain, saveSocks5=saveSocks5)
