# /bin/env python3
# -*- coding: UTF-8 -*-


class Castle(object):
    id:str
    name:str
    allianceName:str
    level:int
    loc:str

    def __init__(self,text):
        lines = text.split(" ")
        self.id = lines[0]
        self.allianceName = lines[1][1:][:-1]
        self.name = lines[2]
        if len(lines) != 5:
            self.name = "".join(lines[2:(len(lines)-3)])
            if len(lines) == 4:
                self.name = "异常"
        
        self.level = int(lines[-2])
        self.loc = lines[-1]