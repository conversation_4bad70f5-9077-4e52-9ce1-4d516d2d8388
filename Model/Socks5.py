# /bin/env python3
# -*- coding: UTF-8 -*-

"""
功能描述：s5模型
编写人：darkedge
编写日期：2022年1月4日
   
"""

from dataclasses import dataclass, field

@dataclass
class Socks5:
    """
    代理信息
    """
    host: str = field(default=None)
    port: str = field(default=None)
    user: str = field(default=None)
    pwd: str = field(default=None)
    
    def __init__(self,s5Text:str):
        if s5Text:
            s1 = s5Text.split("@")
            if len(s1) > 1:
                s2 = s1[0].split(":")
                self.user = s2[0]
                self.pwd = s2[1]

                s3 = s1[1].split(":")
                self.host = s3[0]
                self.port = s3[1]
            else:
                s2 = s1[0].split(":")
                self.host = s2[0]
                self.port = s2[1]