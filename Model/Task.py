from enum import Enum

class StartTaskEnum(Enum):
    none = 0
    success = 1
    fail = 2
    full = 3
    """满队列"""
    limit = 4
    """带兵太少"""
    limitMax = 5
    """超过最大限制"""

    @property
    def helperString(self):
        if self == StartTaskEnum.none:
            return "无效数据"
        elif self == StartTaskEnum.success:
            return "成功"
        elif self == StartTaskEnum.fail:
            return "失败"
        elif self == StartTaskEnum.full:
            return "满队列"
        elif self == StartTaskEnum.limit:
            return "受限"
        elif self == StartTaskEnum.limitMax:
            return "超过最大限制"
        return "未知状态"