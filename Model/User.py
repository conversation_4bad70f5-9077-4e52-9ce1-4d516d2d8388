# /bin/env python3
# -*- coding: UTF-8 -*-

"""
功能描述：用户基础模型
编写人：darkedge
编写日期：2021年12月31日

"""
import datetime
import json
import math
import os
import secrets
import threading
import time
from dataclasses import dataclass, field

import Unit.enum as Enum
from Model.Kingdom import Kingdom

# trunk-ignore-all(ruff/F401)
from Unit.DeviceInfo import (
    checkLocalVersion,
    createUAWithOS,
    createUserKey,
    getDeviceInfo,
    makeAppleToken,
    removeDeviceInfo,
    saveDeviceInfo,
)
from Unit.Logger import RedisHandler, createLoggerWithKey, logger

limitTroopQuantity = 1
ShrineLocs = [
    [1024, 1024],
    [384, 384],
    [640, 384],
    [896, 384],
    [1152, 384],
    [1408, 384],
    [1664, 384],
    [384, 640],
    [640, 640],
    [896, 640],
    [1152, 640],
    [1408, 640],
    [1664, 640],
    [384, 896],
    [640, 896],
    [896, 896],
    [1152, 896],
    [1408, 896],
    [1664, 896],
    [384, 1152],
    [640, 1152],
    [896, 1152],
    [1152, 1152],
    [1408, 1152],
    [1664, 1152],
    [384, 1408],
    [640, 1408],
    [896, 1408],
    [1152, 1408],
    [1408, 1408],
    [1664, 1408],
    [384, 1664],
    [640, 1664],
    [896, 1664],
    [1152, 1664],
    [1408, 1664],
    [1664, 1664],
]

AdminBarkKeys = [
    "bAzCab9gbvpDza7YYsRjgM",
]

LoadingList = [
    [
        2,
        4,
        6,
        8,
        10,
        20,
        20,
    ],
    [
        1.5,
        3,
        4.5,
        6,
        7.5,
        12,
        12,
    ],
    [
        1,
        2,
        3,
        4,
        5,
        8,
        8,
    ],
]

TroopNames = ["步兵", "弓兵", "骑兵"]


@dataclass
class User(object):
    """
    用户信息
    """

    email: str = field(default=None)
    pwd: str = field(default=None)
    userKey: str = field(default=None)
    googleToken: str = field(default=None)
    appleSubId: str = field(default=None)
    token: str = field(default=None)
    socks5: str = field(default=None)
    saveSocks5: bool = field(default=False)
    isMain: bool = field(default=False)
    """ 是否是主号 """
    isGoogle: bool = field(default=False)
    """是否是谷歌号"""
    isWebDevice: bool = field(default=True)
    """是否是网页设备"""

    wsMatch3 = "https://socm-lok-live.leagueofkingdoms.com/socket.io/"
    """小游戏ws链接"""

    fakeIP = None
    """伪装IP"""
    questFinishList = {}
    """已完成的任务列表"""

    hasInit: bool = field(default=False, init=False)
    """ 是否已经初始化 """
    canTeleport: bool = field(default=False, init=False)
    """ 是否可以传送 """

    # 设备信息
    deviceInfo: dict = field(default_factory=dict, init=False)
    ua: str = field(default=None, init=False)
    invalidStatu: bool = False
    noWSUser = False
    """不登录的情况下获取账号信息"""

    # 用户信息
    name: str = None
    userId: str = None
    publicKey: str = None
    """钱包地址"""
    id: str = None
    fieldObjectId: str = None
    loc: list = None
    level: int = 1
    _resources: list = None
    isMaxTreasure: bool = False
    """是否满级宝藏"""
    isMaxOrbPortal: bool = False
    """是否满级次元门"""
    tmpParams: dict = None
    """临时参数"""

    @property
    def resources(self):
        if self._resources is None:
            return [999999999, 999999999, 999999999, 999999999]
        return self._resources

    @resources.setter
    def resources(self, resources):
        self._resources = resources

    """资源"""
    crystal: int = 0
    """砖石"""
    vip: int = 0
    """vip等级"""
    vipPoint: int = 0
    """vip点"""
    vipLastTime = 0
    """vip最后领取时间"""
    dsaVipLastTime = 0
    """dsavip最后领取时间"""
    skillUseLastTime = 0
    """技能最后使用时间"""
    enterTime: int = 0
    """进入时间"""
    mintTime: int = 0
    """打包时间"""
    actionPoint: int = Enum.DEFAULT_ACTION_POINTS
    """活动点"""
    dragoActionPoint: int = 0
    """'龙AP"""
    dailyPoint: int = 0
    """每日点数"""
    allianceId = None
    """联盟id"""
    allianceMaxResearches = {}
    """满级科技"""
    kingdomId = None
    """id"""
    kingdom: dict = None
    """王国信息 未使用"""
    power: int = 0
    """战力"""
    troops: list = field(default_factory=list, init=False)
    """部队信息"""
    kingdomTroops: list = field(default_factory=list, init=False)
    """城堡部队信息"""
    troopSum: int = 0
    """部队总数"""
    marchLimit: int = 2
    """出兵队列数"""
    marchSize: int = 0
    """带兵数"""
    numTroops: int = 0
    """总兵数"""
    maxTroops: int = 0
    """最大部队数"""
    troopFields: list = field(default_factory=list, init=False)
    """在野部队信息"""
    fieldTasks: list = field(default_factory=list, init=False)
    """在野任务"""
    _kingdomTasks: list = field(default_factory=list, init=False)
    """王国任务"""
    isLevel2: bool = False
    """是否是等级2兵"""
    realWorld: int = 0
    """真实世界"""
    attackCount: int = 0
    """攻击次数"""
    insufficient_resources: bool = False
    """是否资源不足"""
    frequentKingdoms: dict = field(default_factory=dict, init=False)
    """频繁的城堡"""

    buildings: list = field(default_factory=list, init=False)
    """建筑列表"""
    researches: list = field(default_factory=list, init=False)
    """研究列表"""
    itemsInfo: dict = field(default_factory=dict, init=False)
    """物品信息"""
    tmpItems: list = field(default_factory=list, init=False)
    """缓存物品信息"""
    crystalTasks: list = None
    """水晶队列"""

    researchGrade: int = 0
    """研究等级"""

    trainingNum = 0
    """练兵数"""
    gatheringNum = 0
    """采集资源数"""
    killMonster = 0
    """击杀怪物数量"""
    speedLoadFlag = False
    """速度道具加载标记"""
    resourcesLoadFlag = False
    """资源道具加载标记"""
    loadingInfos = None
    """装载信息"""
    workDragos = None
    """挖矿龙"""

    # 外部使用
    buyNum: int = 0
    """购买次数"""
    bugCount: int = 0
    """bug成功次数"""
    mailId: str = None
    """邮件id"""
    isBug: bool = False
    """账号bug标记"""

    canSummon: bool = False
    """是否可以召唤"""

    # 内部结构
    captchaLock: threading.BoundedSemaphore = None
    """验证码锁"""
    captchaTime: int = 0
    """验证码时间"""
    captchaInfo = None
    """验证码信息"""
    hasCaptcha: bool = False
    """已打验证"""
    silverTime: int = 0
    """领取白银宝箱时间"""
    silverNum: int = 0
    """白银宝箱数量"""
    goldTime: int = 0
    """领取金宝箱时间"""
    platinumTime: int = 0
    """领取铂金宝箱时间"""
    placeHolderStr: str = ""
    """占位日志字符串"""
    goDirection = secrets.randbelow(3)
    """出发方向"""
    harvestList = None
    """收集列表"""
    lastHarvest500 = False
    """最后建筑收集500"""

    crystalMaxLevel = 9
    """最大水晶等级内控"""

    # ws地址
    wsField: str = "https://socf-lok-live.leagueofkingdoms.com/socket.io/"
    wsKingdom: str = "https://sock-lok-live.leagueofkingdoms.com/socket.io/"

    # 外部增量
    gobackList: dict = field(default_factory=dict, init=False)
    """已撤回列表"""

    logMgr = logger
    """日志系统"""
    memoryLog = None

    useBarkNoti = False
    """使用推送通知"""
    userBarkKey: str = None
    """用户通知ID"""

    isAllianceLeader: bool = False
    """是否是国王"""

    cvcEventOpen: bool = False
    """CVC事件开启"""
    rouletteEvent: dict = None
    """轮盘事件"""
    collectionEventTime: str = None
    """收集事件时间"""
    stopOpenRallyTime: int = 0
    """停止开团时间"""
    garantuaEventTime: int = 0
    """卡冈时间"""
    lastWarJoinTime: int = 0
    """最后战争跟团时间"""
    def __post_init__(self):
        self.captchaLock = threading.BoundedSemaphore(1)
        self.harvestList = [104, 106, 107, 108]
        self.loadingInfos = [0, 0, 0, 0]
        self.tmpParams = {}

    @property
    def worldId(self):
        return self.loc and self.loc[0] or 9999

    @property
    def silverGet(self):
        return self.silverTime > 0 and time.time() - self.silverTime > 0

    @property
    def goldGet(self):
        return self.goldTime > 0 and time.time() - self.goldTime > 0

    @property
    def platinumGet(self):
        return self.platinumTime > 0 and time.time() - self.platinumTime > 0

    @property
    def isLogin(self):
        if self.token is None:
            return False
        else:
            return True

    @property
    def isInvalid(self):
        return self.invalidStatu

    @property
    def googleTokenName(self):
        if self.googleToken or self.isGoogle:
            if self.email is None:
                from Unit.Redis import loadJWTInfo

                info = loadJWTInfo(self.googleToken)
                if info:
                    self.email = info.get("email")
            return self.email
        return None

    @property
    def key(self):
        """表示名"""
        return self.appleSubId or self.userKey or self.googleTokenName or self.email

    def logStr(self, msg):
        s = "账号:%s [%s] %s" % (self.key, self.placeHolderStr, msg)
        if self.name:
            s = "昵称:%s %s" % (self.name, s)
        return s

    @property
    def appleToken(self):
        if self.appleSubId:
            return makeAppleToken(self.appleSubId)
        return None

    # 日志方法
    def debuglog(self, msg, exc_info=None):
        self.logMgr.debug(self.logStr(msg), exc_info=exc_info)

    def log(self, msg):
        self.logMgr.info(self.logStr(f"pid:{os.getpid()} {msg}"))

    def errorLog(self, msg, exc_info=None):
        self.logMgr.error(self.logStr(f"pid:{os.getpid()} {msg}"), exc_info=exc_info)

    def debugToConsole(self):
        consoleHander = self.logMgr.handlers[0]
        consoleHander.setLevel(0)

    def becomeLogSelfOnly(self):
        self.logMgr = createLoggerWithKey(self.key or self.kingdomId)
        self.log("独立日志系统")

    def addRedisHander(self):
        self.becomeLogSelfOnly()
        sh = RedisHandler(self.key or self.kingdomId)
        sh.clearLogs()
        sh.setLevel(20)
        self.logMgr.addHandler(sh)
        self.log(f"启动redis日志 key:{sh.key}")

    # def addMemoryHander(self, capacity=200, level=20):
    #     self.logMgr = createLoggerWithKey(self.key or self.kingdomId)
    #     sh = MemoryHandler(capacity)
    #     sh.setLevel(level)
    #     sh.setFormatter("%(asctime)s: %(message)s")
    #     self.logMgr.addHandler(sh)
    #     self.log("添加内存日志")
    #     self.memoryLog = sh

    # def getMemoryMsgs(self):
    #     return [f'{v.asctime}:{v.getMessage()}' for v in self.memoryLog.buffer]

    def buildList(self, position):
        localMap = {
            1: "城堡",
            2: "仓库",
            3: "瞭望塔",
            4: "宝库",
            5: "学院",
            6: "医院",
            7: "联盟大厅",
            8: "城墙",
            9: "交易所",
            101: "金矿场",
            102: "金矿场",
            103: "金矿场",
            104: "农场",
            105: "兵营",
            106: "石材场",
            107: "木材场",
            108: "金矿场",
            109: "农田",
            110: "石材场",
            111: "木材厂",
            114: "金矿场",
            115: "金矿场",
            116: "营房",
            117: "金矿场",
            118: "金矿场",
        }
        return localMap[position]

    def buildTypeList(self, position):
        localMap = {
            101: 40100205,
            102: 40100205,
            103: 40100205,
            104: 40100202,
            105: 40100201,
            106: 40100204,
            107: 40100203,
            108: 40100205,
            109: 40100202,
            110: 40100204,
            111: 40100203,
            114: 40100205,
            115: 40100205,
            116: 40100201,
            117: 40100205,
            118: 40100205,
        }
        return localMap[position]

    def checkGridInLocs(self, locs):
        """检查网格是否在locs中"""
        if not self.loc:
            return False
        for loc in locs:
            if self.loc[0] == int(loc[0]):
                if (
                    abs(self.loc[1] - int(loc[1])) <= 2
                    and abs(self.loc[2] - int(loc[2])) <= 2
                ):
                    return True
        return False

    def checkFrequentKingdom(self, kingdom: Kingdom):
        """检查是否频繁的王国"""
        if kingdom.name == self.name:
            self.log("自己打你🐴")
            return True
        t = self.frequentKingdoms.get(kingdom.name)
        if t:
            if time.time() - t < 600:
                self.log("王国 %s 攻击频繁 10分钟后再试" % kingdom.name)
                return True
        return False

    def checkTroopsMax(self, troops):
        """校验兵数量 True 兵力不足"""
        sum = self.sumTroops(troops)
        # if sum >= self.marchSize:
        if sum >= limitTroopQuantity:
            return False
        return True

    def checkShield(self, info):
        """检查保护盾"""
        fo = info.get("fo")
        occupied = fo.get("occupied")
        if occupied:
            shield = occupied.get("shield")
            if shield and shield == 1:
                return True
        return False

    def checkResourceEmpty(self, num):
        """无法练兵认证"""
        if self.resources is None:
            return True
        else:
            multipleCount = num / 100
            lows = [3000 * multipleCount, 0, 6000 * multipleCount, 1500 * multipleCount]
            if self.isLevel2:
                lows = [
                    6000 * multipleCount,
                    0,
                    12000 * multipleCount,
                    3000 * multipleCount,
                ]
            for i in range(0, 3):
                if int(self.resources[i]) < lows[i]:
                    return True

        return False

    def checkMintTime(self):
        """检测打包时间"""
        if time.time() - self.mintTime < 60 * 60:
            self.debuglog("包装频繁，1小时候再试")
            return True
        return False

    def setMintTime(self):
        """设置打包时间"""
        self.mintTime = time.time()

    def checkTreasure(self, treasureList):
        """检查宝藏"""
        if not treasureList or self.isMaxTreasure:
            return
        if len(list(filter(lambda x: x.get("level") > 0, treasureList))) == 0:
            return
        orbPortals = list(filter(lambda x: x.get("code") == 10503001, treasureList))
        if not self.isMaxOrbPortal and orbPortals:
            orbPortal = orbPortals[0]
            if orbPortal.get("level") == 4:
                self.isMaxOrbPortal = True
                self.log("满橙次元门")
            else:
                levelPrices = [0, 10, 30, 60, 100, 150]
                skillLevel = orbPortal.get("skillLevel")
                piece = orbPortal.get("piece")
                for skill in skillLevel:
                    piece += levelPrices[skill]
                if piece >= 600:
                    self.isMaxOrbPortal = True
                    self.log("满橙次元门")

        for treasure in treasureList:
            code = treasure.get("code")
            if code // 1000 == 10504:
                # 号角 3神器过滤
                if code in [10504013, 10504014, 10504015, 10504008]:
                    continue
                if treasure.get("level", 0) == 5:
                    continue
                return
        self.isMaxTreasure = True
        self.log("满橙号")

    def sumTroops(self, troops):
        """统计兵力"""
        sum = 0
        for troop in troops:
            sum += troop.get("amount")
        return sum

    def getBuildingLevel(self, position):
        """获取指定位置建筑等级"""
        for building in self.buildings:
            if building.get("position") == position:
                level = building.get("level")
                state = building.get("state")
                if state == 2:
                    return (level or 0) + 1
                return level
        return 0

    def getBuildingLevelWithCode(self, code):
        """获取指定代码建筑等级"""
        building = self.getBuilding(code)
        if building:
            level = building.get("level")
            return level
        return 0

    def setBuildingLevel(self, position, level):
        """设置指定位置建筑等级"""
        for building in self.buildings:
            if building.get("position") == position:
                building["level"] = level
                building["state"] = 1
                if position == 1:
                    self.level = level
                return True
        self.buildings.append({"position": position, "level": level, "state": 1})
        return False

    def setBuilding(self, position):
        """设置指定位置建筑等级状态"""
        for building in self.buildings:
            if building.get("position") == position:
                building["state"] = 2
                return True

    def getBuilding(self, code):
        """获取指定代码建筑"""
        for building in self.buildings:
            if building.get("code") == code:
                return building
        return None

    def resetBuilding(self):
        """刷新本地建筑数据"""
        for building in self.buildings:
            state = building.get("state")
            if state == 2:
                building["state"] = 1
                level = building["level"]
                building["level"] = level + 1
                position = building["position"]
                if position == 1:
                    self.level = level + 1

    def setResarchLevel(self, code, level):
        """设置科技等级"""
        for research in self.researches:
            if research.get("code") == code:
                research["level"] = level
                return True
        self.researches.append({"code": code, "level": level})
        return False

    def getResearchesInfo(self, researchCode):
        """获取科技信息"""
        for research in self.researches:
            if research.get("code") == researchCode:
                return research

    def getItemsInfo(self, itemList):
        """获取物品信息"""
        itemsInfo = {}
        for item in itemList:
            code = item.get("code")
            if code:
                itemsInfo[code] = item.get("amount")
        self.itemsInfo = itemsInfo
        return itemsInfo

    def itemCount(self, itemCode):
        """获取物品数量"""
        if self.itemsInfo is None or len(self.itemsInfo) == 0:
            return 0
        res = self.itemsInfo.get(itemCode)
        if res is None:
            return 0
        return res

    def loadItemsInfo(self, items):
        """读取物品信息"""
        self.tmpItems = items
        if items:
            actionPoints = [
                Enum.ITEM_CODE_ACTION_POINTS_10,
                Enum.ITEM_CODE_ACTION_POINTS_20,
                Enum.ITEM_CODE_ACTION_POINTS_50,
                Enum.ITEM_CODE_ACTION_POINTS_100,
            ]
            points = {
                Enum.ITEM_CODE_ACTION_POINTS_10: 10,
                Enum.ITEM_CODE_ACTION_POINTS_20: 20,
                Enum.ITEM_CODE_ACTION_POINTS_50: 50,
                Enum.ITEM_CODE_ACTION_POINTS_100: 100,
            }
            self.getItemsInfo(
                items  # [Enum.ITEM_CODE_CRYSTAL_10, Enum] + actionPoints + [10104029]
            )
            crystalItemCount = self.itemCount(Enum.ITEM_CODE_CRYSTAL_10) * 10
            actionPointSum = 0
            for k in actionPoints:
                actionPointSum += self.itemCount(k) * points[k]
            if self.actionPoint != Enum.DEFAULT_ACTION_POINTS:
                actionPointSum + self.actionPoint
            from Unit.Redis import redisHelper

            totalCrystal = crystalItemCount + self.crystal
            if self.level > 10:
                redisHelper.setCrystalItem(self.key, totalCrystal)
                redisHelper.setRecordCrystal(self.name, totalCrystal)
                redisHelper.setStockEnergy(self.key, actionPointSum)
            if len(items) > 20:
                redisHelper.setItemList(self.name, items)

            self.loadSpeedItems()
            self.loadResourceItems()

    def loadSpeedItems(self, force=False):
        """加载速度道具"""
        if self.speedLoadFlag and not force:
            return
        self.speedLoadFlag = True

        itemStr = ""
        for classification, itemMap in Enum.ITEM_CODE_SPEEDUP_MAP.items():
            sum = 0
            for code, value in itemMap.items():
                sum += self.itemCount(code) * value

            if sum > 0:
                day = round(sum / 60 / 60 / 24, 2)
                name = Enum.ITEM_CODE_SPEEDUP_MAP_NAME[classification]
                if day > 365:
                    itemStr += f"{name}:{round(day / 365, 2)}年 "
                else:
                    itemStr += f"{name}:{day}天 "

        if itemStr:
            self.log(f"加速道具: {itemStr}")
        return itemStr

    def loadResourceItems(self, force=False):
        """加载资源道具"""
        if self.resourcesLoadFlag and not force:
            return
        self.resourcesLoadFlag = True

        itemUnits = [
            0,
            1_000,
            5_000,
            10_000,
            50_000,
            100_000,
            500_000,
            1_000_000,
            5_000_000,
            10_000_000,
        ]
        resources = [0, 0, 0, 0]
        # 根据index，code遍历 Enum.BUYABLE_CARAVAN_ITEM_CODE_LIST
        for index, code in enumerate(Enum.BUYABLE_CARAVAN_ITEM_CODE_LIST):
            resourceIndex = index // 10
            resources[resourceIndex] += itemUnits[index % 10] * self.itemCount(code)

        resourceNames = ["粮食", "木材", "石材", "金矿"]
        itemStr = ""
        for index, resource in enumerate(resources):
            name = resourceNames[index]
            if resource > 1_000:
                resource = round(resource / 1_000, 2)
                if resource > 1_000:
                    resource = round(resource / 1_000, 2)
                    if resource > 1_000:
                        resource = round(resource / 1_000, 2)
                        itemStr += f"{name}:{resource}B "
                    else:
                        itemStr += f"{name}:{resource}M "
                else:
                    itemStr += f"{name}:{resource}K "
            else:
                itemStr += f"{name}:{resource} "

        if itemStr:
            self.log(f"仓库资源: {itemStr}")
        return itemStr

    def loadTroops(self):
        """加载兵力"""
        troopStr = ""
        for troop in self.troops:
            code = troop.get("code")
            level = code % 10
            troopType = code // 100 % 10
            name = f"{TroopNames[troopType - 1]} T{level}"
            amount = troop.get("amount")
            unit = ""
            if amount > 1_000:
                amount = round(amount / 1_000, 2)
                if amount > 1_000:
                    amount = round(amount / 1_000, 2)
                    unit = "M"
                else:
                    unit = "K"
            troopStr += f"{name}:{amount}{unit} "
        return troopStr

    def makeDeviceInfo(self):
        """生成设备信息"""
        if not self.deviceInfo:
            self.deviceInfo = getDeviceInfo(
                self.email,
                self.userKey,
                self.appleSubId,
                kingdomId=self.kingdomId,
                isIOS=not self.isWebDevice,
            )
        OS = self.deviceInfo.get("OS")
        # if self.deviceInfo["platform"] == "web":
        #     self.clearSelf()
        #     removeDeviceInfo(self.email, self.userKey)
        #     return self.makeDeviceInfo()
        self.ua = createUAWithOS(OS)

    def clearSelf(self):
        """清除自己的信息"""
        self.token = None
        from Unit.Redis import redisHelper

        redisHelper.removeToken(
            email=self.email,
            userKey=self.userKey,
            appleSubId=self.appleSubId,
            kingdomId=self.kingdomId,
        )
        # removeDeviceInfo(self.email, self.userKey)
        self.ua = None
        self.hasInit = False
        self.invalidStatu = False
        # self.deviceInfo = None

    @property
    def sumResources(self):
        """统计资源总数"""
        sum = 0
        if self.resources:
            for resource in self.resources:
                if resource is not None:
                    sum += resource
        return sum

    @property
    def maxSafeResource(self):
        """获取保护资源上限"""
        level = self.getBuildingLevel(2)

        return (level + 1) * 50 * 1000 * 4 + 1000 * 300

    @property
    def realMaxSafeResource(self):
        """真实保护资源上限"""
        level = self.getBuildingLevel(2)

        return (level + 1) * 50 * 1000 * 4

    @property
    def inCVC(self):
        """是否在CVC"""
        if self.loc and self.loc[0] > 10000:
            return True
        return False

    @property
    def inCollectionEvent(self):
        """是否在收集事件"""
        if self.collectionEventTime:
            return self.compareDateTimeWithNow(self.collectionEventTime) > 0
        return False

    @property
    def header(self):
        """header信息"""
        header = {
            "Accept-Encoding": "gzip, identity",
            # "accept": "*/*",
            "TE": "identity",
            "Connection": "Keep-Alive, TE",
            # "accept-language": "zh-CN,zh;q=0.9",
            "Content-Type": "application/x-www-form-urlencoded",
            # "sec-ch-ua": "\" Not A;Brand\";v=\"99\", \"Chromium\";v=\"96\", \"Google Chrome\";v=\"96\"",
            # "sec-ch-ua-mobile": "?0",
            # "sec-ch-ua-platform": "\"macOS\"",
            # "sec-fetch-dest": "empty",
            # "sec-fetch-mode": "cors",
            # "sec-fetch-site": "same-site"
        }
        if self.fakeIP:
            header["X-Forwarded-For"] = self.fakeIP
            header["Client-IP"] = self.fakeIP

        if self.isLogin:
            header["x-access-token"] = self.token
        if self.ua:
            header["user-agent"] = self.ua
        return header

    def distanceWithLoc(self, loc, loc2=None):
        """
        计算2点之间的距离
        """
        if len(loc) == 2:
            return self.distanceWithLoc([0] + loc, loc2)
        if loc2 is None:
            loc2 = self.loc
        if loc2 is None or loc is None:
            print("????")
        return round(
            math.sqrt(math.pow(loc2[1] - loc[1], 2) + math.pow(loc2[2] - loc[2], 2)), 3
        )

    def setNetworks(self, networks: dict):
        """设置网络"""
        if networks:
            kingdoms = networks.get("kingdoms")
            fields = networks.get("fields")
            if kingdoms:
                self.wsKingdom = kingdoms[0]
            if fields:
                self.wsField = fields[0]

    def timestampWithdatetime(self, timeStr):
        """
        返回对应时间的时间戳
        """
        return datetime.datetime.strptime(timeStr, "%Y-%m-%dT%H:%M:%S.%fZ").timestamp()

    def compareTimeWithNow(self, timeEnd):
        """
        比较时间 true 表示在时间范围内
        """
        nowT = time.time() - 8 * 60 * 60
        return timeEnd - nowT

    def compareDateTimeWithNow(self, timeStr):
        return self.compareTimeWithNow(self.timestampWithdatetime(timeStr))

    def loadEvents(self, events):
        """加载事件"""
        self.collectionEventTime = None
        for event in events:
            mastCode = event.get("mastCode")
            if mastCode in [
                601034,
                601035,
                601086,  # 周年庆？
                601088,  # 情人节？
            ]:
                self.collectionEventTime = event.get("endDate")
            # elif mastCode == 602:
            #     self.debuglog("检测到卡冈活动时间")
            #     endDate = event.get("endDate")
            #     if endDate:
            #         self.garantuaEventTime = self.timestampWithdatetime(endDate)

    def loadCvcEvent(self, events):
        """加载cvc活动"""
        for event in events:
            mastCode = event.get("mastCode")
            if mastCode == 601082:
                self.debuglog("检测到大陆cvc活动时间")
                eventId = event.get("_id")
                if eventId and self.realWorld == 20:
                    from Unit.Redis import redisHelper

                    redisHelper.setCvcEventId(eventId)

    def loadPkgs(self, pkgs):
        """加载礼包"""
        if pkgs:
            for pkg in pkgs:
                # 卡冈礼包
                if pkg.get("code") == 80802901:
                    expired = pkg.get("expired")
                    self.debuglog("检测到卡冈活动时间")
                    if expired:
                        self.garantuaEventTime = self.timestampWithdatetime(expired)

    def loadBoosts(self, boost):
        """加载负重信息"""
        for item in boost:
            if item[0] == 62:
                self.loadingInfos[0] = round(item[2], 2)
            elif item[0] == 63:
                self.loadingInfos[1] = round(item[2], 2)
            elif item[0] == 64:
                self.loadingInfos[2] = round(item[2], 2)
            elif item[0] == 69:
                self.loadingInfos[3] = round(item[2], 2)
        self.debuglog(f"负重更新:{self.loadingInfos}")

    def hasItemBuff(self, itemCode):
        """是否有指定buff"""
        if self.buffList:
            for buff in self.buffList:
                if buff.get("param", {}).get("itemCode") == itemCode:
                    return True
        return False

    def hasShield(self):
        """是否有盾"""
        return self.hasItemBuff(10102026)

    def hasPrivateSafe(self):
        """是否有反侦查"""
        return self.hasItemBuff(10102022)

    @property
    def resourceFormat(self):
        """资源格式化"""
        resources = []
        for resource in self.resources:
            if resource > 1_000:
                resource = round(resource / 1_000, 2)
                if resource > 1_000:
                    resource = round(resource / 1_000, 2)
                    if resource > 1_000:
                        resource = round(resource / 1_000, 2)
                        resources.append(f"{resource}B")
                    else:
                        resources.append(f"{resource}M")
                else:
                    resources.append(f"{resource}K")
            else:
                resources.append(f"{resource}")
        return resources

    def printSelf(self):
        self.speedLoadFlag = False
        sumResources = round(self.sumResources / 1000 / 1000.0, 2)
        resources = json.dumps(
            [round(v and int(v) / 1000 / 1000.0 or 0, 2) for v in self.resources]
        )
        resourceFormatStr = ",".join(self.resourceFormat)
        self.log(
            f"等级:{self.level} vipLv:{self.vip} 资源 {resourceFormatStr} 砖石:{self.crystal} 总资源:{sumResources} 坐标 {self.loc} 总兵力 {self.numTroops} 战力 {self.power}"
        )
        from Unit.Redis import redisHelper

        resources = json.loads(resources)
        redisHelper.saveResourceInfo(
            self.key,
            [self.worldId, self.level]
            + resources
            + [self.crystal, self.publicKey and 1 or 0],
        )

    @property
    def cavalrySum(self):
        cavalrySum = 0
        for troop in self.troops:
            code = troop.get("code")
            if code // 100 % 10 == 3 and code % 10 >= 5:
                cavalrySum += troop.get("amount")
        return cavalrySum

    @property
    def dragoCaveLevel(self):
        count = 0
        for building in self.buildings:
            if building["code"] == 40100110:
                level = building["level"]
                count += min(level // 7, 3) + 1
        return max(count, 1)

    @property
    def dragoMaxMintCount(self):
        """龙穴最大铸币次数 最大4次"""
        count = self.dragoCaveLevel
        return min(count, 4)

    def addQuest(self, quest):
        """添加任务"""
        self.questFinishList[f'{quest.get("_id")}_{quest.get("targetCode")}'] = quest

    def returnSortFields(
        self,
        fields: list,
        maxDistance: int = 9999,
        maxLevel: int = 99,
        minLevel: int = 1,
        historyLoc: list = None,
        levelPriority: bool = False
    ) -> list:
        """根据距离排序 - 性能优化版本
        Args:
            fields: 待排序的资源列表，每个元素包含 loc 和 level 字段
            maxDistance: 最大允许距离，默认9999
            maxLevel: 最大允许等级，默认99
            minLevel: 最小允许等级，默认1
            historyLoc: 历史访问位置列表，用于避免重复选择
            levelPriority: 是否优先按等级排序，默认False
        Returns:
            按距离排序后的资源列表，距离相同时按对象id排序
            当levelPriority为True时，按等级降序排序，等级相同时按距离排序
        """
        if not fields:
            return []

        # 将历史位置转换为集合以提高查找效率
        history_set = {tuple(loc) for loc in (historyLoc or [])}

        # 预过滤不符合条件的目标
        def is_valid_target(info: dict) -> bool:
            loc = info.get("loc")
            level = info.get("level")
            return (minLevel <= level <= maxLevel and
                    tuple(loc) not in history_set and
                    self.distanceWithLoc(loc) <= maxDistance)

        # 使用filter和列表推导式优化过滤和排序过程
        valid_fields = filter(is_valid_target, fields)
        if levelPriority:
            # 当levelPriority为True时，先按等级降序，再按距离升序
            sortable_fields = [(-info["level"], self.distanceWithLoc(info["loc"]), info) for info in valid_fields]
            return [field for _, _, field in sorted(sortable_fields, key=lambda x: (x[0], x[1], id(x[2])))]
        else:
            # 原有的排序逻辑：按距离升序，距离相同时按id排序
            sortable_fields = [(self.distanceWithLoc(info["loc"]), info) for info in valid_fields]
            return [field for _, field in sorted(sortable_fields, key=lambda x: (x[0], id(x[1])))]

    def returnSortFieldsWithLevelDes(
        self, fields, clearOccupied=False, clearHidden=False, maxLevel=99
    ):
        """根据等级降序排序"""
        sortFields = []
        fieldLevels = {}

        if fields and len(fields) > 0:
            for info in fields:
                code = info.get("code")
                level = info.get("level")
                hidden = info.get("hidden")
                param = info.get("param")
                if param:
                    value = param.get("value")
                    if level > maxLevel:
                        continue
                if code == 20100105:
                    level = value // 50
                levels = fieldLevels.get(level)
                if levels is None:
                    levels = []
                    fieldLevels[level] = levels
                if clearOccupied:
                    if info.get("occupied") is not None:
                        continue
                if hidden and clearHidden:
                    continue
                levels.append(info)

        if len(fieldLevels):
            keys = list(fieldLevels.keys())
            keys.sort(reverse=True)
            [
                sortFields.extend(
                    self.returnSortFields(fieldLevels[key], maxLevel=maxLevel)
                )
                for key in keys
            ]

        return sortFields

    def rightLaw(self, loc, locs, dp=224):
        """右边规则"""
        tmpGoDirection = self.goDirection + 1
        tmpGoDirection %= 4
        dMap = [[0, 1], [1, 0], [0, -1], [-1, 0]]
        dx = dMap[tmpGoDirection][0]
        dy = dMap[tmpGoDirection][1]
        newLoc = [loc[0] + dx * dp, loc[1] + dy * dp]
        key = f"{newLoc[0]}_{newLoc[1]}"
        if key in locs:
            dx = dMap[self.goDirection][0]
            dy = dMap[self.goDirection][1]
            newLoc = [loc[0] + dx * dp, loc[1] + dy * dp]
            key = f"{newLoc[0]}_{newLoc[1]}"
        else:
            self.goDirection = tmpGoDirection
        locs[key] = 1
        return newLoc

    def loadFieldTasks(self, historyList=None):
        locs = []
        for fieldTask in self.fieldTasks:
            param = fieldTask.get("param")
            if param:
                foLoc = param.get("foLoc")
                if foLoc:
                    locs.append(foLoc)
                    if historyList is not None:
                        historyList.append(foLoc)
                toLoc = param.get("toLoc")
                if toLoc:
                    locs.append(toLoc)
                    if historyList is not None:
                        historyList.append(toLoc)
        return locs

    def clearFieldTasks(self):
        """清理过期任务"""
        needRmoves = []
        for fieldTask in self.fieldTasks:
            expectedEnded = fieldTask.get("expectedEnded")
            if expectedEnded:
                leftSecond = self.compareDateTimeWithNow(expectedEnded)
                if leftSecond < 10:
                    needRmoves.append(fieldTask.get("_id"))
        self.fieldTasks = list(
            filter(lambda x: x.get("_id") not in needRmoves, self.fieldTasks)
        )

    def loadTroopsInfo(self, troops):
        troopMap = {}
        troopRunMap = {}
        moreTroopMap = []
        names = TroopNames
        if not self.noWSUser:
            names = [
                self.redString(names[0]),
                self.yellowString(names[1]),
                self.blueString(names[2]),
            ]
        for supportTroop in troops:
            currentTroops = supportTroop.get("troops")
            status = supportTroop.get("status")
            mo = supportTroop.get("mo")
            kingdom = mo.get("kingdom")
            kingdomName = kingdom.get("name")
            # worldId = kingdom.get("worldId")

            if len(currentTroops) > 1:
                moreTroopMap.append(kingdomName)

            for troop in currentTroops:
                code = troop.get("code")
                name = names[code // 100 % 10 - 1]
                level = code % 10
                amount = troop.get("amount")
                key = f"{name}T{level}"
                writeMap = troopMap
                if status != 1:
                    writeMap = troopRunMap

                if writeMap.get(key):
                    writeMap[key] += amount
                else:
                    writeMap[key] = amount

        return troopMap, troopRunMap, moreTroopMap

    def loadBattleInfo(self, battle):
        leaderKingdom = battle.get("leaderKingdom")
        leaderKingdomName = leaderKingdom.get("name")
        worldId = leaderKingdom.get("worldId")
        if worldId:
            battle["leaderKingdomWorldId"] = worldId
        targetKingdomName = ""
        targetKingdom = battle.get("targetKingdom")
        if targetKingdom:
            targetKingdomName = targetKingdom.get("name")
        else:
            targetBuilding = battle.get("targetBuilding")
            targetShrine = battle.get("targetShrine")
            if targetBuilding:
                info = targetBuilding.get("info")
                name = info.get("name")
                building = targetBuilding.get("building")
                loc = building.get("loc")
                targetKingdomName = f"{name} {loc}"
            elif targetShrine:
                toLoc = battle.get("toLoc")
                targetKingdomName = f"神殿/门 {toLoc[1]}_{toLoc[2]}"
        if targetKingdomName:
            battle["leaderKingdomName"] = leaderKingdomName
            battle["targetKingdomName"] = targetKingdomName
            return True
        return False

    def loadBattleDetailInfo(self, battle):
        rallyTroops = battle.get("rallyTroops")
        troopMap, troopRunMap, moreTroopMap = self.loadTroopsInfo(rallyTroops)
        if troopMap:
            battle["troopMap"] = troopMap
        else:
            battle["troopMap"] = {}

        if troopRunMap:
            battle["troopRunMap"] = troopRunMap
        else:
            battle["troopRunMap"] = {}

        battle["moreTroopMap"] = moreTroopMap or []
        self.loadBattleInfo(battle)

    def checkTroopInfoEqual(self, troop, leaderName):
        # moId = troop.get("moId")
        mo = troop.get("mo")
        # currentTroops = troop.get("troops")
        kingdom = mo.get("kingdom")
        worldId = kingdom.get("worldId")
        name = kingdom.get("name")
        if self.realWorld != worldId:
            return False
        return name != leaderName

    def checkTokenExp(self, expHour=24):
        """校验token是否只剩48小时过期"""
        from Unit.Redis import loadJWTInfo

        info = loadJWTInfo(self.token)
        if info:
            exp = info["exp"]
            leftTime = int(exp) - time.time()
            if leftTime < (expHour - 8) * 60 * 60:
                self.errorLog(f"token 剩余时间不足 {leftTime}秒")
                return True
            else:
                if leftTime < (8 - 8) * 60 * 60 and self.pwd is None:
                    self.errorLog("token 剩余时间不足 剔除")
                    return True
            if checkLocalVersion(info["version"]):
                self.errorLog("token需要刷新")
                return True
        else:
            self.log("checkTokenExp 没有token")
        return False

    def loadEmailWithToken(self):
        """从token中加载名称"""
        from Unit.Redis import loadJWTInfo

        info = loadJWTInfo(self.token)
        if not info:
            return
        iss = info.get("iss")
        if iss == "accounts.google.com":
            self.googleToken = self.token
            self.isGoogle = True
            self.token = None
            return

        kingdomId = info.get("kingdomId")
        self.kingdomId = kingdomId
        if kingdomId and not self.isGoogle:
            if self.email:
                tmpNames = self.email.split("@")
                if len(tmpNames) >= 2:
                    self.email = f"{kingdomId}@{tmpNames[1]}"
            else:
                self.email = f"{kingdomId}@gmail.com"

    def loadKingdomId(self):
        if not self.kingdomId:
            self.kingdomId = self.tokenWithKingomId()

    def tokenWithKingomId(self):
        from Unit.Redis import loadJWTInfo

        info = loadJWTInfo(self.token)
        kingdomId = info.get("kingdomId")
        return kingdomId

    def locInShrine(self, loc):
        """判断是否在神殿"""
        for shrineLoc in ShrineLocs:
            if abs(shrineLoc[0] - loc[0]) <= 3 and abs(shrineLoc[1] - loc[1]) <= 3:
                return True
        return False

    def loadRedisToken(self):
        from Unit.Redis import redisHelper

        return redisHelper.getToken(
            self.email, self.userKey, self.kingdomId, self.appleSubId
        )

    def ping(self):
        if self.kingdomId:
            from Unit.Redis import redisHelper

            redisHelper.ping(self.kingdomId)

    def pong(self):
        if self.kingdomId:
            from Unit.Redis import redisHelper

            return redisHelper.pong(self.kingdomId)
        return False

    def redString(self, string):
        """
        红色字体
        """
        return f"\033[31m{string}\033[0m"

    def blueString(self, string):
        """
        蓝色字体
        """
        return f"\033[34m{string}\033[0m"

    def yellowString(self, string):
        """
        黄色字体
        """
        return f"\033[33m{string}\033[0m"

    def barkNoti(self, msg, isAdmin=False):
        from Api.BarkApi import barkSendMsg

        name = self.name or self.email
        msg = f"{name} {msg}"
        if not self.useBarkNoti or not name:
            return
        if self.userBarkKey and len(self.userBarkKey) > 0:
            barkSendMsg("挂机通知", msg, self.userBarkKey, "王国")
        if isAdmin:
            [barkSendMsg("挂机通知", msg, key, "王国") for key in AdminBarkKeys]

    def buildMarchTroop(self, code, amount=0):
        return {
            "code": code,
            "level": 0,
            "select": 0,
            "amount": amount,
            "dead": 0,
            "wounded": 0,
            "seq": 0,
        }

    def reSortMarchTroops(self, marchTroops):
        """
        根据code重新排列部队
        Args:
            marchTroops: 需要排序的部队列表
        Returns:
            排序后的部队列表
        """
        # 1. 先根据code % 10分组,同一组的按code升序排列
        level_groups = {}
        for troop in marchTroops:
            level = troop["code"] % 10  # 获取等级
            if level not in level_groups:
                level_groups[level] = []
            level_groups[level].append(troop)

        # 对每个等级组内的部队按code升序排列
        for level in level_groups:
            level_groups[level].sort(key=lambda x: x["code"])

        # 2. 不同组的按code % 10降序排列
        sorted_levels = sorted(level_groups.keys(), reverse=True)

        # 3. 得出最终的marchTroops
        result = []
        for level in sorted_levels:
            result.extend(level_groups[level])

        return result

    def getLoadingForTroop(self, code, maxLoading, maxAmount):
        """获取兵种的负重"""
        troopType = code // 100 % 10
        troopLevel = code % 10
        troopLoading = LoadingList[troopType - 1][troopLevel - 1] * (
            1 + self.loadingInfos[troopType - 1] + self.loadingInfos[-1]
        )
        troopLoading = math.floor(troopLoading)
        needAmount = math.ceil(maxLoading / troopLoading)
        if needAmount > maxAmount:
            needAmount = maxAmount
        return needAmount, needAmount * troopLoading

    # 从缓存数据中获取兵力
    def buildMarchTroopFromKingdom(self, troopCode, amount):
        for troop in self.kingdomTroops:
            if int(troop.get("code")) == troopCode:
                _amount = troop.get("amount")
                if _amount >= amount:
                    troop["amount"] = _amount - amount
                    return self.buildMarchTroop(troopCode, amount)

        if troopCode % 10 > 1:
            return self.buildMarchTroopFromKingdom(troopCode - 1, amount)
        else:
            return None

    # 获取科技等级
    def getResearchInfoLevel(self, researchCode):
        info = self.getResearchesInfo(researchCode)
        if not info:
            return 0
        return info["level"]

    # 检测研究自定义等级
    def checkResearchLevel(self):
        if self.researchGrade == 0:
            self.researchGrade = 1

        if self.researchGrade <= 2:
            # 资源生产
            resourceProductionLevel = self.getResearchInfoLevel(30103001)
            group2Code = 30102034
            if self.level > 30:
                # 35级生产最后一个
                group2Code = 30102044
            group2Level = self.getResearchInfoLevel(group2Code)
            if resourceProductionLevel == 10 and group2Level >= 5:
                self.researchGrade = 2

        if self.researchGrade >= 2:
            # T5
            group1Codes = [30101053, 30101054, 30101055]
            if self.level >= 35:
                # 队列+2
                group1Codes = [30101064]
            for code in group1Codes:
                if self.getResearchInfoLevel(code) < 1:
                    return
            self.researchGrade = 3

        if self.researchGrade >= 3:
            # 生成全部满级
            if self.checkResearchFull(30102044, 10) == (None, None):
                self.researchGrade = 4

        if self.researchGrade >= 4:
            # 战斗最后一个5级
            if self.getResearchInfoLevel(30101083) >= 5:
                self.researchGrade = 5

        if self.researchGrade >= 5:
            # 医院2
            if self.checkResearchFull(30101069, 10) == (None, None):
                self.researchGrade = 6

        if self.researchGrade >= 6:
            # 先进全满
            codeGroup = [30103040, 30103037, 30103034]
            for code in codeGroup:
                if self.checkResearchFull(code, 10) != (None, None):
                    return
            self.researchGrade = 7

        if self.researchGrade >= 7:
            # 35级战斗全满
            if self.checkResearchFull(30101083, 10) == (None, None):
                self.researchGrade = 8

    def checkSelfAttacked(self, battle):
        targetKingdomId = battle.get("targetKingdomId")
        if targetKingdomId and targetKingdomId == self.kingdomId:
            self.log("正在被攻击!")
            self.barkNoti("正在被攻击!", isAdmin=self.level >= 35)
            return True
        return False

    def canWorkDragos(self, availableDragos):
        # 有指定工作龙
        if self.workDragos and availableDragos:
            workDragos = list(
                filter(
                    lambda x: x.get("tokenId") in self.workDragos
                    and x.get("lair", {}).get("status", 0) == 1,
                    availableDragos,
                )
            )
            if len(workDragos) > 0:
                return workDragos
        return None


if __name__ == "__main__":
    user = User()
    print(user.__dict__)
