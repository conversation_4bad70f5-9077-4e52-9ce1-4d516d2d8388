# /bin/env python3
# -*- coding: UTF-8 -*- 

# 用户异常
class UserError(Exception):
    def __init__(self, message,errorCode = 0):
        self.message = message
        self.errorCode =errorCode

    def __str__(self):
        return self.message

    @classmethod
    def webNoOnline(cls):
        return cls("掉线了",-8)
        
    @classmethod
    def noKing(cls):
        return cls("没有王国",-11)
    
    @classmethod
    def noKingdom(cls):
        return cls("没有王国",-14)

    @classmethod
    def noEnoughCrystals(cls):
        return cls("没有足够的水晶",-12)
    
    @classmethod
    def noEnoughResources(cls):
        return cls("资源异常移民号",-13)

    @classmethod
    def immigrationFail(cls):
        return cls("移民异常",-13)

    @classmethod
    def notInAlliance(cls):
        return cls("没有联盟",-15)
    
    @classmethod
    def noauth(cls,message="token失效"):
        return cls(message,-4)

    @classmethod
    def limitRetry(cls):
        return cls("系统限制",-5)

    @classmethod
    def limitLater(cls):
        return cls("登录限制 10分钟后再试",-7)

    @classmethod
    def pushIdError(cls):
        return cls("推送ID错误",-6)
        
    @classmethod
    def needEmailAuth(cls):
        return cls("需要邮箱验证",1)
    
    @classmethod
    def mismatchPassword(cls):
        return cls("密码不匹配",1)

    @classmethod
    def captcha(cls):
        return cls("需要验证码")

    @classmethod
    def googleCaptcha(cls):
        return cls("需要谷歌验证码",31)

    @classmethod
    def invalidEmail(cls):
        return cls("邮箱无效",33)
    @classmethod
    def captchaError(cls):
      return cls("连续5次验证码错误")

    @classmethod
    def logout(cls):
        return cls("主动退出")

    @classmethod
    def noUsers(cls):
        return cls("没有账号",41)

    @classmethod
    def banteleport(cls):
        return cls("被封禁传送地方")

    @classmethod
    def noTeleportItem(cls):
        return cls("没有飞行道具")

    @classmethod
    def toMuchTeleportItem(cls):
        return cls("尝试飞行太多次失败，滚吧",1)

    @classmethod
    def hasMarch(cls):
        return cls("还在挂机/有行军",1)

    @classmethod
    def lowPower(cls):
        return cls("战五渣",1)
    
    @classmethod
    def levelTooHigh(cls):
        return cls("等级过高",1)
        
    @classmethod
    def finish(cls):
        return cls("完成停止")

    @classmethod
    def sock5Error(cls):
        return cls("sock5异常",-3)

    @classmethod
    def threadExit(cls):
        return cls("线程主动退出",-2)

    @classmethod
    def noKindom(cls):
        return cls("哦吼！你号没了？",41)

    @classmethod
    def noAccount(cls):
        return cls("哦吼！账号不存在!",41)
    
    @classmethod
    def banUser1(cls):
        return cls("哦吼！账号被封禁!",41)

    @classmethod
    def deletedWithinPeriod(cls):
        return cls("账号被删除",41)
        
    @classmethod
    def exceedGuestLimit(cls):
        return cls("超过游客限制",6)

    @classmethod
    def exceedAccountIp(cls):
        return cls("超过账号IP限制",7)

    @classmethod
    def googleAuthError(cls):
        return cls("谷歌token超时",34)

    @classmethod
    def ininsufficientResources(cls):
        return cls("资源不足",11)

    @classmethod
    def notAvailableEmail(cls):
        return cls("邮箱渠道不可用",32)

    @classmethod
    def notAvailableRegister(cls):
        return cls("注册失败",99)

    @classmethod
    def EncryptKeyChange(cls):
        return cls("加密变更",42)

    @classmethod
    def dragoMultipleMinting(cls):
        return cls("龙卡mint了",101)