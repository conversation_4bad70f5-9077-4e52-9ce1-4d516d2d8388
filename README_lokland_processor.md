# LOK Land Data Processor

这个工具用于从本地CSV文件中读取landId，通过`user.landInfo(landId)`获取loka数据，并将结果写回CSV文件。

## 文件说明

1. **process_lokland_data.py** - 主要的处理脚本
2. **test_lokland_processor.py** - 测试脚本，用于验证功能
3. **lokland_data.csv** - 输入的CSV文件，包含landId等信息

## 使用方法

### 1. 测试功能

首先运行测试脚本确保功能正常：

```bash
python test_lokland_processor.py
```

这个脚本会：
- 测试单个landId的landInfo调用
- 处理前5条记录进行验证

### 2. 处理完整数据

使用主脚本处理所有数据：

```bash
# 使用默认参数
python process_lokland_data.py

# 指定输入输出文件
python process_lokland_data.py --input lokland_data.csv --output result.csv

# 指定token和代理
python process_lokland_data.py --token "your_token" --socks5 "127.0.0.1:1087"
```

### 3. 参数说明

- `--input, -i`: 输入CSV文件路径（默认: lokland_data.csv）
- `--output, -o`: 输出CSV文件路径（默认: lokland_data_with_loka.csv）
- `--token, -t`: 用户token（可选，有默认值）
- `--socks5, -s`: 代理设置（可选，格式: host:port）

## 输入文件格式

输入的CSV文件应包含以下列：
- `land_id`: 土地ID
- 其他列会被保留到输出文件中

示例：
```csv
land_id,coordinates,x_coordinate,y_coordinate,price_eth,full_name
160208,"(384,1880)",384,1880,0.0188,"LOK Land #160208 (x:384,y:1880)"
150769,"(648,1584)",648,1584,0.0189,"LOK Land #150769 (x:648,y:1584)"
```

## 输出文件格式

输出文件会在原有列的基础上添加以下列：
- `loka_data`: 完整的landInfo数据（JSON格式）
- `loka_amount`: loka数量
- `loka_status`: 处理状态（success/failed/error）
- `process_time`: 处理时间

## 功能特点

1. **错误处理**: 对每个landId的处理都有独立的错误处理
2. **进度显示**: 每处理10条记录显示一次进度
3. **延迟控制**: 每次请求间隔0.5秒，避免请求过快
4. **数据完整性**: 保留原始CSV的所有列
5. **状态跟踪**: 记录每条记录的处理状态和时间

## 注意事项

1. 确保网络连接正常
2. 如果使用代理，确保代理服务器可用
3. token需要有效且有足够的权限
4. 处理大量数据时可能需要较长时间
5. 建议先用测试脚本验证功能

## 错误排查

如果遇到问题：

1. 检查输入文件是否存在且格式正确
2. 验证token是否有效
3. 确认网络连接和代理设置
4. 查看日志输出了解具体错误信息

## 示例输出

```
2025-08-01 10:00:00 - INFO - 初始化用户连接...
2025-08-01 10:00:01 - INFO - 开始处理 lokland_data.csv 文件
2025-08-01 10:00:02 - INFO - 处理第 1 行，landId: 160208
2025-08-01 10:00:03 - INFO - landId 160208 获取成功，loka数量: 1500
2025-08-01 10:00:04 - INFO - 处理第 2 行，landId: 150769
2025-08-01 10:00:05 - INFO - landId 150769 获取成功，loka数量: 2300
...
2025-08-01 10:05:00 - INFO - 处理完成！
2025-08-01 10:05:00 - INFO - 总计处理: 194 条记录
2025-08-01 10:05:00 - INFO - 成功: 190, 失败: 3, 错误: 1
2025-08-01 10:05:00 - INFO - 结果已保存到 lokland_data_with_loka.csv
```
