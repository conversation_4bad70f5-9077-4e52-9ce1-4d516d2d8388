
import asyncio
import time,random,json
from time import sleep
from Unit.Logger import logger
import threading

from selenium import webdriver
from selenium.webdriver.common.desired_capabilities import DesiredCapabilities
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import WebDriver<PERSON>ait

import re
from Unit.ChromeExtension import easy_create_proxyauth_extension
import sys,os
isDebug = True if sys.gettrace() else False

class GoogleSelenium:
    def __init__(self, username, password,proxy=None):
        self.username = username
        self.password = password
        self.proxy = proxy
    
    def runLoginGoogle(self):
        logger.info(f'{self.username} 开始登录 {self.proxy is not None and self.proxy or "无代理"}')
        try:
            co = webdriver.ChromeOptions()
            # co.add_argument('--headless')
            if self.proxy:
                proxyauth_plugin_path = easy_create_proxyauth_extension(self.proxy)
                co.add_extension(proxyauth_plugin_path)
            # else:
            #     co.add_argument('--headless')
            sv = Service('./chromedriver')
            self.driver = webdriver.Chrome(options=co,service=sv)
            self.driver.implicitly_wait(15)

            # self.driver.get('https://stackoverflow.com/users/login?ssrc=head&returnurl=https%3a%2f%2fstackoverflow.com%2f')
            self.driver.get(f"https://accounts.google.com/o/oauth2/auth/oauthchooseaccount?redirect_uri=storagerelay%3A%2F%2Fhttps%2Fplay.leagueofkingdoms.com%3Fid%3Dauth{random.randint(********, *********)}&response_type=permission%20id_token&scope=email%20profile%20openid&openid.realm&include_granted_scopes=true&client_id=************-1j7bt4qbkkniu3e4d6a5muqv4668p4la.apps.googleusercontent.com&ss_domain=https%3A%2F%2Fplay.leagueofkingdoms.com&prompt=select_account&fetch_basic_profile=true&gsiwebsdk=2&flowName=GeneralOAuthFlow")
            # sleep(3)
            # self.driver.find_element_by_xpath('//*[@id="openid-buttons"]/button[1]').click()
            t = 0

            if self.tryFind('//input[@type="email"]'):
                self.driver.find_element(by=By.XPATH,value='//input[@type="email"]').send_keys(self.username)
                self.driver.find_element(by=By.XPATH,value='//*[@id="identifierNext"]').click()
            else:
                pass

            # time.sleep(3)

            if self.tryFind('//input[@type="password"]'):
                # time.sleep(2)
                self.driver.find_element(by=By.XPATH,value='//input[@type="password"]').send_keys(self.password)
                self.driver.find_element(by=By.XPATH,value='//*[@id="passwordNext"]').click()
            else:
                pass

            # time.sleep(3)
            while self.driver.current_url[:40] != "https://accounts.google.com/signin/oauth":
                sleep(1)
                print("等待重定向")
                if self.driver.current_url[:37] == "https://accounts.google.com/speedbump":
                    try:
                        self.driver.find_element(by=By.XPATH,value='//input[@type="submit"]').click()
                        # sleep(5)
                    except:
                        pass
            
            response = None
            while response is None:
                # time.sleep(2)
                zWl5kd = self.driver.find_element(by=By.CLASS_NAME,value="zWl5kd")
                if zWl5kd:
                    response = zWl5kd.get_attribute("data-credential-response")
                    if not response:
                        continue
                    # print(response)
                    location = re.search("ey.+", response).span()
                    if location:
                        tokenString = response[location[0]:]
                        token = tokenString.split('\\')[0]
                        logger.debug(token)
                        self.driver.quit()
                        # self.appendFile('tmpaccount/succeed.txt', f'{username}----{password}----{token}\n')
                        return token
            logger.error("error?")
        except Exception as e:
            logger.debug(e,exc_info=True)
            logger.error("谷歌异常模拟登录失败")
            # self.appendFile('tmpaccount/fail.txt', f'{username}----{password}----\n')

        return None

    def tryFind(self,xpath,reply=0):
        WebDriverWait(self.driver, 15).until(EC.element_to_be_clickable((By.XPATH, xpath)))
        return True
        if reply > 50:
            return False
        try:
            self.driver.find_element(by=By.XPATH,value=xpath)
            return True
        except:
            time.sleep(1)
            return self.tryFind(xpath,reply+1)

    def appendFile(self,filepath,text):
        fileLock.acquire()
        with open(filepath,'a+') as f:
            f.write(text)
        fileLock.release()