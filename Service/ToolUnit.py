#!/usr/bin/python3
# -*- coding: utf-8 -*-
"""
功能描述：工具类
编写人：darkedge
编写日期：2023年09月26日
   
"""


import threading
import time

from Api.Ipinfo import getIP,get_generate_204
from Unit.FileTool import loadS5List
from Unit.Logger import logger
from concurrent.futures import Thread<PERSON>oolExecutor
from tqdm import tqdm

def testLocalSocks5(path="./socks5.txt"):
    logger.info("代理测试")
    s5List = loadS5List(path)
    succeeds = []
    fails = []
    loadIPs = {}
    ths = []
    for s5 in s5List:

        def run(s5):
            available = False
            try:
                ip = getIP(s5)
                if ip:
                    loadIPs[ip] = s5
                    available = True
                    print(f"{s5} ip:{ip}")
            except Exception:
                pass
            finally:
                if available:
                    succeeds.append(f"||{s5}")
                else:
                    fails.append(f"||{s5}")

        th = threading.Thread(target=run, args=(s5,), daemon=True)
        ths.append(th)
        th.start()
        time.sleep(0.2)

    [th.join() for th in ths]
    return fails, loadIPs

def testLocalSocks5Speed(path=None,max_workers=30):
    logger.info("代理测速度")
    if path is None:
        path = "./socks5.txt"
    s5List = loadS5List(path)
    fails = []
    delays = []

    # 使用tqdm显示进度条
    with tqdm(total=len(s5List)) as pbar:
        def local_get_generate_204(s5):
            res,ip = get_generate_204(s5,hasIP=False)
            pbar.update(1)
            return res,ip

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            results = executor.map(local_get_generate_204, s5List)
            for s5, res in zip(s5List, results):
                delay = res[0]
                ip = res[1]
                if delay != -1:
                    delays.append((delay, s5, ip))
                    ipStr = ip and f'ip:{ip}' or ''
                    logger.debug(f"{s5} delay:{delay} {ipStr}")
                else:
                    fails.append(s5)
        delays = sorted(delays, key=lambda x: x[0])
    return fails, delays