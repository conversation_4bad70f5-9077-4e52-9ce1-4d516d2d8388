import logging
import sys
from typing import Callable
from zeroless import (Client, Server, log)
import threading
from time import sleep

isDebug = True if sys.gettrace() else False

ZeroLocalPort = 10100
ZeroTopic = b"league"

ZeroSendLock = threading.Lock()

hasAddLog = False
class ZeroService(object):
    client:Client = None
    sh:threading.Thread = None
    logInfo = None
    callback:Callable = None
    
    def __init__(self,logInfo = None, debugMode = False) -> None:
        # Setup console logging
        if isDebug or debugMode:
            global hasAddLog
            if not hasAddLog:
                hasAddLog = True
                consoleHandler = logging.StreamHandler()
                log.setLevel(logging.DEBUG)
                log.addHandler(consoleHandler)

        self.client = Client()
        self.client.connect_local(ZeroLocalPort)
        if logInfo:
            self.logInfo = logInfo

    def __del__(self):
        self.client.disconnect_all()
        if self.logInfo:
            self.logInfo("zeroclient deinit")

    def work(self):
        listenForPub = self.client.sub([ZeroTopic])
        self.log("ZeroService start")
        try:
            for _topic,msg in listenForPub:
                self.log(f"ZeroService receive:{msg}")
                if self.callback and callable(self.callback):
                    self.callback(msg)
        except Exception as e:
            self.log(f"ZeroService error:{e}")

    def start(self,callback):
        self.callback = callback
        self.th = threading.Thread(target=self.work, name="ZeroService",daemon=True)
        self.th.start()
        return self
    
    def end(self):
        self.client.disconnect_all()

    def hold(self):
        self.th.join()

    def log(self,msg):
        if callable(self.logInfo):
            self.logInfo(msg)

    @classmethod
    def send(cls, msg):
        if isinstance(msg,str):
            msg = msg.encode("utf-8")
        with ZeroSendLock:
            pub = Server(ZeroLocalPort).pub(ZeroTopic, embed_topic=True)
            sleep(0.5)
            pub(msg)
            
        # sleep(1)

class ZeroClient(object):
    service:Server = None
    logInfo = None
    def __init__(self,logInfo = None) -> None:
        # Setup console logging
        consoleHandler = logging.StreamHandler()
        log.setLevel(logging.DEBUG)
        log.addHandler(consoleHandler)

        self.service = Server(ZeroLocalPort)
        
        if logInfo:
            self.logInfo = logInfo

    def log(self,msg):
        if callable(self.logInfo):
            self.logInfo(msg)

    def send(self, msg):
        pub = self.service.pub(ZeroTopic, embed_topic=True)
        pub(msg)
    
if __name__ == "__main__":
    z = ZeroService()
    z.start()
    z.hold()