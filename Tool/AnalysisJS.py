# /bin/env python3
# -*- coding: UTF-8 -*- 


if __name__ == '__main__':
    availableLines = []
    with open("./some.js") as f:
        lines = f.readlines()
        mustWrite = False
        for line in lines:
            if len(line) > 48 and line[:48]== '''fetch("https://lok-api-live.leagueofkingdoms.com''':
                mustWrite = True
            elif len(line) == 6 and line == '''}); ;\n''':
                if mustWrite:
                    availableLines.append(line)
                mustWrite = False
            
            if mustWrite:
                availableLines.append(line)
    
    with open('./finish.js',"w+") as f:
        f.writelines(availableLines)
    
