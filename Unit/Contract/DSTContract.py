

from Unit.EthUnit import EthService,web3,Web3
from Unit.Logger import logger
import json

def loadABIJson(path):
    with open(path) as f:
        return json.loads(f.read())
    

ADDRESS = "******************************************"
ABI = loadABIJson("Unit/Contract/DST.json")

class DstContract(object):
    def __init__(self):
        self.contract = EthService.contractConnection(ADDRESS, ABI)

    def getBalance(self, address):
        return Web3.from_wei(self.contract.functions.balanceOf(address).call(),"ether")

    def claimMintRewardAndShare(self, address, toAddress, amount):
        return self.contract.functions.transfer(address, amount).buildTransaction({"from": address})
