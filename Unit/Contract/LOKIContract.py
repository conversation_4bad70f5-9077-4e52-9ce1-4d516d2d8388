

from Unit.EthUnit import EthService,web3,Web3
from Unit.Logger import logger
import json

def loadABIJson(path):
    with open(path) as f:
        return json.loads(f.read())
    

ADDRESS = "******************************************"
ABI = loadABIJson("Unit/Contract/LOKI.json")

class LOKIContract(object):
    def __init__(self):
        self.contract = EthService.contractConnection(ADDRESS, ABI)

    def tokensOfOwner(self, address):
        return self.contract.functions.tokensOfOwner(address).call()

    def tokenOfOwnerByIndex(self, address, index):
        return self.contract.functions.tokenOfOwnerByIndex(address, index).call()
        
    def tokens(self, address):
        return self.contract.functions.tokens(address,1).call()

    def getBalance(self, address):
        return self.contract.functions.balanceOf(address).call()

    def transfer(self, address, tokenId):
        return self.contract.functions.transfer(address, tokenId).buildTransaction({"from": address})
    
    def transferFrom(self, fromAddress,toAddres, tokenId):
        return self.contract.functions.transferFrom(fromAddress,toAddres, tokenId).buildTransaction({"from": fromAddress})
