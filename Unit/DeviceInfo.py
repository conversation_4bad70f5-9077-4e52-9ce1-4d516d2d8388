# /bin/env python3
# -*- coding: UTF-8 -*-
"""
功能描述：生成一套设备信息
编写人：darkedge
编写日期：2021年12月23日
   
"""

import json
import random
import secrets
import string
import threading
import time

# trunk-ignore(ruff/F401)
from base64 import b64decode, b64encode

from Unit.Redis import loadJWTInfo, redisHelper

# 大版本.UI版本.数据版本.API版本
version = "1.1816.164.252"
iOSVersion = "1.1630.136.207"
# ebaf17c68d5eadd103fea26d4f92a2c4bf61fdff:9fd1b5cb739b477e3db490d59728635f26ecd77b3b33198110ccbdd23c66a1cc
# carv client_id:client_secret
versionLock = threading.Lock()


def checkGameVersion():
    with versionLock:
        needClear = False
        _version = redisHelper.getGameVersion(True)
        if _version is None or _version != version:
            needClear = True
        if not needClear:
            _IOSVersion = redisHelper.getGameVersion(False)
            if _IOSVersion is None or _IOSVersion != iOSVersion:
                needClear = True
        if needClear:
            redisHelper.setGameVersion(True, version)
            redisHelper.setGameVersion(False, iOSVersion)
            # 2025.01.21 不再清除token
            # redisHelper.clearSomeKeys()


localnames = None


def loadNamesWithOne():
    global localnames
    if localnames is None:
        with open("name.txt", "r") as f:
            localnames = [s.strip() for s in f.readlines()]
    return secrets.choice(localnames)


def createName() -> str:
    name = loadNamesWithOne()
    i = secrets.randbelow(2) + 1
    for _i in range(i):
        index = secrets.randbelow(len(name))
        char = secrets.choice(string.ascii_letters + string.digits)
        names = list(name)
        names.insert(index, char)
        name = "".join(names)
    return name


def createName2() -> str:
    i = secrets.randbelow(8) + 2
    strValue = ""
    for _i in range(i):
        strValue += secrets.choice(string.ascii_letters + string.digits)
    return strValue


def createUAWithOS(OS) -> str:
    # Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36
    chromeVersion = "*********"  # "96.0.4664.%d" % random.randint(100,110)

    if OS[:3] == "Mac":
        return (
            "Mozilla/5.0 (Macintosh; Intel %s) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/%s Safari/537.36"
            % (OS, chromeVersion)
        )
    elif OS[:3] == "Win":
        return (
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/%s Safari/537.36 Edg/96.0.1054.43"
            % chromeVersion
        )
    elif OS[:3] == "iOS":
        return "BestHttp"
    return None


def createMacDeviceInfo(isIOS=True):
    platform = None
    language = None
    OSValue = None
    dversion = None
    bundle = ""
    if isIOS:
        OSValue = f"iOS 15.{secrets.randbelow(3) + 3}"
        dversion = iOSVersion
        language = "ChineseSimplified"
        platform = "ios"
        bundle = "com.nplusent.lok"

    else:
        mainNum = secrets.randbelow(15) + 1
        lastNum = 0
        if mainNum == 16:
            lastNum = secrets.randbelow(5)
        else:
            lastNum = secrets.randbelow(8) + 1

        OSValue = "Mac OS X 10_%d_%d" % (mainNum, lastNum)
        OSValue = secrets.choice([OSValue, "Windows 10"])
        languages = ["Chinese", "English", "Japanese"]
        language = secrets.choice(languages)
        platform = "web"
        dversion = version

    return {
        "OS": OSValue,
        "country": "USA",
        "language": language,
        "version": dversion,
        "platform": platform,
        "build": "global",
        "bundle": bundle,
    }


def saveDeviceInfo(
    deviceInfo, email=None, userKey=None, appleSubId=None, kingdomId=None
):
    return redisHelper.saveDeviceInfo(
        deviceInfo, email, userKey, appleSubId, kingdomId=kingdomId
    )


def getDeviceInfo(
    email=None, userKey=None, appleSubId=None, kingdomId=None, isIOS=True
):
    if email is None and userKey is None and appleSubId is None and kingdomId is None:
        return None
    deviceInfo = redisHelper.getDeviceInfo(
        email, userKey, appleSubId, kingdomId=kingdomId
    )
    if deviceInfo is None:
        deviceInfo = createMacDeviceInfo(isIOS=isIOS)
        saveDeviceInfo(deviceInfo, email, userKey, appleSubId, kingdomId=kingdomId)
    if deviceInfo["platform"] == "web":
        deviceInfo["version"] = version
    else:
        deviceInfo["version"] = iOSVersion
    return deviceInfo


def removeDeviceInfo(email=None, userKey=None, appleSubId=None, kingdomId=None):
    if email is None and userKey is None and appleSubId is None and kingdomId is None:
        return None
    return redisHelper.removeDeviceInfo(email, userKey, appleSubId, kingdomId=kingdomId)


def makeAppleToken(subId):
    jwtHead = "eyJraWQiOiJmaDZCczhDIiwiYWxnIjoiUlMyNTYifQ"
    jwtFoot = "FSiCH5hm7KA-Ins7Qm5bPLNQIN-U7KEJNaaZyzHu6ulcxVEzeKqi8VF5mgI7OBEzevw9qzhXBnb-ClKaji5GJ8WHxqjsebvw9BkAmTg4J-FpyKl34YwsSOVJ6j6OWflsyaKrKRyWKpOVDPiY62FnzzTiCTteSRxPvQyWTB3NO2f4VUld0pWpmOrEf3GvcxBLUbYO1zp3gRq9P0UjvvMPsh1FKqiYlSQT5gJYtE1xIDQ5q_7OO5ZUnDuQSQvME5J9bqZLz3RWspUPQ_GxhjnR4Pk48Ad-wnrV9f-5yV5Zx31fz0ohVBPfO4bQ6c6fss_rXVsJxKLgUq89dmjg3IA9IQ"
    t = time.time()
    iat = int(t) - 60
    jwtData = {
        "iss": "https://appleid.apple.com",
        "aud": "com.nplusent.lok",
        "exp": iat + 61 * 60 * 24,
        "iat": iat,
        "sub": f"001689.{subId}.0957",  # "001689.4dbcdbd002fd4bcdba8090feab609fd2.0957"
        "c_hash": "W9TRskYQUI1uzFKABY-VDA",
        "email": f"{subId}@privaterelay.appleid.com",
        "email_verified": "true",
        "is_private_email": "true",
        "auth_time": iat,
        "nonce_supported": True,
    }
    jwtB64Value = b64encode(json.dumps(jwtData).encode()).decode()
    jwt = f'{jwtHead}.{jwtB64Value.strip("=")}.{jwtFoot}'
    return jwt


def createUserKey():
    userKeys = []
    counts = [8, 4, 4, 4, 12]
    for i in counts:
        userKeys.append("".join(random.sample(string.hexdigits + string.digits, i)))

    return "-".join(userKeys).lower()


def createAppleSubId():
    userKey = createUserKey()
    return userKey.replace("-", "")


def randomEmailPrefix(minSize=7, maxSize=11):
    n = minSize
    if maxSize - minSize > 0:
        n = secrets.randbelow(maxSize - minSize) + minSize

    return "".join(random.sample(string.hexdigits + string.digits, n)).lower()


def createIOSNotificationIdentifier():
    userKeys = []
    for _i in range(4):
        userKeys.append("".join(random.sample(string.hexdigits + string.digits, 16)))
    identifier = "".join(userKeys).lower()
    return identifier


def createChromeNotificationIdentifier():
    return f"https://fcm.googleapis.com/fcm/send/fEKWNKnTkgk:APA91bFjKJ9o-8UBUTvy63MaBUwXyhBlX_OAKGYcqEB7sT6QUWqYI611o8MWmSrUNxcT0cA11upro31SZXJl78bK0SYDw0Yp63Mws-hoGvQ-wGD6_2tEE0I-{randomEmailPrefix(20,20)}"


def checkTokenVersion(token):
    # 校验token版本和当前版本
    tokenInfo = loadJWTInfo(token)
    tokenVersion = int(tokenInfo.get("version", 0))
    return checkLocalVersion(tokenVersion)


def checkLocalVersion(ver):
    versions = version.split(".")
    localVersion = int(versions[1])
    if ver < localVersion - 5:
        return True
    return False


def getUIVersion():
    return int(version.split(".")[1])


def getTableVersion():
    return int(version.split(".")[2])


checkGameVersion()
