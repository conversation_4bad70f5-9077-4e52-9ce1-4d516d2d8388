# /bin/env python3
# -*- coding: UTF-8 -*-

"""
功能描述：区块链相关
编写人：darkedge
编写日期：2022年02月08日
   
"""

# import json
import random
import time
from decimal import Decimal

import requests
from eth_account.messages import encode_defunct
from web3 import Account, HTTPProvider, Web3
from web3.middleware import ExtraDataToPOAMiddleware

from Unit.Logger import logger

rpcs = [
    # 'https://rpc-mainnet.matic.network',
    "https://polygon-mainnet.blastapi.io/8d0d5706-0720-4268-975e-2a9a0a84a466",
    "https://polygon-rpc.com",
    # "https://rpc-mainnet.matic.quiknode.pro/",
    # "https://polygon-mainnet.rpcfast.com?api_key=xbhWBI1Wkguk8SNMu1bvvLurPGLXmgwYeC4S6g2H7WdwFigZSmPWVZRxrskEQwIf",
]
rpc = random.choice(rpcs)
web3 = None
initFlag = False
fixedMode = False
isBSC = False
isArenaZ = False

MaxBaseFeePerGas = Web3.to_wei(80, "Gwei")
MaxGasPrice = Web3.to_wei(150, "Gwei")

gasMap = {
    1: 1,  # eth
    56: 1,  # bsc
    137: 20,  # matic
    7897: 20, # Arena-Z
}

basePriorityFeePerGas = {
    1: 1.5,  # eth
    56: 1.5,  # bsc
    137: 40,  # matic
    7897: 0.000, # Arena-Z
}

maxPriorityFeePerGasMap = {1: 1.5, 56: 1, 137: 30, 7897: 1}  # eth  # bsc  # matic

# 小费优先级
priorityGasMap = {
    60:35,
    100:40,
    160:45,
    200:60
}

def initWeb3(rpcAddress, request_kwargs=None):
    global web3, initFlag
    print(f"使用节点{rpcAddress}")
    web3 = Web3(HTTPProvider(rpcAddress, request_kwargs=request_kwargs))
    web3.middleware_onion.inject(ExtraDataToPOAMiddleware, layer=0)
    initFlag = True
    print(
        f"MaxGasPrice{Web3.from_wei(MaxGasPrice,'Gwei')} MaxBaseFeePerGas:{Web3.from_wei(MaxBaseFeePerGas,'Gwei')}"
    )


def modifyWeb3Socks5(socks5):
    proxies = {"https": f"socks5://{socks5}", "http": f"socks5://{socks5}"}
    initWeb3(rpc, request_kwargs={"proxies": proxies})


def changeGasPrice(gasPrice, baseFeePerGas):
    global MaxGasPrice, MaxBaseFeePerGas
    MaxGasPrice = Web3.to_wei(gasPrice, "Gwei")
    MaxBaseFeePerGas = Web3.to_wei(baseFeePerGas, "Gwei")
    print(f"MaxGasPrice{gasPrice} MaxBaseFeePerGas:{baseFeePerGas}")

def useArenaZ():
    global isArenaZ, rpc
    isArenaZ = True
    rpc = "https://rpc.arena-z.gg"
    initWeb3(rpc, web3.provider.get_request_kwargs())
    changeGasPrice(0.1, 0.1)
    print("使用Arena-Z")

def reInitWeb3():
    initWeb3(rpc, web3.provider.get_request_kwargs())


def attachModules(modules):
    web3.attach_modules(modules)

class EthService(object):
    lastTimeout = 0
    """链上超时判定"""
    highGasValue = 200
    """高gas模式下的值"""
    lowerGasValue = 90
    """低gas模式下的值"""
    lastGasPrice = 0
    """上次gas价格"""

    @classmethod
    def contractConnection(cls, address, abi):
        if not initFlag:
            initWeb3(rpc)
        return web3.eth.contract(address=web3.to_checksum_address(address), abi=abi)

    def __init__(self, privateKey):
        if not initFlag:
            initWeb3(rpc)
        account: Account = Account().from_key(privateKey)
        self.account = account
        self.address = account.address
        self.lastNonce = -1
        self.lastTx = None
        self.nonceFailCount = 0
        self.privateKey = privateKey

    def estimate_gas(self, some_data):
        data = ""
        if isinstance(some_data, dict) and some_data.get("data"):
            data = some_data["data"]
        else:
            data = some_data
        try:
            gas = 0
            if isinstance(data, dict) and data.get("to"):
                if data.get("gas") and data.get("chainId") == 7897:
                    gas = data["gas"]
                else:
                    gas = web3.eth.estimate_gas(data)
            else:
                gas = web3.eth.estimate_gas({"to": self.address, "data": data})
        except requests.exceptions.HTTPError as e:
            self.error(f"网络异常 {e}")
            time.sleep(10)
            return self.estimate_gas(some_data)
        return gas

    @property
    def nonce(self):
        nonce = web3.eth.get_transaction_count(self.address)
        if self.lastNonce == nonce or nonce < self.lastNonce:
            self.nonceFailCount += 1
            if self.nonceFailCount > 150:
                self.nonceFailCount = 0
                self.lastNonce += 1
                self.info("卡链强制结束")
                exit(0)
                return self.lastNonce
            self.info(f"相同的nonce,等待区块 {self.lastNonce},{nonce}")
            time.sleep(random.randint(1, 3))
            return self.nonce
        else:
            self.nonceFailCount = 0
            self.lastNonce = nonce
            return nonce

    @property
    def baseFeePerGas(self):
        if isBSC:
            return Web3.from_wei(5, "gwei")
        try:
            baseFeePerGas = web3.eth.get_block("pending")["baseFeePerGas"]
            if baseFeePerGas > MaxBaseFeePerGas:
                self.info(
                    f"当前baseFeePerGas价格过高{Web3.from_wei(baseFeePerGas, 'gwei')},3-5秒后再试"
                )
                time.sleep(random.randint(3, 5))
                return self.baseFeePerGas
            return baseFeePerGas
        except KeyboardInterrupt:
            exit(0)
        except Exception:
            self.debug("获取baseFeePerGas失败")
            time.sleep(5)
            return self.baseFeePerGas

    @property
    def gasPrice(self):
        self.lastGasPrice = web3.eth.gas_price
        return self.lastGasPrice

    @property
    def priorityGasValue(self) -> int:
        for k,v in priorityGasMap.items():
            if self.lastGasPrice <= web3.to_wei(k, "Gwei"):
                return v
        return 80

    def checkLastTransaction(self):
        t = time.time()
        pending = web3.eth.get_transaction_count(self.address, "pending")
        last = web3.eth.get_transaction_count(self.address)
        t2 = time.time()
        self.debug(f"checkLastTransaction:{pending},{last},{round(t2 - t,2)}")
        return last != pending

    def reLoadNonce(self, msg=""):
        nonce = web3.eth.get_transaction_count(self.address)
        self.error(f"{msg},重新获取nonce:{self.lastNonce} -> {nonce}")
        self.lastNonce = 0
        self.lastTimeout = time.time()

    def reInitWeb3(self, msg=""):
        reInitWeb3()
        self.reLoadNonce(msg)

    def buildTransactionParams(self, data, isV2=True, isCollection=False, miniGas=0):
        chainId = data.get("chainId", 1)
        gasM = gasMap[chainId]
        gas = self.estimate_gas(data)

        if fixedMode:
            nonce = self.nonce
            if not data.get("gas"):
                data["gas"] = gas * gasM
            data["maxPriorityFeePerGas"] = web3.to_wei(0.5, "Gwei")
            data["maxFeePerGas"] = MaxBaseFeePerGas  # - web3.to_wei(0.5, 'Gwei')
            data["nonce"] = nonce
            if data.get("gas") < miniGas:
                data["gas"] = miniGas
            return data
        gasPrice = self.gasPrice
        if gasPrice > MaxGasPrice:
            self.info(f"当前gasPrice过高{Web3.from_wei(gasPrice, 'gwei')},10秒后再试")
            for _i in range(10):
                gasPrice = self.gasPrice
                if gasPrice > MaxGasPrice:
                    time.sleep(1)
                else:
                    break

            return self.buildTransactionParams(
                data, isV2=isV2, isCollection=isCollection, miniGas=miniGas
            )
        nonce = self.nonce
        baseFeePerGas = self.baseFeePerGas
        if not data.get("gas"):
            data["gas"] = gas * gasM
        maxPriorityFeePerGas = web3.to_wei(basePriorityFeePerGas[chainId], "Gwei")

        if isV2:
            maxFeePerGas = baseFeePerGas + maxPriorityFeePerGas * 2
            if not data.get("maxFeePerGas"):
                if isCollection:
                    if chainId != 137:
                        data["maxFeePerGas"] = baseFeePerGas + web3.to_wei(0.5, "Gwei")
                        data["maxPriorityFeePerGas"] = web3.to_wei(0, "Gwei")
                    else:
                        data["maxFeePerGas"] = maxFeePerGas + web3.to_wei(10, "Gwei")
                        data["maxPriorityFeePerGas"] = (
                            maxPriorityFeePerGas + web3.to_wei(10, "Gwei")
                        )
                        data["gas"] = 21000
                else:
                    data["maxFeePerGas"] = maxFeePerGas
                    data["maxPriorityFeePerGas"] = maxPriorityFeePerGas
            else:
                maxFeePerGas = data["maxFeePerGas"]
                maxPriorityFeePerGas = data["maxPriorityFeePerGas"]
                if (
                    maxFeePerGas - baseFeePerGas > web3.to_wei(4, "Gwei")
                    and chainId == 1
                ):
                    # data['maxPriorityFeePerGas'] = web3.to_wei(1, 'Gwei')
                    data["maxFeePerGas"] = baseFeePerGas + web3.to_wei(2, "Gwei")
                if chainId == 137:
                    data["maxPriorityFeePerGas"] += web3.to_wei(40, "Gwei")

        else:
            data["gasPrice"] = gasPrice
        data["nonce"] = nonce
        if data.get("gas") < miniGas:
            data["gas"] = miniGas
        return data

    def mint(self, data, isV2=True, delValue=True):
        data = self.buildTransactionParams(data, isV2=isV2)
        if delValue:
            del data["value"]
        return self.sendTransaction(data)

    def balance(self, address = None, unit="ether"):
        if not address:
            address = self.address
        balance = web3.eth.get_balance(address)
        return web3.from_wei(balance, unit=unit)

    def getEthValue(self, value, unit="ether"):
        return web3.from_wei(web3.to_wei(value, unit=unit), unit=unit)

    def signTransaction(self, params):
        return self.account.sign_transaction(params)

    def sendTransaction(self, params, signed=None):
        self.debug(f"sendTransaction:{params}")
        if not signed:
            signed = self.signTransaction(params)
        try:
            tx = web3.eth.send_raw_transaction(signed.raw_transaction).hex()
            if tx and not tx.startswith("0x"):
                tx = "0x" + tx
            self.lastTx = tx
            return tx
        except ValueError as e:
            message = e.args[0].get("message")
            code = e.args[0].get("code")
            if message.find("nonce too low") >= 0:
                self.reLoadNonce("nonce too low")
            elif message == "we can't execute this request":
                self.reLoadNonce("can't execute this request")
            else:
                if code == -32000:
                    self.error("replacement transaction underpriced")
                    self.lastTimeout = time.time()
                    self.reLoadNonce("replacement transaction underpriced")
                    time.sleep(5)
                elif code == -32603:
                    self.error("sendTransaction request failed or timed out 等待重试!")
                    time.sleep(10)
                    return self.sendTransaction(params, signed=signed)
                else:
                    self.error(f"sendTransaction ValueError:{e}")
                    self.reLoadNonce("sendTransaction ValueError")
                    raise e
        except Exception as e:
            self.lastNonce += 1
            self.error(f"sendTransaction error:{e}", exc_info=True)
            raise e

    def signMessage(self, message):
        msghash = encode_defunct(text=message)
        signed = self.account.sign_message(msghash)
        signedHex = signed.signature.hex()
        if signedHex and not signedHex.startswith("0x"):
            signedHex = "0x" + signedHex
        return signedHex

    def toChecksumAddress(self, address):
        return Web3.to_checksum_address(address)

    def addressIsEqual(self, address):
        return self.toChecksumAddress(self.address) == self.toChecksumAddress(address)

    def transfer(self, value, to, chainId=137):
        """
        发送转账
        """
        if Decimal(self.balance()) < Decimal(value):
            self.info(f"当前余额不足{self.balance()} 需转出:{value}")
            return False
        data = self.buildTransactionParams(
            {"value": web3.to_wei(value, "ether"), "to": to, "chainId": chainId, "gas": 21000},
            isCollection=value == 0,
        )
        if value == 0:
            balance = Decimal(self.balance())
            feeGas = web3.from_wei(
                (data["maxFeePerGas"] + data["maxPriorityFeePerGas"]) * data["gas"],
                "ether",
            )
            if balance < feeGas:
                self.info(f"当前余额不足手续费{balance} 手续费预估:{feeGas}")
                return False
            data["value"] = web3.to_wei(balance - feeGas, "ether")

        tx = self.sendTransaction(data)
        if tx:
            self.info(f"转账tx:{tx}")
            return True
        return False

    def getTx(self, tx):
        return web3.eth.get_transaction(tx)

    def debug(self, msg):
        logger.debug(f'{self.address or ""} {msg}')

    def info(self, msg):
        logger.info(f'{self.address or ""} {msg}')

    def error(self, msg, exc_info=False):
        logger.error(f'{self.address or ""} {msg}', exc_info=exc_info)
