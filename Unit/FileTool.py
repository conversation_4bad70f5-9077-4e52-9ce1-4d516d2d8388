# /bin/env python3
# -*- coding: UTF-8 -*- 
'''
功能描述：实现配置读写功能
编写人：darkedge
编写日期：2021年12月23日
   
'''
import hashlib
import json

def loadConfig(path="./account.txt"):
    users = []
    emails = {}
    with open(path) as lines:
        from Model.Account import Account
        accounts = lines.readlines()
        for account in accounts:
            account = account.strip('\n').strip()
            if len(account):
                if len(account) < 5:
                    continue
                user = Account(account)
                if user.ok:
                    if emails.get(user.email):
                        continue
                    emails[user.email] = True
                    users.append(user)
    return users

# 写入配置
def writeConfig(users:list,path="./account.txt"):
    with open(path,"w+") as f:
        for user in users:
            f.write(user.text + "\n")

__s5List = {}

def loadS5List(path="./socks5.txt"):
    '''
    获取所有s5
    '''
    l = __s5List.get(path)
    if not l or len(l) == 0:
        sock5Map = {}
        with open(path) as lines:
            from Model.Account import Account
            accounts = lines.readlines()
            for account in accounts:
                account = account.strip('\n')
                if len(account):
                    user = Account(account)
                    sock5Map[user.s5] = user
        socks5List = []
        for s5 in sock5Map:
            socks5List.append(s5)
        __s5List[path] = socks5List
    return __s5List[path]

# 获取文件md5
def get_file_md5(file_path):
    with open(file_path, 'rb') as f:
        md5obj = hashlib.md5()
        md5obj.update(f.read())
        hash = md5obj.hexdigest()
        return str(hash).upper()

def loadSelfToken(path="./token.txt"):
    '''读取自己的token'''
    token = None
    try:
        with open(path) as f:
            token = f.readline().strip('\n')
    except:
        pass
    return token

def loadShrineIds():
    foIds = None
    with open("shrineIds.json") as f:
        txt = f.read()
        foIds = json.loads(txt)
    return foIds

def append_to_file(text,file_path):
    try:
        with open(file_path, 'a', encoding='utf-8') as file:
            file.write(text + '\n')
        print("Data appended to the file successfully.")
    except IOError as e:
        print(f"Error: Unable to write to the file '{file_path}'.")
        print(e)

def transform_line(line):
    if line:
        ip, port, username, password = line.strip().split(':')
        transformed_line = f'||{username}:{password}@{ip}:{port}'
        return transformed_line
    return None

def reloadSocks5(path="./socks5.txt"):
    with open(path, 'r') as f:
        for line in f.readlines():
            transformed_line = transform_line(line)
            if line:
                __s5List[transformed_line] = 1
    
    with open(path, 'w') as f:
        lines = [f'{v.strip()}\n' for v in __s5List.keys()]
        f.writelines(lines)

