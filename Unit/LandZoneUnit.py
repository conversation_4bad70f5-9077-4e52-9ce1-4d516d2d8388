# /bin/env python3
# -*- coding: UTF-8 -*-
"""
功能描述：领地区块单元
编写人：darkedge
编写日期：2022年5月3日
   
"""

import math
import secrets
import time

MaxSingleZoneSize = 54
MaxSixThreadZoneSize = MaxSingleZoneSize * 6


def loadCrystalLands():
    """
    加载水晶领地
    """
    # lands = []
    with open("lands.txt", "r", encoding="utf-8") as f:
        txt = f.read()
        lines = txt.strip().split(",")
        return [int(v) for v in lines]
    return []


crystalLands = loadCrystalLands()
"""砖石所在区块"""


def distanceBetween(loc1, loc2):
    """
    计算两个坐标的距离
    """
    return round(
        math.sqrt(math.pow(loc2[0] - loc1[0], 2) + math.pow(loc2[1] - loc1[1], 2)), 3
    )


def returnAdjacentCrystalLands(loc, maxCount=MaxSingleZoneSize, power=0):
    """
    返回邻近的水晶领地
    """
    # lands = []
    if len(loc) == 3:
        return returnAdjacentCrystalLands(loc[1:], maxCount=maxCount, power=power)
    x = loc[0]
    y = loc[1]
    weightMap = {}
    for land in crystalLands:
        landX = land % 64 * 32
        landY = land // 64 * 32
        if power > 0:
            if abs(y - landY) > power * 32:
                continue
        if power < 0:
            if abs(x - landX) > abs(power) * 32:
                continue

        distance = distanceBetween([landX, landY], [x, y])
        if weightMap.get(distance):
            weightMap[distance + secrets.randbelow(98) + 1 / 100.0] = land
        else:
            weightMap[distance] = land

    distances = sorted(weightMap.keys())
    if len(distances) > maxCount:
        distances = distances[:maxCount]
    return [weightMap[distance] for distance in distances]


def zoneWithXY(x, y):
    zone = int(y / 32) * 64 + int(x / 32)
    return zone


def isPointOnPolygonEdge(point, polygon):
    if isinstance(point, list):
        point = NewPoint(point[0], point[1])

    if polygon.contains(point):
        return True

    x, y = point.x, point.y
    for i in range(len(polygon.exterior.coords) - 1):
        edge_start = polygon.exterior.coords[i]
        edge_end = polygon.exterior.coords[i + 1]
        x1, y1 = edge_start
        x2, y2 = edge_end
        # 判断点是否在边的整数坐标范围内
        if min(x1, x2) <= x < max(x1, x2) and min(y1, y2) <= y < max(y1, y2):
            return True

    return False


def AllZoneOfPolygon(polygon):
    from shapely.geometry import Point

    from Unit.Redis import MONTHNUM, redisHelper

    if redisHelper.getJson(f"{polygon}"):
        return redisHelper.getJson(f"{polygon}")

    t1 = time.time()
    zones = {}
    # invalidZones = {}
    envelope = polygon.envelope
    min_x, min_y, max_x, max_y = envelope.bounds
    for x in range(int(min_x), int(max_x) + 1, 2):
        for y in range(int(min_y), int(max_y) + 1, 2):
            zone = zoneWithXY(x, y)
            if zones.get(zone):  # or invalidZones.get(zone):
                continue
            if isPointOnPolygonEdge(Point(x, y), polygon):
                zones[zone] = True
            # else:
            #     invalidZones[zone] = True
    zones = [v for v in zones.keys()]
    t2 = time.time()
    print(f"AllZoneOfPolygon耗时:{round(t2-t1,2)}")
    redisHelper.setJson(f"{polygon}", zones, ex=MONTHNUM)
    return zones

def PolygonWithPoint(x,y):
    startX = x // 400 * 400
    startY = y // 400 * 400
    endX = startX + 400
    endY = startY + 400

    points = [
        (startX, startY),
        (endX, startY),
        (endX, endY),
        (startX, endY),
        (startX, startY),
    ]

    return NewPolygon(points)

def NewPoint(x, y):
    from shapely.geometry import Point

    return Point(x, y)


def NewPolygon(points):
    from shapely.geometry.polygon import Polygon

    return Polygon(points)
