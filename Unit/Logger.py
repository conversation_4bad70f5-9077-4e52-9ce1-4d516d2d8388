# /bin/env python3
# -*- coding: UTF-8 -*-

"""
功能描述：实现控制台和文件同时记录日志的功能
编写人：darkedge
编写日期：2021年12月13日

步骤分析：
  1-配置日志记录器名称
  2-配置日志级别
  3-配置日志格式（可以分别设置，也可以统一设置）
  4-创建并添加handler-控制台
  5-创建并添加handler-文件
  6-提供对外获取logger
   
"""

import logging
import os
import platform
import sys
import re
from logging import handlers

import redis

version = "1.0.1"


class MemoryHandler(handlers.BufferingHandler):
    """自定义内存日志"""

    def flush(self):
        self.acquire()
        try:
            maxCount = len(self.buffer)
            del self.buffer[: maxCount // 2]
        finally:
            self.release()


class RedisHandler(handlers.BufferingHandler):
    redis = None
    enable = False
    key = None
    """redis"""

    def __init__(self, key):
        super().__init__(20)
        self.key = f"log_{key}"
        self.initRedis()

    def initRedis(self):
        host = "localhost"
        if platform.system() == "Linux":
            host = "redis"

        try:
            self.redis = redis.Redis(host=host, port=6379, db=4)
            self.redis.set("test", value="test", ex=100)
            self.enable = True
        except redis.exceptions.ConnectionError:
            print("redis连接失败")
        except Exception as e:
            print("redis connect error: %s" % e)

    def emit(self, record):
        if self.enable:
            index = self.redis.rpush(
                self.key, f"{record.asctime}:{record.getMessage()}"
            )
            if index > 100:
                self.redis.lpop(self.key)

    def getLogs(self):
        if self.enable:
            res = self.redis.lrange(self.key, 0, -1)
            logs = [shellColorToWeb(v.decode()) for v in res]
            logs = list(map(lambda x: re.sub(r'使用代理.+?ip', '使用代理***ip', x), logs))
            logs.reverse()
            return logs
        return []

    def clearLogs(self):
        if self.enable:
            self.redis.delete(self.key)


class Log:

    def __init__(
        self,
        filename="debug.log",
        errorfilename="error.log",
        infofilename="info.log",
        loggerName="mainName",
        subKey=None,
    ):
        """
        配置logger记录器名称
        配置日志级别
        配置日志格式
        """
        dirname, runfilename = os.path.split(os.path.abspath(sys.argv[0]))
        mainName = runfilename.split(".")[0]
        dirPrefix = "./log/" + mainName + "/"
        if subKey:
            dirPrefix = f"./log/{mainName}/{subKey}/"

        self.filename = dirPrefix + filename
        self.errorfilename = dirPrefix + errorfilename
        self.infofilename = dirPrefix + infofilename
        f_dir, f_name = os.path.split(self.filename)
        os.makedirs(f_dir, exist_ok=True)  # 当前目录新建log文件夹
        self.logger = logging.getLogger(loggerName)
        if subKey:
            while len(self.logger.handlers) > 0:
                self.logger.removeHandler(self.logger.handlers[-1])
        self.logger.setLevel(logging.DEBUG)
        self.format = logging.Formatter("%(levelname)s-%(asctime)s: %(message)s")

        self.__addstreamHan()
        self.__addfileHan()
        self.logger.info(f"version:{version}")

    def __addstreamHan(self):
        """
        定义添加控制台输出handler
        """
        sh = logging.StreamHandler()
        sh.setFormatter(self.format)
        sh.setLevel(logging.INFO)
        self.logger.addHandler(sh)

    def __addfileHan(self):
        """
        定义添加写入文件handler
        """
        #    fh = logging.FileHandler('test.log')
        #    fh.setFormatter(self.format)
        #    self.logger.addHandler(fh)
        th = handlers.TimedRotatingFileHandler(
            filename=self.filename,
            when="H",
            interval=12,
            backupCount=6,
            encoding="utf-8",
            delay=True,
        )  # 往文件里写入指定间隔时间自动生成文件的Handler
        th.getFilesToDelete()
        # 实例化TimedRotatingFileHandler
        # interval是时间间隔，backupCount是备份文件的个数，如果超过这个个数，就会自动删除，when是间隔的时间单位，单位有以下几种：
        # S 秒
        # M 分
        # H 小时
        # D 天
        # 'W0'-'W6' 每星期（interval=0时代表星期一：W0）
        # midnight 每天凌晨
        #    th.suffix = "%Y-%m-%d_%H-%M-%S.log"
        th.setLevel(logging.DEBUG)
        th.setFormatter(self.format)  # 设置文件里写入的格式
        th.max_bytes_per_file = 1024 * 1024 * 1024 * 10
        self.logger.addHandler(th)

        th2 = handlers.TimedRotatingFileHandler(
            filename=self.errorfilename,
            when="D",
            interval=1,
            backupCount=3,
            encoding="utf-8",
        )
        #    th2.suffix = "%Y-%m-%d_%H-%M-%S.log"
        th2.setLevel(logging.ERROR)
        th2.setFormatter(
            logging.Formatter(
                "%(asctime)s - %(filename)s[line:%(lineno)d]: %(message)s"
            )
        )  # 设置文件里写入的格式
        self.logger.addHandler(th2)

        th3 = handlers.TimedRotatingFileHandler(
            filename=self.infofilename,
            when="D",
            interval=1,
            backupCount=5,
            encoding="utf-8",
        )
        #    th3.suffix = "%Y-%m-%d_%H-%M-%S.log"
        th3.setLevel(logging.INFO)
        th3.setFormatter(self.format)  # 设置文件里写入的格式
        th3.max_bytes_per_file = 1024 * 1024 * 1024 * 10
        self.logger.addHandler(th3)

    def getLog(self):
        """
        定义对外提供logger的方法
        注意：此处的方法不要定义为getLogger，否则就是重写log的方法了
        """
        return self.logger


logger = Log().getLog()


def createLoggerWithKey(subKey):
    """
    自定义key生成logger
    """
    return Log(loggerName=subKey, subKey=subKey).getLog()

def shellColorToWeb(s):
    # 定义颜色映射
    color_map = {
        "31": "red",
        "33": "green",
        "34": "blue"
    }

    # 替换逻辑
    def replace_color(match):
        color_code = match.group(1)  # 提取颜色代码，如 "31"
        text = match.group(2)        # 提取文本内容
        color = color_map.get(color_code, "black")  # 获取对应颜色，默认黑色
        return f'<span style="color:{color};">{text}</span>'

    # 使用正则替换
    return re.sub(r'\x1b\[(\d+)m(.*?)\x1b\[0m', replace_color, s)

if __name__ == "__main__":
    logger.info("1234?")
