# /bin/env python3
# -*- coding: UTF-8 -*- 

'''
功能描述：区块链相关
编写人：darkedge
编写日期：2022年02月08日
   
'''

import asyncio
import httpx
import json
import base64
import time,datetime,random
import threading
from httpx_socks import AsyncProxyTransport
from Unit.Logger import logger

async def requestRefreshMetaData(tokenId,socks5=None):
    '''
    异步刷新元数据
    '''
    transport = None
    if socks5:
        transport = AsyncProxyTransport.from_url(f'socks5://{socks5}')
    client = httpx.AsyncClient(transport=transport, timeout=30)
    try:
        response = await client.patch('https://api-matic.playdapp.com/item/metadata', data={'contractAddress':'0x4d544035500d7ac1b42329c70eb58e77f8249f0f','tokenId': tokenId})
        res: httpx.Response = response
        if res.status_code == 200:
            resJson = res.json()
            if resJson.get("data") is True:
                return True
    except Exception as e:
        logger.error(e,exc_info=True)
        pass
    finally:
        await client.aclose()
    return False
