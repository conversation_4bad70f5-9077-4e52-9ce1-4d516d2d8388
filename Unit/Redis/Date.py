# /bin/env python3
# -*- coding: UTF-8 -*-

"""
功能描述：日期库
编写人：darkedge
编写日期：2023年03月28日

步骤分析：
   
"""

import datetime
import time

import pytz


def currentTime():
    return int(time.time() * 100000)


tz = pytz.timezone("Asia/Shanghai")
utctz = pytz.timezone("UTC")


def tzDate(timezone=tz):
    return datetime.datetime.now(timezone)


# 当前日期


def todayStr():
    time_str = tzDate().strftime("%Y-%m-%d")
    return time_str


def todayUTCStr():
    time_str = tzDate(utctz).strftime("%Y-%m-%d")
    return time_str

def yesterdayUTCStr():
    # 获取当前日期并减去一天
    time_str = (tzDate(utctz) - datetime.timedelta(days=1)).strftime("%Y-%m-%d")
    return time_str

def currentHour():
    """当前小时"""
    return tzDate().hour

def currentMinute():
    """当前分钟"""
    return tzDate().minute

def isUTCMonday():
    """utc的星期一"""
    return tzDate(utctz).weekday() == 0

def todayUTCTimestamp():
    """utc当日时间戳"""
    today = datetime.date.today()
    today_utc = utctz.localize(datetime.datetime.combine(today, datetime.time.min))
    timestamp = int(today_utc.timestamp())
    return timestamp


def secondToDate(sec):
    """时间戳转日期"""
    if not sec:
        return ""
    if isinstance(sec,str):
        sec = float(sec)
    d1 = datetime.timedelta(seconds=int(sec))
    if d1.days != 0:
        d2 = datetime.timedelta(seconds=d1.seconds)
        return str(f"{d1.days}d{d2}")
    return str(d1)
    # return str(datetime.timedelta(seconds=int(sec)))
