# /bin/env python3
# -*- coding: UTF-8 -*-

'''
功能描述：JWT解析
编写人：darkedge
编写日期：2023年03月28日

   
'''

import base64
import json

def loadJWTInfo(token):
    '''从token中加载信息'''
    if not token:
        return
    tmpSplit = token.split('.')
    if len(tmpSplit) != 3:
        return
    data = tmpSplit[1]
    pdLen = 4 - len(data) % 4
    data += '=' * pdLen

    info = json.loads(base64.b64decode(data).decode())
    return info