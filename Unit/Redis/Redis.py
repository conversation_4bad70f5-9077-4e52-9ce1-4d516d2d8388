# /bin/env python3
# -*- coding: UTF-8 -*-

"""
功能描述：实现redis的功能和文件同步功能
编写人：darkedge
编写日期：2021年12月16日

步骤分析：

"""
import json
import random
import threading
import time

# trunk-ignore(ruff/F403)
from .Date import *
from .Date import currentTime, todayStr, todayUTCStr, yesterdayUTCStr
from .JWT import loadJWTInfo
from .RedisMgr import RedisMgr, logger

loclock = threading.Lock()  # 申请一把锁


MINUTESNUM = 60
HOURNUM = MINUTESNUM * 60
DAYNUM = HOURNUM * 24
WEEKNUM = DAYNUM * 7
MONTHNUM = DAYNUM * 30


class RedisHelper:
    debugLog = None

    def __init__(self, mgr: RedisMgr = None):
        self.locs = []
        self.mainLocs = []
        self.repairLocs = []
        self.teleports = {}
        self.repairs = {}
        self.noShieldLocs = []
        self.clearTime = time.time()
        if mgr is None:
            mgr = redisMgr
        self.redisMgr = mgr

    @property
    def enable(self):
        return self.redisMgr.enable

    def debug(self, msg):
        if callable(self.debugLog):
            self.debugLog(msg)
        else:
            logger.debug(msg)

    def checkClear(self):
        if time.time() - self.clearTime > HOURNUM:
            self.clearAll()

    def clearAll(self):
        self.clearTime = time.time()
        self.locs = []
        self.mainLocs = []
        self.repairLocs = []
        self.noShieldLocs = []

    def clearSomeKeys(self):
        self.clearLstProtect()
        self.clearTokens()

    def get(self, key):
        if self.enable:
            value = self.redisMgr.get(key)
            if value:
                return value.decode("utf-8")
        return None

    def mget(self, *keys):
        if self.enable:
            return self.redisMgr.mget(*keys)
        return []

    def getJson(self, key):
        value = self.get(key)
        if value:
            return json.loads(value)
        return None

    def set(self, key, value, logMsg=None, ex=None):
        if self.enable:
            if value is None:
                self.removeKey(key)
            else:
                self.redisMgr.set(key, value, ex=ex)
            if logMsg:
                self.debug(logMsg)

    def setJson(self, key, value, logMsg=None, ex=None):
        self.set(key, json.dumps(value), logMsg, ex)

    def mset(self, *args):
        if self.enable:
            self.redisMgr.mset(*args)

    def removeKey(self, key):
        if self.enable and self.redisMgr.exists(key):
            self.redisMgr.delete(key)
            self.debug("删除key %s" % key)

    def removekeyWithAll(self, key):
        if self.enable:
            try:
                self.redisMgr.delete(*self.keys(f"*{key}*"))
            except Exception:
                pass

    def keys(self, keys):
        return self.redisMgr.keys(keys)

    def rpop(self, key):
        if self.enable:
            return self.redisMgr.rpop(key)
        return None

    def rpush(self, key, *values):
        if self.enable:
            self.redisMgr.rpush(key, *values)

    def sadd(self, key, *values):
        if self.enable:
            self.redisMgr.sadd(key, *values)

    def smembers(self, key):
        if self.enable:
            values = self.redisMgr.smembers(key)
            if values:
                return [v.decode() for v in values]
        return []

    def srem(self, key, *values):
        if self.enable:
            return self.redisMgr.srem(key, *values)
        return False

    def batch_set_with_ex(self, data_dict, ex = MONTHNUM * 6, batch_size=500):
        """
        批量设置带过期时间的数据,自动分批处理

        Args:
            data_dict (dict): 要设置的键值对字典
            ex (int): 过期时间(秒)
            batch_size (int): 每批处理的数据条数,默认500

        Returns:
            bool: 是否全部设置成功
        """
        if not self.enable or not data_dict:
            return False

        try:
            # 将字典转为items列表便于分片
            items = list(data_dict.items())
            total_items = len(items)

            # 计算需要的批次数
            total_batches = (total_items + batch_size - 1) // batch_size

            success_count = 0
            for batch_num in range(total_batches):
                start_idx = batch_num * batch_size
                end_idx = min((batch_num + 1) * batch_size, total_items)
                current_batch = items[start_idx:end_idx]

                pipe = self.redisMgr.pipeline()
                for key, value in current_batch:
                    if isinstance(value, (dict, list)):
                        value = json.dumps(value)
                    pipe.setex(key, ex, value)

                # 执行当前批次
                pipe.execute()
                success_count += len(current_batch)

                # 记录进度日志
                if total_batches > 1:
                    progress = (batch_num + 1) / total_batches * 100
                    self.debug(
                        f"Redis批量设置进度: {progress:.1f}% ({success_count}/{total_items})"
                    )

            return success_count == total_items

        except Exception as e:
            logger.error(f"batch_set_with_ex error: {e}")
            return False

    def batch_get(self, keys, batch_size=500) -> dict:
        """
        批量获取数据,自动分批处理

        Args:
            keys (list): 要获取的key列表
            batch_size (int): 每批处理的数据条数,默认500

        Returns:
            dict: 返回key-value字典
        """
        if not self.enable or not keys:
            return {}

        try:
            total_keys = len(keys)
            total_batches = (total_keys + batch_size - 1) // batch_size
            result = {}

            for batch_num in range(total_batches):
                start_idx = batch_num * batch_size
                end_idx = min((batch_num + 1) * batch_size, total_keys)
                current_batch = keys[start_idx:end_idx]

                pipe = self.redisMgr.pipeline()
                for key in current_batch:
                    pipe.get(key)

                # 执行当前批次并处理结果
                values = pipe.execute()
                batch_result = {}

                for key, val in zip(current_batch, values):
                    if val is None:
                        batch_result[key] = None
                        continue

                    try:
                        decoded = val.decode()
                        if decoded.startswith('{') or decoded.startswith('['):
                            batch_result[key] = json.loads(decoded)
                        else:
                            batch_result[key] = decoded
                    except (UnicodeDecodeError, json.JSONDecodeError):
                        batch_result[key] = None

                result.update(batch_result)

                # 记录进度日志
                if total_batches > 1:
                    progress = (batch_num + 1) / total_batches * 100
                    self.debug(
                        f"Redis批量获取进度: {progress:.1f}% ({len(result)}/{total_keys})"
                    )

            return result

        except Exception as e:
            logger.error(f"batch_get error: {e}")
            return {}

    def setKingdomInfo(self, key, value):
        """保存enter数据"""
        rkey = f"kingdominfo_{key}"
        self.setJson(rkey, value, ex=WEEKNUM)

    def getKingdomInfo(self, key):
        """获取enter数据"""
        rkey = f"kingdominfo_{key}"
        return self.getJson(rkey)

    def saveResourceInfo(self, email, value):
        """
        保存资源信息
        """
        key = "resource_%s" % email
        self.setJson(key, value, ex=HOURNUM * 12)

    def removeResourceInfo(self, email):
        """
        删除资源信息
        """
        key = "resource_%s" % email
        if self.enable:
            self.removeKey(key)

    def allResourceInfos(self):
        """
        获取所有保存的资源
        """
        if self.enable:
            keys = self.keys("resource_*")
            if len(keys):
                values = self.redisMgr.mget(*keys)
                keys = [key.decode("utf-8")[9:] for key in keys]
                values = [json.loads(value.decode("utf-8")) for value in values]
                return dict(zip(keys, values))
        return []

    def clearResourceInfos(self):
        """
        清除所有资源信息
        """
        if self.enable:
            keys = self.keys("resource_*")
            if len(keys):
                self.redisMgr.delete(*keys)
                self.debug("清除所有资源")

    def addLoc(self, loc):
        self.checkClear()

        if loc not in self.locs:
            self.locs.append(loc)
            key = "loc"
            for i in loc:
                key += "_" + str(i)

            self.set(key, 1, "添加坐标 %s" % loc, ex=HOURNUM * 8)

    def removeLoc(self, loc):
        if loc in self.locs:
            self.locs.remove(loc)
        key = "loc"
        for i in loc:
            key += "_" + str(i)

        if self.enable:
            self.removeKey(key)

    def availableLoc(self):
        if self.enable:
            locs = self.keys("loc_*")
            locs = [loc.decode("utf-8")[4:].split("_") for loc in locs]
            return locs
        return []

    # 加锁随机取号
    def randomGetLoc(self):
        loc = None
        loclock.acquire()
        locs = self.availableLoc()
        if len(locs) > 0:
            loc = random.choice(locs)
            self.removeLoc(loc)
        loclock.release()
        return loc

    def addChannelValue(self, channel, channelList, key, value):
        if key not in channelList:
            channelList[key] = value
            redisKey = "%s_%s" % (channel, key)
            self.set(redisKey, value, "添加%s %s" % (channel, key), ex=WEEKNUM)

    def removeChannelKey(self, channel, channelList, key):
        if key in channelList:
            del channelList[key]
        redisKey = "%s_%s" % (channel, key)
        if self.enable:
            self.removeKey(redisKey)
            self.debug("删除%s %s" % (channel, key))

    def clearChannel(self, channel):
        if self.enable:
            redisKeys = self.keys("%s_*" % channel)
            if len(redisKeys) > 0:
                self.redisMgr.delete(*redisKeys)
                self.debug("清空%s" % channel)

    # 同步redis和列表
    def syncChannelList(self, channel, channelList, newList):
        mergeList = {}
        mergeList.update(newList)

        if self.enable:
            redisKeys = self.keys("%s_*" % channel)
            if len(redisKeys) > 0:
                redisValues = self.redisMgr.mget(redisKeys)
                for i in range(len(redisKeys)):
                    redisKey = redisKeys[i]
                    realKey = redisKey.decode("utf-8")
                    key = realKey[len(channel) + 1 :]
                    if key in mergeList:
                        continue
                    mergeList[key] = redisValues[i].decode("utf-8")

            redisList = {}
            for key in mergeList:
                redisKey = "%s_%s" % (channel, key)
                redisList[redisKey] = mergeList[key]

            if len(redisList):
                self.redisMgr.mset(redisList)
        channelList.clear()
        channelList.update(mergeList)
        return channelList

    # def syncRepairs(self,repairs):
    #     mergeRepairs = {}
    #     mergeRepairs.update(repairs)

    #     if self.enable:
    #         redisKeys = self.keys('repair_*')
    #         redisValues = self.redisMgr.mget(redisKeys)
    #         for i in range(len(redisKeys)):
    #             redisKey = redisKeys[i]
    #             realKey = redisKey.decode('utf-8')
    #             email = realKey[7:]
    #             if email in mergeRepairs:
    #                 continue
    #             mergeRepairs[email] = redisValues[i].decode('utf-8')

    #         self.redisMgr.mset(mergeRepairs)
    #     return mergeRepairs

    def addNoShieldLoc(self, loc):
        locStr = "_".join(str(i) for i in loc)
        key = "noShield_%s" % locStr

        if locStr not in self.allNoShieldLocs():
            self.noShieldLocs.append(locStr)
            self.set(key, 1, ex=DAYNUM)

    def removeNoShieldLoc(self, loc):
        locStr = "_".join(str(i) for i in loc)
        key = "noShield_%s" % locStr
        if locStr in self.allNoShieldLocs():
            self.noShieldLocs.remove(locStr)

        self.removeKey(key)

    def allNoShieldLocs(self):
        self.checkClear()
        if len(self.noShieldLocs) == 0:
            if self.enable:
                keys = self.keys("noShield_*")
                keys = [key.decode("utf-8")[9:].split("_") for key in keys]
                self.noShieldLocs = keys

        return self.noShieldLocs

    def addMainLoc(self, loc):
        locStr = "_".join(str(i) for i in loc)
        key = "mainloc_%s" % locStr
        if locStr not in self.allMainLocs():
            self.mainLocs.append(locStr)
            self.set(key, 1, ex=100 * MINUTESNUM)

    def removeMainLoc(self, loc):
        locStr = "_".join(str(i) for i in loc)
        key = "mainloc_%s" % locStr
        if locStr in self.allMainLocs():
            self.mainLocs.remove(locStr)

        self.removeKey(key)

    def allMainLocs(self):
        self.checkClear()
        if len(self.mainLocs) == 0:
            if self.enable:
                keys = self.keys("mainloc_*")
                keys = [key.decode("utf-8")[8:].split("_") for key in keys]
                self.mainLocs = keys

        return self.mainLocs

    def checkMainWithRepair(self, loc):
        allMainLocs = self.allMainLocs()
        for mainLoc in allMainLocs:
            if int(loc[0]) == int(mainLoc[0]):
                if (
                    abs(int(loc[1]) - int(mainLoc[1])) <= 3
                    and abs(int(loc[2]) - int(mainLoc[2])) <= 3
                ):
                    return True
        return False

    def addRepairLoc(self, loc):
        locStr = "_".join(str(i) for i in loc)
        key = "repairloc_%s" % locStr
        if locStr not in self.allRepairLocs():
            self.repairLocs.append(locStr)
            self.set(key, 1, ex=100 * MINUTESNUM * 2)

    def allRepairLocs(self):
        self.checkClear()
        if len(self.repairLocs) == 0:
            if self.enable:
                keys = self.keys("repairloc_*")
                keys = [key.decode("utf-8")[10:].split("_") for key in keys]
                self.repairLocs = keys

        return self.repairLocs

    def checkRepairWithMain(self, loc):
        """
        检查是否有维修位置在主基地附近的数量
        """
        allRepairLocs = self.allRepairLocs()
        sum = 0
        for _l in allRepairLocs:
            if int(loc[0]) == 0:
                break
            elif int(_l[0]) == int(loc[0]):
                if (
                    abs(int(_l[1]) - int(loc[1])) <= 3
                    and abs(int(_l[2]) - int(loc[2])) <= 3
                ):
                    sum += 1
        return sum

    def addRemoveAccount(self, email, text):
        """
        添加待删除主账号
        """
        key = "removeAccount_%s" % email
        self.set(key, 1)
        self.addChannelValue("teleport", self.teleports, email, text)

    def allRemoveAccount(self):
        """
        获取待删除主账号，并清空
        """
        if self.enable:
            keys = self.keys("removeAccount_*")
            if len(keys) > 0:
                self.redisMgr.delete(*keys)
                keys = [key.decode("utf-8")[14:] for key in keys]
                return keys
        return []

    def saveDeviceInfo(
        self, deviceInfo, email=None, userKey=None, appleSubId=None, kingdomId=None
    ):
        """
        保存设备信息
        """
        key = None

        if kingdomId:
            key = "deviceInfo_%s" % kingdomId
        elif appleSubId:
            key = "deviceInfo_%s" % appleSubId
        elif userKey:
            key = "deviceInfo_%s" % userKey
        elif email:
            key = "deviceInfo_%s" % email

        if key:
            self.set(key, json.dumps(deviceInfo), ex=WEEKNUM * 2)
            return True
        return False

    def getDeviceInfo(self, email=None, userKey=None, appleSubId=None, kingdomId=None):
        """
        获取设备信息
        """
        key = None

        if kingdomId:
            key = "deviceInfo_%s" % kingdomId
        elif appleSubId:
            key = "deviceInfo_%s" % appleSubId
        elif userKey:
            key = "deviceInfo_%s" % userKey
        elif email:
            key = "deviceInfo_%s" % email

        if key:
            value = self.get(key)
            if value:
                self.set(key, value, ex=WEEKNUM * 2)
                return json.loads(value)

        return None

    def removeDeviceInfo(
        self, email=None, userKey=None, appleSubId=None, kingdomId=None
    ):
        """
        删除设备信息
        """
        key = None
        if kingdomId:
            key = "deviceInfo_%s" % kingdomId
        elif appleSubId:
            key = "deviceInfo_%s" % appleSubId
        elif userKey:
            key = "deviceInfo_%s" % userKey
        elif email:
            key = "deviceInfo_%s" % email

        if key:
            self.removeKey(key)
            return True

        return False

    def setGameVersion(self, isWeb, version):
        """
        设置游戏版本
        """
        key = "gameVersion_%s" % (isWeb and "web" or "ios")
        self.set(key, version, ex=DAYNUM * 30)
        return True

    def getGameVersion(self, isWeb):
        """
        获取游戏版本
        """
        key = "gameVersion_%s" % (isWeb and "web" or "ios")
        value = self.get(key)
        if value:
            self.set(key, value, ex=DAYNUM * 30)
            return value
        return None

    def savePushId(self, pushId, email=None, userKey=None):
        """保存pushId"""
        key = None
        if email:
            key = "pushId_%s" % email
        elif userKey:
            key = "pushId_%s" % userKey

        if key:
            self.set(key, pushId, ex=WEEKNUM)
            return True
        return False

    def getPushId(self, email=None, userKey=None):
        """获取pushId"""
        key = None
        if email:
            key = "pushId_%s" % email
        elif userKey:
            key = "pushId_%s" % userKey

        if key:
            value = self.get(key)
            if value:
                self.set(key, value, ex=WEEKNUM)
                return value
        return None

    def removePushId(self, email=None, userKey=None):
        """删除pushId"""
        key = None
        if email:
            key = "pushId_%s" % email
        elif userKey:
            key = "pushId_%s" % userKey

        if key:
            self.removeKey(key)
            return True
        return False

    def saveKingdomIdWithUserKey(self, kingdomId, userKey):
        """
        保存kingdomId
        """
        key = "kingdomId_%s" % userKey
        self.set(key, kingdomId, ex=WEEKNUM)
        return True

    def getKingdomIdWithUserKey(self, userKey):
        """
        获取kingdomId
        """
        key = "kingdomId_%s" % userKey
        value = self.get(key)
        if value:
            self.set(key, value, ex=WEEKNUM)
            return value
        return None

    def saveToken(self, token, email=None, userKey=None, appleSubId=None):
        """
        保存游戏token
        """
        key = None
        if appleSubId:
            key = "token_%s" % appleSubId
        elif userKey:
            key = "token_%s" % userKey
        elif email:
            key = "token_%s" % email

        info = loadJWTInfo(token)
        ex = 1
        if info:
            exp = info["exp"]
            kingdomId = info["kingdomId"]
            tex = int(exp) - time.time() + 8 * HOURNUM
            if tex > 0:
                ex = int(tex)
            if kingdomId:
                self.set(f"token_{kingdomId}", token, ex=ex)
                if userKey:
                    self.saveKingdomIdWithUserKey(kingdomId, userKey)
                if appleSubId:
                    self.saveKingdomIdWithUserKey(kingdomId, appleSubId)
        if key:
            self.set(key, token, ex=ex)
            return True
        return False

    def getToken(self, email=None, userKey=None, kingdomId=None, appleSubId=None):
        """
        获取游戏token
        """
        key = None
        if appleSubId:
            key = "token_%s" % appleSubId
        elif userKey:
            key = "token_%s" % userKey
        elif email:
            key = "token_%s" % email

        if appleSubId and not kingdomId:
            kingdomId = self.getKingdomIdWithUserKey(appleSubId)
        if userKey and not kingdomId:
            kingdomId = self.getKingdomIdWithUserKey(userKey)

        value = None
        if key:
            value = self.get(key)
        if not value and kingdomId:
            value = self.get(f"token_{kingdomId}")
        if value:
            self.saveToken(value, email, userKey, appleSubId=appleSubId)
            return value
        return None

    def removeToken(self, email=None, userKey=None, appleSubId=None, kingdomId=None):
        """
        删除游戏token
        """
        key = None
        if appleSubId:
            key = "token_%s" % appleSubId
        elif userKey:
            key = "token_%s" % userKey
        elif email:
            key = "token_%s" % email

        if appleSubId and not kingdomId:
            kingdomId = self.getKingdomIdWithUserKey(appleSubId)
        if userKey and not kingdomId:
            kingdomId = self.getKingdomIdWithUserKey(userKey)
        if kingdomId:
            self.removeKey(f"token_{kingdomId}")

        if key:
            token = self.get(key)
            if token:
                if not kingdomId:
                    info = loadJWTInfo(token)
                    if info:
                        kingdomId = info["kingdomId"]
                        if kingdomId:
                            return self.removeToken(
                                email=email,
                                userKey=userKey,
                                appleSubId=appleSubId,
                                kingdomId=kingdomId,
                            )
                self.removeKey(key)
            return True

        return False

    @property
    def fakeIpKey(self):
        return "fakeIp_%s"

    def saveFakeIp(self, mainKey, kingdomId=None, fakeIp=None):
        """
        保存fakeIp信息
        """
        key = None
        if mainKey:
            key = self.fakeIpKey % mainKey
            self.set(key, fakeIp, ex=MONTHNUM)
            if kingdomId:
                key = self.fakeIpKey % kingdomId
                self.set(key, fakeIp, ex=MONTHNUM)
            return True
        return False

    def getFakeIp(self, mainKey, kingdomId=None, fakeIp=None):
        """
        获取fakeIp信息
        """
        if kingdomId:
            key = self.fakeIpKey % kingdomId
            value = self.get(key)
            if value:
                return value

        if mainKey:
            key = self.fakeIpKey % mainKey

            if key:
                value = self.get(key)
                if value:
                    return value
            if fakeIp:
                if self.saveFakeIp(mainKey, kingdomId, fakeIp):
                    return fakeIp
        return None

    def clearFakeIp(self):
        """清理fakeIp信息"""
        if self.enable:
            keys = self.keys(self.fakeIpKey % "*")
            if len(keys) > 0:
                self.redisMgr.delete(*keys)
                return True
        return False

    @property
    def socks5Key(self):
        return "socks5_%s"

    def saveSocks5Info(self, mainKey, kingdomId=None, socks5=None):
        """
        保存socks5信息
        """
        key = None
        if mainKey:
            key = self.socks5Key % mainKey
            self.set(key, socks5, ex=WEEKNUM)
            if kingdomId:
                key = self.socks5Key % kingdomId
                self.set(key, socks5, ex=WEEKNUM)
            return True
        return False

    def getSocks5Info(self, mainKey, kingdomId=None, socks5=None):
        """
        获取socks5信息
        """
        if kingdomId:
            key = self.socks5Key % kingdomId
            value = self.get(key)
            if value:
                return value

        if mainKey:
            key = self.socks5Key % mainKey

            if key:
                value = self.get(key)
                if value:
                    return value
            if socks5:
                if self.saveSocks5Info(mainKey, kingdomId, socks5):
                    return socks5
        return None

    def clearSocks5(self):
        """清理socks5信息"""
        if self.enable:
            keys = self.keys(self.socks5Key % "*")
            if len(keys) > 0:
                self.redisMgr.delete(*keys)
                return True
        return False

    def clearTokens(self):
        """清理token信息"""
        if self.enable:
            keys = self.keys("token_*")
            if len(keys) > 0:
                self.redisMgr.delete(*keys)
                return True
        return False

    def saveEmailKey(self, email, pwd, t=None):
        """
        保存邮箱密码
        """
        key = "emailKey_%s|%s" % (email, pwd)
        if t is None:
            t = currentTime()
        self.set(key, t)

    def removeEmailKey(self, email, pwd):
        """
        删除邮箱密码
        """
        key = "emailKey_%s|%s" % (email, pwd)
        self.removeKey(key)

    def allEmailKey(self):
        """
        获取所有邮箱密码
        """
        if self.enable:
            keys = self.keys("emailKey_*")
            if len(keys) > 0:
                values = self.redisMgr.mget(*keys)
                values = [int(v) for v in values]
                keys = [key.decode("utf-8")[9:] for key in keys]
                mind = dict(zip(values, keys))
                tmp = [v for v in mind.keys()]
                tmp.sort()
                lastKeys = [mind[key] for key in tmp]
                return lastKeys
        return []

    def saveEmailKeyWithZone(self, email, pwd, zone, t=None, isDaily=False):
        """
        保存邮箱密码
        """
        key = "emailKey_%s|%s_%s" % (email, pwd, zone)
        if isDaily:
            key = "emailKey_daily_%s|%s_%s" % (email, pwd, zone)
        if t is None:
            t = currentTime()
        self.set(key, t)

    def removeEmailKeyWithZone(self, email, pwd, zone):
        """
        删除邮箱密码
        """
        key = "emailKey_%s|%s_%s" % (email, pwd, zone)
        self.removeKey(key)

    def allEmailKeyWithZone(self, zone):
        """
        获取所有指定区邮箱密码
        """
        if self.enable:
            keys = self.keys("emailKey_*_%s" % zone)
            if len(keys) > 0:
                values = self.redisMgr.mget(*keys)
                values = [int(v) for v in values]
                keys = [key.decode("utf-8")[9:].split("_")[0] for key in keys]
                mind = dict(zip(values, keys))
                tmp = [v for v in mind.keys()]
                tmp.sort()
                lastKeys = [mind[key] for key in tmp]
                return lastKeys
        return []

    def removeEmailKeyWithAll(self, email, pwd):
        """
        删除所有email数据
        """
        key = "emailKey_%s*" % email
        if self.enable:
            try:
                self.redisMgr.delete(*self.keys(key))
            except Exception:
                pass

    def resetEmailKeyWithZone(self, zone):
        """
        重置指定区邮箱数据
        """
        allEmails = self.allEmailKeyWithZone(zone)
        if len(allEmails):
            [self.removeEmailKeyWithZone(*v.split("|"), zone) for v in allEmails]
            [
                self.saveEmailKey(*v.split("|"), random.randint(10, 999999))
                for v in allEmails
            ]

    def saveUserKey(self, userKey, t=None):
        """
        保存游客数据
        """
        key = "userKey_%s" % userKey
        if t is None:
            t = currentTime()
        self.set(key, t)

    def removeUserKey(self, userKey):
        """
        删除游客数据
        """
        key = "userKey_%s" % userKey
        self.removeKey(key)

    def allUserKey(self):
        """
        返回所有游客数据
        """
        if self.enable:
            keys = self.keys("userKey_*")
            if len(keys) > 0:
                values = self.redisMgr.mget(*keys)
                newValues = []
                for v in values:
                    if v is None:
                        v = 0
                    newValues.append(int(v))
                keys = [key.decode("utf-8")[8:] for key in keys]
                mind = dict(zip(newValues, keys))
                tmp = [v for v in mind.keys()]
                tmp.sort()
                lastKeys = [mind[key] for key in tmp]
                return lastKeys
        return []

    def randomUserKey(self):
        """乱序游客数据"""
        if self.enable:
            keys = self.keys("userKey_*")
            if len(keys) > 0:
                values = self.redisMgr.mget(*keys)
                values = [int(v) for v in values]
                keys = [key.decode("utf-8")[8:] for key in keys]
                for i in range(len(values)):
                    v = values[i]
                    if v < 10:
                        k = keys[i]
                        self.saveUserKey(k, random.randint(11, 9999999))

    def allUserKeyDetail(self):
        """
        返回所有游客数据
        """
        if self.enable:
            keys = self.keys("userKey_*")
            if len(keys) > 0:
                values = self.redisMgr.mget(*keys)
                values = [int(v) for v in values]
                keys = [key.decode("utf-8")[8:] for key in keys]
                return dict(zip(values, keys))
        return []

    def getOneUserKey(self):
        """
        获取一个游客数据
        """
        keys = self.allUserKey()
        if len(keys):
            key = random.choice(keys)[:36]
            self.removeUserKey(key)
            return key
        return None

    def getOneLevel11UserKey(self):
        """
        获取一个11级游客数据
        """
        with self.redisMgr.lock("getOneLevel11UserKey"):
            keys = self.allUserKeyWithZone(1011)
            if len(keys):
                key = random.choice(keys)
                self.removeUserKeyWithZone(key, 1011)
                return key
        return None

    def clearUserKeys(self):
        """
        清除游客数据
        """
        if self.enable:
            keys = self.keys("userKey_*")
            if len(keys) > 0:
                self.redisMgr.delete(*keys)

    def saveUserKeyWithZone(self, userKey, zone, isDaily=False):
        """
        保存完成的游客数据
        """
        key = "userKey_%s_%s" % (userKey, zone)
        if isDaily:
            key = "userKey_daily_%s_%s" % (userKey, zone)
        self.set(key, currentTime())

    def removeUserKeyWithZone(self, userKey, zone):
        """
        删除完成的游客数据
        """
        key = "userKey_%s_%s" % (userKey, zone)
        self.removeKey(key)

    def allUserKeyWithZone(self, zone):
        """
        返回所有完成的游客数据
        """
        if self.enable:
            keys = self.keys("userKey_*_%s" % zone)
            if len(keys) > 0:
                values = self.redisMgr.mget(*keys)
                values = [int(v) for v in values]
                keys = [key.decode("utf-8")[8:].split("_")[0] for key in keys]
                mind = dict(zip(values, keys))
                tmp = [v for v in mind.keys()]
                tmp.sort()
                lastKeys = [mind[key] for key in tmp]
                return lastKeys
        return []

    def resetUserKeyWithZone(self, zone):
        """
        重置指定区游客数据
        """
        allUsers = self.allUserKeyWithZone(zone)
        if len(allUsers):
            [self.removeUserKeyWithZone(v, zone) for v in allUsers]
            [self.saveUserKey(v, random.randint(10, 999999)) for v in allUsers]

    def saveUserKeyWithUsed(self, userKey):
        """
        保存使用过的游客数据
        """
        key = "userKey_%s_used" % userKey
        self.set(key, currentTime())

    def removeUserKeyWithUsed(self, userKey):
        """
        删除使用过的游客数据
        """
        key = "userKey_%s_used" % userKey
        self.removeKey(key)

    def allUserKeyWithUsed(self):
        """
        返回所有使用过的游客数据
        """
        if self.enable:
            keys = self.keys("userKey_*_used")
            if len(keys) > 0:
                keys = [key.decode("utf-8")[8:].split("_")[0] for key in keys]
                return keys
        return []

    def removeUserKeyWithAll(self, userKey):
        """
        删除所有游客数据
        """
        key = "userKey_*%s*" % userKey
        if self.enable:
            try:
                self.redisMgr.delete(*self.keys(key))
            except Exception:
                pass

    def saveSignEndUser(self, ex, email=None, userKey=None):
        """
        保存签到结束的用户
        """
        key = None
        if email:
            key = "signEndUser_%s" % email
        elif userKey:
            key = "signEndUser_%s" % userKey
        if key:
            self.set(key, 1, ex=ex)
            return True
        return False

    def allSignEndUser(self):
        """
        返回所有签到结束的用户
        """
        if self.enable:
            keys = self.keys("signEndUser_*")
            if len(keys) > 0:
                keys = [key.decode("utf-8")[12:] for key in keys]
                return keys
        return []

    def saveAppleSubId(self, appleSubId) -> bool:
        """
        保存appleSubId
        """
        key = "appleSubId_%s" % appleSubId
        self.set(key, 1, logMsg=f"保存苹果id:{appleSubId}")
        return True

    def removeAppleSubId(self, appleSubId) -> bool:
        """
        删除appleSubId
        """
        key = "appleSubId_%s" % appleSubId
        self.removeKey(key)
        return True

    def getAllAppleSubId(self):
        """
        获取所有appleSubId
        """
        if self.enable:
            keys = self.keys("appleSubId_*")
            if len(keys) > 0:
                keys = [key.decode("utf-8")[11:] for key in keys]
                return keys
        return []

    def clearAppleSubId(self):
        """
        清除所有appleSubId
        """
        if self.enable:
            keys = self.keys("appleSubId_*")
            if len(keys) > 0:
                self.redisMgr.delete(*keys)
                return True
        return False

    def getOneAppleSubId(self):
        """
        获取一个苹果id
        """
        with self.redisMgr.lock("getOneAppleSubId"):
            keys = self.getAllAppleSubId()
            if len(keys):
                key = random.choice(keys)
                self.removeAppleSubId(key)
                return key
        return None

    def getIPBan(self, ip):
        """
        检测ip是否被ban了
        """
        key = "ipBan_%s" % ip
        value = self.get(key)
        if value:
            return True
        return False

    def setIPBan(self, ip):
        """
        设置ip被ban了
        """
        key = "ipBan_%s" % ip
        self.set(key, 1, ex=DAYNUM)

    def setStartTime(self, key):
        """
        设置开始时间
        """
        keyStr = "startTime_%s" % key
        t = int(time.time())
        self.set(keyStr, t, ex=DAYNUM * 2)
        return t

    def getStartTime(self, key):
        """
        获取开始时间
        """
        keyStr = "startTime_%s" % key
        t = self.get(keyStr)
        return t
        # if t is None:
        #     t = self.setStartTime(key)
        # t = int(t)
        # return t

    def removeStartTime(self, key):
        """
        删除开始时间
        """
        keyStr = "startTime_%s" % key
        self.removeKey(keyStr)

    def setProcessTime(self, key, value=None):
        """
        设置进度
        """
        keyStr = "processTime_%s" % key
        if value is None:
            value = 0
        else:
            value = int(value)
        self.set(keyStr, value, ex=DAYNUM * 2)
        return value

    def getProcessTime(self, key):
        """
        获取进度
        """
        keyStr = "processTime_%s" % key
        t = self.get(keyStr)
        if t is None:
            t = self.setProcessTime(key)
        t = int(float(t))
        return t

    def getDelKey(self, key):
        """获取删除的key"""
        keyStr = "delKey_%s" % key
        t = self.get(keyStr)
        return t

    def setDelKey(self, key):
        """设置删除的key"""
        keyStr = "delKey_%s" % key
        self.set(keyStr, 1, ex=DAYNUM)

    def saveWebUser(self, userId, user):
        """
        保存web用户
        """
        key = "webUser_%s" % userId
        self.set(key, user, ex=WEEKNUM)

    def removeWebUser(self, userId):
        """
        删除web用户
        """
        key = "webUser_%s" % userId
        self.removeKey(key)

    def getWebUser(self, userId):
        """
        获取web用户
        """
        key = "webUser_%s" % userId
        user = self.get(key)
        return user

    def saveAllianceBattleList(self, allianceId, battleList):
        """
        保存联盟战斗列表
        """
        key = "allianceBattleList_%s" % allianceId
        self.setJson(key, battleList, ex=MINUTESNUM * 5)

    def getAllianceBattleList(self, allianceId):
        """
        获取联盟战斗列表
        """
        key = "allianceBattleList_%s" % allianceId
        battleList = self.getJson(key)
        if battleList is None:
            battleList = []
        return battleList

    def getAllianceBattleListAll(self):
        """
        获取所有联盟id
        """
        keys = self.keys("allianceBattleList_*")
        return [key.decode("utf-8")[19:] for key in keys]

    def saveLstProtect(self, value):
        """
        设置保护时间
        """
        key = "lstProtect"
        self.setJson(key, value, ex=WEEKNUM)

    def getLstProtect(self):
        """
        获取保护列表
        """
        key = "lstProtect"
        lstProtect = self.getJson(key)
        if lstProtect is None:
            lstProtect = []
        return lstProtect

    def clearLstProtect(self):
        """
        清除保护列表
        """
        key = "lstProtect"
        self.removeKey(key)

    def saveFieldTasks(self, kingdomId, fieldTasks):
        """
        保存王国在野列表
        """
        key = "fieldTasks_%s" % kingdomId
        self.setJson(key, fieldTasks, ex=HOURNUM * 2)

    def getFieldTasks(self, kingdomId):
        """
        获取王国在野列表
        """
        key = "fieldTasks_%s" % kingdomId
        fieldTasks = self.getJson(key)
        if fieldTasks is None:
            fieldTasks = []
        else:
            self.removeFieldTasks(kingdomId)
        return fieldTasks

    def removeFieldTasks(self, kingdomId):
        """
        删除王国在野列表
        """
        key = "fieldTasks_%s" % kingdomId
        self.removeKey(key)

    def saveFieldTask(self, kingdomId, fieldTask):
        """
        保存王国在野数据
        """
        key = f'fieldTask_{kingdomId}_{fieldTask["_id"]}'
        self.setJson(key, fieldTask, ex=HOURNUM * 2)

    def getFieldTaskAll(self, kingdomId):
        """
        获取王国在野数据集合
        """
        keys = self.keys(f"fieldTask_{kingdomId}_*")
        fieldTasks = []
        for key in keys:
            fieldTask = self.getJson(key)
            self.removeKey(key)
            if fieldTask is not None:
                fieldTasks.append(fieldTask)
        return fieldTasks

    def setBan(self, mainKey):
        """设置禁止"""
        key = f"ban_{mainKey}"
        self.set(key, 1, ex=WEEKNUM)

    def getBan(self, mainKey):
        """获取禁止"""
        key = f"ban_{mainKey}"
        return self.get(key)

    def saveDKData(self, worldId, data):
        """保存王国数据"""
        key = f"dkData_{worldId}"
        self.setJson(key, data, ex=MINUTESNUM)

    def getDKData(self, worldId):
        """获取王国数据"""
        key = f"dkData_{worldId}"
        return self.getJson(key)

    def tryLockDKData(self, worldId):
        """尝试锁定王国数据"""
        key = f"dkData_{worldId}"
        self.redisMgr.lock(key)

    def setBufferUse(self, mainKey):
        """设置已使用buff"""
        key = f"buffer_{mainKey}"
        self.set(key, 1, ex=DAYNUM)

    def getBufferUse(self, mainKey):
        """获取buffer使用"""
        key = f"buffer_{mainKey}"
        return self.get(key)

    def setSelfRallyEffective(self, rallyId):
        """设置自己团战部队有效"""
        key = f"self_rally_{rallyId}"
        self.set(key, 1, ex=HOURNUM)

    def getSelfRallyEffective(self, rallyId):
        """获取自己团战部队有效"""
        key = f"self_rally_{rallyId}"
        return self.get(key)

    def getCeleryTaskId(self, mainKey):
        """获取任务id"""
        key = f"celery_task_id_{mainKey}"
        return self.get(key)

    def setCeleryTaskId(self, mainKey, task_id):
        """设置任务id"""
        key = f"celery_task_id_{mainKey}"
        self.set(key, task_id, ex=MONTHNUM * 6)

    def removeCeleryTaskId(self, mainKey):
        """删除任务id"""
        key = f"celery_task_id_{mainKey}"
        self.removeKey(key)

    def getBuyVipFinish(self, mainKey):
        """获取购买vip标识符"""
        key = f"user_{mainKey}_buyvip"
        self.get(key)

    def setBuyVipFinish(self, mainKey):
        """设置购买vip标识符"""
        key = f"user_{mainKey}_buyvip"
        self.set(key, 1, ex=DAYNUM)

    def setItemList(self, mainKey, value):
        """设置物品列表"""
        if not mainKey:
            return
        key = f"user_itemList_{mainKey}"
        self.setJson(key, value, ex=WEEKNUM)

    def getItemList(self, mainKey):
        """获取物品列表"""
        if not mainKey:
            return None
        key = f"user_itemList_{mainKey}"
        return self.getJson(key)

    def getItemLists(self):
        """获取所有物品列表"""
        keys = self.keys("user_itemList_*")
        values = self.redisMgr.mget(keys)
        return dict(
            zip(
                [key.decode("utf-8")[14:] for key in keys],
                [json.loads(v) for v in values],
            )
        )

    def setStockEnergy(self, mainKey, value):
        """设置库存能量"""
        key = f"user_{mainKey}_stockenergy"
        self.set(key, value, ex=WEEKNUM)

    def getStockEnergy(self, mainKey):
        """设置库存能量"""
        key = f"user_{mainKey}_stockenergy"
        value = self.get(key)
        if value:
            return int(value)
        return 0

    def setCrystalItem(self, mainKey, value):
        """设置库存砖石"""
        key = f"user_{mainKey}_crystalItem"
        self.set(key, value, ex=WEEKNUM)

    def getCrystalItem(self, mainKey):
        """设置库存砖石"""
        key = f"user_{mainKey}_crystalItem"
        value = self.get(key)
        if value:
            return int(value)
        return 0

    def setRecordCrystal(self, name, value, dateStr=None):
        """设置记录砖石"""
        if dateStr is None:
            dateStr = todayUTCStr()
        key = f"record_crystal_{dateStr}_{name}"
        self.set(key, value, ex=MONTHNUM)

    def getRecordCrystal(self, name, dateStr=None):
        """获取记录砖石"""
        if dateStr is None:
            dateStr = todayUTCStr()
        key = f"record_crystal_{dateStr}_{name}"
        value = self.get(key)
        if value:
            return int(value)
        return 0

    def getRecordCrystals(self):
        """获取记录砖石"""
        keys = self.keys("record_crystal_*")
        values = self.redisMgr.mget(keys)
        return dict(
            zip([key.decode("utf-8")[15:] for key in keys], [int(v) for v in values])
        )

    def getDiffRecordCrystal(self, name):
        """获取今日收益"""
        todayValue = self.getRecordCrystal(name)
        yesterdayValue = self.getRecordCrystal(name, dateStr=yesterdayUTCStr())
        return todayValue - yesterdayValue

    def setSafeCrystalLoc(self, loc):
        """设置安全矿点"""
        key = f'safe_crystal_{"_".join([str(i) for i in loc])}'
        self.set(key, 1, ex=HOURNUM)

    def getSafeCrystalLoc(self, loc):
        """获取安全矿点 True表示不可用"""
        key = f'safe_crystal_{"_".join([str(i) for i in loc])}'
        if self.get(key):
            return True
        return False

    def setSafeMonsterLoc(self, loc):
        """设置安全怪物点"""
        key = f'safe_monster_{"_".join([str(i) for i in loc])}'
        self.set(key, 1, ex=HOURNUM)

    def getSafeMonsterLoc(self, loc):
        """获取安全怪物点 True表示不可用"""
        key = f'safe_monster_{"_".join([str(i) for i in loc])}'
        if self.get(key):
            return True
        return False

    def getTodayRegisterCount(self):
        key = f"register_count_{todayStr()}"
        value = self.get(key)
        if value:
            return int(value)
        return 0

    def addTodayRegisterCount(self):
        key = f"register_count_{todayStr()}"
        value = self.getTodayRegisterCount()
        self.set(key, value + 1, ex=DAYNUM)

    def ping(self, kingdomId):
        if kingdomId:
            key = f"ping_{kingdomId}"
            self.set(key, 1, ex=30)

    def pong(self, kingdomId):
        if kingdomId:
            key = f"ping_{kingdomId}"
            if self.get(key):
                return True
        return False

    @property
    def socks5TimeoutKey(self):
        return "socks5Timeout"

    def removeSocks5Timeout(self):
        key = self.socks5TimeoutKey
        self.removeKey(key)

    def setSocks5TimeoutList(self, values):
        key = self.socks5TimeoutKey
        if self.redisMgr.enable:
            self.redisMgr.delete(key)

            if len(values):
                try:
                    self.redisMgr.sadd(key, *values)
                except Exception as e:
                    logger.error(f"setSocks5TimeoutList : {key} {values} {e}")

    def getSocks5TimeoutList(self):
        key = self.socks5TimeoutKey
        if self.redisMgr.enable:
            values = self.redisMgr.smembers(key)
            if values:
                return values
        return []

    def setSocks5TimeoutBackup(self, value, backup):
        key = f"{self.socks5TimeoutKey}_{value}"
        self.set(key, backup, ex=DAYNUM)

    def getSocks5TimeoutBackup(self, value):
        key = f"{self.socks5TimeoutKey}_{value}"
        return self.get(key)

    def clearSocks5TimeoutBackup(self):
        """清除socks5超时备份"""
        key = f"{self.socks5TimeoutKey}_*"
        keys = self.keys(key)
        if len(keys):
            self.redisMgr.delete(*keys)

    @property
    def garrisonRateKey(self):
        return "garrisonRateKey"

    def setGarrisonRates(self, value):
        if isinstance(value, list):
            self.setJson(self.garrisonRateKey, value)
        else:
            self.debug(f"setGarrisonRates 异常输入: {value}")

    def getGarrisonRates(self):
        rate = self.getJson(self.garrisonRateKey)
        if not rate:
            return [5, 3, 2]
        return rate

    def setRallySolwInfo(self, rallyId, name, speed):
        key = f"rallySlowV2_{rallyId}"
        info = {"name": name, "speed": speed}
        self.setJson(key, info, ex=60 * 10)

    def getRallySolwInfo(self, rallyId):
        key = f"rallySlowV2_{rallyId}"
        value = self.getJson(key)
        if value:
            return value
        return {"name": f"{rallyId}", "speed": 10}

    @property
    def cvcEventIdKey(self):
        # 大陆cvc活动id
        return "cvc_event_id_20"

    def getCvcEventId(self):
        """获取大陆cvc活动id"""
        key = self.cvcEventIdKey
        value = self.get(key)
        if not value:
            return None
        return value

    def setCvcEventId(self, value):
        """设置大陆cvc活动id"""
        key = self.cvcEventIdKey
        self.set(key, value, ex=60 * 60 * 24 * 14)

    @property
    def workDragosKey(self):
        """工作龙矿缓存"""
        return "workDragos_%s"

    def setWorkDragos(self, kingdomId, values):
        """设置工作龙矿缓存"""
        key = self.workDragosKey % kingdomId
        self.setJson(key, values, ex=DAYNUM * 30)

    def getWorkDragos(self, kingdomId):
        """获取工作龙矿缓存"""
        key = self.workDragosKey % kingdomId
        values = self.getJson(key)
        if values:
            return values
        return []


redisMgr = RedisMgr()
redisHelper = RedisHelper()

if __name__ == "__main__":
    logger.debug("test")
    redisHelper.set("hello", "world")

    hp = RedisHelper()
    # channel
    channelKey = "xh"
    hp.addChannelValue(channelKey, hp.repairs, "wek", "1")
    print(hp.repairs)

    print(hp.syncChannelList(channelKey, hp.repairs, {"sdf": "222", "qwe": "333"}))

    print(hp.repairs)

    hp.removeChannelKey(channelKey, hp.repairs, "wek")

    print(hp.repairs)

    # loc
    hp.addLoc(["1", "2", "3"])
    hp.addLoc(["1", "2", "3"])
    hp.removeLoc(["1", "2", "3"])
    hp.addLoc(["2", "2", "3"])
    print(hp.availableLoc())
    hp.removeLoc(["2", "2", "3"])
    print(hp.availableLoc())
