# /bin/env python3
# -*- coding: UTF-8 -*-

"""
功能描述：实现redis的功能
编写人：darkedge
编写日期：2023年03月28日

步骤分析：
   
"""

import redis
import platform
from Unit.Logger import logger


class RedisMgr(redis.Redis):
    def __init__(self, db=0):
        self.enable = True

        host = 'localhost'
        if platform.system() == 'Linux':
            host = 'redis'
        try:
            super().__init__(host=host, port=6379, db=db)
            # self.redis = super().__init__(host=host, port=6379, db=0)
            self.set("test", "test", ex=100)
        except redis.exceptions.ConnectionError:
            logger.error('redis连接失败')
            self.enable = False
        except Exception as e:
            logger.error('redis connect error: %s' % e)
            self.enable = False
