from Unit.Redis import redisHelper
from Model.Account import Account
from Unit.FileTool import loadConfig,writeConfig,get_file_md5
import copy

# 同步文件和redis的数据
def syncPathAndRedis(path,syncKey,accounts,redisHelperList,needClear=True):
    syncedList = {}
    syncList = {}

    for account in accounts:
        syncList[account.email] = account.text
    
    syncedList = redisHelper.syncChannelList(syncKey, redisHelperList, syncList)
    
    newAccounts = []
    removeList = []
    
    for email in syncedList:
        text = syncedList[email]
        if len(text) < 10:
            removeList.append(email)
            continue
        account = Account(text)
        newAccounts.append(account)

    #删除异常key
    for key in removeList:
        redisHelper.removeChannelKey(syncKey, redisHelperList, key)

    # 回写空数据
    if needClear:
        writeConfig([],path)
    else:
        if len(syncedList) != len(syncList):
            writeConfig(newAccounts,path)
    
    
    return copy.deepcopy(newAccounts)
