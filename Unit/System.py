# /bin/env python3
# -*- coding: UTF-8 -*- 

'''
功能描述：cnd
编写人：darkedge
编写日期：2023年04月24日

'''

import subprocess

def execute_command(command):
    process = subprocess.Popen(command, shell=True, stdout=subprocess.PIPE)
    output, error = process.communicate()
    return output.decode("utf-8")

def execute_commandSplit(command):
    process = subprocess.Popen(command.split(), stdout=subprocess.PIPE)
    output, error = process.communicate()
    return output.decode("utf-8")