# /bin/env python3
# -*- coding: UTF-8 -*-

"""
功能描述：加密解密
编写人：darkedge
编写日期：2022年06月22日

   
"""
import base64
import json
import os
import threading
import zlib

nodLocalKey = None
LocalConfig = None
keyPath = None
fileLock = threading.Lock()

for path in ["./", "../"]:
    p = f"{path}config.json"
    if os.path.exists(p):
        with open(p, "r") as f:
            text = f.read()
            config = json.loads(text)
            nodLocalKey = config["key"]
            LocalConfig = config
            print(nodLocalKey)
            keyPath = p


def changeNodKey(value, key="key"):
    try:
        fileLock.acquire()
        global nodLocalKey
        if key == "key":
            nodLocalKey = value
        with open(keyPath, "r+") as f:
            text = f.read()
            config = json.loads(text)
            config[key] = value
            string = json.dumps(config, indent=4)
            f.seek(0)
            f.write(string)
            f.truncate()
    except Exception:
        pass
    finally:
        fileLock.release()


def refreshNodKey():
    global nodLocalKey
    with fileLock:
        with open(keyPath, "r") as f:
            text = f.read()
            config = json.loads(text)
            value = config["key"]
            if nodLocalKey != value:
                print(f"refreshNodKey {nodLocalKey} -> {value}")
                try:
                    from Unit.Logger import logger

                    logger.info(f"refreshNodKey {nodLocalKey} -> {value}")
                except Exception as e:
                    logger.error(e, exc_info=True)
                nodLocalKey = value


class UserEncrypt:
    """新版加密解密"""

    autoExitIfChangeEncrypt = True
    """自动停止如果加密改变"""
    newNodKey: str = None

    @property
    def localConfig(self):
        return LocalConfig

    @property
    def nodKey(self):
        return nodLocalKey

    def checkRegionHash(self, regionHash):
        """检查区域hash 是否正确"""
        region = self.decodeRegionHash(regionHash)
        if region:
            if isinstance(region, str):
                splitRegion = region.split("-")
                if len(splitRegion) == 3:
                    if splitRegion[1] == self.nodKey:
                        return True
                    else:
                        self.newNodKey = splitRegion[1]
                        if not self.autoExitIfChangeEncrypt:
                            self.errorLog(
                                f"regionHash不一致 更新 {self.nodKey} -> {self.newNodKey}"
                            )
                            changeNodKey(self.newNodKey)
                            return True
        else:
            if not nodLocalKey:
                return True
        return False

    def decodeRegionHash(self, regionHash):
        return base64.b64decode(regionHash).decode()

    def encryptOrDecrypt(self, hexList):
        keys = self.nodKey
        for i in range(0, len(hexList)):
            hexList[i] = hexList[i] ^ ord(keys[i % len(keys)])
        return self.hexlist2bytes(hexList)

    def str2hexlist(self, string, bString=None):
        """字符串转换为十六进制列表"""
        if not bString:
            bString = string.encode()
        return list(bString)

    def hexlist2bytes(self, hexList):
        """十六进制列表转换为字节"""
        return bytes(hexList)

    def hexlist2str(self, hexList):
        """十六进制列表转换为字符串"""
        bString = self.hexlist2bytes(hexList)
        string = bString.decode()
        return string

    def encrypt(self, string):
        """加密"""
        if not self.nodKey:
            return base64.b64encode(string.encode()).decode()
        hexList = self.str2hexlist(string)
        encryptHexs = self.encryptOrDecrypt(hexList)
        return base64.b64encode(encryptHexs).decode()

    def decrypt(self, b64String):
        """解密"""
        encryptBStr = base64.b64decode(b64String)
        if not self.nodKey:
            return encryptBStr.decode()
        hexList = self.str2hexlist("", bString=encryptBStr)
        decryptHexs = self.encryptOrDecrypt(hexList)
        decryptStr = self.hexlist2str(decryptHexs)
        return decryptStr

    def jsonToBase64String(self, jsonValue):
        """
        json转base64
        """
        return self.encrypt(json.dumps(jsonValue))

    def base64StringToJson(self, base64String):
        """
        base64转json
        """
        # return json.loads(base64.b64decode(base64String).decode())
        return json.loads(self.decrypt(base64String))

    def uint8ArrayToJson(self, packs, b64=True):
        """
        从uint8解码到base64
        再从base64转成json
        """
        b = bytes(packs)
        deb = zlib.decompress(b, 31)
        b64String = deb.decode()
        if b64:
            return self.base64StringToJson(b64String)
        else:
            return json.loads(b64String)


if __name__ == "__main__":
    # a = [84,77,38,105,26,213,182,226,134,199,190,21,28]
    # print(UserEncrypt().encryptOrDecrypt(a))
    # a= "VE0maRrVtuKGx74VHB"

    a = "东方红"
    b = UserEncrypt().encrypt(a)
    print(b)
    c = UserEncrypt().decrypt(b)
    print(c)
    # print(UserEncrypt().encryptOrDecrypt([0,0,0,0,0,0,0]))
    # a = UserEncrypt().encrypt("{'token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJfaWQiOiI2MmEzZTYxYTY1NjcwMDAxYzczZDZiY2QiLCJraW5nZG9tSWQiOiI2MmEzZTYxYTY1NjcwMDAxYzczZDZiZDAiLCJ2ZXJzaW9uIjoxNDYyLCJidWlsZCI6MCwicGxhdGZvcm0iOiJpb3MiLCJ0aW1lIjoxNjU2MDAxNjQ5NTAwLCJjbGllbnRYb3IiOiIwIiwiaWF0IjoxNjU2MDAxNjQ5LCJleHAiOjE2NTY2MDY0NDksImlzcyI6Im5vZGdhbWVzLmNvbSIsInN1YiI6InVzZXJJbmZvIn0.NHtuBtEF-dPLO1YVoLCZD5Jn2oPgPeC3K6FjI0gPWks'}")
    # b = UserEncrypt().decrypt(a)
    # print(b)
