#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
"""
功能描述：UserInfo聚合功能
编写人：darkedge
编写日期：2022年06月07日
   
"""
import asyncio
import datetime
import math
import random
import secrets
import threading
import time
from concurrent.futures import ThreadPoolExecutor

import schedule
import websocket

from Api.UserInfo import Enum, StartTaskEnum, UserInfo, todayUTCStr, removeDragoMineListWithMember
from Api.User.AutoOptions import TreasureAttackPages,TreasureCollectPages
from Model.UserError import UserError
from Unit.FileTool import loadS5List
from Unit.LandZoneUnit import MaxSixThreadZoneSize, crystalLands, PolygonWithPoint, isPointOnPolygonEdge, NewPolygon
from Unit.Logger import logger
from Unit.Redis import currentHour, currentMinute, isUTCMonday, redisHelper
from Celery.redis import redisCeleryHelper
from Unit.UserEncrypt import refreshNodKey
from Api.FlaskHelper import updateToken

BaseWorldId = 20
NotMyWorldIds = [25, 28, 29]
MineDelayWorldIds = [24,17,]

class MiningHelper:
    """挖矿helper"""

    lastRallyTime = 0
    """最后查团时间"""
    user: UserInfo
    """用户API"""

    reEnterCount = 0
    """重进次数"""

    lastAutoTreatmentTime = 0
    """最后自动治疗时间"""

    maxMonsterDistance = 50
    """最大怪物距离"""

    canSearchMonster = True
    """是否可以搜怪"""

    workDragos = None
    """工作龙"""

    autoJoinAllianceTag = None
    """自动加入联盟标签"""

    monsterCount = 0
    """怪物计数器"""

    onlyMonster = False
    """只打怪"""
    def __init__(self, user: UserInfo):
        self.user: UserInfo = user
        self.user.useBarkNoti = True
        self.helpUserKey = None
        self.maxTroopNum = 2000
        self.battleTroopNum = 30000
        self.marchLimit = None
        """最大队列数量"""
        self.descLoc = user.loc
        self.autoPackage = False
        self.noInTerritory = False
        self.isJoinAllianceBattle = False
        self.autoMonster = False
        self.autoWar = False
        self.autoStartRally = False
        self.isGoback = False
        self.autoBuy = True
        self.autoSkill = False
        self.autoEat = False
        self.highDelay = True
        self.crystalAcceleration = False
        self.autoVip = True
        self.autoUseItem = False
        self.autoPkg = True
        self.autoLevelUp = False
        self.autoGlobalCrystal = False
        self.deputyToken = None
        self.maxCrystal = 999999
        self.todayGetCrystal = 0
        self.maxCrystalLevel = 9
        """最大挖矿等级"""
        self.minCrystalLevel = 1
        """最小挖矿等级"""
        self.autoMinGame = False
        """自动小游戏"""
        self.autoCollection = False
        """自动采集资源"""

        self.today = None
        self.crystalNow = 0
        self.crystalList = []
        self.crystalListLock = threading.Lock()
        self.canRepair = True
        self.helperUser: UserInfo = None

        self.monsterList = []
        self.monsterListLock = threading.Lock()

        self.resourceList = []
        self.resourceListLock = threading.Lock()

        self.miningTimes = range(24)
        self.battleTimes = range(24)

        self.detailKillList = {
            "anyLevel": 5,
            "20200202": 2,
            "20200203": 1,
            "20200204": 1,
            "20700505": 1,
            "20700506": 4,
            "20200206": 1,
            "20200207": 1,
        }

        self.searchHelper = AppleUserHelper(user)
        self.globalTime = 0
        self.autoReLogin = True
        """自动重新登录"""

    def updateWithDict(self, dic):
        # 读取字典到自身
        self.__dict__.update(dic)
        # 转换工作龙
        if self.workDragos:
            if isinstance(self.workDragos, str):
                self.workDragos = self.workDragos.split(",")
            self.workDragos = [int(i) for i in self.workDragos]
            self.user.workDragos = self.workDragos
            redisHelper.setWorkDragos(self.user.kingdomId, self.workDragos)
        return self

    def addCrystal(self, info):
        """添加水晶"""
        with self.crystalListLock:
            loc = info.get("loc")
            level = info.get("level")
            if (
                level > self.maxCrystalLevel
                or level < self.minCrystalLevel
                or self.user.inCVC
            ):
                return
            self.user.debuglog(f"水晶队列 准备添加水晶 {loc}")
            if info not in self.crystalList and loc not in self.user.crystalHistory:
                d1 = self.user.distanceWithLoc(loc)
                for i in range(len(self.crystalList)):
                    currentInfo = self.crystalList[i]
                    currentLoc = currentInfo.get("loc")
                    currentLevel = currentInfo.get("level")
                    if currentLoc == loc:
                        return
                    d2 = self.user.distanceWithLoc(currentLoc)
                    if d2 > d1 or level > currentLevel:
                        self.crystalList.insert(i, info)
                        self.user.debuglog(f"水晶队列 添加水晶插入 {loc}")
                        return
                self.user.debuglog(f"水晶队列 添加水晶 {loc}")
                self.crystalList.append(info)

    def removeCrystal(self, loc):
        """移除水晶"""
        with self.crystalListLock:
            for info in self.crystalList:
                if info.get("loc") == loc:
                    self.user.debuglog(f"水晶队列 删除水晶{loc}")
                    self.crystalList.remove(info)
                    return

    def clearCrystalList(self):
        """清空水晶列表"""
        with self.crystalListLock:
            self.crystalList.clear()

    def getCrystalOne(self) -> dict:
        """获取一个水晶信息"""
        with self.crystalListLock:
            self.user.placeHolderStr = str(len(self.crystalList))
            if len(self.crystalList) > 0:
                return self.crystalList.pop(0)
        return None

    def addResources(self, resources):
        """添加资源"""
        with self.resourceListLock:
            self.resourceList.clear()
            self.resourceList.extend(resources)
            self.user.debuglog(f'资源队列 添加资源 {len(resources)}')

    def removeResource(self, loc):
        """移除资源"""
        with self.resourceListLock:
            for resource in self.resourceList:
                if resource.get("loc") == loc:
                    self.resourceList.remove(resource)
                    return

    def getResourceOne(self) -> dict:
        """获取一个资源"""
        resource = None
        with self.resourceListLock:
            if len(self.resourceList) > 0:
                resource = self.resourceList.pop(0)
        if resource and resource.get("loc") in self.user.crystalHistory:
            return self.getResourceOne()
        return resource

    def getMaxDistance(self):
        """获取最大怪物距离"""
        if self.onlyMonster:
            self.maxMonsterDistance = 400
            return
        distance = redisCeleryHelper.getMaxMonsterDistance()
        if distance == 0:
            self.maxMonsterDistance = 1
        else:
            if self.user.inCVC:
                self.maxMonsterDistance = distance
            else:
                self.maxMonsterDistance = 10
                if self.autoCollection:
                    self.maxMonsterDistance = 70

    def addMonsters(self, monsters):
        """添加怪物"""
        with self.monsterListLock:
            self.monsterList.clear()
            # 过滤距离
            monsters = list(filter(lambda x: self.user.distanceWithLoc(x.get("loc")) <= self.maxMonsterDistance, monsters))
            self.user.debuglog(f"怪物队列 准备添加怪物 {len(monsters)} 距离{self.maxMonsterDistance}")
            if self.user.inCVC:
                polygon = PolygonWithPoint(self.user.loc[-2], self.user.loc[-1])
                #过滤cvc块
                monsters = list(filter(lambda x: isPointOnPolygonEdge(x.get("loc")[-2:],polygon=polygon), monsters))
            self.monsterList.extend(monsters)
            self.user.debuglog(f'怪物队列 添加怪物 {len(monsters)}')

    def removeMonster(self, loc):
        """移除怪物"""
        with self.monsterListLock:
            for monster in self.monsterList:
                if monster.get("loc") == loc:
                    self.monsterList.remove(monster)
                    return

    def getMonsterOne(self) -> dict:
        """获取一个怪物"""
        with self.monsterListLock:
            if len(self.monsterList) > 0:
                self.getMaxDistance()
                return self.monsterList.pop(0)
        return None

    def getRallyMonsterOne(self):
        """获取一个团怪"""
        monsters = sorted(redisCeleryHelper.getRallyMonsterList(self.user.worldId), key= lambda x: int(x.split("_")[0]), reverse= True)
        dragonMonsters = list(filter(lambda x: int(x.split("_")[0]) < 4 , monsters))
        otherMonsters = list(filter(lambda x: int(x.split("_")[0]) >= 4 , monsters))

        monster = None
        monsters = dragonMonsters or otherMonsters

        if monsters:
            monster = monsters[0]
            loc = [int(k) for k in monster.split("_")]
            spLevel = loc[0]
            minDistance = 9999
            for m in monsters:
                loc = [int(k) for k in m.split("_")]
                if loc[0] != spLevel:
                    continue

                distance = self.user.distanceWithLoc(loc[1:])
                if distance < minDistance:
                    minDistance = distance
                    monster = m


        if monster:
            redisCeleryHelper.removeRallyMonsterListWithMember(self.user.worldId, monster)
            loc = [int(k) for k in monster.split("_")]
            loc[0] = self.user.worldId
            if loc in self.user.attackedList:
                return self.getRallyMonsterOne()
            self.user.log(f'剩余团怪数量:{len(monsters) - 1}')
            return monster

        return None

    def getCVCMonsterOne(self):
        """获取一个CVC(Clash vs Clash)怪物
        Returns:
            dict: 包含怪物等级和位置信息的字典，如果没有可用怪物则返回None
        """
        # 从Redis获取CVC怪物列表，使用worldId * 10作为key
        monsters = redisCeleryHelper.getRallyMonsterList(self.user.worldId * 10)
        if not monsters:
            return None
        # 将怪物信息转换为标准格式，包含等级和位置信息
        # 格式: [{"level": 等级, "loc": [世界ID, x坐标, y坐标]}]
        infos = [{"level": int(m.split("_")[0]), "loc": [self.user.worldId, int(m.split("_")[1]), int(m.split("_")[2])]} for m in monsters]
        # 根据历史攻击记录对怪物进行排序，优先选择未攻击过的怪物
        infos = self.user.returnSortFields(infos, historyLoc=self.user.attackedList, levelPriority= self.onlyMonster)
        if infos:
            # 获取第一个怪物信息
            info = infos[0]
            loc = info.get("loc")
            level = info.get("level")
            # 构造怪物标识符
            value = f"{level}_{loc[1]}_{loc[2]}"
            # 从Redis中移除该怪物
            redisCeleryHelper.removeRallyMonsterListWithMember(self.user.worldId * 10, value)
            # 如果该怪物已被攻击过，递归获取下一个怪物
            if loc in self.user.attackedList:
                return self.getCVCMonsterOne()
            # 记录日志
            self.user.debuglog(f"打怪使用cvc怪 {loc}")
            # 将该怪物标记为已拒绝，防止其他玩家选择
            redisCeleryHelper.setRejectMonster(value)
            # 返回怪物信息
            return info
        return None

    def locInTerritory(self, loc):
        return False
        # x = loc[1]
        # y = loc[2]

        # for territory in territoryList:
        #     territoryX = territory[0]
        #     territoryY = territory[1]
        #     if abs(x - territoryX) < 12 and abs(y - territoryY) < 12:
        #         return True
        # return False

    def fieldCallBack(self, info):
        """field回调"""
        charm = self.user.charmCodeCheck(info)
        loc = info.get("loc")
        if charm:
            self.user.info(f"{charm} {loc}")
        else:
            code = info.get("code")
            if code and code == 20100105:
                occupied = info.get("occupied")
                if occupied:
                    self.removeCrystal(loc)
                    return
                param = info.get("param")
                value = param.get("value")

                if value % 50 == 0:
                    if not self.locInTerritory(loc):
                        self.user.debuglog(f"水晶队列 需要添加水晶 {loc} {value}")
                        self.addCrystal(info)

                    # else:
                    #     key = f'{loc[1]}_{loc[2]}'
                    #     if not territoryLocs.get(key):
                    #         territoryLocs[key] = 1
                    #         self.user.info(f"联盟水晶 {loc}")
                else:
                    self.user.info(f"水晶队列 尾矿 {loc} {value}")

    def getTodayCrystal(self):
        u1 = self.user
        if u1.crystal > 0 and self.today != todayUTCStr():
            value = u1.getKingdomTodayCrytstal()
            if not value:
                u1.setKingdomTodayCrystal(u1.crystal)
            self.today = todayUTCStr()
        return u1.getKingdomTodayCrytstal()

    def checkMaxCrystal(self):
        """检查水晶数量是否大于最大值"""
        return self.todayGetCrystal < self.maxCrystal

    def checkInventoryEnery(self, minValue = 200000):
        """检查库存能量"""        
        value = redisHelper.getStockEnergy(self.user.email) 
        return value < minValue and value > 0
        
    def checkMaxFieldTasks(self):
        """检测最大队列"""
        if self.marchLimit == 1:
            # 单队列模式 当他为2时跳过
            # 当他当前含矿量>100时跳过
            if len(self.user.fieldTasks) >= 2:
                return False
            for fieldTask in self.user.fieldTasks:
                code = fieldTask.get("code", 0)
                if code == 4:
                    param = fieldTask.get("param", {})
                    amount = param.get("amount", 0)
                    if amount >= 100:
                        return False
            return True
        else:
            return len(self.user.fieldTasks) < self.marchLimit

    def checkMaxCrystalFieldTasks(self, maxCount = 6):
        """超过水晶上限"""
        self.user.clearFieldTasks()
        if self.marchLimit <= len(self.user.fieldTasks):
            return False
        crystalTasks = list(filter(lambda x: x.get("param",{}).get("gatherType",0) == 4, self.user.fieldTasks))
        if len(crystalTasks) > maxCount and self.isJoinAllianceBattle:
            return False
        return True

    def checkMaxRallyFieldTasks(self,maxCount = 5):
        """超过开团上限"""
        self.user.clearFieldTasks()
        if self.marchLimit <= len(self.user.fieldTasks):
            return False
        rallyTasks = list(filter(lambda x: x.get("code") in [5,10,11], self.user.fieldTasks))
        if len(rallyTasks) > maxCount:
            return False
        return True

    def checkMaxResourceFieldTasks(self, maxCount = 6):
        """超过采集资源上限"""
        self.user.clearFieldTasks()
        if self.marchLimit <= len(self.user.fieldTasks):
            return False
        resourceTasks = list(filter(lambda x: x.get("param",{}).get("gatherType",4) < 4, self.user.fieldTasks))
        if len(resourceTasks) > maxCount:
            return False
        return True

    def checkNeedGoBackTask(self):
        if self.marchLimit == 1:
            if len(self.user.fieldTasks) > 1:
                tasks = {}
                sum = 0
                for fieldTask in self.user.fieldTasks:
                    code = fieldTask.get("code", 0)
                    status = fieldTask.get("status", 0)
                    if code == 4 and status == 1:
                        param = fieldTask.get("param", {})
                        amount = param.get("amount", 0)
                        sum += amount
                        tasks[amount - random.randint(1, 100) / 100] = fieldTask

                if sum > 100:
                    allKeys = list(tasks.keys())
                    if len(allKeys) > 1:
                        allKeys.sort()
                        task = tasks[allKeys[0]]
                        param = task.get("param")
                        moId = param.get("moId")
                        self.user.debuglog(
                            f"队列:{self.user.fieldTasks},当前撤回{moId}"
                        )
                        self.user.fieldMarchReturn(moId)
                        self.user.randomSleep(5, 10, msg="当前队列收益溢出 主动撤回")

    def getNetToken(self):
        """获取洗脚城token"""
        # 洗脚城获取坐标
        return self.searchHelper.getAppleUser()
        deputyToken = self.getLocalRegisterUser()
        # deputyToken = requestToken()
        if not deputyToken:
            self.user.errorLog("洗脚城获取token异常")
            if not self.deputyToken:
                print("无token退出")
                exit(0)
        # else:
        #     self.user.log(f"从洗脚城获取token:{deputyToken}")
        #     self.deputyToken = deputyToken

    def tryVipAndMint(self):
        if self.user.isInvalid:
            return
        try:
            # u1.debuglog("schedule触发tryHarvestAndMint")
            if self.autoVip:
                # hour = currentHour()
                # if hour < 8 and hour >= 2:
                #     '''自动领取只在每天凌晨2-8点生效'''
                self.user.tryClaimVip()
                self.user.tryClaimDsaVip()
                self.user.tryFreeChests()

            self.user.itemList()
            # 禁用mint
            if self.autoPackage:
                self.user.tryUseDragonMint()
            # if self.autoPackage and self.user.myresources():
            #     self.user.mintMaxResource(minResource=1000000*20,randomAny=True)
            if self.autoPackage and self.user.crystal > 10_0000 and not isUTCMonday():
                self.user.autoBuyVipShopAll()
            if self.autoBuy:
                hour = currentHour()
                if hour % 2 == 0:
                    self.user.tryAutoBuyCaravan()

        except Exception as e:
            self.user.errorLog(e, exc_info=True)

    def tryHarvestAndClearMail(self):
        try:
            if not self.user.isBug:
                self.user.tryHarvestAll()
            if self.autoPkg:
                self.user.tryClaimPkg()
            for _ in range(10):
                if not self.user.tryDeleteResourceMail():
                    break

            self.user.tryAllianceGiftClaimAll()
            if self.user.allianceId and not self.user.getAllianceResearchMax():
                if False not in [v > 100 * 1000 * 1000 for v in self.user.resources]:
                    self.user.log("尝试捐科技")
                    if not self.user.tryAllianceDonateAll(maxLevel=30):
                        self.user.errorLog("捐科技异常")
            self.user.printSelf()
        except Exception as e:
            self.user.errorLog(e, exc_info=True)

    def tryAutoSummon(self):
        """自动召唤"""
        if not self.user.inCVC and self.user.worldId == BaseWorldId:
            self.user.autoSummon()

    def tryCollectionEvent(self):
        if self.autoCollection and self.user.inCollectionEvent:
            if self.user.autoCollectionEvent():
                self.user.log("自动采集活动完成")
                self.autoCollection = False

    def trySearchCrystalList(self, isClear=True):
        """搜索水晶"""
        power = 4
        # if self.autoCollection:
        #     power = 2
        maxMarchLimit = self.marchLimit == 1 and 2 or self.marchLimit
        if len(self.user.fieldTasks) >= maxMarchLimit:
            return
        elif maxMarchLimit - len(self.user.fieldTasks) < 3:
            return

        helperUser = self.searchHelper.buildHelperUser()
        self.user.debuglog(f"开始搜索水晶{power} loc:{self.user.loc}")

        helperUser.tmpMarchList = {}

        def marchCallBack(objects):
            """行军回调"""
            for object in objects:
                loc = object.get("toLoc")
                # trunk-ignore(ruff/F841)
                key = f"{loc[1]}_{loc[2]}"
                # helperUser.tmpMarchList[key] = 1

        t = time.time()
        # objects = helperUser.wsGetFields(loc=self.user.loc, power=power, more=True,
        #  marchCallBack=marchCallBack, isCrystalOnly=True, isCVC=self.user.worldId > 10000)

        codes = Enum.OBJECT_MONSTER_CODE_LIST + [Enum.OBJECT_CODE_CRYSTAL_MINE]
        if self.autoCollection:
            codes = Enum.OBJECT_MONSTER_CODE_LIST + Enum.OBJECT_MINE_CODE_LIST

        objects = None

        if self.autoCollection:
            objects = helperUser.searchCrystalWithZoneSize(
                loc=self.user.loc, zones=self.user.coordinateTransformation(self.user.loc, power), codes = codes
            )
        else:
            objects = helperUser.searchCrystalWithZoneSize(
                loc=self.user.loc, zoneSize=MaxSixThreadZoneSize // 2, codes = codes
            )
    
        crystals = []
        try:
            if isinstance(objects, dict):
                crystals = objects.get(Enum.OBJECT_CODE_CRYSTAL_MINE, [])
                monsters = [item for key,subList in objects.items() if key in Enum.OBJECT_MONSTER_CODE_LIST for item in subList]
                if monsters:
                    monsters = helperUser.returnSortFields(monsters, historyLoc=self.user.attackedList, levelPriority= self.onlyMonster)
                    monsters = list(filter(lambda x: not x.get("hidden"), monsters))
                    self.addMonsters(monsters)
                if self.autoCollection:
                    resources = [item for key,subList in objects.items() if key in Enum.OBJECT_MINE_CODE_LIST and key != Enum.OBJECT_CODE_CRYSTAL_MINE for item in subList]
                    if resources:
                        resources = helperUser.returnSortFields(resources, historyLoc=self.user.crystalHistory)
                        resources = list(filter(lambda x: not x.get("hidden"), resources))
                        self.addResources(resources)

            elif isinstance(objects, list):
                crystals = objects

        # trunk-ignore(ruff/E722)
        except Exception as e:
            if self.autoCollection:
                self.user.errorLog(e, exc_info=True)
        dt = time.time() - t
        self.user.debuglog(
            f"结束搜索水晶{power} 耗时{round(dt,2)},有效数据{len(crystals) or 0}"
        )
        if not crystals:
            if helperUser.isInvalid:
                self.getNetToken()
            return

        if isClear:
            self.clearCrystalList()
        crystals = helperUser.returnSortFields(
            crystals, historyLoc=self.user.crystalHistory
        )
        for crystal in crystals:
            hidden = crystal.get("hidden")
            occupied = crystal.get("occupied")
            if hidden or occupied:
                continue
            self.addCrystal(crystal)

    def trySearchMonster(self, isExtra = False):
        """搜怪"""
        power = 3
        helperUser = self.searchHelper.buildHelperUser()
        if self.autoCollection and not self.user.inCVC:
            power = 2
        if self.onlyMonster:
            power = 4
        self.user.debuglog(f"开始搜索怪物{power} loc:{self.user.loc} isExtra:{isExtra}")
        t = time.time()
        objects = []
        if isExtra:
            # 获取内圈和外圈区域
            inner_zones = self.user.cvcCoordinateTransformation(self.user.loc, 4) if self.user.inCVC else self.user.coordinateTransformation(self.user.loc, 4)
            outer_zones = self.user.cvcCoordinateTransformation(self.user.loc, 5) if self.user.inCVC else self.user.coordinateTransformation(self.user.loc, 5)
            real_zones = list(filter(lambda x: x not in inner_zones, outer_zones[1:]))
            search_zones = [outer_zones[0]] + random.sample(real_zones, min(45, len(real_zones)))

            objects = helperUser.wsGetFields(loc=self.user.loc, power=power, more=True, zone=search_zones)
            self.user.debuglog(f"额外搜索 : {objects and len(objects) or 0}")
        else:
            objects = helperUser.wsGetFields(loc=self.user.loc, power=power, more=True)
        monsters = []

        try:
            if isinstance(objects, dict):
                monsters = [item for key,subList in objects.items() if key in Enum.OBJECT_MONSTER_CODE_LIST for item in subList]
                if monsters:
                    monsters = helperUser.returnSortFields(monsters, historyLoc=self.user.attackedList, levelPriority= self.onlyMonster)
                    monsters = list(filter(lambda x: not x.get("hidden"), monsters))
                    if self.onlyMonster and not isExtra and len(monsters) < 200:
                        # 当前打怪模式且范围内的挂低于预期
                        extraMonsters = self.trySearchMonster(isExtra=True)
                        # 外部搜索的怪更多(假设等级分布一样)
                        if extraMonsters:
                            if extraMonsters[0].get("level") > monsters[0].get("level"):
                                monsters = extraMonsters
                    if isExtra:
                        self.user.debuglog(f"结束搜索怪物 额外 有效数据{len(monsters)}")
                        return monsters
                    self.addMonsters(monsters)
                resources = [item for key,subList in objects.items() if key in Enum.OBJECT_MINE_CODE_LIST and key != Enum.OBJECT_CODE_CRYSTAL_MINE for item in subList]
                if resources:
                    resources = helperUser.returnSortFields(resources, historyLoc=self.user.crystalHistory)
                    resources = list(filter(lambda x: not x.get("hidden"), resources))
                    self.addResources(resources)

        # trunk-ignore(ruff/E722)
        except Exception as e:
            self.user.errorLog(e, exc_info=True)
        dt = time.time() - t
        self.user.debuglog(
            f"结束搜索怪物{power} 耗时{round(dt,2)},有效数据{len(monsters) or 0}"
        )
        if not monsters:
            if helperUser.isInvalid:
                self.getNetToken()
            return

    def tryRallyMonitor(self):
        """尝试团监控"""
        # helperUser = self.searchHelper.buildHelperUser()
        pass

    def tryGetMail(self):
        try:
            self.user.checkMailInfo()
            self.user.randomSleep(1, 3)
            res = self.user.mailList(3)
            if res:
                mails = res.get("mails")
                if mails:
                    for mail in mails:
                        like = mail.get("like")
                        if not like:
                            subject = mail.get("subject")
                            if subject:
                                title = subject.get("title")
                                if title and title == "Warning":
                                    self.user.errorLog("收到警告邮件?")
                                    self.user.mailLike(mail.get("_id"))
                                    self.user.barkNoti("收到警告邮件", isAdmin=True)
                                    # exit(0)
        except Exception as e:
            self.user.errorLog(e, exc_info=True)

    def taskAll(self):
        """获取队列"""
        try:
            self.reEnterCount += 1
            if self.reEnterCount > 12:
                self.reEnterCount = 0
                self.user.wsWithKingdomClose()
                self.user.enter()
                self.user.wsWithKingdomRestart()
                return
            self.user.enterKingdomAsyncTasks()
            self.user.randomSleep(3, 5)
            self.user.wsGetFields(power=1)
        except Exception as e:
            self.user.errorLog(e, exc_info=True)

    def tryJoinAllianceBattle(self):
        """
        尝试加入联盟战斗
        检查时间是否在战斗时间段内
        检查是否在CVC活动中
        检查是否需要挖矿保护
        自动开团和跟团逻辑
        """
        # 获取当前时间
        nowDate = datetime.datetime.now()
        hour = currentHour()
        # 检查是否在战斗时间段内
        if hour not in self.battleTimes:
            return

        # CVC活动检查
        if self.user.inCVC and not self.user.cvcEventOpen:
            return
        
        # 挖矿保护时间检查
        minute = nowDate.minute
        if 5 < minute < 20:
            if self.user.worldId == BaseWorldId:
                # 挖矿保护 只在9-57分钟内挖矿 21区
                self.user.debuglog("挖矿保护ing")
                return

        # 自动开团检查
        if self.autoStartRally:
            # 自动开团
            self.tryAutoStartRally()

        # 检查是否可以跟新团
        if not self.user.newRally:
            t = time.time()
            # 3分钟内不重复跟团
            if t - self.lastRallyTime > 60 * 3:
                self.lastRallyTime = t
                self.user.newRally = True
                self.user.debuglog("重置最后跟团时间")
            else:
                return

        # 判断是否参与战斗
        warRally = self.autoWar
        # 如果有护盾,则根据护盾开关决定是否参与战斗
        if warRally and self.user.hasShield():
            warRally = redisCeleryHelper.getShieldRallySwitch()
            if not warRally:
                self.user.debuglog("已开盾 不跟战争团")

        # 如果在联盟中,尝试加入战斗
        if self.user.allianceId:
            self.user.autoJoinBattle(
                troopNum=self.battleTroopNum,  # 出战部队数量
                minGroupCount=1200000,  # 最小集结人数
                detailKillList=self.detailKillList,  # 详细击杀列表
                autoEnergy=True,  # 自动补充体力
                warJoin=warRally,  # 是否参与战争
            )

    def tryAutoStartRally(self):
        # 检查是否在停止开团时间内(30分钟)
        if time.time() - self.user.stopOpenRallyTime < 60 * 30:
            self.user.debuglog("停止开团中")
            return

        # 获取当前小时
        hour = currentHour()
        # 8-9点期间检查卡冈活动
        if hour in [8, 9]:
            if self.user.compareTimeWithNow(self.user.garantuaEventTime) > 0:
                self.user.debuglog("卡冈活动时间 停止开团中")
                return

        # 检查活力值是否足够
        if self.user.actionPoint < 200:
            # 尝试补充活力值
            if redisHelper.getStockEnergy(self.user.email) < 1000 or self.user.chargeEnergy(200) == 0:
                return

        # 设置最大尝试次数
        maxTest = 5
        # CVC期间只尝试1次
        if self.user.inCVC:
            maxTest = 1
        for _ in range(maxTest):
            # 设置最大队列数
            maxFieldTasks = self.user.inCVC and 10 or 5
            # 自动采集时最大队列数为4
            if self.autoCollection and not self.user.inCVC:
                maxFieldTasks = 4
            # 检查是否可以开启新的集结
            if self.checkMaxRallyFieldTasks(maxFieldTasks):
                # 获取一个集结目标
                monster = self.getRallyMonsterOne()
                if not monster:
                    self.user.debuglog("触发开团 有效队列 目标为空")
                    time.sleep(10)
                    break
                loc = [int(k) for k in monster.split("_")]
                loc[0] = self.user.worldId
                self.user.debuglog(f"触发开团 有效队列 目标loc:{loc}")
                # 开启集结
                res = self.user.startRally(loc, 3, self.battleTroopNum)
                if res == StartTaskEnum.success:
                    redisCeleryHelper.setRejectMonster(monster)
                    self.user.debuglog("触发开团成功")
                elif res in [StartTaskEnum.limit, StartTaskEnum.limitMax, StartTaskEnum.full]:
                    self.user.debuglog("触发开团失败")
                    time.sleep(10)
                    break
                time.sleep(3)
            # else:
            #     self.user.debuglog("触发开团队列不足")

    def tryRepairWall(self):
        if self.canRepair and self.user.tryRepairWall2():
            self.canRepair = False

    def tryMinGame(self):
        if self.user.checkWSWithMatch3App():
            self.user.wsWithMatch3App()

    def tryAutoReLogin(self):
        if self.user.cvcEventOpen:
            if not self.user.checkTokenExp(60):
                self.user.log('cvc期间 token剩余超过60小时 不重登')
                return

        needEnter = False
        with self.user.requestLock:
            if self.user.isInvalid:
                return
            self.user.log("尝试自动重登")
            self.user.wsWithKingdomClose()
            time.sleep(5)
            # if self.user.email and self.user.pwd:
            #     self.user.login()
            # else:
            needEnter = self.user.requestConnect()
            self.user.log(f"重登完成 状态:{needEnter}")

        if needEnter:
            updateToken(self.user.token)
            self.user.enter()
            self.user.wsWithKingdomRestart()

    def tryAutoMonster(self):
        if len(self.monsterList) == 0:
            self.user.debuglog('怪物队列为空')
            self.getMaxDistance()
            return None
        nowDate = datetime.datetime.now()
        hour = currentHour()
        # 检查当前时间是否在战斗时间段内
        if hour not in self.battleTimes:
            # 如果不在战斗时间段，且用户不在CVC模式且开启了自动采集
            if not self.user.inCVC and self.autoCollection:
                # 允许继续执行自动打怪逻辑
                pass
            else:
                # 否则直接返回，不执行后续打怪逻辑
                return None

        if self.user.inCVC and not self.user.cvcEventOpen:
            self.canSearchMonster = False
            self.monsterList.clear()
            self.user.debuglog('怪物队列清空(在cvc但非cvc时间)')
            return None

        self.monsterCount += 1
        if self.monsterCount % 2 == 0 and self.maxMonsterDistance <= 70:
            return None

        minute = nowDate.minute
        if 10 < minute < 20:
            if self.user.worldId == BaseWorldId and not self.autoCollection:
                # 挖矿保护 只在9-57分钟内挖矿 21区
                self.user.debuglog("挖矿保护ing 打怪")
                return None

        if self.user.actionPoint < 50:
            if redisHelper.getStockEnergy(self.user.email) < 1000 or self.user.chargeEnergy(100) == 0:
                self.user.debuglog("怪物队列 能量不足")
                return None
        monster = self.getCVCMonsterOne()
        if not monster:
            monster = self.getMonsterOne()
        if monster:
            loc = monster.get("loc")
            level = monster.get("level")
            if self.user.distanceWithLoc(loc) > self.maxMonsterDistance:
                self.user.debuglog(f"距离过远 {loc}")
                self.user.attackedList.append(loc)

            self.user.debuglog(f"怪物队列 准备触发打怪 {loc}")
            self.user.attackMonster(loc,level=level,isTreasure=True,wsUser=self.searchHelper.buildHelperUser())
            return True
        else:
            self.user.debuglog('怪物队列 获取怪物失败')
            return False

    def tryAutoTreatment(self):
        """自动治疗"""
        safeTime = 300
        t = time.time()
        if self.autoWar and self.user.inCVC and t - self.user.lastWarJoinTime < 5 * 60:
            safeTime = 30
        if self.onlyMonster:
            safeTime = 600
        if t - self.lastAutoTreatmentTime > safeTime:
            self.lastAutoTreatmentTime = t
            self.user.autoTreatment()

    def tryUseRedDragon(self):
        if self.user.worldId != BaseWorldId:
            return
        if self.user.level < 35:
            return
        if self.user.cvcEventOpen:
            return
        if random.randint(1, 5) != 3:
            return
        hour = currentHour()
        if hour in [8, 9] or hour not in self.battleTimes:
            return
        if self.user.increasePutDragonDailyQuota():
            self.user.tryUseRedDragon()

    def tryDragoMines(self):
        # 尝试挖龙矿
        # 1. 检查龙矿数据
        # 2. 检查是否有指定工作龙
        # 3. 挖龙矿
        if self.user.dragoActionPoint <= 0 or self.user.inCVC:
            return
        if not self.user.allianceId:
            self.user.addBotWorkLog("没有联盟不能挖龙矿")
            return
        hour = currentHour()
        minute = currentMinute()
        if hour % 12 == 8 and minute < 28:
            self.user.debuglog("tryDragoMines 龙矿刷新时间未到")
            return
        # 1. 检查龙矿数据
        dragoMines = redisCeleryHelper.getDragoMineList(self.user.worldId)
        if len(dragoMines) == 0:
            self.user.debuglog("tryDragoMines 龙矿数据为空")
            return
        maxLevel = 4
        minLevel = 3
        if self.workDragos[0] == 1:
            maxLevel = 2
            minLevel = 2
        dragoMines = list(filter(lambda x: maxLevel >= int(x.split("_")[0]) >= minLevel, dragoMines))
        if self.user.realWorld == 17:
            # 屏蔽A2区域
            polygon = NewPolygon([(1124,1124),(1124,1180),(1180,1180),(1180,1124)])
            def filterPolygon(x):
                splitList = [int(v) for v in x.split("_")]
                level = splitList[0]
                loc = splitList[1:]
                if isPointOnPolygonEdge(loc, polygon):
                    removeDragoMineListWithMember(self.user.worldId, level, loc)
                    return False
                return True
            dragoMines = list(filter(filterPolygon, dragoMines))
        if len(dragoMines) == 0:
            self.user.debuglog("tryDragoMines 龙矿数据等级不足")
            return
        # 2. 检查是否有指定工作龙
        dragos = self.user.dragoLairList()
        workDragos = self.user.canWorkDragos(dragos)
        if workDragos:
            tmpDragoMines = []
            worldId = self.user.worldId
            dragoMine = None
            for dragoMine in dragoMines:
                arr = dragoMine.split("_")
                level = int(arr[0])
                tmpDragoMines.append(
                    {
                        "level": level,
                        "loc": [worldId, int(arr[1]), int(arr[2])],
                        "code": Enum.OBJECT_CODE_DRAGON_SOUL_CAVERN,
                        "param": {"value": level * 1000},
                    }
                )

            historyLoc = self.user.loadFieldTasks(self.user.crystalHistory)
            if tmpDragoMines:
                lv4Mines = [v for v in tmpDragoMines if v.get("level") == 4]
                if len(lv4Mines) > 0:
                    tmpDragoMines = lv4Mines
                    self.user.debuglog(f"tryDragoMines 龙矿数据等级4 {len(lv4Mines)}")
                tmpDragoMines = self.user.returnSortFields(tmpDragoMines, historyLoc=historyLoc)

            self.user.debuglog(f"tryDragoMines 龙矿数据:{len(tmpDragoMines)} 工作龙:{len(workDragos)}")
            for dragoMine in tmpDragoMines[:len(workDragos)]:
                with self.user.requestLock:
                    self.user.addBotWorkLog("准备挖矿")
                level = dragoMine.get("level")
                res = self.user.collectionResource(dragoMine.get("loc"), self.maxTroopNum, self.maxTroopNum, isCrystal=True, noT5= True, useDrago= True, historyLoc=historyLoc, wsUser=self.searchHelper.buildHelperUser(), level=level)
                if res == StartTaskEnum.full:
                    self.user.debuglog("满队列触发下次刷新")
                    self.user.randomSleep(20, 30, msg="龙矿满队列，禁止交互ing")
                    return self.tryDragoMines()
                elif res == StartTaskEnum.limit:
                    self.user.log("资源队列达到上限？龙矿")
                    return
                elif res == StartTaskEnum.limitMax:
                    self.user.log("没有联盟？龙矿")
                    return
                elif res == StartTaskEnum.fail:
                    self.user.randomSleep(2, 3)
                    self.user.addBotWorkLog(f"挖龙矿失败 lv:{level}")
                    return self.tryDragoMines()
                elif res == StartTaskEnum.success:
                    self.user.randomSleep(2, 3)
                    self.user.addBotWorkLog(f"挖龙矿成功 lv:{level}")
        else:
            self.user.debuglog("tryDragoMines 没有指定工作龙")

    def tryAutoJoinAlliance(self):
        """尝试自动加入联盟"""
        hour = currentHour()
        if hour not in self.miningTimes:
            return
        if self.autoJoinAllianceTag and len(self.autoJoinAllianceTag) > 0:
            self.user.tryJoinAllianceByTag(self.autoJoinAllianceTag)

    def scheduleThread(self):
        try:
            asyncio.set_event_loop(asyncio.new_event_loop())
            schedule.every(15).to(20).minutes.do(self.tryVipAndMint)
            schedule.every(5).to(8).minutes.do(self.user.tryHelpAll)
            schedule.every(20).to(30).minutes.do(self.tryGetMail)
            schedule.every(20).to(30).minutes.do(self.taskAll)
            schedule.every(100).to(120).minutes.do(self.tryHarvestAndClearMail)
            schedule.every(6).to(7).hours.do(self.user.claimEvent)
            schedule.every(11).to(12).hours.do(self.user.claimCVCEvent)
            schedule.every(5).to(6).hours.do(self.user.tryRoulette_2023)
            if self.autoSkill:
                schedule.every(10).to(22).minutes.do(self.user.tryUseCollectionSkill)
                schedule.every(20).to(30).minutes.do(self.tryAutoSummon)

            if self.autoEat:
                schedule.every(60).to(61).minutes.do(self.user.tryUseBuffer)

            schedule.every(25).to(30).seconds.do(self.tryAutoTreatment)

            if self.isJoinAllianceBattle:
                if not self.user.allianceId and not self.autoJoinAllianceTag:
                    self.user.log("没有联盟无法自动跟团")
                    self.user.barkNoti("没有联盟无法自动跟团")
                elif not self.user.allianceId and self.autoJoinAllianceTag:
                    self.user.addBotWorkLog(f"当前没有联盟，尝试加入联盟:{self.autoJoinAllianceTag}")
                    self.tryAutoJoinAlliance()

                if not self.user.inCVC:
                    schedule.every(25).seconds.do(self.tryJoinAllianceBattle)
                schedule.every(3).to(4).hours.do(self.user.troopRecover)

            schedule.every(31).to(32).minutes.do(self.tryRepairWall)

            if self.autoMinGame:
                schedule.every(20).to(30).minutes.do(self.tryMinGame)
                if self.user.level >= 35:
                    self.detailKillList["anyLevel"] = 4
                schedule.every(12).to(13).hours.do(self.user.autoOpenResource)

            # 自动开宝箱
            if self.autoMinGame or self.isJoinAllianceBattle:
                n,m = 15, 20
                # 黄金宝箱数量大于10000 则每3分钟开一次
                if self.user.itemCount(Enum.ITEM_CODE_GOLD_CHEST) > 10000:
                    n,m = 3,4
                schedule.every(n).to(m).minutes.do(self.user.autoOpenTreasureBox)

            if self.autoLevelUp:
                schedule.every(30).to(40).minutes.do(self.user.autoLevelTo35)

            if self.autoReLogin:
                schedule.every(10).to(14).hours.do(self.tryAutoReLogin)

            if self.autoMonster and not self.onlyMonster:
                n,m = 30, 35
                schedule.every(n).to(m).seconds.do(self.tryAutoMonster)

            if self.autoCollection:
                schedule.every(60).to(70).minutes.do(self.tryCollectionEvent)

            if self.workDragos and not self.user.inCVC:
                # 自动龙矿
                schedule.every(100).to(120).seconds.do(self.tryDragoMines)
                # 在每小时20分调用方法
                schedule.every().hour.at(":20").do(self.user.kingdomProfileMy)
                self.user.log("自动龙矿启动")

            if self.autoJoinAllianceTag:
                schedule.every(5).to(10).minutes.do(self.tryAutoJoinAlliance)
                self.user.log("自动加入联盟启动")
            if self.user.realWorld == BaseWorldId and self.user.level >= 35:
                schedule.every(50).to(70).minutes.do(self.tryUseRedDragon)

            def heartbeat():
                self.user.log("heartbeat")

            schedule.every(2).hours.do(heartbeat)

            while not self.user.isInvalid:
                try:
                    schedule.run_pending()
                except websocket._exceptions.WebSocketTimeoutException:
                    self.user.errorLog(
                        f"WebSocketTimeoutException异常:{self.user.socks5}"
                    )
                except Exception as e:
                    self.user.errorLog(e, exc_info=True)

                time.sleep(0.1)
                # u1.debuglog("schedule触发Run")
        except Exception as e:
            self.user.errorLog(f"scheduleThread error:{e}", exc_info=True)
        finally:
            schedule.clear()
            self.user.log("scheduleThread结束")

    def tryGoBackThread(self):
        logger.info("开启多倍线程")
        asyncio.set_event_loop(asyncio.new_event_loop())
        timeout = 70
        domain = False
        if self.user.level < 30:
            timeout = 300
        elif self.highDelay:
            timeout = 200
            domain = True
        while not self.user.isInvalid:
            # hour = currentHour()
            # if hour % 12 == 0:
            #     self.user.randomSleep(600,msg="12休息")
            #     continue
            if not self.checkMaxCrystal():
                self.user.randomSleep(300, 600)
                continue
            self.user.gobackOnlyOne(
                timeout=timeout, domain=domain, needRefresh=False, maxValue=999999
            )
            t = 0
            if self.user.lastMoTime > 600:
                t = 60
            self.user.randomSleep(10, c=t)

    def searchThread(self):
        try:
            logger.info("开启搜索线程")
            asyncio.set_event_loop(asyncio.new_event_loop())
            resetTokenTime = time.time()

            resetCount = random.randint(50, 55)
            while not self.user.isInvalid:

                if secrets.randbelow(5) == 2:
                    self.user.debuglog(f'搜索线程被触发 {self.checkMaxCrystalFieldTasks()} {self.checkMaxCrystal()}')

                if not self.checkMaxCrystal():
                    self.user.randomSleep(600, 1200, msg = "水晶达到上限休息")
                    continue

                hour = currentHour()
                nowDate = datetime.datetime.now()
                minute = nowDate.minute
                self.user.randomSleep(30)
                if hour not in self.miningTimes:
                    continue

                # if (hour - 8) % 12 == 0:
                #     continue

                if (
                    self.autoGlobalCrystal
                    and (self.globalTime <= minute <= self.globalTime + 12 and (minute - self.globalTime) % 6 == 0)
                    and not self.user.getExceedCrystalDailyQuota()
                ):
                    self.makeGlobalCrystalInfo()
                    self.user.randomSleep(
                        30, msg=f"闲时水晶触发:{len(self.crystalList)}"
                    )
                    continue
                self.trySearchCrystalList()
                self.user.randomSleep(10)
                if time.time() - resetTokenTime > resetCount * 60:
                    resetTokenTime = time.time()
        except Exception as e:
            self.user.errorLog(f"searchThread error {e}", exc_info=True)
        finally:
            self.user.log("searchThread结束")

    def monsterSearchThread(self):
        """开启搜怪线程"""
        try:
            logger.info("开启搜怪线程")
            asyncio.set_event_loop(asyncio.new_event_loop())
            resetTokenTime = time.time()

            resetCount = random.randint(50, 55)
            while self.canSearchMonster and not self.user.isInvalid:
                if self.checkInventoryEnery():
                    break
                self.trySearchMonster()
                sleepTime = 60 * 2 if self.onlyMonster else 60 * 5
                self.user.randomSleep(sleepTime)
                if time.time() - resetTokenTime > resetCount * 60:
                    resetTokenTime = time.time()
        except Exception as e:
            self.user.errorLog(f"monsterSearchThread error {e}", exc_info=True)
        finally:
            self.user.log("monsterSearchThread结束")

    def rallyMonitorThread(self):
        """开启团监控线程"""
        try:
            logger.info("开启团监控线程")
            asyncio.set_event_loop(asyncio.new_event_loop())
            # resetTokenTime = time.time()

            while self.user.inCVC and not self.user.isInvalid:
                self.tryRallyMonitor()
                self.user.randomSleep(10)
        except Exception as e:
            self.user.errorLog(f"rallyMonitorThread error {e}", exc_info=True)
        finally:
            self.user.log("rallyMonitorThread结束")
        

    def startThreads(self):
        """开启线程"""
        self.user.debuglog("开启辅助线程")
        th1 = threading.Thread(
            target=self.searchThread, name="searchThread", daemon=True
        )
        th2 = threading.Thread(
            target=self.scheduleThread, name="scheduleThread", daemon=True
        )
        if self.maxCrystalLevel == 0:
            self.user.log("挖矿等级0 不挖矿 修墙")
            if self.autoMonster:
                th1 = threading.Thread(target=self.monsterSearchThread, name="monsterSearchThread", daemon=True)
                th1.start()
        elif self.user.inCVC:
            self.user.log("在CVC中不挖矿")
            if self.autoMonster:
                th1 = threading.Thread(target=self.monsterSearchThread, name="monsterSearchThread", daemon=True)
                th1.start()
            elif self.autoStartRally:
                th1 = threading.Thread(target=self.rallyMonitorThread, name="rallyMonitorThread", daemon=True)
                th1.start()
        else:
            th1.start()
        th2.start()

        # if self.isGoback:
        #     th3 = threading.Thread(
        #         target=self.tryGoBackThread, name="gobackThread", daemon=True)
        #     th3.start()

        # if self.crystalAcceleration:
        #     pass

    def makeGlobalCrystalInfo(self, worldId = None):
        """生成全局水晶信息"""
        if not self.autoGlobalCrystal:
            return
        if worldId and worldId != self.user.worldId:
            return


        crystals = redisCeleryHelper.getCrystalList(self.user.worldId)
        if crystals:
            tmpCrystals = []
            worldId = self.user.worldId
            for crystal in crystals:
                arr = crystal.split("_")
                level = int(arr[0])
                if level > self.maxCrystalLevel or level < self.minCrystalLevel:
                    continue
                tmpCrystals.append(
                    {
                        "level": level,
                        "loc": [worldId, int(arr[1]), int(arr[2])],
                        "code": 20100105,
                        "param": {"value": level * 50},
                    }
                )
            if tmpCrystals:
                sortedList = self.user.returnSortFields(tmpCrystals)
                getNum = self.marchLimit - len(self.user.fieldTasks) - 2
                if getNum > 0:
                    getNum = min(3, getNum)
                if len(sortedList) < 20:
                    [self.addCrystal(v) for v in random.choices(sortedList, k = min(len(sortedList), getNum))]
                else:
                    [self.addCrystal(v) for v in sortedList[:getNum]]

    def tryChangeTreasure(self,page):
        """尝试切换宝藏"""
        self.user.log(f"尝试切换宝藏 {page}")
        if self.user.realWorld != 20:
            self.user.log("非20大陆")
            return
        if self.user.level != 35:
            self.user.log("非35级")
            return
        if self.autoStartRally:
            self.user.log("开团号不换装备")
            return
        if page > 1:
            self.user.errorLog(f"没有对应索引:{page}")
            return
        pageTreasures = [TreasureAttackPages, TreasureCollectPages]
        self.user.autoTreasures(page, pageTreasures[page])

    def tryCvcKingKick(self, foIds):
        if self.user.realWorld != BaseWorldId:
            return
        if self.user.allianceId != '619df0c09001ff0c6017220f':
            # LDAT
            return
        if foIds and isinstance(foIds, str):
            foIds = [foIds]

        if self.user.isAllianceLeader:
            if self.user.isInvalid:
                self.user.addBotWorkLog("踢人失败 账号异常")
                self.user.barkNoti("踢人失败 账号异常", isAdmin=True)
                self.user.isAllianceLeader = False
                return

            for foId in foIds:
                if foId == '67a95a12e3b141545799f29a':
                    self.user.log('tryCvcKingKick 免疫德勒')
                    return
                info = redisCeleryHelper.getCvcFoIdInfo(foId)
                if info:
                    worldId = info.get("worldId")
                    if worldId and worldId != self.user.realWorld:
                        name = info.get("name")
                        self.user.addBotWorkLog(f"踢人失败 不是{self.user.realWorld}大陆 {name} {worldId}")
                        return
                    kingdomId = info.get("kingdomId")
                    if kingdomId:
                        redisCeleryHelper.setCvcKingdomInfo(kingdomId, {"foId": ""})
                with self.user.requestLock:
                    time.sleep(1)
                    res = self.user.cvcKingKick(foId, useLock=False)
                    msg = res and f"踢人成功 {res}" or "踢人失败"
                self.user.addBotWorkLog(msg)
                self.user.randomSleep(1,2)
                self.user.barkNoti(msg, isAdmin=True)

    def tryUseKingSkill(self, skillCode:int):
        """尝试使用国王技能"""
        if self.user.realWorld != 20:
            return
        if self.user.allianceId != '619df0c09001ff0c6017220f':
            # LDAT
            return

        if self.user.isAllianceLeader:
            self.user.addBotWorkLog(f"使用国王技能{skillCode}")
            self.user.tryShrineSkill(skillCode)

    def tryGarrisonTemple(self):
        """尝试驻防神殿"""
        if self.user.realWorld != BaseWorldId:
            return
        if self.user.level < 35:
            return
        if self.user.inCVC:
            return

        shrineList = self.user.shrineList()
        if not shrineList:
            return
        for shrine in shrineList:
            fo = shrine.get("fo")
            loc = fo.get("loc")
            if loc:
                self.user.tryGarrison(loc)
                self.user.randomSleep(3,4,msg="驻防神殿休息")

    def run(self):
        """主运行函数"""
        refreshNodKey()
        u1: UserInfo = self.user
        if u1.isBug:
            u1.wsGetFields(power=1)

        # 设置代理和零服务
        u1.delegate = self
        if u1.realWorld not in NotMyWorldIds:
            u1.setupZeroService()

        redisHelper.debugLog = u1.debuglog
        redisCeleryHelper.debugLog = u1.debuglog

        # 获取战斗时间
        self.battleTimes = redisCeleryHelper.getBattleTimes()
        if u1.realWorld in MineDelayWorldIds and u1.cvcEventOpen:
            self.battleTimes = list(range(24))
            u1.log(f"CVC期间 {MineDelayWorldIds} 全天战斗")

        # 判断是否开启自动战斗 - 35级 20的号 且在cvc
        self.autoWar = (
            self.user.level == 35 and self.user.realWorld not in NotMyWorldIds and (self.user.inCVC or self.user.realWorld == BaseWorldId)
        )

        # 判断是否开启自动打怪 - 35级 20大陆 且骑兵足够
        if self.user.level == 35 and self.autoMinGame and self.user.realWorld == BaseWorldId and not self.autoStartRally:
            troopSufficient = self.user.cavalrySum > (60_0000 + 10 * self.battleTroopNum)
            self.autoMonster = troopSufficient
            if not self.autoMonster:
                self.user.addBotWorkLog(f"骑兵不足{self.user.cavalrySum} 不自动打怪")
        # 强制打怪
        if self.onlyMonster:
            self.autoMonster = True
            self.autoStartRally = False
            self.isJoinAllianceBattle = False
            self.user.log("强制打怪")

        # CVC活动未开启时不搜怪
        if self.user.inCVC and not self.user.cvcEventOpen:
            self.canSearchMonster = False

        # 判断是否开启自动采集 - 非禁止大陆 35级以上 且在采集活动中
        self.autoCollection = self.user.realWorld not in NotMyWorldIds and self.user.level >= 35 and self.user.inCollectionEvent
        if self.autoCollection:
            self.user.log("开启自动采集资源")
            self.tryCollectionEvent()

        if self.user.cvcEventOpen:
            time.sleep(2)
            self.user.cvcEventList()

        u1.taskAll()

        lastMsg = None

        # 设置最大队列数
        if not self.marchLimit:
            self.marchLimit = u1.marchLimit or 1
        s = ""
        if self.workDragos:
            s = f" 挖龙矿[{len(self.workDragos)}]"
        u1.log(f"最大队列数:{self.marchLimit} 最大矿等级{self.maxCrystalLevel} {s}")

        # 非CVC且有水晶等级限制 或 自动打怪时获取网络令牌
        if (self.maxCrystalLevel != 0 and not u1.inCVC) or self.autoMonster:
            self.getNetToken()
            if self.autoGlobalCrystal and not u1.inCVC:
                self.globalTime = random.randint(25, 30)
                u1.log(f"闲时水晶:{self.globalTime}分")

        # 开启cvcrank安全保护
        u1.cvcRankSafeKingdom(u1.kingdomId)
        redisCeleryHelper.setCvcKingdomInfo(u1.kingdomId, {"name":u1.name, "worldId":u1.realWorld})

        if u1.inCVC:
            u1.allianceInfoMy()

        self.startThreads()

        # 主循环
        startTime = time.time()
        todayValue = 0
        todayExceedCrystalDailyQuota = None
        while not u1.isInvalid:
            # 检查websocket连接
            if u1.checkWSWithKingdomApp():
                with u1.requestLock:
                    u1.log("断开重连ing?")
                    pass
                u1.debuglog("wsKing断开重连")
                u1.trySetDeviceInfo()
                u1.wsWithKingdomApp()

            # 强制打怪
            if self.onlyMonster:
                self.user.clearFieldTasks()
                if self.marchLimit > len(self.user.fieldTasks):
                    try:
                        if self.tryAutoMonster() is None:
                            u1.randomSleep(3)
                    except Exception as e:
                        u1.errorLog(f'自动打怪异常 {e}',exc_info=True)
                u1.randomSleep(2, 5)

            # 自动采集资源逻辑
            elif self.autoCollection and not self.user.inCVC:
                maxCount = 6
                if self.user.level != 35:
                    maxCount = 99
                if self.checkMaxResourceFieldTasks(maxCount):
                    try:
                        resourceInfo = self.getResourceOne()
                        if resourceInfo:
                            historyLoc = u1.loadFieldTasks(u1.crystalHistory)
                            res = u1.collectionResource(resourceInfo.get("loc"),
                                                        maxCount= 100000,
                                                        historyLoc=historyLoc,
                                                        loadingMax= 5_1000,
                                                        )
                            if res == StartTaskEnum.full:
                                u1.debuglog("满队列触发下次刷新")
                            elif res == StartTaskEnum.limit:
                                u1.randomSleep(30,60,msg="资源队列达到上限？")
                            elif res == StartTaskEnum.fail:
                                continue
                            elif res == StartTaskEnum.success:
                                u1.randomSleep(5, 10)
                    except Exception as e:
                        u1.errorLog(f'自动收集资源异常 {e}',exc_info=True)
                else:
                    u1.randomSleep(5, 10)
                u1.randomSleep(1, 2)

            # 自动挖水晶逻辑
            elif self.maxCrystalLevel != 0 and not self.user.inCVC:
                todayExceedCrystalDailyQuota = u1.getExceedCrystalDailyQuota()
                todayValue = self.getTodayCrystal()
                todayValue = todayValue or 0
                self.todayGetCrystal = u1.crystal - int(todayValue)
                if (
                    not todayExceedCrystalDailyQuota
                    and self.checkMaxCrystalFieldTasks()
                    and self.checkMaxCrystal()
                ):
                    crystalInfo = self.getCrystalOne()
                    if crystalInfo:
                        historyLoc = u1.loadFieldTasks(u1.crystalHistory)
                        res = u1.quickCrystal(
                            crystalInfo,
                            historyLoc,
                            50100306,
                            self.maxTroopNum,
                            wsUser=self.searchHelper.buildHelperUser(),
                        )
                        if res == StartTaskEnum.full:
                            u1.debuglog("满队列触发下次刷新")
                        elif res == StartTaskEnum.fail:
                            continue
                        elif res == StartTaskEnum.success:
                            u1.randomSleep(2, 3)
                            if self.user.worldId in MineDelayWorldIds:
                                u1.randomSleep(5)
                else:
                    self.clearCrystalList()
                    u1.randomSleep(5, 10)

                u1.randomSleep(1, 2)
            else:
                # 自动加入联盟战斗 / 开团
                if self.user.inCVC and self.isJoinAllianceBattle:
                    self.tryJoinAllianceBattle()
                u1.randomSleep(1, 2)

            # 日志输出
            todayGetStr = ""
            if todayValue:
                todayGetStr = f"今日已获取{self.todayGetCrystal}水晶 采集次数:{u1.getKingdomTodayCrystalCount()}"
                if not self.checkMaxCrystal() or todayExceedCrystalDailyQuota:
                    todayGetStr = f"今日已获取{self.todayGetCrystal}水晶 已达标"
            if self.autoCollection:
                todayGetStr += f'收集:{self.user.tmpParams.get("collectionCount",0)} 活动等级:{self.user.tmpParams.get("collectionLevel",0)}'
            buffStr = ""
            if u1.hasShield():
                buffStr += " 盾🛡️"
            if u1.hasPrivateSafe():
                buffStr += " 反侦查🔍"
            if self.workDragos:
                buffStr += f" 🐲AP{u1.dragoActionPoint}"

            msg = f"一轮 {u1.taskStr} 攻击{u1.attacked} 侦查{u1.spyed} 水晶数量:{u1.crystal} {todayGetStr} 邮件:{u1.newMailCount} {buffStr}"
            if self.isJoinAllianceBattle:
                averageCount = u1.attackCount / math.ceil(
                    (time.time() - startTime) / 3600
                )
                msg = f"{msg} 跟团次数:{u1.attackCount} 频率:{round(averageCount,2)}"
            if self.autoLevelUp and u1.researchGrade > 0:
                msg += f" 研究等级:{u1.researchGrade}"
            if lastMsg != msg:
                lastMsg = msg
                u1.log(msg)

            time.sleep(0.1)

        # 结束处理
        u1.endZeroService()
        self.user.log("主线程结束")


class AppleUserHelper:
    def __init__(self, employer: UserInfo):
        self.employer = employer
        self.helperUser: UserInfo = None
        self.helperUserKey = None
        self.deputyToken = None

    @property
    def helperKey(self):
        return f"{self.employer.kingdomId}_helper_apple"

    def getHelperUserKey(self):
        key = self.helperKey
        value = redisHelper.get(key)
        if value:
            self.setHelperUserKey(value)
        return value

    def setHelperUserKey(self, value):
        key = self.helperKey
        redisHelper.set(key, value)

    def removeHelperUserKey(self):
        key = self.helperKey
        redisHelper.removeKey(key)

    def getAppleUser(self):
        """获取本地搜索用户"""
        self.helpUserKey = None
        userKey = self.getHelperUserKey()

        if userKey:
            self.helpUserKey = userKey
            helperUser = None
            if self.helperUser:
                if self.helperUser.appleSubId == userKey:
                    helperUser = self.helperUser
                else:
                    self.helperUser = None
            if helperUser is None:
                helperUser: UserInfo = UserInfo(
                    appleSubId=userKey,
                    socks5=random.choice(loadS5List()),
                    saveSocks5=True,
                )
            self.helperUser = helperUser
            token = redisHelper.getToken(appleSubId=userKey)
            try:
                if token:
                    helperUser.token = token
                    helperUser.initLog(needTestS5=False)
                    helperUser.wallInfo()
                else:
                    helperUser.login()
                self.deputyToken = helperUser.token
                self.employer.log(f"使用搜索账号:{userKey}")
                return helperUser.token
            except UserError as e:
                errorCode = e.errorCode
                codes = [41, 6, 31, -2]
                if errorCode in codes:
                    self.employer.log("搜索小号没了")
                    self.removeHelperUserKey()
                    helperUser.invalidStatu = True
                    helperUser.clearSelf()
                    return self.getAppleUser()
                elif errorCode == -4:
                    helperUser.clearSelf()
                    return self.getAppleUser()
            except Exception as e:
                self.employer.errorLog(e, exc_info=True)
        else:
            userKey = redisHelper.getOneAppleSubId()
            # if not userKey:
            #     from guestRegister import registerWithDynamicProxy,clearUserKeys
            #     clearUserKeys()
            #     userKey = registerWithDynamicProxy(maxCount=1,autoSave=False,autoExitIfChangeEncrypt=False,fackIP=True)
            if userKey:
                self.employer.log(f"获得搜索用户:{userKey}")
                self.setHelperUserKey(userKey)
                return self.getAppleUser()
            else:
                self.employer.log("获得搜索用户失败 无法搜索水晶")

    def buildHelperUser(self) -> UserInfo:
        """构建helper用户"""
        helperUser: UserInfo = UserInfo(
            f"{self.employer.name}_help",
            token=self.deputyToken,
            socks5=secrets.choice(loadS5List()),
            saveSocks5=True,
        )
        helperUser.loc = self.employer.loc
        helperUser.loadEmailWithToken()
        return helperUser


def searchAll(tokens, userLoc, codes=None, logInfo=None, zones=None, maxLevel=9):
    t1 = time.time()
    if zones is None:
        zones = crystalLands
    s5List = loadS5List()
    users = [
        UserInfo(
            f"crystal_{secrets.randbelow(9999) + 1}@search",
            token=token,
            socks5=secrets.choice(s5List),
            saveSocks5=True,
        )
        for token in tokens
    ]
    data = {}

    def searchCrystal(user: UserInfo, zones):
        datas = {}
        try:
            datas = user.searchCrystalWithZoneSize(user.loc, zones=zones, codes=codes)
        except Exception as e:
            if callable(logInfo):
                logInfo(f"searchAll : {e}")
        # finally:
        return datas

    with ThreadPoolExecutor(max_workers=len(users)) as executor:
        splitNum = len(zones) // len(users)
        tasks = []
        for index in range(len(users)):
            user = users[index]
            if not user.token:
                continue
            user.loadEmailWithToken()
            user.initLog(needTestS5=False)
            user.loc = userLoc
            subZones = zones[index * splitNum : (index + 1) * splitNum]
            if index == len(users) - 1:
                subZones = zones[index * splitNum :]
            tasks.append(executor.submit(searchCrystal, user, subZones))
        executor.shutdown(wait=True)
        for task in tasks:
            datas = task.result()
            for k, v in datas.items():
                if k in data:
                    data[k] += v
                else:
                    data[k] = v
    t2 = time.time()
    user = users[0]
    for k, v in data.items():
        data[k] = user.returnSortFieldsWithLevelDes(v)


    worldId = userLoc[0]
    dragoMines = data.get(Enum.OBJECT_CODE_DRAGON_SOUL_CAVERN, [])
    if dragoMines:
        dragoMines = user.returnSortFieldsWithLevelDes(
            dragoMines, clearOccupied=True, clearHidden=True, maxLevel=maxLevel
        )
        dragoMines = [
            f'{dragoMine["level"]}_{dragoMine["loc"][1]}_{dragoMine["loc"][2]}'
            for dragoMine in dragoMines
        ]
        redisCeleryHelper.setDragoMineList(worldId, dragoMines)

    crystals = data.get(Enum.OBJECT_CODE_CRYSTAL_MINE, [])
    if crystals:
        crystals = user.returnSortFieldsWithLevelDes(
            crystals, clearOccupied=True, clearHidden=True, maxLevel=maxLevel
        )
        crystals = [
            f'{crystal["level"]}_{crystal["loc"][1]}_{crystal["loc"][2]}'
            for crystal in crystals
        ]
        redisCeleryHelper.setCrystalList(worldId, crystals)
    t3 = time.time()
    if callable(logInfo):
        logInfo(f"总耗时:{round(t3-t1,2)},获取地图数据:{round(t2-t1,2)},获取地图数据耗时:{round(t3 - t2,2)}")
    return data

def getRecordCrystals():
    records = redisHelper.getRecordCrystals()
    dates = {}
    for record,value in records.items():
        values = record.split("_")
        date = values[0]
        name = values[1]
        if date not in dates:
            dates[date] = {}
        dates[date][name] = value

    sorted_dict = {key: dates[key] for key in sorted(dates.keys())}
    return sorted_dict

if __name__ == "__main__":
    logger.info("hello")
