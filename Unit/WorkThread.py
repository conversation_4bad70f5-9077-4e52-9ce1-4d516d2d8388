# /bin/env python3
# -*- coding: UTF-8 -*- 

import json
import time,random
from Model.UserError import UserError
from Model.Account import Account
from Api.UserInfo import UserInfo
import threading
import asyncio
from Unit.Logger import logger
from Unit.FileTool import loadConfig,writeConfig,get_file_md5,loadS5List
from Unit.SyncTool import syncPathAndRedis

canRun = True
    
def setCanRun(value):
    global canRun
    canRun = value

# 自定义工作线程
class WorkThread(threading.Thread):
    def __init__(self, userInfo,callBack,threadPool=None):
        threading.Thread.__init__(self)
        self.userInfo = userInfo
        self.name = userInfo.email or userInfo.userKey
        self.callBack = callBack
        self.threadPool = threadPool
        
    def run(self):
        asyncio.set_event_loop(asyncio.new_event_loop())
        threadPool = self.threadPool
        if threadPool:
            threadPool.acquire()
        u1 = self.userInfo
        # logger.info("开启线程： " + self.name)
        if self.callBack:
            if self.callBack(u1):
                setCanRun(False)
        if threadPool:
                    threadPool.release()
    
    

# 多线程运行
def threadRun(path,syncKey,redisHelperList,workCallBack,endCallBack,isThread=True,threadRunCount=100):
    threadPool = threading.BoundedSemaphore(threadRunCount)
    fileMd5 = ''

    while True:
        setCanRun(True)
        newFileMd5 = get_file_md5(path)
        newAccount = []
        if newFileMd5 != fileMd5:
            # 读取本地并同步
            newAccount = loadConfig(path)

        accounts = syncPathAndRedis(path,syncKey,newAccount,redisHelperList)
        fileMd5 = get_file_md5(path)
        
        if len(accounts) == 0:
            raise UserError.noUsers()
        
        threads = []
        try:
            for account in accounts:
                if canRun is False:
                    raise UserError.threadExit()
                user = account.returnUserInfo()
                if isThread:
                    thread = WorkThread(user,workCallBack,threadPool)
                    thread.setDaemon(True)
                    thread.start()
                    threads.append(thread)
                else:
                    if workCallBack(user):
                        break
        except UserError as e:
            if e.errorCode == -2:
                # 没有位置主动结束
                pass
            else:
                raise e
        for t in threads:
            t.join()
        
        accounts = [Account(redisHelperList[key])for key in redisHelperList]

        # writeConfig(accounts,path)
        endCallBack(accounts)



def threadRunWithSocks5(usersCallBack,workCallBack,endCallBack,isThread=False,threadRunCount=100):
    '''
    随机使用socks5使用账号
    '''
    threadPool = threading.BoundedSemaphore(threadRunCount)
    socks5List = loadS5List()
    if len(socks5List) == 0:
        logger.error("socks5列表为空")
        return
    count = 0
    while True:
        setCanRun(True)
        users = usersCallBack(count)
        if len(users) == 0:
            endCallBack(None)
            continue
            raise UserError.noUsers()
        
        count += 1
        threads = []
        try:
            for user in users:
                if canRun is False:
                    raise UserError.threadExit()
                user.socks5 = random.choice(socks5List)
                if isThread:
                    thread = WorkThread(user,workCallBack,threadPool)
                    thread.setDaemon(True)
                    thread.start()
                    threads.append(thread)
                else:
                    if workCallBack(user):
                        break
        except UserError as e:
            if e.errorCode == -2:
                # 没有位置主动结束
                pass
            else:
                raise e
        for t in threads:
            t.join()

        endCallBack([])
        