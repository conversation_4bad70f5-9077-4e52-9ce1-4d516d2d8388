import json
import random
from time import sleep

from Service.Zero import ZeroService


class UserZero:
    zeroService: ZeroService = None
    delegate = None

    def __del__(self):
        if self.zeroService:
            self.zeroService.end()
            if self.debuglog:
                self.debuglog("ZeroService end")
        if hasattr(super(), '__del__'):
            super().__del__()  # 调用父类的 __del__ 方法（如果存在）


    def setupZeroService(self):
        self.zeroService = ZeroService(logInfo=self.debuglog)
        self.zeroService.start(lambda msg: self.parsingMsg(msg.decode("utf-8")))

    def endZeroService(self):
        self.delegate = None
        if self.zeroService:
            self.zeroService.end()
            self.zeroService.callback = None
            self.zeroService = None

    def parsingMsg(self, msg):
        try:
            jsonObj = json.loads(msg)
            to = jsonObj.get("to")
            if to:
                self.debuglog(f"指定目标参数:{to}:{jsonObj}")
                if self.kingdomId != to:
                    return
            self.doMethod(jsonObj["method"], jsonObj["params"], jsonObj.get("delay"))
            # 如果用户登出，则结束ZeroService
            if self.isInvalid:
                self.endZeroService()
        except Exception as e:
            if self.errorLog:
                self.errorLog(f"UserZero parsingMsg error:{e}", exc_info=True)
            return

    def doMethod(self, methodName, params, delay):
        method = None
        if methodName in dir(self):
            method = getattr(self, methodName)
        elif self.delegate and methodName in dir(self.delegate):
            method = getattr(self.delegate, methodName)

        if callable(method):
            if delay is None:
                t = random.randint(1, 20) / 10
                sleep(t)
            elif delay > 0:
                sleep(delay)
            method(*params)
        else:
            if self.errorLog:
                self.errorLog(f"UserZero doMethod error: {methodName} is not callable")


class ZeroSend:
    @classmethod
    def send(cls, methodName, params=None, to=None, delay=None):
        if params is None:
            params = []
        msg = json.dumps({"method": methodName, "params": params, "to": to, "delay": delay})
        try:
            ZeroService.send(msg)
        except Exception as e:
            print(f"ZeroSend send error: {e}")

    @classmethod
    def makeGlobalCrystalInfo(cls, worldId=None):
        cls.send(
            "makeGlobalCrystalInfo",
            [
                worldId,
            ],
        )

    @classmethod
    def tryGarrison(cls, toLoc, onlyMelee=True, quantity=300000, ratio=None):
        cls.send(
            "tryGarrison",
            [
                toLoc,
                onlyMelee,
                quantity,
                ratio,
            ],
        )

    @classmethod
    def tryChangeTreasure(cls, page):
        cls.send(
            "tryChangeTreasure",
            [
                page,
            ],
        )

    @classmethod
    def tryCvcKingKick(cls, foIds):
        cls.send(
            "tryCvcKingKick",
            [
                foIds,
            ],
        )

    @classmethod
    def tryTeleport(cls, kingdomId, loc):
        cls.send(
            "tryTeleport",
            [
                loc,
            ],
            to=kingdomId,
        )

    @classmethod
    def tryCvcMove(cls, kingdomId, enter:bool):
        cls.send(
            "tryCvcMove",
            [
                enter,
            ],
            to=kingdomId,
        )

    @classmethod
    def tryUseItem(cls, code, count = 1, to = None):
        cls.send(
            "tryUseItem",
            [
                code,
                count,
            ],
            to=to,
        )

    @classmethod
    def tryPrintResource(cls, to=None):
        cls.send("tryPrintResource", [], to=to, delay=0)

    @classmethod
    def tryUseKingSkill(cls, skillCode:int):
        cls.send("tryUseKingSkill", [skillCode,])

    @classmethod
    def tryGarrisonTemple(cls):
        cls.send("tryGarrisonTemple", [])

    @classmethod
    def tryReturnAllSupport(cls):
        cls.send("tryReturnAllSupport", [])
