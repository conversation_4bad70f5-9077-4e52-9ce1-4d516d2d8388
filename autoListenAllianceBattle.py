# /bin/env python3
# -*- coding: UTF-8 -*-
"""
功能描述：自动检测开团兵种
编写人：darkedge
编写日期：2022年05月03日
   
"""


import asyncio
import datetime
import json
import os
import random
import sys
import threading
import time

import fire
import pytz
import requests

from Api.DataRecord import dataDisplay
from Api.UserInfo import UserInfo
from Unit.FileTool import loadS5List
from Unit.Logger import logger
from Unit.Redis import redisHelper, tzDate
from Unit.WorkThread import WorkThread

isDebug = True if sys.gettrace() else False
sleepTime = 20


def main(token):
    """
    token: token
    """
    # print(loc[0])
    # loc = [int(v) for v in loc.split(",")]
    user = UserInfo(
        f"{random.randint(1, 99999999)}@safe.com",
        token=token,
        socks5=random.choice(loadS5List()),
    )
    if False:
        user.initLog()
    else:
        user.login()
    while not user.isInvalid:
        t = time.time()
        realWorldId = isDebug and 20 or user.realWorld
        results = user.monitoringAllianceBattle(realWorldId)
        t2 = time.time()
        if results:
            valueStr = ""
            for v in results:
                if not v:
                    continue
                valueStr += user.buildBattleMessage(v,realWorldId, user.redString)
            os.system("clear")
            print(f"查询耗时{round(t2-t,2)}\n", tzDate().strftime("%m-%d_%H:%M:%S"))
            print(valueStr)
        else:
            os.system("clear")
            print(f"查询耗时{round(t2-t,2)}\n", tzDate().strftime("%m-%d_%H:%M:%S"))
            print("没有有效数据\n")
        if t2 - t < sleepTime:
            print(f"休息{round(sleepTime - t2 + t,2)}秒")
            time.sleep(sleepTime - t2 + t)


if __name__ == "__main__":
    try:
        if isDebug:
            print("debug")
            token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ZU_jkVNIbg1bA2ozdA7zikhcJ56wROK3opU0BLVhMY0"
            main(token)
        else:
            fire.Fire(main)
    except KeyboardInterrupt:
        pass
