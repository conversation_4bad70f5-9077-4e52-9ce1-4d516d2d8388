# /bin/env python3
# -*- coding: UTF-8 -*-
"""
功能描述：自动检测驻守兵种
编写人：darkedge
编写日期：2022年04月26日
   
"""


# import asyncio
# import datetime
# import json
import os
import random
import sys
import threading
import time

import fire
# import pytz
# import requests

# from Api.DataRecord import dataDisplay
from Api.UserInfo import UserInfo
from Unit.FileTool import loadS5List, loadShrineIds
from Unit.Logger import logger
from Unit.Redis import redisHelper #, tzDate
# from Unit.WorkThread import WorkThread

isDebug = True if sys.gettrace() else False


def printInfo(user: UserInfo, foId, loc, interval=1, delay=0):
    if delay > 0:
        time.sleep(delay)
    while not user.isInvalid:
        t = time.time()
        value, showAllList = user.monitoringShrineSupportList(foId)
        if value:
            os.system("clear")
            printStr = f"\n{loc}\n{value}"
            sum = 0
            for v in showAllList:
                sum += v
            rates = [f"{int(round(v/sum,2) * 100)}%" for v in showAllList]
            rateStr = ",".join(rates)
            logger.info(printStr + "\n比例:" + rateStr)

            # print(f"{loc}\n",value,"\n\n",tzDate().strftime("%m-%d_%H:%M:%S"))
        t2 = time.time() - t
        if t2 < interval:
            time.sleep(interval - t2)


def main(loc, token):
    """
    loc: 坐标
    token: token
    """
    loc = [int(loc[0]), int(loc[1]), int(loc[2])]
    # print(loc[0])
    # loc = [int(v) for v in loc.split(",")]
    user = UserInfo(
        f"{random.randint(1, 99999999)}@safe.com",
        token=token,
        socks5=random.choice(loadS5List()),
    )
    if len(token) == 36:
        user = UserInfo(userKey=token, socks5=random.choice(loadS5List()))

    locKey = f"foId_{loc[0]}_{loc[1]}_{loc[2]}"
    foIds = loadShrineIds()
    foId = redisHelper.get(locKey)
    if not foId:
        if foIds:
            foId = foIds.get(locKey)
            user.log(f'读取本地foIds 当前Id:{foId or "无"}')
        else:
            user.log("ws获取")
    else:
        user.log("缓存获取")

    user.login()
    user.wsWithKingdomApp(isDebug=True)
    if not user.allianceId:
        user.tryJoinAllianceBySystem()

    if not foId:
        fields = user.wsGetFields(loc, more=True)
        if fields:
            builds = [20400401, 20600101, 20600102]
            for key in fields:
                if not foId:
                    if key in builds or key >= 20700101:
                        values = fields[key]
                        for value in values:
                            if loc == value.get("loc"):
                                foId = value.get("_id")
                                redisHelper.set(locKey, foId)
                                break

    if foId:
        threads = []
        for i in range(2):
            t = threading.Thread(
                target=printInfo, args=(user, foId, loc, 1, i * 0.5), daemon=True
            )
            threads.append(t)
            t.start()
        for t in threads:
            t.join()

    else:
        user.log("没有找到可以对应建筑的id")


if __name__ == "__main__":
    try:
        if isDebug:
            print("debug")
            # token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJfaWQiOiI2MmI0ZmZmMWUxYmEwMTZmMWJjMjA1ZjUiLCJraW5nZG9tSWQiOiI2MmI0ZmZmMWUxYmEwMTZmMWJjMjA1ZjYiLCJ2ZXJzaW9uIjoxNDYyLCJidWlsZCI6MCwicGxhdGZvcm0iOiJpb3MiLCJ0aW1lIjoxNjU2MzA5ODYzNzM2LCJjbGllbnRYb3IiOiIwIiwiaWF0IjoxNjU2MzA5ODYzLCJleHAiOjE2NTY5MTQ2NjMsImlzcyI6Im5vZGdhbWVzLmNvbSIsInN1YiI6InVzZXJJbmZvIn0.Zh-LcApQ8zaWsClbjPDf5ygXsAdNZ33E7oZloQUzCnA"
            loc = [100003, 1600, 1400]
            main(loc, "c30275c9-b396-00d7-aa94-9e7128152901")
        else:
            fire.Fire(main)
    except KeyboardInterrupt:
        pass
