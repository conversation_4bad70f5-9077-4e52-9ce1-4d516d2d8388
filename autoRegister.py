# /bin/env python3
# -*- coding: UTF-8 -*-
'''
功能描述：一键注册游客账号
编写人：darkedge
编写日期：2023年06月23日
'''

from guestRegister import registerWithDynamicProxy
from concurrent.futures import ThreadPoolExecutor
import fire
import sys
isDebug = True if sys.gettrace() else False

def main(count=9999,workCount=1):
    with ThreadPoolExecutor(max_workers=workCount) as executor:
        for i in range(workCount):
            executor.submit(registerWithDynamicProxy,maxCount=count,autoExitIfChangeEncrypt=False,fackIP=True)
    
        executor.shutdown(wait=True)

if __name__ == '__main__':
    if isDebug:
        main(workCount=3)
    else:
        fire.Fire(main)
