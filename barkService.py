#!/usr/bin/python3
# -*- coding: utf-8 -*-

'''
功能描述：自动检测并发送推送
编写人：darkedge
编写日期：2022年02月24日

'''

from Api.BarkApi import barkSendMsg
from Api.UserInfo import UserInfo
from Api.User.WebSocket import fieldNames,charmCodes
from Unit.Logger import logger
from Unit.FileTool import loadS5List
import threading
import asyncio
import time,random,json
from mining import searchForLoc
import schedule

searchLocs = []
worldId = 21
sendedList = {
}

def loadKeys(path="./bark.txt"):
    keys = []
    with open(path) as f:
        lines = f.readlines()
        for line in lines:
            keys.append(line.strip())
    return keys
    
def loadids(path="./enemy.txt"):
    ids = []
    with open(path) as f:
        lines = f.readlines()
        for line in lines:
            ids.append(line.strip())
    return ids

def clearSendedListJob():
    global sendedList
    sendedList = {}
    logger.info("清空已发送记录")

def searchJob():
    logger.info("启动搜索")
    overList = [[],[],[]]
    u1 = UserInfo()
    u1.loc = [worldId,1024,1024]
    threads = []
    searchFields = [20100105]
    searchFields2 = searchFields + [20300101,20400401,20600101]

    for loc in searchLocs:
        th = threading.Thread(target=searchForLoc,args=(loc,searchFields,searchFields2,overList,))
        th.setDaemon(True)
        th.start()
        threads.append(th)
    
    for th in threads:
        th.join()
    sortList = []
    for ol in overList:
        sortList += u1.returnSortFields(ol)

    logger.info(msg=f"搜索结果:{len(sortList)}")
    msgs = []
    if len(sortList):
        for info in sortList:
            loc = info.get("loc")
            level = info.get("level")
            code = info.get("code")
            name = fieldNames.get(code)
            key = f"{loc[1]}_{loc[2]}"
            if sendedList.get(key):
                continue
            else:
                sendedList[key] = 1
            msgs.append(f"Lv.{level}{name} {loc[1]},{loc[2]}")
            # logger.info(f'{name} {loc} Lv.{level}')
        if len(msgs) > 0:
            trySendMsg(msgs)

def searchEnemyForLoc(loc,searchFields1,searchFields2,overList):
    return searchForLoc(loc, searchFields1, searchFields2,overList,noMan=False)

def searchEnemy():
    logger.info("启动敌对搜索")
    overList = [[],[],[]]
    u1 = UserInfo()
    u1.loc = [worldId,1024,1024]
    threads = []
    searchFields = [20100101,20100102,20100103,20100104,20300101]
    searchFields2 = searchFields
    descIds = loadids()

    stage = 12
    startPoint = (2048 - stage * 160) / 2
    for i in range(stage * stage):
        x = i % stage
        y = int(i / stage)
        loc = [21,startPoint + x * 160,startPoint + y * 160]
        th = threading.Thread(target=searchEnemyForLoc,args=(loc,searchFields,searchFields2,overList,))
        th.setDaemon(True)
        th.start()
        threads.append(th)
    
    for th in threads:
        th.join()
    sortList = []
    for ol in overList:
        sortList += ol

    logger.info(msg=f"搜索结果:{len(sortList)}")
    msgs = []
    if len(sortList):
        for info in sortList:
            occupied = info.get("occupied")
                
            find = False
            if occupied:
                id = occupied.get("id")
                allianceId = occupied.get("allianceId")
                name = occupied.get("name")
                shield = occupied.get("shield")
                code = info.get("code")

                if id in descIds:
                    find = True
                # elif allianceId in allianceIds:
                #     find = True
                # else:
                #     if name in names:
                #         find = True

                if find:
                    msgs.append(f"搜索到了 {name} {info.get('loc')} {fieldNames.get(code)} {shield and '开盾' or '没盾'}")

        if len(msgs) > 0:
            trySendEnemyMsg(msgs)

def trySendMsg(msgs):
    if len(msgs) > 0:
        title = "水晶矿通知"
        msgKeys = loadKeys()
        msgText = "\n".join(msgs)
        for key in msgKeys:
            barkSendMsg(title,msgText,key)
        logger.info(f"发送消息:{msgText}")

def trySendEnemyMsg(msgs):
    if len(msgs) > 0:
        title = "敌军通知"
        msgKeys = loadKeys("enemybark.txt")
        msgText = "\n".join(msgs)
        for key in msgKeys:
            barkSendMsg(title,msgText,key)


def main():
    
    # 5*32 = 160

    startPoint = 159
    x = 80
    y = 80
    while y < 2000:
        if y > 1024 and x < 1040:
            searchLocs.append([worldId,x,y])

        if x > 1040:
            x = startPoint
            y += 160
        else:
            x += 160

    logger.info(f"坐标数:{len(searchLocs)}")
    # searchEnemy()
    # return
    schedule.every(1).hours.do(clearSendedListJob)
    schedule.every(2).to(3).minutes.do(searchJob)
    # schedule.every(15).minutes.do(searchEnemy)
    # searchJob()
    while True:
        schedule.run_pending()
        time.sleep(1)

if __name__ == "__main__":
    main()