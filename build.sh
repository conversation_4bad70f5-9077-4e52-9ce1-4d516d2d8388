#!/bin/bash -e
rm -rf league
rm -rf league.zip
rm -rf ../league-package
mkdir ../league-package
shopt -s extglob
cp -r !(drago|log|xenWork|chromedriver|old|test|temple|*.log) ../league-package
shopt -u extglob
cd ../league-package
rm -rf build.json
rm -rf research.json
rm -rf .trunk
rm -rf turnup.txt
rm -rf name.txt
rm -rf test
rm -rf dragoKey.txt
rm -rf privateKeys.txt
rm -rf wsData
rm -rf old
rm -rf xenWork
rm -rf .git
rm -rf .idea
rm -rf .vscode
rm -rf tmpaccount
rm -rf nft
rm -rf egg
rm -rf drago
rm -rf tmpSocks
rm -rf chromedriver
rm -rf log
rm -rf logs
rm -rf redis-data
rm -rf guests.txt
rm -rf *bark.txt
rm -rf *enemy.txt
rm -rf token.txt
rm -rf users.txt
rm -rf reload
echo '删除完成'
python3 -O -m compileall -b . -x migrations
find . -name "sample_db.sqlite" |xargs rm -rf 
find . -name "*.js" |xargs rm -rf 
find . -name "*account.txt" |xargs rm -rf 
find . -name "*f.txt" |xargs rm -rf
find . -not -path "./migrations/*" -name "*.py"|xargs rm -rf
find . -name "__pycache__" |xargs rm -rf 
find . -name ".DS_Store" |xargs rm -rf 
find . -name "*.png" |xargs rm -rf 
find . -name "*socks5.txt" |xargs rm -rf 
find . -name "socks5_*.txt" |xargs rm -rf
rm -rf ./build.sh
zip -r ../league/league.zip ./
cd ..
rm -rf ./league-package

echo "build success"
python3 -V


