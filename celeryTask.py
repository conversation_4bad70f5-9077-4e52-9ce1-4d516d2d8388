# /bin/env python3
# -*- coding: UTF-8 -*-
"""
功能描述：celery分布式任务
编写人：darkedge
编写日期：2022年2月22日
   
"""
import sys
import time

from Celery.app import app
# trunk-ignore(ruff/F403)
from Celery.task import *
from Unit.FileTool import loadS5List

# from Celery.schedule import *

isDebug = True if sys.gettrace() else False
s5List = loadS5List()


@app.task(name="test", bind=True)
def logUserInfoTask(self):
    # print(self.task_id)
    from Api.UserInfo import UserInfo

    user = UserInfo("<EMAIL>")
    user.addRedisHander()
    self.userInfo = user
    self.email = user.email
    if not self.request.called_directly:
        self.update_state(state="RUN")
    for index in range(100):
        time.sleep(5)
        user.log(f"发呆中...{index}")


if __name__ == "__main__":
    # from Unit.Redis import redisHelper

    r = logUserInfoTask.apply_async()
    # redisHelper.setCeleryTaskId('<EMAIL>', r.task_id)
    print(r)
    print(r.__dict__)
    # from Unit.Logger import RedisHandler
    # r = RedisHandler('<EMAIL>')
    # while True:
    #     time.sleep(5)
    #     print(r.getLogs())
    from celery.result import AsyncResult

    async_result = AsyncResult(r.id, app=app)
    print("debug:" + async_result)

    # task_id = redisHelper.getCeleryTaskId("<EMAIL>")
    # from celery.app.control import Control
    # celery_control = Control(app=app)
    # celery_control.revoke(task_id,terminate=True)
