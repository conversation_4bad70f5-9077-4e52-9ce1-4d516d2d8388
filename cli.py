"""
命令行输入工具

此脚本提供了一个命令行界面，允许用户通过选项执行不同的功能。
用户可以通过命令行参数或交互式输入选择要执行的功能。

功能选项：
- clearToken: 清空token
- clearSocks: 清空socks记录

使用方法：
1. 通过命令行参数运行脚本，例如：
   python cli.py --option1
2. 如果没有提供参数，将进入交互式输入模式。
"""

import argparse
from Unit.Redis import redisHelper

class CommandExecutor:
    def __init__(self):
        self.functions = {}

    def add_function(self, option, function):
        """添加新的功能选项"""
        self.functions[option] = function

    def execute_function(self, option):
        """根据选项执行相应的功能"""
        func = self.functions.get(option)
        if func:
            func()
        else:
            print("无效的选项。")

def main():
    parser = argparse.ArgumentParser(description='命令行输入工具')
    
    # 创建 CommandExecutor 实例
    executor = CommandExecutor()
    
    # 添加选项和对应的函数
    executor.add_function('clearToken', execute_clearToken)
    executor.add_function('clearSocks', execute_clearSocks)
    # 可以在这里添加更多选项

    # 添加命令行选项
    add_arguments(parser)
    
    args = parser.parse_args()
    
    # 根据选项执行不同的功能
    if any(vars(args).values()):
        for key, value in vars(args).items():
            if value:  # 如果选项被选中
                executor.execute_function(key)
    else:
        # 交互式输入选项
        interactive_input(parser, executor)

def add_arguments(parser):
    """添加命令行选项"""
    parser.add_argument('--clearToken', action='store_true', help='清空token')
    parser.add_argument('--clearSocks', action='store_true', help='清空socks记录')
    # 可以在这里添加更多选项

def interactive_input(parser, executor):
    """处理交互式输入"""
    options = {key: value for key, value in vars(parser.parse_args()).items() if value is False}
    help_texts = [f"{index + 1}. --{key}: {parser._option_string_actions[f'--{key}'].help}" for index, key in enumerate(options.keys())]
    
    # 自动生成选项提示
    user_input = input("\n".join(help_texts) + "\n请输入选项序号: ")
    
    if user_input.isdigit() and 1 <= int(user_input) <= len(options):
        selected_option = list(options.keys())[int(user_input) - 1]
        executor.execute_function(selected_option)
    else:
        print("无效的选项。请提供有效的选项。")

def execute_clearToken():
    redisHelper.clearSomeKeys()    

def execute_clearSocks():
    redisHelper.clearChannel("teleport")
    redisHelper.clearSocks5()
    redisHelper.clearSocks5TimeoutBackup()
if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n主动结束")
