# /bin/env python3
# -*- coding: UTF-8 -*- 

from Api.DataRecord import tzDate
from Unit.Redis import redisHelper
from Unit.Logger import logger

def disGuest():
    allKeys = redisHelper.allUserKey()
    emailAllKeys = redisHelper.allEmailKey()

    accountMap = {
        "email": 0,
        "guest":0,
        "used":0,
        "daily":0,
        "apple":0,
    }

    for key in allKeys:
        s = key.split('_')
        if len(s) == 1:
            accountMap["guest"] += 1
        else:
            if s[0] == 'daily':
                accountMap["daily"] += 1
                continue
            loc = s[1]
            if accountMap.get(loc) is None:
                accountMap[loc] = 0
            accountMap[loc] += 1
    
    for key in emailAllKeys:
        s = key.split('_')
        if len(s) == 1:
            accountMap["email"] += 1
        else:
            loc = f'email{s[1]}'
            if accountMap.get(loc) is None:
                accountMap[loc] = 0
            accountMap[loc] += 1

    accountMap["apple"] = len(redisHelper.getAllAppleSubId())
    
    account_map_strings = [f"{k}:{v}" for k,v in accountMap.items()]
    current_time = tzDate().strftime("%m-%d_%H:%M")
    logger.info(f'\n{current_time}')
    stock_info = "\n".join(account_map_strings)
    logger.info(f"库存\n{stock_info}")
    



if __name__ == '__main__':
    # dataDisplay()
    disGuest()