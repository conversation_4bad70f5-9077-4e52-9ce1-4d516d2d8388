# yml配置
services:
  worker:
    container_name: league
    build: .
    stop_signal: SIGINT
    depends_on:
      redis:
        condition: service_started
    ports:
      # - "10022:22"
      - 9999:9999
      - 5555:5555

    volumes:
      - ./:/league
      - ./logs:/var/log

    environment:
      - TZ=Asia/Shanghai
      - FLASK_DEBUG=1
      - FLASK_ENV=development
    restart: always
    tty: true
    stdin_open: true
    privileged: true
    # entrypoint: ["sh"]

  redis:
    image: redis
    container_name: redis
    ports:
      - 6379:6379
    volumes:
      - ./redis-data:/data
    environment:
      TZ: Asia/Shanghai
    restart: always
    entrypoint: redis-server
