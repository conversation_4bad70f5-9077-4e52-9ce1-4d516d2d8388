from Unit.SyncTool import syncPathAndRedis
from Unit.Logger import logger
from Unit.Redis import redisMgr,redisHelper
from Unit.FileTool import writeConfig
if __name__ == '__main__':
    logger.info("导出小号们")
    syncPathAndRedis("./taccount.txt","teleport",[],redisHelper.teleports,needClear=False)
    syncPathAndRedis("./raccount.txt","repair",[],redisHelper.repairs,needClear=False)
    userKeys = redisHelper.allUserKey()
    userKeys = [len(value.split("_")) == 1 and value or '' for value in userKeys]
    with open('guestaccount.txt',"w+") as f:
        for key in userKeys:
            if len(key) > 0:
                f.write(key + "\n")