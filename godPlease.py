from Api.UserInfo import UserInfo
from Api.DataRecord import dataDisplay
import time
import random
import threading
import json
from Unit.FileTool import loadS5List
from Unit.Logger import logger
from Unit.Redis import redisHelper,tzDate
from concurrent.futures import ThreadPoolExecutor
import asyncio
import requests
import sys,os
import fire
import datetime,pytz

def main(kingdomId,token):
    '''
    kingdomId:王国id
    token:用户token
    '''
    s5List = loadS5List()
    localS5 = None
    # localS5 = "127.0.0.1:1086"
    
    u1 = UserInfo(f"god@{random.randint(1, 99999999)}.com",token=token,socks5=localS5)
    u1.isWebDevice = True
    u1.initLog(needTestS5=False)
    # if u1.login():
    #     u1.wsWithKingdomApp()
    u1.randomSleep(5,msg=f"请神{kingdomId}")
    u1.tryUseUpSkillToUse(kingdomId)

if __name__ == '__main__':
    try:
       fire.Fire(main)
    except KeyboardInterrupt:
        pass