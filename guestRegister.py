# /bin/env python3
# -*- coding: UTF-8 -*- 
"""
功能描述：注册游客账号
编写人：darkedge
编写日期：2021年12月23日
   
"""
import json
import time,random
from Model.Kingdom import Kingdom
from Model.Account import Account
from Model.UserError import UserError
from Api.UserInfo import UserInfo
import threading
from Unit.Logger import logger
from Unit.FileTool import loadConfig,writeConfig
from Unit.Redis import redisHelper
from Unit.DeviceInfo import createUserKey
from Api.GoogleCaptcha import get_response,create_task
from Api.Ipinfo import getIP,generate_public_ipv4
from Api.ProxyApi import allocateIP, changeKeyTo2,whiteAdd,createRealSocks5
import fire
import sys
isDebug = True if sys.gettrace() else False

registerFailCount = 0
canRun = True
userKeys = []

useGoogleCaptcha = False

def addRegisterFailCount():
    global registerFailCount
    registerFailCount += 1

def resetRegisterFailCount():
    global registerFailCount
    registerFailCount = 0

def setCanRun(value):
    global canRun
    canRun = value

def clearUserKeys():
    userKeys.clear()

# 自定义工作线程
class WorkThread(threading.Thread):
    def __init__(self, userInfo,callBack,threadPool):
        threading.Thread.__init__(self)
        self.userInfo = userInfo
        self.name = userInfo.email or userInfo.userKey
        self.callBack = callBack
        self.threadPool = threadPool
        
    def run(self):
        threadPool = self.threadPool
        if threadPool:
            threadPool.acquire()
        u1 = self.userInfo
        logger.info("开启线程： " + self.name)
        if self.callBack:
            if self.callBack(u1):
                setCanRun(False)
        if threadPool:
                    threadPool.release()
    
    

# 多线程运行
def threadRun(path,workCallBack,endCallBack,isThread=True,threadRunCount=100):
    threadPool = threading.BoundedSemaphore(threadRunCount)

    setCanRun(True)

    sock5List = {}
    with open(path) as lines:
        accounts = lines.readlines()
        for account in accounts:
            account = account.strip('\n')
            if len(account):
                user = Account(account)
                sock5List[user.s5] = user

    newAccounts = loadConfig(path)

    for account in sock5List:
        user = sock5List[account].returnUserInfo(saveSocks5=False)
        user.userKey = createUserKey()
        workCallBack(user)
        if not canRun:
            break
            
    # threads = []
    # try:
    #     account = random.choice(newAccounts)
    #     user = account.returnUserInfo()
    #     user.userKey = createUserKey()
    #     if workCallBack(user):
    #         setCanRun(False)
    # except Exception as e:
    #     print(e)
    #     endCallBack(None)
    # while canRun:
    #     account = random.choice(newAccounts)
    #     user = account.returnUserInfo()
    #     user.userKey = createUserKey()
    #     if isThread:
    #         thread = WorkThread(user,workCallBack,threadPool)
    #         thread.start()
    #         threads.append(thread)
    #     else:
    #         if workCallBack(user):
    #             break

    
    # writeConfig(accounts,path)
    endCallBack([])


def registerWithDynamicProxy(isV2=True,maxCount=99999,autoSave=True,autoExitIfChangeEncrypt=True,fackIP=False, needSave=True):
    logger.info("动态ip注册 %s" % (isV2 and "V2模式" or "V1模式"))
    lastIp = None
    # ip = getIP()
    # logger.info("本地ip %s" % ip)
    # if not ip:
    #     raise Exception("获取IP失败")
    
    # if not whiteAdd(ip):
    #     raise Exception("设置白名单失败")
    if fackIP:
        logger.info("欺骗ip模式")
    recaptchaRes = None
    gt = 0
    while True:
        if len(userKeys) >= maxCount:
            logger.info("已注册:%d个账号" % len(userKeys))
            break
        if isV2 and useGoogleCaptcha and recaptchaRes is None:
            taskId = create_task()
            if not taskId:
                continue
            currentTime = time.time()
            recaptchaRes = get_response(taskId)
            gt = time.time()
            logger.info("获得谷歌验证 耗时%d" % (time.time() - currentTime))
        s5 = None
        s5Ip = None
        if not fackIP:
            s5 = allocateIP()
            logger.info("获取到代理:%s" % s5)
            if s5 is None:
                logger.error("代理异常")
                return
            s5Ip = s5.split(":")[0]
            if redisHelper.getIPBan(s5Ip):
                logger.info("ip受限 休息5秒")
                time.sleep(5)
                continue
        t = time.time()
        if s5 or fackIP:
            maxRegisterCount = 3
            while maxRegisterCount > 0:
                u1 = UserInfo(userKey=createUserKey())
                if fackIP:
                    u1.fakeIP = generate_public_ipv4()
                    s5Ip = u1.fakeIP
                else:
                    u1.socks5 = createRealSocks5(s5)
                u1.autoExitIfChangeEncrypt = autoExitIfChangeEncrypt
                u1.initLog(needTestS5=False)
                try:
                    if isV2 and useGoogleCaptcha and recaptchaRes is None:
                        taskId = create_task()
                        if not taskId:
                            continue
                        currentTime = time.time()
                        recaptchaRes = get_response(taskId)
                        gt = time.time()
                        logger.info("获得谷歌验证 耗时%d" % (time.time() - currentTime))
                    if time.time() - t > (30 + (3-maxRegisterCount) * 5):
                        redisHelper.setIPBan(s5Ip)
                        logger.info("代理超时")
                        break
                    if isV2 and (time.time() - gt > 110 and useGoogleCaptcha):
                        logger.info("谷歌验证超时")
                        recaptchaRes = None
                        continue
                    if u1.guestRegister(isV2=isV2,token = recaptchaRes):
                        u1.log("注册成功:%s" % u1.userKey)
                        redisHelper.addTodayRegisterCount()
                        maxRegisterCount -= 1
                        resetRegisterFailCount()
                        try:
                            if u1.enter() and (needSave or not fackIP):
                                u1.newAccountEvent()
                        except KeyboardInterrupt as e:
                            raise e
                        except Exception as e:
                            logger.error(f"注册失败: {e}",exc_info=True)
                        finally:
                            if u1.fakeIP and needSave:
                                redisHelper.saveFakeIp(u1.userKey,u1.kingdomId,u1.fakeIP)
                                
                            if autoSave:
                                if needSave:
                                    redisHelper.saveUserKey(u1.userKey)
                                else:
                                    if not fackIP:
                                        pass
                            else:
                                return u1.userKey
                            userKeys.append(Account('',userKey=u1.userKey))
                        recaptchaRes = None
                    else:
                        redisHelper.setIPBan(s5Ip)
                        logger.info("代理超时")
                        break
                except UserError as e:
                    if e.errorCode == 7:
                        redisHelper.setIPBan(s5Ip)
                        logger.info("ip受限 换ip")
                        break
                    elif e.errorCode == -3:
                        redisHelper.setIPBan(s5Ip)
                        logger.info("s5异常")
                    elif e.errorCode == 32 or e.errorCode == 6:
                        logger.error("傻逼官方又关注册了")
                        endCallBack(None)
                        return None
                    elif e.errorCode == 99:
                        logger.error("注册失败")
                        if lastIp != s5Ip:
                            lastIp = s5Ip
                            addRegisterFailCount()
                        if registerFailCount > 10:
                            logger.error("注册失败10次 异常")
                            endCallBack(None)
                            exit(0)
                        # time.sleep(5)
                except Exception as e:
                    logger.info("注册失败:%s" % e)
                    
            redisHelper.setIPBan(s5Ip)

        else:
            logger.info("获取ip异常")
            time.sleep(10)

def workCallBack(u1:UserInfo):
    try:
        if u1.login(isRegister=True):
            u1.log("注册成功 %s" % u1.userKey)
            redisHelper.saveUserKey(u1.userKey)
            userKeys.append(u1)
    except UserError as e:
        if e.errorCode == 6:
            # 上限了
            u1.log(e.message)
            setCanRun(False)
        elif e.errorCode == 7:
            u1.log(e.message)
        else:
            u1.errorLog(e,exc_info=True)
    except Exception as e:
        u1.errorLog(e,exc_info=True)

def endCallBack(accounts):
    logger.info("注册结束 本次共注册 %d 个账号" % len(userKeys))
    writeConfig(userKeys,"./guests.txt")


# def main():
#     threadRun("./socks5.txt", workCallBack, endCallBack,threadRunCount=10,isThread=False)

def registerGuest():
    userKey = createUserKey()
    # userKey = "bff56191-0434-4976-95c2-860b2394cf30"
    userInfo = UserInfo(userKey=userKey,socks5="jhc:123456@***************:33026")
    userInfo.login(isRegister=True)
    # if userInfo.login():
    #     userInfo.requrestSetDeviceInfo()
    #     print("ok")

#"bff56191-0434-4976-95c2-860b2394cf30"

def main(isV2=False,maxCount=99999,autoExitIfChangeEncrypt=True,fackIP=False, needSave=True):
    registerWithDynamicProxy(isV2=isV2,maxCount=maxCount,autoExitIfChangeEncrypt=autoExitIfChangeEncrypt,fackIP=fackIP, needSave= needSave)
    
if __name__ == '__main__':
    logger.info("启动脚本")
    if not redisHelper.redisMgr.enable:
        logger.error("redis连接失败")
        exit(0)
    from Api.ProxyApi import changeKeyTo1
    # changeKeyTo2()
    try:
        if isDebug:
            main(isV2=True,maxCount=1,autoExitIfChangeEncrypt=False,fackIP=True)
        else:
            fire.Fire(main)
    except KeyboardInterrupt:
        pass