# /bin/env python3
# -*- coding: UTF-8 -*- 

import json
import time,random
from Model.Kingdom import Kingdom
from Model.Account import Account
from Model.UserError import UserError
from Api.UserInfo import UserInfo
import threading
from Unit.Logger import logger
from Unit.FileTool import loadConfig,writeConfig
from Unit.Redis import redisHelper
class WorkThread(threading.Thread):
    def __init__(self, userInfo):
        threading.Thread.__init__(self)
        self.userInfo = userInfo
        self.name = userInfo.email
        
    def run(self):
        retry = 0
        u1 = self.userInfo
        logger.info("开启线程： " + self.name)
        u1.display()
        return 
        def callBack(u1):
            u1.getSilverFree()
            u1.getGoldFree()
            

        while not u1.invalidStatu:
            try:
                if u1.isLogin is False:
                    u1.login()

                if u1.enter():
                    if retry == 0:
                        u1.claimEvent()
                    u1.runGridAttackForAlonely(callBack)
            except UserError as e:
                if e.errorCode == -4:
                    retry += 1
                    if retry > 3:
                        logger.error("账号异常：" + u1.email)
                    else:
                        u1.invalidStatu = False
                        time.sleep(3 * 60)
                        u1.log("3分钟后重新登录")
                else:
                    u1.invalidStatu = True
                    u1.errorLog(e.message,exc_info=True)
            
def main():
    accounts = loadConfig('./account.txt')
    newAccounts = []
    allRemoveAccount = redisHelper.allRemoveAccount()
    if len(allRemoveAccount) > 0:
        for account in accounts:
            if account.email not in allRemoveAccount:
                newAccounts.append(account)
        logger.info("一共删除%d个账号" % (len(accounts) - len(newAccounts)))
        writeConfig(newAccounts,"./account.txt")
        accounts = newAccounts
        

    if len(accounts):
        logger.info("一共%d个账号" % len(accounts))
        threads = []
        for account in accounts:
            thread = WorkThread(account.returnUserInfo(isMain=True))
            thread.setDaemon(True)
            thread.start()
            threads.append(thread)
        
        for t in threads:
            t.join()
        logger.info ("任务结束")


def test():
    u1 = UserInfo(userKey="fc43c68b-ceb3-4e46-93b6-242ed529c57e",
                  socks5="127.0.0.1:1086")
    
    u1.login()
    if u1.isLogin:
        if u1.enter():
            u1.claimEvent()
            u1.claimEvent()
            u1.tryUseItems()
            u1.enter()
            # u1.runGridAttackForAlonely()


if __name__ == '__main__':
    logger.info("启动脚本")
    # test()
    main()
