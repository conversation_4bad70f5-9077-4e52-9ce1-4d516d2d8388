# /bin/env python3
# -*- coding: UTF-8 -*-
'''
功能描述：手动检测开团兵种
编写人：darkedge
编写日期：2022年06月27日
   
'''


from Api.UserInfo import UserInfo
from Api.DataRecord import dataDisplay,tzDate
import time
import random
import threading
import json
from Unit.FileTool import loadS5List
from Unit.Logger import logger
from Unit.Redis import redisHelper
from Unit.WorkThread import WorkThread
import asyncio
import requests
import sys,os
import fire
import datetime,pytz

isDebug = True if sys.gettrace() else False
sleepTime = 20

def main(token):
    '''
    token: token
    '''
    # print(loc[0])
    # loc = [int(v) for v in loc.split(",")]
    user = UserInfo(f'{random.randint(1, 99999999)}@safe.com',
                    token=token, socks5=random.choice(loadS5List()))
    if False:
        user.initLog()
    else:
        user.login()
    user.wsWithKingdomApp(isDebug=True)
    lastId = None
    while not user.isInvalid:
        inputId = input("请输入团战id:")
        if inputId and len(inputId):
            lastId = inputId
            os.system("clear")
        else:
            if not lastId:
                print('没有缓存id')
                continue
            else:
                os.system("clear")
                print("使用缓存id")

        t = time.time()
        result = user.monitoringAllianceBattleOnce(rallyMoId=lastId)
        t2 = time.time()

        if result:
            valueStr = ""
            v = result
            troopMap = v["troopRunMap"]
            sum = 0
            for key in troopMap:
                sum += troopMap[key]
            if sum > 0:
                valueStr += f'\n{v["leaderKingdomName"]} 攻击 {v["targetKingdomName"]} 总兵力:{sum // 10000}万 剩余时间{user.redString(v["overTime"])} 距离:{v["distance"]} 消息:{v["message"]}\n'
                for key in troopMap:
                    value = troopMap[key]
                    if value > 0:
                        valueStr += f'{key}:{value} {round(value/sum * 100,2)}%\n'

            # print(f"查询耗时{round(t2-t,2)}\n",datetime.datetime.now(tz).strftime("%m-%d_%H:%M:%S"))
            print(valueStr)
        else:
            print(f"查询耗时{round(t2-t,2)}\n",datetime.datetime.now(tz).strftime("%m-%d_%H:%M:%S"))
            print("没有有效数据\n")
        # if t2 - t < sleepTime:
        #     print(f'休息{round(sleepTime - t2 + t,2)}秒')
        #     time.sleep(sleepTime - t2 + t)

if __name__ == '__main__':
    try:
        if isDebug:
            print("debug")
            token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJfaWQiOiI2MmI1MDU0MGY3ZWM1MTZlYzQ0OTAyYjEiLCJraW5nZG9tSWQiOiI2MmI1MDU0MGY3ZWM1MTZlYzQ0OTAyYjIiLCJ3b3JsZElkIjo0MiwidmVyc2lvbiI6MTQ2MiwiYnVpbGQiOjAsInBsYXRmb3JtIjoiaW9zIiwidGltZSI6MTY1NjMyNzAwNTMzMywiY2xpZW50WG9yIjoiMCIsImlhdCI6MTY1NjMyNzAwNSwiZXhwIjoxNjU2OTMxODA1LCJpc3MiOiJub2RnYW1lcy5jb20iLCJzdWIiOiJ1c2VySW5mbyJ9.CxL201fPHX3GarD865k5xwYpT-dlD6POwCjZ6PQIzZM0"
            main(token)
        else:
            fire.Fire(main)
    except KeyboardInterrupt:
        pass
