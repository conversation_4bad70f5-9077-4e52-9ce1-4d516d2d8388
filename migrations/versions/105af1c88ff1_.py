"""empty message

Revision ID: 105af1c88ff1
Revises: 
Create Date: 2022-12-26 17:30:38.433315

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '105af1c88ff1'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('mining_work_man',
    sa.<PERSON>umn('id', sa.Integer(), nullable=False),
    sa.<PERSON>umn('email', sa.String(length=120), nullable=True),
    sa.Column('pwd', sa.String(length=120), nullable=True),
    sa.Column('name', sa.String(length=40), nullable=True),
    sa.Column('userKey', sa.String(length=36), nullable=True),
    sa.Column('kingdom_id', sa.String(length=40), nullable=True),
    sa.Column('token', sa.String(length=2048), nullable=True),
    sa.Column('socks5', sa.String(length=200), nullable=True),
    sa.Column('params', sa.String(length=2048), nullable=True),
    sa.<PERSON>umn('active', sa.<PERSON>(), nullable=True),
    sa.Column('confirmed_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('email'),
    sa.UniqueConstraint('name'),
    sa.UniqueConstraint('socks5'),
    sa.UniqueConstraint('userKey')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('mining_work_man')
    # ### end Alembic commands ###
