"""empty message

Revision ID: 1196aa3ec9bb
Revises: 105af1c88ff1
Create Date: 2023-07-21 18:17:03.885575

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '1196aa3ec9bb'
down_revision = '105af1c88ff1'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('token_work_man', schema=None) as batch_op:
        batch_op.add_column(sa.Column('appleSubId', sa.String(length=32), nullable=True))
        batch_op.add_column(sa.Column('created_at', sa.DateTime(), nullable=True))
        batch_op.create_unique_constraint("uq_appleSubId", ['appleSubId'])

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('token_work_man', schema=None) as batch_op:
        batch_op.drop_constraint("uq_appleSubId", type_='unique')
        batch_op.drop_column('created_at')
        batch_op.drop_column('appleSubId')

    # ### end Alembic commands ###
