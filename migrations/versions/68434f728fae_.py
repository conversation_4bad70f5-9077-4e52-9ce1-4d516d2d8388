"""empty message

Revision ID: 68434f728fae
Revises: 9b14d562d55c
Create Date: 2025-01-09 14:55:27.102995

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy import text
import uuid
# revision identifiers, used by Alembic.
revision = '68434f728fae'
down_revision = '9b14d562d55c'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('user', schema=None) as batch_op:
        batch_op.add_column(sa.Column('fs_uniquifier', sa.String(length=64), nullable=True))
        batch_op.create_unique_constraint('unique_fs_uniquifier', ['fs_uniquifier'])


    # 逐行更新用户的 fs_uniquifier
    connection = op.get_bind()
    result = connection.execute(text("SELECT id FROM user WHERE fs_uniquifier IS NULL"))
    for row in result:
        connection.execute(
            text("UPDATE user SET fs_uniquifier = :uuid WHERE id = :id"),
            {"uuid": uuid.uuid4().hex, "id": row[0]}
        )

    # 设置为非空
    with op.batch_alter_table('user', schema=None) as batch_op:
        batch_op.alter_column('fs_uniquifier', nullable=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('user', schema=None) as batch_op:
        batch_op.drop_constraint('unique_fs_uniquifier', type_='unique')
        batch_op.drop_column('fs_uniquifier')

    # ### end Alembic commands ###
