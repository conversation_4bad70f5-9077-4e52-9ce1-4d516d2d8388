"""empty message

Revision ID: 9b14d562d55c
Revises: 1196aa3ec9bb
Create Date: 2023-07-27 17:32:27.914869

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '9b14d562d55c'
down_revision = '1196aa3ec9bb'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('log_record_model',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=40), nullable=True),
    sa.Column('type', sa.Integer(), nullable=True),
    sa.Column('msg', sa.String(length=1024), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('active', sa.<PERSON>(), nullable=True),
    sa.<PERSON>eyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('log_record_model')
    # ### end Alembic commands ###
