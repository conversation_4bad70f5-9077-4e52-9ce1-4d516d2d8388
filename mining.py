# /bin/env python3
# -*- coding: UTF-8 -*-
"""
功能描述：大号挖矿打怪
编写人：darkedge
编写日期：2021年12月25日
   
"""

import asyncio
import datetime
import functools
import json
import math
import os
import random
import sys
import threading
import time
from urllib.parse import quote

import requests
import schedule

from Api.DataRecord import currentHour
from Api.UserInfo import UserInfo
from Model.Kingdom import Kingdom
from Model.UserError import UserError
from Unit.FileTool import loadS5List, loadSelfToken
from Unit.Logger import logger
from Unit.UserInfoHelper import MiningHelper

isDebug = True if sys.gettrace() else False

sendList = {}

s5List = loadS5List("socks5_mining.txt")
autoEnergy = False

territoryLocs = {}
safeFields = [
    # [1024,1024],#议会
    # [896,1152],#神殿
]


def enableAutoEnergy():
    global autoEnergy
    autoEnergy = True
    logger.info("开启自动充能")


disLocs = [
    [21, 831, 1287],
    [21, 849, 1252],
    [21, 855, 1185],
    [21, 910, 1204],
    [21, 901, 1080],
    [21, 936, 1062],
    [21, 880, 1020],
    [21, 1012, 1130],
    [21, 987, 1148],
    [21, 995, 1264],
    [21, 795, 1154],
    [21, 920, 1163],
    [21, 967, 1092],
]


def hasSend(loc):
    key = "send_" + str(loc)
    if sendList.get(key):
        return True
    sendList[key] = True
    return False


def tryVipAndMint(u1: UserInfo):
    try:
        # u1.debuglog("schedule触发tryHarvestAndMint")

        u1.tryClaimVip()
        # u1.getSilverFree()
        # # 不收集
        # # u1.tryHarvestAll()
        hour = currentHour()
        if hour % 3 == 0:
            u1.tryAutoBuyCaravan(resourceAll=True)
    except Exception as e:
        u1.errorLog(e, exc_info=True)


def gathering(u1: UserInfo):
    """挖矿模式"""
    u1.log("挖矿模式")
    lastMode = True
    inputValue = input("是否只挖尾矿(y/n)默认y:")
    if len(inputValue) and inputValue.lower() == "n":
        u1.log("挖全部")
        lastMode = False

    maxCount = 9999999
    minCount = 3000
    descStr = ""
    if lastMode:
        maxCount = int((u1.marchSize / 5) / 100) * 100
        maxCount = max(maxCount, 40000)

        minCount = int(maxCount / 5 * 3.5 / 100) * 100
        descStr = f"尾矿极值{minCount * 2.5}"

    u1.log(f"挖矿最大出兵量{maxCount} 最小出兵量{minCount} {descStr}")
    while True:
        if u1.checkWSWithKingdomApp():
            u1.trySetDeviceInfo()
            u1.wsWithKingdomApp(isDebug=isDebug)
        u1.tryGathering(
            maxCount=maxCount,
            minCount=minCount,
            dsecType=None,
            minLevel=2,
            mustLast=lastMode,
            power=4,
        )


def autoJoinAllianceBattle(u1: UserInfo, mustList=[], troopNum=1, auto=False):
    u1.log("打团模式")
    minLevel = 1
    troopNum = 50000
    enableAutoEnergy()
    if auto:
        pass
    else:
        # inputValue = input("是否自动充能(y/n)默认n:")
        # if inputValue and inputValue.lower() == "y":
        #     enableAutoEnergy()

        # inputValue = input("请输入最小等级(默认1):")
        # if inputValue and inputValue.isdigit():
        #     minLevel = int(inputValue)

        inputValue = input("请输入出兵模式(默认50000):")
        if inputValue and inputValue.isdigit():
            troopNum = int(inputValue)
            # if troopNum != 10000 and troopNum != 1:
            #     u1.log("输入异常")
            #     exit(-1)

        # mustGreenDragon = input("是否强制打绿龙(y/n):")
        # if len(mustGreenDragon) and mustGreenDragon.lower() == "y":
        #     mustList.append(20200202)

        # mustRedDragon = input("是否强制打红龙(y/n):")
        # if len(mustRedDragon) and mustRedDragon.lower() == "y":
        #     mustList.append(20200203)

        # mustGoldDragon = input("是否强制打金龙(y/n):")
        # if len(mustGoldDragon) and mustGoldDragon.lower() == "y":
        #     mustList.append(20200204)

        # mustKnight = input("是否强制打骑士(y/n):")
        # if len(mustKnight) and mustKnight.lower() == "y":
        #     mustList.append(20200201)

    print(f"自动进团{troopNum}")
    u1.enterKingdomAsyncTasks()
    t = 0
    lastMsg = ""
    startTime = time.time()
    schedule.every(30).to(40).minutes.do(u1.enterKingdomAsyncTasks)
    schedule.every(10).minutes.do(u1.tryClaimVip)

    while True:
        schedule.run_pending()
        t += 1
        if t % 10 == 0:
            u1.troopRecover()
        if u1.checkWSWithKingdomApp():
            u1.trySetDeviceInfo()
            u1.wsWithKingdomApp(isDebug=isDebug)

        res = u1.autoJoinBattle(
            troopNum=troopNum,
            minLevel=minLevel,
            mustList=mustList,
            autoEnergy=autoEnergy,
        )
        if res is False:
            return
        else:
            u1.randomSleep(30, 60)
            # u1.enterKingdomAsyncTasks()

        averageCount = u1.attackCount / math.ceil((time.time() - startTime) / 3600)
        msg = f"一轮循环 队列{len(u1.fieldTasks)} 活力:{u1.actionPoint} 跟团次数:{u1.attackCount} 频率:{round(averageCount,2)}"
        if lastMsg != msg:
            lastMsg = msg
            u1.log(msg)


def attackMoster(u1: UserInfo):
    u1.log("打怪模式")
    onlyCavalry = False
    minLevel = input("请输入最小等级(默认3):")
    if len(minLevel) == 0:
        minLevel = 3
    else:
        if minLevel.isdigit():
            minLevel = int(minLevel)
            if minLevel > 9:
                u1.log("这么牛逼的怪还没出生")
                exit(-1)
        else:
            u1.log("等级输入错误")
            exit(-1)

    maxLevel = input("请输入最大等级(默认4):")
    if len(maxLevel) == 0:
        maxLevel = 4
    else:
        if maxLevel.isdigit():
            maxLevel = int(maxLevel)
        else:
            u1.log("等级输入错误")
            exit(-1)

    mining = True
    inputValue = input("顺手挖水晶矿(y/n)默认y:")
    if len(inputValue) and inputValue.lower() == "n":
        mining = False

    inputValue = input("骑兵优先(y/n)默认n:")
    if len(inputValue) and inputValue.lower() == "n":
        onlyCavalry = True

    descMonster = None
    # orcInput = input("指定攻击兽人(y/n):")
    # if len(orcInput) and orcInput.lower() == "y":
    #     descMonster = 20200101
    #     logger.info("指定攻击兽人")
    # else:
    #     orcInput = input("指定攻击骷髅(y/n):")
    #     if len(orcInput) and orcInput.lower() == "y":
    #         descMonster = 20200102
    #         logger.info("指定攻击骷髅")
    #     else:
    #         orcInput = input("指定攻击傀儡(y/n):")
    #         if len(orcInput) and orcInput.lower() == "y":
    #             descMonster = 20200103
    #             logger.info("指定攻击傀儡")

    def fieldCallBack(info):
        charm = u1.charmCodeCheck(info)
        if charm:
            loc = info.get("loc")
            u1.log(f"{charm} {loc}")

    schedule.every(15).minutes.do(tryVipAndMint, u1)
    schedule.every(60).to(61).minutes.do(u1.tryUseCollectionSkill)
    while True:
        # 20200104
        schedule.run_pending()
        u1.troopRecover()
        if u1.checkWSWithKingdomApp():
            u1.trySetDeviceInfo()
            u1.wsWithKingdomApp(isDebug=isDebug)
        if u1.checkWSWithFieldApp():
            u1.wsWithFieldApp(
                isDebug=False,
                zone=u1.coordinateTransformation(u1.loc, power=4),
                fieldCallBack=fieldCallBack,
            )

        res = u1.tryAttackMonster(
            descMonster,
            maxDistance=300,
            minLevel=minLevel,
            maxLevel=maxLevel,
            power=4,
            mining=mining,
            onlyCavalry=onlyCavalry,
        )
        if res:
            u1.randomSleep(10)
        elif res is False:
            u1.randomSleep(30)
        else:
            break
        u1.log(f"一轮循环 队列{len(u1.troopFields)} 活力:{u1.actionPoint}")


def crystalOrTreasure(u1: UserInfo, auto=False):
    """宝藏精灵或者水晶矿"""
    u1.errorLog("此功能已停用")


def crystalOrTreasureNew(u1: UserInfo, auto=False):
    """宝藏精灵或者水晶矿新版"""
    maxTroopNum = 2000
    descLoc = None
    autoPackage = False
    isGoback = False
    autoBuy = False
    highDelay = False
    crystalAcceleration = False
    autoVip = True
    maxCrystal = 25000
    isSingleQueue = False
    isAutoSkill = False
    isJoinAllianceBattle = False
    maxCrystalLevel = 3
    minCrystalLevel = 1
    autoEat = False
    battleTroopNum = 30000

    if not auto:
        inputValue = input("出兵数量(默认2000):")
        if inputValue.isdigit():
            maxTroopNum = int(inputValue)
            u1.log(f"出兵数量{maxTroopNum}")

        inputValue = input("请输入目标区域(x,y 不输入为自身):")
        if len(inputValue) == 0:
            descLoc = None
        else:
            values = inputValue.split(",")
            if len(values) != 2:
                raise UserError("区域异常")

            descLoc = [u1.worldId] + [int(v) for v in values]

        inputValue = input("是否自动打包(y/n):")
        if len(inputValue):
            if inputValue.lower() == "n":
                autoPackage = False
            elif inputValue.lower() == "y":
                autoPackage = True

        # inputValue = input("是否单队列(y/n):")
        # if len(inputValue) and inputValue.lower() == "y":
        #     isSingleQueue = True
        #     isGoback = True
        #     highDelay = True
        #     maxCrystal = 999999
        # else:
        #     inputValue = input("是否自动回撤(y/n):")
        #     if len(inputValue) and inputValue.lower() == "y":
        #         isGoback = True
        #         highDelay = True
        #         # inputValue = input("是否启用高间隔版本(y/n):")
        #         # if len(inputValue) and inputValue.lower() == "y":
        #         #     highDelay = True
        #         inputValue = input("自动停止采集值(25000):")
        #         if len(inputValue) and inputValue.isdigit():
        #             maxCrystal = int(inputValue)
        #             print(f'采集{maxCrystal} 后自动停止')

        inputValue = input("是否自动收集(y/n):")
        if len(inputValue) and inputValue.lower() == "y":
            isAutoSkill = True
            print("开启自动技能")

        inputValue = input("是否自动跟团(y/n):")
        if len(inputValue) and inputValue.lower() == "y":
            isJoinAllianceBattle = True
            inputValue = input("跟团数量(默认30000):")
            if inputValue.isdigit():
                battleTroopNum = int(inputValue)
                u1.log(f"跟团出兵数量{battleTroopNum}")
        inputValue = input("是否自动购买商城(y/n):")
        if len(inputValue) and inputValue.lower() == "y":
            autoBuy = True

        # inputValue = input("是否启用水晶加速(y/n):")
        # if len(inputValue) and inputValue.lower() == "y":
        #     crystalAcceleration = True

        # inputValue = input("是否自动领取Vip(y/n):")
        # if len(inputValue) and inputValue.lower() == "y":
        #     autoVip = True
        inputValue = input("最高挖几级(3)?")
        if inputValue and inputValue.isdigit():
            maxCrystalLevel = int(inputValue)
            if maxCrystalLevel > 0:
                inputValue = input("最低挖几级(1)?")
                minCrystalLevel = int(inputValue or 1)

        else:
            maxCrystalLevel = 3

        inputValue = input("自动吃药(y/n):")
        if inputValue and inputValue.lower() == "y":
            autoEat = True

    quickCrystalMode(
        u1,
        maxTroopNum=maxTroopNum,
        descLoc=descLoc,
        autoPackage=autoPackage,
        noInTerritory=False,
        isJoinAllianceBattle=isJoinAllianceBattle,
        isGoback=isGoback,
        autoBuy=autoBuy,
        highDelay=highDelay,
        crystalAcceleration=crystalAcceleration,
        autoVip=autoVip,
        autoUseItem=False,
        maxCrystal=maxCrystal,
        isSingleQueue=isSingleQueue,
        isAutoSkill=isAutoSkill,
        maxCrystalLevel=maxCrystalLevel,
        autoEat=autoEat,
        battleTroopNum=battleTroopNum,
        minCrystalLevel=minCrystalLevel,
    )


def crystalOrTreasureNewHightAuto(u1: UserInfo):
    """水晶矿自动新版"""
    maxTroopNum = 2000
    descLoc = None
    autoPackage = False
    isGoback = True
    autoBuy = False
    highDelay = True
    crystalAcceleration = True
    autoVip = True
    quickCrystalMode(
        u1,
        maxTroopNum=maxTroopNum,
        descLoc=descLoc,
        autoPackage=autoPackage,
        noInTerritory=False,
        isJoinAllianceBattle=False,
        isGoback=isGoback,
        autoBuy=autoBuy,
        highDelay=highDelay,
        crystalAcceleration=crystalAcceleration,
        autoVip=autoVip,
        autoUseItem=False,
    )


def sweepCharacter(u1: UserInfo):
    """清空周围符"""
    u1.errorLog("此功能已停用")


def autoAttack(u1: UserInfo):
    locs = []
    continueInput = True
    inputValue = input("是否攻击九宫格(y/n):")
    if inputValue.lower() == "y":
        continueInput = False
        baseX = u1.loc[1] - 2
        baseY = u1.loc[2] - 2
        for i in range(9):
            if i == 4:
                continue
            loc = [baseX + i % 3 * 2, baseY + i // 3 * 2]
            u1.log(f"添加坐标{loc[0]},{loc[1]}")
            locs.append(loc)

    while continueInput:
        if len(locs) != 0:
            inputValue = input("是否继续增加坐标(y/n):")
            if len(inputValue) and inputValue.lower() == "n":
                break

        locDesc = input("请输入目的坐标(1,1):")
        locStr = locDesc.split(",")
        loc = [int(v) for v in locStr]
        locs.append(loc)

    onlyInfantry = True
    inputValue = input("是否只使用步兵(y/n)默认y:")
    if len(inputValue) and inputValue.lower() == "n":
        onlyInfantry = False
        u1.log("使用全部部队")

    while True:
        for loc in locs:
            kingdom = Kingdom(f"any{random.randint(1, 9999999)}", loc)
            kingdom.maxCount = 5
            u1.attackKingdomAlone(kingdom, onlyInfantry=onlyInfantry)
        if len(locs) > 1:
            u1.randomSleep(1, 2)
        else:
            u1.randomSleep(15)
        u1.log("一轮循环结束")


def autoBattle(u1: UserInfo, maxCount=8):
    """自动开团"""
    while True:
        if u1.checkWSWithKingdomApp():
            u1.trySetDeviceInfo()
            u1.wsWithKingdomApp(isDebug=isDebug)
        if u1.getTroops():
            u1.taskAll()
            if len(u1.fieldTasks) < maxCount:
                pass

        u1.randomSleep(30)


def onlyTreasure(u1: UserInfo):
    """只打宝藏"""
    inputValue = input("是否自动充能(y/n)默认n:")
    if len(inputValue) and inputValue.lower() == "y":
        enableAutoEnergy()

    treasureMaxLevel = input("请输入宝藏最大等级(默认3):")
    if len(treasureMaxLevel) == 0:
        treasureMaxLevel = 3
    else:
        treasureMaxLevel = int(treasureMaxLevel)

    onlyHidden = input("只打隐藏精灵(y/n):")
    if len(onlyHidden) and onlyHidden.lower() == "y":
        onlyHidden = True
    else:
        onlyHidden = False

    sendMsg = input("高级精灵是否发送到联盟(y/n):")
    sendFlag = False
    if len(sendMsg) and sendMsg.lower() == "y":
        sendFlag = True
        u1.log("发送到联盟内")

    u1.tmpMarchList = {}

    def marchCallBack(objects):
        """行军回调"""
        for object in objects:
            loc = object.get("toLoc")
            key = f"{loc[1]}_{loc[2]}"
            u1.tmpMarchList[key] = 1

    rawLoc = [u1.loc[0], 1024, 1024]
    locs = {f"1024_1024": 1}
    searchLoc = rawLoc
    errorCount = 0
    dp = 224

    t = 0
    attackCount = 0

    while True:
        if time.time() - t > 60 * 60:
            t = time.time()
            searchLoc = rawLoc
            logger.info(f"重置坐标{searchLoc}")

        if (
            searchLoc[1] > 2045 + dp
            or searchLoc[2] > 2045 + dp
            or searchLoc[1] < -dp
            or searchLoc[2] < -dp
        ):
            logger.info("坐标异常 下一个")
            searchLoc = [u1.worldId] + u1.rightLaw(searchLoc[1:], locs, dp)
            u1.log(f"搜索坐标:{searchLoc}")
            errorCount += 1
            if (
                errorCount > 500
                or searchLoc[1] > 2140
                or searchLoc[2] > 2140
                or searchLoc[1] < -100
                or searchLoc[2] < -100
            ):
                searchLoc = rawLoc
                locs = {f"1024_1024": 1}
                t = time.time()
                logger.info(f"重置坐标500 {searchLoc}")
            continue

        errorCount = 0
        if u1.checkWSWithKingdomApp():
            u1.trySetDeviceInfo()
            u1.wsWithKingdomApp(isDebug=True)

        if u1.level < 15:
            if random.randint(1, 10) == 5:
                u1.troopRecover()

        if u1.getTroops():
            u1.taskAll()
            if len(u1.fieldTasks) < u1.marchLimit:
                historyLoc = u1.loadFieldTasks()
                u1.log(f"搜索坐标:{searchLoc}")

                resList, resObjects = u1.searchFields(
                    [20200104, 20300101, 20400401, 20600101],
                    loc=searchLoc,
                    power=4,
                    show=False,
                    marchCallBack=marchCallBack,
                )
                if resObjects is None:
                    # u1.randomSleep(30,60)
                    u1.log("数据异常")
                    resObjects = {}
                treasures = []
                otherFields = {}
                canAttackTreasures = []
                """归类"""
                for info in resObjects:
                    code = info.get("code")
                    if code == 20200104:
                        treasures.append(info)
                    else:
                        loc = info.get("loc")
                        key = f"{loc[1]}_{loc[2]}"
                        otherFields[key] = 1

                for info in treasures:
                    loc = info.get("loc")
                    level = info.get("level")
                    if loc in historyLoc:
                        continue

                    isHidden = False
                    for i in range(1, 4):
                        x = i % 2
                        y = i // 2
                        key = f"{loc[1] - x}_{loc[2] - y}"
                        if key in otherFields:
                            isHidden = True
                            break

                    if level > treasureMaxLevel:
                        u1.log(
                            f"{level}级的宝藏不能攻击 {isHidden and '隐藏' or '地表'}"
                        )
                        if sendFlag and not isHidden:
                            if not hasSend(loc):
                                u1.log("发送到联盟")
                                name = "宝藏精灵"
                                u1.chatNew(loc, f"Lv.{level}{name}Lv.{level}")
                                u1.randomSleep(2)
                        continue
                    if onlyHidden:
                        # 只打隐藏精灵
                        if isHidden:
                            canAttackTreasures.append(info)
                    else:
                        key = f"{loc[1]}_{loc[2]}"
                        if u1.tmpMarchList.get(key):
                            # 被人打了
                            continue
                        canAttackTreasures.append(info)

                if len(canAttackTreasures) == 0:
                    u1.log("没有可攻击的宝藏")
                    searchLoc = [u1.worldId] + u1.rightLaw(searchLoc[1:], locs)
                    u1.log(f"搜索坐标:{searchLoc}")
                    continue

                if u1.actionPoint > 30:
                    u1.log(f"当前活力{u1.actionPoint}")
                    for info in canAttackTreasures:
                        if u1.actionPoint > 30:
                            if u1.tryAttackTreasure(info, historyLoc, maxLevel=9):
                                attackCount += 1
                        elif autoEnergy:
                            energy = u1.chargeEnergy(200)
                            if energy > 0:
                                continue
                else:
                    u1.log("活力不足")
                    if autoEnergy:
                        energy = u1.chargeEnergy(200)
                        if energy > 0:
                            continue
                    u1.checkDragon()
                    break

                u1.randomSleep(2, 5, msg=f"攻击次数{attackCount}")
            else:
                u1.randomSleep(20, 50, msg=f"队列不足")


def onlyCrystal(u1: UserInfo):
    """隐藏的水晶矿"""
    u1.errorLog("此功能已停用")


def searchSomeFields(u1: UserInfo):
    """搜索某种资源"""
    u1.errorLog("此功能已停用")


def quickCrystalMode(
    u1: UserInfo,
    maxTroopNum=None,
    descLoc=None,
    isNotT5=False,
    autoPackage=False,
    noInTerritory=False,
    isJoinAllianceBattle=False,
    isGoback=True,
    autoBuy=True,
    highDelay=True,
    crystalAcceleration=True,
    autoVip=True,
    autoUseItem=True,
    maxCrystal=999999,
    isSingleQueue=False,
    isAutoSkill=False,
    maxCrystalLevel=3,
    autoEat=False,
    battleTroopNum=30000,
    minCrystalLevel=1,
):
    """快速水晶矿模式"""
    if maxTroopNum is None:
        maxTroopNum = 1000

    helper = MiningHelper(u1)
    helper.maxTroopNum = maxTroopNum
    if descLoc:
        helper.descLoc = descLoc
    helper.autoPackage = autoPackage
    helper.noInTerritory = noInTerritory
    helper.isJoinAllianceBattle = isJoinAllianceBattle
    helper.isGoback = isGoback
    helper.autoBuy = autoBuy
    helper.highDelay = highDelay
    helper.crystalAcceleration = crystalAcceleration
    helper.autoVip = autoVip
    helper.autoUseItem = autoUseItem
    helper.autoSkill = isAutoSkill
    helper.maxCrystal = maxCrystal
    helper.maxCrystalLevel = maxCrystalLevel
    helper.minCrystalLevel = minCrystalLevel
    helper.autoEat = autoEat
    helper.battleTroopNum = battleTroopNum
    if u1.realWorld != 47:
        times = []
        for i in range(24):
            if i % 4 != 0:
                times.append(i)
        helper.miningTimes = times
    helper.run()


def onCall(u1: UserInfo):
    t = 0
    while not u1.isInvalid:
        t += 1
        if u1.checkWSWithKingdomApp():
            u1.trySetDeviceInfo()
            u1.wsWithKingdomApp(isDebug=True)

        time.sleep(60)
        if t % 2 == 0:
            u1.log(f"token:{u1.token}")
            u1.wallInfo()


def autoHeal(u1: UserInfo):
    useCrystal = False
    inputValue = input("是否使用水晶治疗(y/n):")
    if inputValue.lower() == "y":
        useCrystal = True

    t = 0
    while not u1.isInvalid:
        t += 1
        if u1.checkWSWithKingdomApp():
            u1.trySetDeviceInfo()
            u1.wsWithKingdomApp(isDebug=True)

        wounded = u1.wounded()
        if useCrystal:
            if wounded:
                res = u1.healInstant()
                if res is not None and res is not False:
                    u1.log("水晶治疗成功 收兵")
                    u1.troopRecover()
        elif u1.autoTreatment(wounded):
            if wounded:
                u1.log("治疗成功 收兵")
                u1.troopRecover()
        time.sleep(5)


def autoDragon(u1: UserInfo):
    code = None
    inputValue = input("是否使用绿龙(y/n):")
    if inputValue.lower() == "y":
        code = 10104105
    else:
        inputValue = input("是否使用红龙(y/n):")
        if inputValue.lower() == "y":
            code = 10104106
        else:
            inputValue = input("是否使用金龙(y/n):")
            if inputValue.lower() == "y":
                code = 10104107

    if not code:
        print("输入异常")
        return
    t = 0
    while not u1.isInvalid:
        t += 1
        if u1.checkWSWithKingdomApp():
            u1.trySetDeviceInfo()
            u1.wsWithKingdomApp(isDebug=True)

        # u1.tryUseDragon()
        u1.itemUse(code, 1)
        time.sleep(2)


def autoRepair(u1: UserInfo):
    asyncio.set_event_loop(asyncio.new_event_loop())
    schedule.every(30).to(31).minutes.do(u1.tryRepairWall2)
    schedule.every(15).minutes.do(u1.tryClaimVip)
    schedule.every(150).to(240).minutes.do(u1.tryHarvestAll)
    schedule.every(60).to(61).minutes.do(u1.tryUseCollectionSkill)

    while not u1.isInvalid:
        if u1.checkWSWithKingdomApp():
            u1.trySetDeviceInfo()
            u1.wsWithKingdomApp(isDebug=True)
            u1.randomSleep(3)
        try:
            schedule.run_pending()
        except Exception as e:
            u1.errorLog(e, exc_info=True)

        time.sleep(0.1)


def main():
    os.environ["LEAGUEDEBUG"] = "1"
    # sys.argv.append('eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.n49Ycm_BwBM8KGMEcTSPJJCRaMeDn4Oeg4zjYdFzA1A')
    localS5 = None
    import platform

    if platform.processor() != "arm":
        localS5 = "127.0.0.1:1087"
    else:
        localS5 = random.choice(s5List)
    u1 = UserInfo(
        "<EMAIL>",
        "qqqqqqq",
        # socks5=random.choice(s5List),
        # socks5="a:b@***************:33087",#random.choice(s5List),
        socks5=localS5,
        token=loadSelfToken(),
    )
    # sys.argv.append("192b0d3b-395e-e8e0-ac02-2c61f4ef9b13")
    # sys.argv.append("eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJfaWQiOiI2MjhjMjAzZTYyYzQxNzZkNTI1NjE5MTYiLCJraW5nZG9tSWQiOiI2MjhjMjAzZTYyYzQxNzZkNTI1NjE5MWIiLCJ2ZXJzaW9uIjoxNDYyLCJidWlsZCI6MCwicGxhdGZvcm0iOiJpb3MiLCJ0aW1lIjoxNjU2Mjk5OTY5MTk3LCJjbGllbnRYb3IiOiIwIiwiaWF0IjoxNjU2Mjk5OTY5LCJleHAiOjE2NTY5MDQ3NjksImlzcyI6Im5vZGdhbWVzLmNvbSIsInN1YiI6InVzZXJJbmZvIn0.SrSXbzvcpdLKQnAhen2pVDlB7IZq6jVuh4VBMdh02JE")
    if len(sys.argv) > 2:
        u1 = UserInfo(sys.argv[1], sys.argv[2], socks5=random.choice(s5List))
        u1.log("邮箱模式")
    elif len(sys.argv) > 1:
        u = sys.argv[1]
        if len(u) == 36:
            u1 = UserInfo(userKey=u, socks5=random.choice(s5List))
            u1.log("游客模式模式")
        else:
            u1 = UserInfo(
                f"somebody{random.randint(1, 9999)}@any.com",
                token=sys.argv[1],
                socks5=random.choice(s5List),
            )
            u1.log(f"token模式{sys.argv[1]}")
    else:
        u1.log("darkedge专属模式")

    # inputValue = input("是否使用web登录(y/n):")
    # if inputValue.lower() == "y":
    u1.autoExitIfChangeEncrypt = False
    u1.isWebDevice = True

    try:
        u1.saveSocks5 = True
        if not u1.pwd and not u1.userKey:
            u1.loadEmailWithToken()
        if not u1.userKey:
            u1.becomeLogSelfOnly()
        if not u1.login():
            u1.log("进入失败")
            return
    except UserError as e:
        if e.errorCode == -4:
            u1.clearSelf()
            u1.log("登录失败")
        else:
            u1.log(f"异常{e}")
        exit(-1)

    if isDebug:
        u1.debuglog = u1.log
    u1.wsWithKingdomApp(show=False, useThread=True, isDebug=isDebug)

    methodList = [
        {
            "name": "挖矿模式",
            "func": gathering,
        },
        {
            "name": "打怪模式",
            "func": attackMoster,
        },
        {
            "name": "加团模式",
            "func": autoJoinAllianceBattle,
        },
        {
            "name": "水晶模式",
            "func": crystalOrTreasure,
        },
        {
            "name": "撸号模式",
            "func": autoAttack,
        },
        {
            "name": "清符模式",
            "func": sweepCharacter,
        },
        {
            "name": "查符模式",
            "func": sweepCharacter,
        },
        {
            "name": "全图精灵模式",
            "func": onlyTreasure,
        },
        {
            "name": "全图水晶矿模式",
            "func": onlyCrystal,
        },
        {
            "name": "全图找资源模式",
            "func": searchSomeFields,
        },
        {
            "name": "全图水晶(my)",
            "func": quickCrystalMode,
        },
        {
            "name": "水晶模式(new)",
            "func": crystalOrTreasureNew,
        },
        {
            "name": "在线模式",
            "func": onCall,
        },
        {
            "name": "治疗模式",
            "func": autoHeal,
        },
        {
            "name": "开龙模式",
            "func": autoDragon,
        },
        {
            "name": "修墙模式",
            "func": autoRepair,
        },
    ]

    u1.log("")
    print("可用模式:")
    for i in range(len(methodList)):
        item = methodList[i]
        print(f"{i+1}.{item['name']}")
    mode = input("请输入模式:")
    if mode.isdigit():
        mode = int(mode)
    else:
        u1.log("模式错误")
        exit(-1)

    if mode > len(methodList) or mode < 1:
        u1.log(f"模式错误 {mode}")
        exit(-1)

    # if mode < 4 and u1.email == '<EMAIL>':
    #     u1.tryClaimDaily()
    # u1.questClaimAll()
    # u1.claimEvent()

    func = methodList[mode - 1]["func"]
    if callable(func):
        func(u1)
    else:
        u1.log("不可调用")
        exit(-1)


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        pass
