# /bin/env python3
# -*- coding: UTF-8 -*-

import json
import random
import sys
import time
from tqdm import tqdm

from Api.UserInfo import Enum, UserInfo
from Model.UserError import UserError
from Unit.EthUnit import EthService, modifyWeb3Socks5, use<PERSON><PERSON><PERSON>
from Unit.FileTool import loadS5List
from Unit.Logger import logger
from Unit.MarketUnit import requestRefreshMetaData

itemCodes = [
    [
        10101019,
        10101020,
        10101021,
        Enum.ITEM_CODE_FOOD_50M,
        Enum.ITEM_CODE_FOOD_100M,
    ],  # 粮食
    [
        10101028,
        10101029,
        10101030,
        Enum.ITEM_CODE_LUMBER_50M,
        Enum.ITEM_CODE_LUMBER_100M,
    ],  # 木头
    [
        10101037,
        10101038,
        10101039,
        Enum.ITEM_CODE_STONE_50M,
        Enum.ITEM_CODE_STONE_100M,
    ],  # 石头
    [
        10101046,
        10101047,
        10101048,
        Enum.ITEM_CODE_GOLD_50M,
        Enum.ITEM_CODE_GOLD_100M,
    ],  # 黄金
]


def tryWalletWork(u1: UserInfo, nfts: list, privateKey=None):
    unmints = []
    minteds = [[], [], [], []]
    ethService = None
    for nft in nfts:
        status = nft["status"]

        if status == 4:
            itemCode = nft["itemCode"]
            for i in range(len(itemCodes)):
                if itemCode in itemCodes[i]:
                    minteds[i].append(nft)
                    break
            # minteds.append(nft)
        elif status == 0:
            unmints.append(nft)

    itemNames = ["粮食", "木头", "石头", "黄金"]
    string = " ".join(
        [f"{itemNames[i]}{len(minteds[i])}个" for i in range(len(itemCodes))]
    )
    u1.log(f"可发行数量:{len(unmints)},可使用数量:{string}")
    if privateKey is None:
        inputValue = input(f"请输入公钥:{u1.publicKey}对应的私钥:")
        if len(inputValue) != 66 and len(inputValue) > 0:
            raise UserError("私钥长度不对")
        privateKey = inputValue
    if len(privateKey) > 0:
        ethService = EthService(privateKey)

    inputValue = input("请选者模式(1发行 2使用 3market刷新 4解绑钱包):")
    if inputValue == "1":
        if not ethService.addressIsEqual(u1.publicKey):
            raise UserError(f"私钥不匹配 {ethService.address}")
        for nft in tqdm(unmints):
            nftItemId = nft.get("_id")
            mintCode = nft.get("mintCode")
            txData = u1.mintNft(nftItemId)
            if txData:
                txHash = ethService.mint(json.loads(str(txData)))
                if txHash:
                    # if u1.mintTx(mintCode, txHash):
                    u1.debuglog(f"发行成功:{txHash}")
                        # print(f"发行成功:{txHash}")
            else:
                u1.log("发行失败")
        time.sleep(3)
    elif inputValue == "2":
        if not ethService.addressIsEqual(u1.publicKey):
            raise UserError(f"私钥不匹配 {ethService.address}")
        useItems = []
        inputValue = input("请输入使用的种类(0粮食1木头2石头3黄金4全部):")
        if inputValue.isdigit():
            index = int(inputValue)
            if index > 3:
                for nfts in minteds:
                    [useItems.append(nft) for nft in nfts]
            else:
                nfts = minteds[index]
                [useItems.append(nft) for nft in nfts]

        inputValue = input(f"可用数量:{len(useItems)}请输入使用数量(默认99999):")
        maxCount = 99999
        if inputValue.isdigit():
            maxCount = int(inputValue)

        currentCount = 0
        for nft in tqdm(useItems[:maxCount]):
            currentCount += 1
            if currentCount > maxCount:
                break
            tokenId = nft.get("tokenId")
            txData = u1.useNft(tokenId)
            if txData:
                txHash = ethService.mint(json.loads(str(txData)))
                if txHash:
                    u1.debuglog(f"使用成功:{txHash}")
        time.sleep(3)
    elif inputValue == "3":
        print("开始刷新所有nft")
        tokens = []
        for nfts in minteds:
            [tokens.append(str(nft["tokenId"])) for nft in nfts]
        s5List = loadS5List()
        tasks = []
        for tokenId in tokens:
            tasks.append(requestRefreshMetaData(tokenId, random.choice(s5List)))
            # if len(tokens) > 10:
            #     u1.runTasks(tasks)
            #     tasks.clear()
        u1.runTasks(tasks)
        # u1.runTasks([requestRefreshMetaData(tokenId,random.choice(s5List)) for tokenId in tokens])
        print("刷新完成")
    elif inputValue == "4":
        if u1.unlinkWallet():
            print("解绑成功")
            if len(privateKey) > 0 and u1.tryLinkWallet(privateKey):
                print("绑定成功")
                u1.walletNetworkChange()
                res = u1.nftItemList()
                if res:
                    u1.publicKey = res.get("publicKey")
                    tryWalletWork(u1, res.get("items"), privateKey=privateKey)
        else:
            print("解绑失败")
    else:
        u1.log("错误选项")


def main():
    logger.info("自动发行脚本")
    token = None
    email = None
    pwd = None
    isA2z = False
    if len(sys.argv) > 1:
        if len(sys.argv) > 2:
            email = sys.argv[1]
            pwd = sys.argv[2]
        else:
            token = sys.argv[1]
    if not token and not email:
        inputValue = input("请输入token:")
        token = inputValue
    if not email:
        email = f"{random.randint(1, 9999999)}@token.cn"
    u1 = UserInfo(email, pwd, token=token, socks5=random.choice(loadS5List()))
    u1.isWebDevice = True
    if pwd:
        u1.login()
    else:
        u1.initLog()

    u1.wsWithKingdomApp(isDebug=True)
    inputValue = input("是否使用a2z(y/n):")
    if inputValue == "y":
        isA2z = True
    modifyWeb3Socks5(u1.socks5)
    if isA2z:
        useArenaZ()
        u1.walletNetworkChange("arenaz")
    res = u1.nftItemList()
    if res:
        u1.publicKey = res.get("publicKey")
        tryWalletWork(u1, res.get("items"))
    else:
        if res is False:
            inputValue = input("是否绑定钱包(y/n):")
            if inputValue == "y":
                inputValue = input(f"请输入公钥:{u1.publicKey}对应的私钥:")
                if len(inputValue) != 66:
                    raise UserError("私钥长度不对")
                if u1.tryLinkWallet(inputValue):
                    print("绑定成功")
                    if isA2z:
                        u1.walletNetworkChange("arenaz")
                    res = u1.nftItemList()
                    if res:
                        u1.publicKey = res.get("publicKey")
                        tryWalletWork(u1, res.get("items"), inputValue)
                else:
                    print("绑定失败")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        pass
