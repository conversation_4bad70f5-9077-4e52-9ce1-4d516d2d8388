# /bin/env python3
# -*- coding: UTF-8 -*- 
'''
功能描述：激活账号
编写人：darkedge
编写日期：2021年12月26日
   
'''

from Model.UserError import UserError
from Api.UserInfo import UserInfo
from Model.Account import Account
from Unit.DeviceInfo import randomEmailPrefix
from Unit.WorkThread import WorkThread
import random
import threading

def workCallBack(u1:UserInfo):
    try:
        if u1.login():
            u1.enter()
    except:
        pass

def activeAccount():
    users = []
    sock5List = {}
    with open("./emailAccount.txt") as lines:
        accounts = lines.readlines()
        for account in accounts:
            account = account.strip('\n')
            if len(account):
                user = Account("|||")
                user.email = account
                user.password = "111111"
                users.append(user)

    with open("./socks5.txt") as lines:
        accounts = lines.readlines()
        for account in accounts:
            account = account.strip('\n')
            if len(account):
                user = Account(account)
                sock5List[user.s5] = user.s5

    s5List = []
    for s5 in sock5List:
        s5List.append(s5)

    threads = []
    
    threadPool = threading.BoundedSemaphore(50)
    for i,v in enumerate(users):
        v.s5 = random.choice(s5List)
        user = v.returnUserInfo()
        if i == 0:
            try:
                if user.login():
                    user.enter()
                    continue
            except UserError as e:
                if e.message == UserError.needEmailAuth():
                    user.log("大昌哥没开激活!")
                    return
                else:
                    user.log(e.message)
                    return

        thread = WorkThread(user,workCallBack,threadPool)
        thread.setDaemon(True)
        thread.start()
        threads.append(thread)

    for t in threads:
        t.join()
    
    print("结束")


if __name__ == '__main__':
    activeAccount()