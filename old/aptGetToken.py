
from Api.UserInfo import UserInfo
from Model.Account import Account
import asyncio
import time,random,json
from time import sleep
from Unit.Logger import logger
from Unit.FileTool import loadS5List,writeConfig,loadConfig
from Api.Ipinfo import getIP
from Api.User.WebSocket import fieldNames
import threading

from selenium import webdriver
from selenium.webdriver.common.desired_capabilities import DesiredCapabilities
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import WebDriverWait

import re
from Unit.ChromeExtension import easy_create_proxyauth_extension
import sys,os

os.makedirs("tmpSocks",exist_ok=True)

lock = threading.BoundedSemaphore(5)
fileLock = threading.Lock()
googleAccountPath = './tmpaccount/google.txt'
class Google:

    def fromAptStartGoogle(self):
        self.driver.get("https://aptoslabs.com/users/sign_in")
        if self.tryFind("//form"):
            forms = self.driver.find_elements(by=By.XPATH,value="//form[@method='post']")
            for f in forms:
                if f.text == 'GOOGLE':
                    f.click()
                    break
        else:
            pass

    def __init__(self, username, password,proxy=None):
        lock.acquire()
        logger.info(f'{username} 开始登录 {proxy is not None and proxy or "无代理"}')
        try:
            co = webdriver.ChromeOptions()
            # co.add_argument('--headless')
            if proxy:
                proxyauth_plugin_path = easy_create_proxyauth_extension(proxy)
                co.add_extension(proxyauth_plugin_path)
            # else:
            #     co.add_argument('--headless')
            sv = Service('./chromedriver')
            self.driver = webdriver.Chrome(options=co,service=sv)
            self.driver.implicitly_wait(15)

            # self.driver.get('https://stackoverflow.com/users/login?ssrc=head&returnurl=https%3a%2f%2fstackoverflow.com%2f')
            leagueUrl = f"https://accounts.google.com/o/oauth2/auth/oauthchooseaccount?redirect_uri=storagerelay%3A%2F%2Fhttps%2Fplay.leagueofkingdoms.com%3Fid%3Dauth{random.randint(********, *********)}&response_type=permission%20id_token&scope=email%20profile%20openid&openid.realm&include_granted_scopes=true&client_id=************-1j7bt4qbkkniu3e4d6a5muqv4668p4la.apps.googleusercontent.com&ss_domain=https%3A%2F%2Fplay.leagueofkingdoms.com&prompt=select_account&fetch_basic_profile=true&gsiwebsdk=2&flowName=GeneralOAuthFlow"
            aptosUrl =  f'https://accounts.google.com/o/oauth2/auth/oauthchooseaccount?access_type=offline&client_id=************-v5uvr7em9iip9drthngl8bt21jbobif2.apps.googleusercontent.com&redirect_uri=https%3A%2F%2Faptoslabs.com%2Fusers%2Fauth%2Fgoogle%2Fcallback&response_type=code&scope=email%20profile&state=dae519ae300ae1130835cdd0f852a1cabd84f7afac018833&flowName=GeneralOAuthFlow'
            self.fromAptStartGoogle()
            # self.driver.get(aptosUrl)
            # sleep(3)
            # self.driver.find_element_by_xpath('//*[@id="openid-buttons"]/button[1]').click()
            t = 0

            if self.tryFind('//input[@type="email"]'):
                self.driver.find_element(by=By.XPATH,value='//input[@type="email"]').send_keys(username)
                self.driver.find_element(by=By.XPATH,value='//*[@id="identifierNext"]').click()
            else:
                pass

            # time.sleep(3)

            if self.tryFind('//input[@type="password"]'):
                # time.sleep(2)
                self.driver.find_element(by=By.XPATH,value='//input[@type="password"]').send_keys(password)
                self.driver.find_element(by=By.XPATH,value='//*[@id="passwordNext"]').click()
            else:
                pass

            # time.sleep(3)
            while self.driver.current_url[:40] != "https://accounts.google.com/signin/oauth":
                sleep(1)
                print("等待重定向")
                if self.driver.current_url[:37] == "https://accounts.google.com/speedbump":
                    try:
                        self.driver.find_element(by=By.XPATH,value='//input[@type="submit"]').click()
                        # sleep(5)
                    except:
                        pass
                elif self.driver.current_url == "https://aptoslabs.com/onboarding/email":
                    self.driver.get("https://aptoslabs.com/foundation-faucet")
                    break
            
            response = None
            while response is None:
                # time.sleep(2)
                zWl5kd = self.driver.find_element(by=By.CLASS_NAME,value="zWl5kd")
                if zWl5kd:
                    response = zWl5kd.get_attribute("data-credential-response")
                    location = re.search("ey.+", response).span()
                    if location:
                        tokenString = response[location[0]:]
                        token = tokenString.split('\\')[0]
                        logger.debug(token)
                        self.driver.quit()
                        self.appendFile('tmpaccount/succeed.txt', f'{username}----{password}----{token}\n')
                        return
            logger.error("error?")
        except Exception as e:
            logger.error(e, exc_info=True)
            self.appendFile('tmpaccount/fail.txt', f'{username}----{password}----\n')
        finally:
            lock.release()

    def tryFind(self,xpath,reply=0):
        WebDriverWait(self.driver, 15).until(EC.element_to_be_clickable((By.XPATH, xpath)))
        return True
        if reply > 50:
            return False
        try:
            self.driver.find_element(by=By.XPATH,value=xpath)
            return True
        except:
            time.sleep(1)
            return self.tryFind(xpath,reply+1)

    def appendFile(self,filepath,text):
        fileLock.acquire()
        with open(filepath,'a+') as f:
            f.write(text)
        fileLock.release()

def main():
    print(f"读取文件 {googleAccountPath} ")
    useProxy = False
    threadNum = 5
    intervalValue = 1
    inputValue = input("请输入线程数(默认5)：")
    if inputValue.isdigit():
        threadNum = int(inputValue)
        global lock
        lock = threading.BoundedSemaphore(threadNum)

    inputValue = input("是否使用代理(y/n)：")
    if inputValue == 'y':
        useProxy = True
    
    inputValue = input("请输入间隔时间(默认1)：")
    if inputValue.isdigit():
        intervalValue = int(inputValue)

    onceCount = threadNum 
    accounts = []
    threads = []
    while True:
        accounts = loadConfig(googleAccountPath)
        if len(accounts) == 0:
            logger.info(f"没有账号 请导入")
            time.sleep(30)
            continue
        else:
            if len(accounts) > onceCount:
                writeConfig(accounts[onceCount:],googleAccountPath)
                accounts = accounts[:onceCount]
            else:
                writeConfig([],googleAccountPath)
        
        for account in accounts:
            socks5 = None
            if useProxy:
                socks5 = random.choice(loadS5List())
            t = threading.Thread(target=Google, args=(account.email, account.password,socks5, ), daemon=True)
            threads.append(t)
            t.start()
        for t in threads:
            t.join()
        
        if intervalValue > 1:
            print(f"等待间隔{intervalValue}秒")
            time.sleep(intervalValue)


if __name__ == '__main__':
    main()