# /bin/env python3
# -*- coding: UTF-8 -*-
'''
功能描述：自动捐款
编写人：darkedge
编写日期：2022年07月05日
   
'''


from Api.UserInfo import UserInfo
from Api.DataRecord import dataDisplay
import time
import random
import threading
import json
from Unit.FileTool import loadS5List
from Unit.Logger import logger
from Unit.Redis import redisHelper,tzDate
from concurrent.futures import ThreadPoolExecutor
import asyncio
import requests
import sys,os
import fire
import datetime,pytz

isDebug = True if sys.gettrace() else False
sleepTime = 20
allianceLock = threading.Lock()

def endWithMsg(u1:UserInfo,msg):
    u1.invalidStatu = True
    u1.wsWithKingdomClose()
    u1.randomSleep(5,10,msg=msg)

def run(u1:UserInfo,allianceId:str):
    asyncio.set_event_loop(asyncio.new_event_loop())

    if u1.login():
        u1.wsWithKingdomApp()
        u1.randomSleep(2,5)

        if not u1.isBug:
            if u1.allianceId != allianceId:
                if u1.allianceId:
                    u1.allianceLeave()
                    u1.randomSleep(3,10,msg='退盟休息')
                u1.randomSleep(10,600,msg='加盟前随机等待')
                if not allianceLock.acquire(timeout=1):
                    u1.debuglog('没有抢到锁')
                    return
                u1.debuglog('抢到进盟锁')
                if not u1.tryAllianceJoin(allianceId):
                    u1.errorLog("加入联盟失败 停止")
                u1.randomSleep(10,60)
                u1.tryHarvestAll()
                endWithMsg(u1, '联盟结束')
                return
        
        if not u1.isBug:
            if u1.allianceId != allianceId:
                endWithMsg(u1, '异常进入异常逻辑')
                return
            # 触发bug
            for i in range(2):
                if u1.isBug:
                    break
                u1.runTasks([u1.bugTrainTroopA(50100101)])
                u1.randomSleep(3,5)
                u1.runTasks([u1.bugTrainTroopA(50100301)])
                u1.randomSleep(3,5)
                u1.runTasks([u1.bugTrainTroopA(50100201)])
                u1.randomSleep(3,5)
            endWithMsg(u1, '触发bug 结束')
            return

        if u1.allianceInfoMy():
            levels = [15,20,25,99]
            for level in levels:
                if not u1.tryAllianceDonateAll(maxLevel=level):
                    u1.randomSleep(2,3,msg=f'满{level}级捐款失败')
                else:
                    break
        else:
            endWithMsg(u1, '没有联盟信息？')
            return
        endWithMsg(u1, '正常结束')

def main(allianceId='61a4ce5947e6e30c5db4ee28',path='donate_20f.txt'):
    if len(allianceId) == 0:
        allianceId = input("请输入联盟id:")
    
    users = []
    with open(f'./tmpaccount/{path}', 'r') as f:
        for line in f.readlines():
            line = line.strip()
            if not line:
                continue
            user = UserInfo(userKey=line,socks5=random.choice(loadS5List()),saveSocks5=True)
            user.isWebDevice = True
            if not redisHelper.getBan(user.key):
                users.append(user)

    logger.info(f'当前有效账号:{len(users)}')
    maxCount = 20
    if len(users) > maxCount:
        users = random.sample(users, maxCount)
    # users = users[80:]
    pool = ThreadPoolExecutor(max_workers=random.randint(10, 20),thread_name_prefix='DonateTrhead')
    for user in users:
        pool.submit(run,user,allianceId)
    pool.shutdown(wait=True)

    print("任务完成")


if __name__ == '__main__':
    try:
       fire.Fire(main)
    except KeyboardInterrupt:
        pass


