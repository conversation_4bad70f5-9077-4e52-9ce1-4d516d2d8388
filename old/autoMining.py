# /bin/env python3
# -*- coding: UTF-8 -*- 
'''
功能描述：无设置挂机
编写人：darkedge
编写日期：2021年12月25日
   
'''

from mining import crystalOrTreasure,autoJoinAllianceBattle,crystalOrTreasureNewHightAuto
import json
import time,random,threading
from urllib.parse import quote
import functools
from Model.Kingdom import Kingdom
from Model.UserError import UserError

from Api.UserInfo import UserInfo
from Api.User.WebSocket import fieldNames,charmCodes
from Unit.FileTool import loadS5List
from Unit.Logger import logger
from Unit.Redis import redisHelper
from Unit.WorkThread import WorkThread
import asyncio
import requests
import sys

sendList = {}

s5List = loadS5List()
monsters = [20200101,20200102,20200103]
farmers = [20100101,20100102,20100103,20100104]

def main():
    u1 = UserInfo("<EMAIL>",
                "qqqqqqq",
                socks5=random.choice(s5List),
                )
    if len(sys.argv) > 3:
        u1 = UserInfo(sys.argv[2],sys.argv[3],socks5=random.choice(s5List))
        u1.log("邮箱模式")
    elif len(sys.argv) > 2:
        u = sys.argv[2]
        if len(u) == 36:
            u1 = UserInfo(userKey=u,socks5=random.choice(s5List))
            u1.log("游客模式模式")
        else:
            u1 = UserInfo(f'somebody{random.randint(1, 9999)}@any.com',token=sys.argv[2],socks5=random.choice(s5List))
            u1.log(f"token模式{sys.argv[2]}")
    else:
        u1.log("darkedge专属模式")

    u1.isWebDevice = True
    u1.saveSocks5 = True

    try:
        if not u1.pwd and not u1.userKey:
            u1.loadEmailWithToken()
        if u1.isLogin is False:
            if not u1.login():
                u1.log("进入失败")
                return
        else:
            if u1.enter() is False:
                u1.log("登录失败")
                return
    except UserError as e:
        if e.errorCode == -4:
            u1.clearSelf()
            u1.log("登录失败")
        else:
            u1.log(f"异常{e}")
        exit(-1)
    
    u1.wsWithKingdomApp(show=False,useThread=True,isDebug=True)
    u1.tryClaimVip()

    mode = 3
    if len(sys.argv) > 1:
        mode = int(sys.argv[1])

    if mode == 1:
        u1.log("水晶模式")
        crystalOrTreasure(u1)
    elif mode == 2:
        u1.log("跟团模式")
        autoJoinAllianceBattle(u1,troopNum=50000,auto=True)
    elif mode == 3:
        u1.log("水晶加速多倍模式")
        crystalOrTreasureNewHightAuto(u1)

    
if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        pass

    