# /bin/env python3
# -*- coding: UTF-8 -*-
'''
功能描述：游客龙bugap 练号
编写人：darkedge
编写日期：2023年04月10日
   
'''

import json
import sys
import time
import random
import math
from Model.Kingdom import Kingdom
from Model.Account import Account
from Model.UserError import UserError
from Api.UserInfo import UserInfo, buildLevelSleepTimes, buildMainLevelSleepTimes, buildFrameLevelSleepTimes, Enum
import threading
from Unit.EthUnit import changeGasPrice
from Unit.Logger import logger
from Unit.FileTool import writeConfig, loadS5List
from Unit.Redis import redisHelper, DAYNUM, todayUTCTimestamp
from Unit.DeviceInfo import createUserKey, createName
from Unit.WorkThread import WorkThread
from bugdst import BugDstWork
from Api.ProxyApi import changeKeyTo2
from guestRegister import registerWithDynamicProxy
import asyncio
import fire
from typing import List
from bugAp import ApWork

isDebug = True if sys.gettrace() else False


def main(dragoKeyPath, maxNum=20):
    realDragoKeyPath = f'./tmpaccount/{dragoKeyPath}'
    try:
        with open(realDragoKeyPath, 'r') as f:
            lines = f.readlines()
            if len(lines) == 0:
                raise FileExistsError()
    except FileNotFoundError as e:
        logger.error("没有找到龙私钥")
        return

    ApWork("",realDragoKeyPath, 1).training(maxNum)

if __name__ == "__main__":
    changeKeyTo2()
    changeGasPrice(400, 300)
    try:
        if isDebug:
            main("t2.txt")
        else:
            fire.Fire(main)
    except KeyboardInterrupt:
        pass
    except Exception as e:
        logger.error(e, exc_info=True)