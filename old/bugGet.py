# /bin/env python3
# -*- coding: UTF-8 -*- 

import json
import time,random
from Model.Kingdom import Kingdom
from Model.Account import Account
from Model.UserError import UserError
from Api.UserInfo import UserInfo
import threading
from Unit.Logger import logger
from Unit.FileTool import loadConfig,writeConfig
from Unit.Redis import redisHelper
from Api.ProxyApi import allocateIP,whiteAdd
import asyncio

threadPool = threading.BoundedSemaphore(4)

def bugGet(u1:UserInfo):
    u1.mailClaim(u1.mailId)

def bugBuy(u1:UserInfo):
    u1.vipshopBuy(********, 20)

def writeLocal(u1:UserInfo):
    with open("./bfaccount.txt","a+") as f:
        f.write("%s|%s|%s\n" % (u1.email,u1.pwd,u1.socks5))

class bWorkThread(threading.Thread):
    def __init__(self, userInfo):
        threading.Thread.__init__(self)
        self.userInfo = userInfo
        self.name = userInfo.email or userInfo.userKey

    def run(self):
        loop = asyncio.new_event_loop()
        self.event_loop = loop
        asyncio.set_event_loop(loop)
        threadPool.acquire()
        retry = 0
        u1 = self.userInfo
        logger.info("开启线程： " + self.name)

        try:
            if u1.login():

                u1.wsWithKingdomApp()
                u1.tryClaimEvents()
                # if u1.level > 10:
                #     u1.tryRoulette()
                u1.checkDragon()


                # if u1.vip >= 2:
                #     res = u1.vipshopList()
                #     vipshop = res.get("vipShop")
                #     items = vipshop.get("items")
                #     for item in items:
                #         code = item.get("code")
                #         numRemain = item.get("numRemain")
                #         if code == ********:
                #             if numRemain != 20:
                #                 u1.log("没有金币可以刷")
                #                 return
                #             break
                #     num = int(u1.crystal / 280)
                #     if num > 0:
                #         u1.log("去刷金币 %d" % num)
                #         runWithConcurrent(u1,bugBuy,countMin=num,countMax=num+5)
            


        
        except Exception as e:
            print(e)
        
        finally:
            if u1.bugCount > 0:
                writeLocal(u1)
                u1.log("成功%d次" % u1.bugCount)
                if u1.bugCount > 2 or u1.sumResources > 30*1000*1000:
                    u1.log("请重点关注我 %d,%s" %(u1.bugCount,u1.resources))
            threadPool.release()
            
def main():
    accounts = loadConfig('./bugaccount.txt')
    
    if len(accounts):
        logger.info("一共%d个账号" % len(accounts))
        threads = []
        for account in accounts:
            thread = bWorkThread(account.returnUserInfo(isMain=False,saveSocks5=False))
            thread.setDaemon(True)
            thread.start()
            threads.append(thread)
        
        for t in threads:
            t.join()
        logger.info ("任务结束")


if __name__ == '__main__':
    logger.info("启动脚本")
    import sys
    main()