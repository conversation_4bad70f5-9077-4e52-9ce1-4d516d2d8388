# /bin/env python3
# -*- coding: UTF-8 -*- 

'''
功能描述：bugMint模块
编写人：darkedge
编写日期：2022年03月24日

'''

from Api.UserInfo import UserInfo
from Model.Account import Account
from Model.UserError import UserError
from Unit.FileTool import loadConfig,writeConfig,loadS5List
from Unit.Logger import logger
from Unit.EthUnit import EthService,modifyWeb3Socks5

import random,time
import sys,os
import threading
from tqdm import tqdm
from Api.ProxyApi import allocateIP,changeKeyTo1,changeKeyTo2,createRealSocks5
from Api.Ipinfo import getIP

isDebug = True if sys.gettrace() else False
onceCount = 3
mintLock = threading.Lock()
failAccountPath = f'./tmpaccount/failMint.txt'
mintedAccountPath = f'./tmpaccount/minted.txt'
succeedCount = 0
failCount = 0
def addSucceedCount():
    global succeedCount
    succeedCount += 1

def addFailCount():
    global failCount
    failCount += 1

def writeFailAccount(user:UserInfo):
    addFailCount()
    with open(failAccountPath,'a+') as f:
        f.write(f'{user.email}|{user.pwd}|{user.token}\n')

def writeMintedAccount(user:UserInfo):
    with open(mintedAccountPath,'a+') as f:
        f.write(f'{user.email}|{user.pwd}|{user.token}\n')

def returnS5(retry=0,useLocal=False):
    if retry > 5 or useLocal:
        logger.info("使用自己的代理")
        return random.choice(loadS5List())
    s5 = allocateIP()
    logger.info("获取到代理:%s" % s5)
    if s5 is None:
        logger.error("代理异常")
        time.sleep(10)
        return returnS5(retry+1)
    else:
        if getIP(createRealSocks5(s5)) is None:
            logger.error(f"获取代理异常到的: {s5}")
            time.sleep(3)
            return returnS5(retry+1)
        
    return createRealSocks5(s5)

def workThreadFunc(user:UserInfo,packageType,privateKey,autoDel):
    mintSucceed = False
    try:
        if user.login():
            user.wsWithKingdomApp(isDebug=True)
            user.randomSleep(5,msg="等待ws连接")

            if user.tryLinkWallet(privateKey):
                user.log("绑定钱包成功")
                pass
            else:
                logger.info("链接钱包失败,请检查")
                return accounts.index(account)
            for i in range(2):
                if user.isBug:
                    break
                user.runTasks([user.bugTrainTroopA(********),user.bugTrainTroopA(********,delay=3),user.bugTrainTroopA(********,delay=6)])

            mintType = packageType
            if mintType == 4:
                mintType = random.randint(0,3)
            
            mintLock.acquire()
            try:
                mintSucceed = user.mint(mintType,2)
            except:
                pass
            finally:
                if mintSucceed:
                    user.randomSleep(3,5,msg="发行延时")
                mintLock.release()

            user.unlinkWallet()
            if not mintSucceed:
                logger.info("发行失败")
                user.errorLog(f'{user.email}|{user.pwd}|{user.token}')
            elif autoDel:
                if user.deleteAccount():
                    logger.info("删除账号成功")
                else:
                    logger.info("删除账号失败")
            else:
                writeMintedAccount(user)
            # else:
            #     user.randomSleep(3,msg="等待结束")

    except UserError as e:
        logger.info(e)
        if e.errorCode == 31:
            mintSucceed = True
    except Exception as e:
        logger.error(e)
    finally:
        if not mintSucceed:
            writeFailAccount(user)
        else:
            addSucceedCount()

def work(accounts,packageType,privateKey,autoDel,useLocal):
    threads = []
    for account in accounts:
        try:
            s5 = returnS5(useLocal=useLocal)
            account.s5 = s5
            user = account.returnUserInfo(saveSocks5=False)
            if user.token or user.googleToken:
                user.isGoogle = True
            th = threading.Thread(target=workThreadFunc,args=(user,packageType,privateKey,autoDel,))
            th.setDaemon(True)
            th.start()
            threads.append(th)
        except Exception as e:
            logger.error(e)

    
    for th in tqdm(threads):
        th.join()
    
def main():
    s5List = loadS5List()
    socks5 = random.choice(s5List)
    ip = getIP(socks5)
    print(f'钱包使用代理: {socks5},ip: {ip}')
    modifyWeb3Socks5(socks5)
    inputValue = input("使用2号key(y/n)?")
    if inputValue.lower() == 'y':
        changeKeyTo2()
    else:
        changeKeyTo1()
    accounts = []
    # accounts = loadConfig("bugaccount.txt")
    # logger.info(f'读取{len(accounts)}个账号')

    packageType = 3
    privateKey = '0x464b566d516b1274fb3b5dfa3ac88ba8642e2920a63083fa26ba0553a9936c93'
    autoDel = False
    useLocal = False
    useGlobalPath = False

    if True:
        inputValue = input(f"请输入需要打包的类型(0玉米1木头2石头3黄金4随机):")
        if inputValue.isdigit():
            packageType = int(inputValue)
            if packageType > 4 or packageType < 0:
                logger.info("异常的类型")
                exit(0)
        inputValue = input("请输入私钥:")
        if len(inputValue) != 66:
            logger.info("私钥长度不对")
            exit(0)
        privateKey = inputValue
        
        inputValue = input("是否自动删除(y/n):")
        if inputValue.lower() == 'y':
            autoDel = True

        inputValue = input("是否使用自己的代理(y/n):")
        if inputValue.lower() == 'y':
            useLocal = True

        inputValue = input("是否使用全局路径(y/n):")
        if inputValue.lower() == 'y':
            useGlobalPath = True

    es = EthService(privateKey)
    publicKey = es.address

    loadPath = f'./tmpaccount/{publicKey}.txt'
    if useGlobalPath:
        loadPath = f'./tmpaccount/gtoken.txt'
        
    if not os.path.exists(loadPath):
        writeConfig([],loadPath)
    
    logger.info(f"读取 {loadPath} 下的账号")
    while True:
        tipStr = f'累计成功{succeedCount}个,累计失败{failCount}个'
        if len(accounts) == 0:
            accounts = loadConfig(loadPath)
            if len(accounts) == 0:
                logger.info(f"没有账号 请导入 {tipStr}")
                time.sleep(30)
            else:
                if len(accounts) > onceCount:
                    writeConfig(accounts[onceCount:],loadPath)
                    accounts = accounts[:onceCount]
                else:
                    writeConfig([],loadPath)
                
                logger.info(f"读取 {len(accounts)} 个账号 {tipStr}")

        if len(accounts) > 0:
            index = work(accounts, packageType, privateKey, autoDel,useLocal)
            if index:
                accounts = accounts[index:]
                inputValue = input("异常中断是否继续(y/n):")
                if inputValue.lower() == 'y':
                    continue
                else:
                    exit(0)
            else:
                accounts.clear()

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        pass
    except Exception as e:
        logger.error(e)
