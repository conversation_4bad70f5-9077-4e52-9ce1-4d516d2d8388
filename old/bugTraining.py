# /bin/env python3
# -*- coding: UTF-8 -*- 
'''
功能描述：游客龙bug练级
编写人：darkedge
编写日期：2023年04月01日
   
'''

import json,sys
import time,random,math
from Model.Kingdom import Kingdom
from Model.Account import Account
from Model.UserError import UserError
from Api.UserInfo import UserInfo,buildLevelSleepTimes,buildMainLevelSleepTimes,buildFrameLevelSleepTimes
import threading
from Unit.Logger import logger
from Unit.FileTool import writeConfig,loadS5List
from Unit.Redis import redisHelper
from Unit.DeviceInfo import createUserKey,createName
from Unit.WorkThread import WorkThread
import asyncio
import fire
from typing import List

isDebug = True if sys.gettrace() else False
threadsLock = None
waitThreadsLock = None
loggerLock = threading.BoundedSemaphore(1)
loggerNamePrefix = ""
loggerMax = 0
loggerCurrent = 0
quickMode = False
bigMode = False
clearMode = True
changeWorldMode = True
clearIndex = None
allianceId = None
halfNum = 5

def setQuickModeEnable():
    global quickMode
    quickMode = True
    logger.info("快速模式开启")

def setBigModeEnable():
    global bigMode
    bigMode = True
    logger.info("11级模式开启")

def setClearModeDisEnable():
    global clearMode
    clearMode = False
    logger.info("清除模式关闭")

def setChangeWorldModeDisEnable():
    global changeWorldMode
    changeWorldMode = False
    logger.info("切换世界模式关闭")

def setLoggerName():
    loggerLock.acquire()
    global loggerCurrent
    loggerCurrent += 1
    logger.name = f'{loggerNamePrefix}-{loggerCurrent}/{loggerMax}'
    loggerLock.release()
    pass

def setLoggerCurrentPop():
    loggerLock.acquire()
    global loggerCurrent
    loggerCurrent -= 1
    loggerLock.release()

def setAllianceId(tryAllianceId):
    global allianceId
    allianceId = tryAllianceId
    logger.info(f"设置加入的联盟id为{allianceId}")

def vipshopBuy(u1:UserInfo):
    u1.vipshopBuy(10101044, u1.buyNum)

class WorkdThread(threading.Thread):
    def __init__(self,user:UserInfo,name:str):
        super().__init__(daemon=True)
        self.user = user
        self.name = "%s_%s" %(name, user.email or user.userKey)
        self.needSleep = False
        self.sleepTime = 0
        self.canStop = False
        self.retry = 0
        self.event_loop = None
    
    def run(self):
        super().run()
        loop = asyncio.new_event_loop()
        self.event_loop = loop
        asyncio.set_event_loop(loop)

    def sleep(self,secs):
        self.user.debuglog("休息%d秒" %secs)
        time.sleep(secs)

    def checkSleep(self,canStop=True):
        # canStop = canStop and self.canStop
        if self.needSleep and not self.canStop:
            if self.sleepTime > 10:
                time.sleep(10)
                self.sleepTime -= 10
            else:
                time.sleep(1)
                self.sleepTime -= 1
            if self.sleepTime <= 0:
                self.needSleep = False
                self.sleepTime = 0
            return True
        return False
        
    
class claimQuestThread(WorkdThread):
    def __init__(self,user:UserInfo):
        super().__init__(user,"claim")
        self.harvestCount = 90
        self.harvestRandomCount = random.randint(120, 200)
        self.noQuestCount = 0

    def run(self):
        super().run()
        u1 = self.user
        while not u1.isInvalid:
            self.harvestCount += 1
            # if self.harvestCount % self.harvestRandomCount == 0:
            #     self.sleep(2)
            #     u1.tryHarvestAll()
            #     self.sleep(3)
            #     self.sleepTime -= 5

            if self.checkSleep():
                continue
            self.needSleep = True

            try:
                hasGet = u1.questClaimAllOnce()
                if u1.dailyPoint < 100 or u1.level != 10:
                    u1.tryClaimDaily(getLevel5=False)
                if u1.allianceId:
                    u1.tryHelpAll()
                if hasGet:
                    self.sleepTime = 30
                else:
                    self.sleepTime = 60
                needUse = False
                for resource in u1.resources:
                    if resource < 1 * 1000 * 1000:
                        needUse = True
                        break
                if needUse:
                    u1.tryUseItems(useTroop=True)

                if u1.level >= 10:
                    self.sleepTime *= 5
        
                # if u1.tryClaimQuest():
                #     u1.tryUseItems(useTroop=True)
                #     self.sleepTime = 30
                #     self.noQuestCount = 0
                # else:
                #     self.sleepTime = 60
                #     self.noQuestCount += 1
                #     if self.noQuestCount > 5:
                #         u1.tryAddClaimQuest()
                #         if u1.checkWSWithKingdomApp():
                #             u1.wsWithKingdomApp(isDebug=isDebug)
                #         self.noQuestCount = 0

                if u1.level >= 10:
                    self.sleepTime *= 3
                
            except UserError as e:
                if e.errorCode < 0:
                    break
                else:
                    u1.errorLog(e,exc_info=True)
            except UserError as e:
                u1.errorLog(e,exc_info=True)

            if self.canStop:
                u1.log("领取主线结束")
                break

class jungleThread(WorkdThread):
    def __init__(self, user:UserInfo):
        super().__init__(user, "jungle")
        self.attackMonster = user.killMonster
        self.lastT = time.time()

    def run(self):
        super().run()
        u1 = self.user
        while not u1.isInvalid:
            try:
                if self.checkSleep(canStop=False):
                    continue
                self.needSleep = True

                if u1.checkWSWithKingdomApp():
                    u1.enter()
                    u1.wsWithKingdomApp(isDebug=isDebug)

                if u1.trainingNum > 0 and len(u1.fieldTasks) < u1.marchLimit:
                    if u1.numTroops > 3000 and self.attackMonster < 13 and u1.actionPoint > 10:
                        if u1.tryAttackMonster(maxLevel=1,maxDistance=200):
                            self.attackMonster += 1
                            # u1.troopRecover()
                    
                    u1.tryGathering(maxDistance=300,maxLevel=5)

                    self.sleepTime = 60
                    # u1.troopRecover()
                else:
                    self.sleepTime = 30
                
                
                # nowt = time.time()
                # if nowt - self.lastT > 60 * 3:
                #     t = redisHelper.getProcessTime(u1.key)
                #     processTime = nowt - self.lastT + t
                #     redisHelper.setProcessTime(u1.key, processTime)
                #     self.lastT = nowt
                
                if self.canStop:
                    u1.log("打野结束")
                    break
            except UserError as e:
                if e.errorCode < 0:
                    break
                else:
                    u1.errorLog(e,exc_info=True)
            except Exception as e:
                u1.errorLog(e,exc_info=True)

class researchThread(WorkdThread):
    def __init__(self,user:UserInfo):
        super().__init__(user,"research")

    def checkResearch(self):
        u1 = self.user
        kingdomTasks = u1.kingdomTasks()
        if kingdomTasks is None:
            self.sleep(5)
            return False

        for task in kingdomTasks:
            code = task.get("code")
            if code == 6:
                status = task.get("status")
                if status == 3:
                    if u1.claim(5,training=True):
                        # u1.log("研究完成")
                        time.sleep(3)
                    
                elif status == 1:
                    # 训练中
                    return False

                break
        return True

    def run(self):
        super().run()
        u1 = self.user
        researches = [30102001,30102002,30102003,30101001,30101002,30101003]
        while not u1.isInvalid:
            if self.retry > 5:
                self.retry = 0
            if self.checkSleep(canStop=False):
                continue

            self.needSleep = True
            try:
                
                if self.checkResearch() is False:
                    self.sleepTime = 30
                    continue
                
                u1.researchList()
                
                # 研究6个1级
                for research in researches:
                    researchInfo = u1.getResearchesInfo(research)
                    if researchInfo is None:
                        if u1.research(research):
                            self.retry = 0
                            # u1.log("开始研究%s" % research)
                            if research < 30102001:
                                self.sleepTime = 70
                            else:
                                self.sleepTime = 5
                            break
                        else:
                            # u1.debuglog("研究失败 延时10s")
                            self.sleepTime = 10
                            self.retry += 1
                        
                if self.sleepTime > 0:
                    continue

                # 研究4个2级
                for research in researches[:4]:
                    researchInfo = u1.getResearchesInfo(research)
                    if researchInfo and researchInfo.get("level") == 1:
                        if u1.research(research):
                            self.retry = 0
                            # u1.log("开始研究%s" % research)
                            if research < 30102001:
                                self.sleepTime = 120
                            else:
                                self.sleepTime = 95
                            break
                        else:
                            # u1.debuglog("研究失败 延时10s")
                            self.sleepTime = 10
                            self.retry += 1
            except UserError as e:
                if e.errorCode < 0:
                    break
                else:
                    u1.errorLog(e,exc_info=True)
            except Exception as e:
                u1.errorLog(e,exc_info=True)
            
            if self.sleepTime > 0:
                continue
            
            u1.log("10级研究完成")
            break

class trainingThread(WorkdThread):
    def __init__(self,user:UserInfo,descZone:int=21,levelMax:int=3):
        super().__init__(user,"training")
        self.buildList = [1,104,105,106,107,108,4,5,9,2,7,6,8,3]
        self.childThreads = []
        self.descZone = descZone
        self.levelMax = levelMax
        self.troopTrainTime = 0
        self.startTime = 0
        self.hasRelease = False
        self.hasReleaseWait = False

    def removeUserInfo(self):
        u1 = self.user
        u1.invalidStatu = True
        if u1.userKey:
            redisHelper.removeUserKeyWithAll(u1.userKey)
        elif u1.email:
            redisHelper.removeEmailKeyWithAll(u1.email, u1.pwd)
        
    def run(self):
        super().run()
        waitThreadsLock.acquire()
        threadsLock.acquire()
        self.startWork()
    
    def startWork(self):
        try:
            needEnd = True
            setLoggerName()
            self.startTime = time.time()
            u1 = self.user
            u1.randomSleep(0,10)
            if u1.login():
                u1.wsWithKingdomApp(isDebug=isDebug)
                u1.randomSleep(5,10)
                if u1.sumResources == 1.2 * 1000 * 1000:
                    u1.deleteAccount()
                    u1.log("过期了")
                    self.removeUserInfo()
                    return
                elif u1.level > 11:
                    u1.log("等级大于11")
                    self.checkOverInfo()
                    return

                self.user.tryHoldBuild()
                if u1.level <= 5:
                    u1.placeHolderStr = "阶段1"
                    if not bigMode and changeWorldMode:
                        u1.changeWorld(self.descZone,allianceId=allianceId)
                    u1.newAccountEvent()
                    self.user.build1()
                    u1.randomSleep(2,4)
                    
                    self.user.levelBuild(2)
                    self.createChildThread()
                    u1.tryClaimVip()
                    u1.getGoldFree()
                    
                    u1.tryChangeFace()
                    
                    if u1.trainingNum == 0:
                        self.troopTrain(100)
                    u1.placeHolderStr = "阶段3"
                    self.user.levelBuild(3)
                    
                    self.troopTrain(200)
                    self.user.build2()
                    u1.tryJoinAlliance()
                    self.troopTrain(300)

                    u1.placeHolderStr = "阶段4"
                    self.user.levelBuild(4)
                    
                    # u1.tryUseItems(useTroop=True)
                    self.user.log(f"砖石:{self.user.crystal} 活跃:{self.user.dailyPoint} 时间:{time.time() - self.startTime}")
                    u1.placeHolderStr = "阶段5"
                    self.user.levelBuild(5)
                    u1.placeHolderStr = "阶段5.1"
                    self.user.log(f"砖石:{self.user.crystal} 活跃:{self.user.dailyPoint} 时间:{time.time() - self.startTime}")
                    self.sleep(5)
                    u1.log("建筑满5")
                else:
                    if u1.level < 11:
                        self.createChildThread()

                u1.tryChangeFace()
                u1.questDaily()


                    # if u1.dailyPoint < 50 or u1.sumResources > 6 * 1000 * 1000 or u1.level >= 6 or u1.vip >= 2:
                    #     u1.log("跳过日常了")
                    # else:
                    #     overTime = 0
                    #     while u1.dailyPoint < 100 and not u1.isInvalid:
                    #         overTime += 1
                    #         # if overTime % 20 == 0:
                    #         #     u1.tryHarvestAll()
                    #         if overTime > 500:
                    #             u1.errorLog("日常超时了")
                    #             break
                    #         self.sleep(10)
                    #         u1.log(f"等待日常任务完成 {u1.dailyPoint}")
                        
                    #     u1.log("日常满100")
                if u1.dailyPoint > 80:
                    u1.log("日常满80")
                self.hasRelease = True
                threadsLock.release()
                
                if u1.isInvalid:
                    u1.errorLog("token已经异常")
                    raise UserError.noauth()

                u1.printSelf()
                u1.log(f'当前日活:{u1.dailyPoint}')

                self.endChildThread()
                self.checkOverInfo()
        except UserError as e:
            if e.errorCode == 6 or e.errorCode == 31 or e.errorCode == 32 or e.errorCode == 41:
                u1.errorLog("你号没了")
                self.removeUserInfo()
            elif e.message == UserError.needEmailAuth().message or e.message == UserError.mismatchPassword().message:
                u1.log("需要邮箱验证")
                self.removeUserInfo()
            elif e.errorCode == -5:
                #系统限制
                u1.log("登录触发限制登录 等待6-12分钟重新登录")
                u1.clearSelf()
                u1.randomSleep(360,720)
                setLoggerCurrentPop()
                needEnd = False
                return self.startWork()
            elif e.errorCode == -4:
                u1.errorLog("token失效导致结束 尝试重新登录？")
                u1.clearSelf()
                u1.randomSleep(40,60)
                setLoggerCurrentPop()
                needEnd = False
                return self.startWork()
            elif e.errorCode < 0:
                u1.errorLog("主动异常:%s" % e.message)
            else:
                u1.errorLog("异常导致结束",exc_info=True)
        except Exception as e:
            u1.errorLog("异常导致结束",exc_info=True)
        finally:
            if needEnd:
                u1.log("结束了")
                if not self.hasRelease:
                    threadsLock.release()
                if not self.hasReleaseWait:
                    waitThreadsLock.release()

    def troopTrain(self,maxCount):
        u1 = self.user
        if time.time() - self.troopTrainTime < 0:
            # u1.debuglog("练兵中 跳过")
            return

        kingdomTasks = u1.kingdomTasks()
        if kingdomTasks is None:
            self.sleep(5)
            return
        for task in kingdomTasks:
            code = task.get("code")
            if code == 3:
                status = task.get("status")
                if status == 3:
                    if u1.claim(105,training=True):
                        u1.trainingNum += 100
                        # u1.log("收兵100")
                        time.sleep(5)
                elif status == 1:
                    # 训练中
                    return

                break
        
        
        if u1.trainingNum < maxCount:
            if u1.trainTroop(training=True):
                time.sleep(3)
                t = u1.trySpeedUpUseItem(2,useAny=True)
                self.troopTrainTime = time.time() + t + 2


    def buyVipLevel(self):
        u1 = self.user
        u1.tryAllianceDonate()
        u1.vipInfo()
        if u1.vip == 1:
            num = u1.vipPoint > 100 and 1 or 2
            u1.vipshopBuy(10101008, num)
    
    def buyResource(self,clear=False,half=False,retry=0):
        u1 = self.user
        if u1.isInvalid:
            raise UserError.noauth()
        if retry > 5:
            u1.errorLog("重试5次都没有购买成功,我尽力了")
            if clear:
                u1.clearCrystal(u1.crystal,descIndex=clearIndex)
            return

        if u1.checkWSWithKingdomApp():
            u1.enter()
            u1.wsWithKingdomApp(isDebug=isDebug)

        oldCrystal = u1.crystal
        crystal = u1.crystal
        num = int(crystal / 14)
        buys = [10101044,10101035,10101026,10101017]
        buyNumRemains = {}
        # if num > 40:
        #     async def buyGold(u1:UserInfo):
        #         await u1.vipshopBuyA(10101044, 20)
        #     u1.bugCount = 0
        #     tasks = [buyGold(u1) for i in range(2)]
        #     u1.runTasks(tasks)
        #     num -= 20 * u1.bugCount

        #     buys = [10101035,10101026,10101017]
        res = u1.vipshopList()
        if res is None:
            u1.randomSleep(3,5)
            return self.buyResource(clear,half,retry+1)
        vipshop = res.get("vipShop")
        items = vipshop.get("items")
        for item in items:
            code = item.get("code")
            numRemain = item.get("numRemain")
            if code in buys and numRemain and numRemain > 0:
                '''只购买一半'''
                if half:
                    if numRemain > halfNum:
                        numRemain = numRemain - halfNum
                    else:
                        numRemain = 0
                buyNumRemains[code] = numRemain

        for code in buys:
            numRemain = buyNumRemains.get(code)
            if numRemain and numRemain > 0:
                if num >= numRemain:
                    if u1.vipshopBuy(code, numRemain):
                        num -= numRemain
                else:
                    if num > 0:
                        if u1.vipshopBuy(code, num):
                            num = 0
                            break
        
        if clear:
            if u1.worldId != self.descZone:
                u1.errorLog("目标错误的号")
                return
            if u1.sumResources < 16.5 * 1000 * 1000 and not bigMode:
                # 小于18M检测商店购买是否卡了
                vipshop = res.get("vipShop")
                items = vipshop.get("items")
                for item in items:
                    code = item.get("code")
                    numRemain = item.get("numRemain")
                    if numRemain and numRemain > 0 and code in buys:
                        u1.log("没买完 休息再试")
                        # u1.tryUseItems()
                        u1.randomSleep(300,360)
                        return self.buyResource(clear,half,retry+1)
                    
            if num > 0:
                crystal = num * 14
                u1.clearCrystal(num * 14,descIndex=clearIndex)


        self.sleep(3)
        u1.log("买资源完成")
        if not clear:
            u1.tryUseItems()
        u1.log("买资源完成2")
        self.sleep(5)


    def levelToTen(self):
        u1 = self.user
        mustList = [1,106,107,8] # 石材 木材 城墙
        buildList = []
        buildList.append(mustList + [104,105,108,5,9,4]) # 6级 农场 营房 金矿 学院宝库交易所
        buildList.append(mustList + [108,5,9,4]) # 7级 金矿 学院宝库交易所
        buildList.append(mustList + [108,9,4])# 8级 金矿 宝库 交易所
        buildList.append(mustList + [9])# 9级 交易所
        buildList.append([mustList[0]])# 10级城堡
        
        level = 6
        for list1 in buildList:
            self.user.levelBuild(level,list1)
            self.sleep(5)
            level += 1
            u1.placeHolderStr = f"阶段7.{level}"
            u1.printSelf()


    def levelToEleven(self):
        u1 = self.user
        mustList = [104,2] # 农场 仓库升到10级
        u1.placeHolderStr = "阶段11"
        for level in range(6,11):
            self.user.levelBuild(level,mustList)
            self.sleep(5)

        self.user.levelBuild(10,[106,8])
        self.sleep(5)
        self.user.levelBuild(11,[1])



    def powerTo30W(self):
        self.user.build4()
        self.user.placeHolderStr = "阶段8.30W"
        upGradeList = [101,102,103,109,110,111,114,115,116,117,118]
        [self.user.levelBuild(level,upGradeList) for level in range(2,7)]

            
    # 开启子线程
    def createChildThread(self):
        u1 = self.user
        thread1 = claimQuestThread(u1)
        # thread2 = troopBuildThread(u1)
        thread3 = researchThread(u1)
        thread4 = jungleThread(u1)

        self.childThreads.append(thread1)
        # if u1.numTroops < 300:
            # self.childThreads.append(thread2)
        
        if len(u1.researches) < 10:
            self.childThreads.append(thread3)

        self.childThreads.append(thread4)

        for t in self.childThreads:
            # t.setDaemon(True)
            t.start()
        u1.log("开启子线程") 

    def endChildThread(self):
        u1 = self.user
        u1.placeHolderStr = "阶段13"

        u1.kingdomProfileMy()
        self.hasReleaseWait = True
        waitThreadsLock.release()

        while not u1.isInvalid and self.user.dailyPoint < 80:
            u1.debuglog(f"点数不够 继续蹲 {self.user.dailyPoint}")
            self.sleep(5 * 60)

        if u1.isInvalid:
            raise UserError.noauth()

        # if u1.checkWSWithKingdomApp():
        #     u1.wsWithKingdomApp(isDebug=isDebug)

        # u1.tryClaimDaily()

        # u1.tryClaimEvents()
        # u1.tryUseItems()                

        u1.log("等待结束子线程")
        u1.invalidStatu = True
        for t in self.childThreads:
            t.canStop = True
            t.join()
        
        u1.log("结束子线程")

    def checkOverInfo(self):
        '''
        任务完成了
        '''
        self.removeUserInfo()
        if self.user.userKey:
            redisHelper.saveUserKeyWithZone(self.user.userKey, self.user.loc[0],isDaily=True)
        elif self.user.email:
            redisHelper.saveEmailKeyWithZone(self.user.email, self.user.pwd, self.user.loc[0],isDaily=True)
        self.user.invalidStatu = True
        self.user.log(f"日常完成:{self.user.dailyPoint} 当前消耗时间:{int(time.time() - self.startTime)}")
        


def trainingMultithread(users:List,descZone=21):
    global loggerNamePrefix,loggerMax
    loggerNamePrefix = f'{descZone}区'
    loggerMax = len(users)

    threads = []
    for user in users:
        thread = trainingThread(user,descZone=descZone,levelMax=5)
        thread.setDaemon(True)
        threads.append(thread)
        
    for thread in threads:
        thread.start()
    
    for thread in threads:
        thread.join()
    

def display(users:List[UserInfo]):
    for user in users:
        user.display()

def loadConfig(users:List[UserInfo],descZone=21,maxCount=30,maxUser=99999,tryAllianceId=None):
    if tryAllianceId:
        setAllianceId(tryAllianceId)
    setQuickModeEnable()
    setClearModeDisEnable()
    setChangeWorldModeDisEnable()

    s5List = loadS5List()
    # if len(s5List) < 10:
    #     logger.info("哥哥你太抠了吧")
    #     return
        
    global threadsLock,waitThreadsLock
    threadsLock = threading.BoundedSemaphore(maxCount)
    waitThreadsLock = threading.BoundedSemaphore(int(maxCount * 2.5))
    runUsers = []

    random.shuffle(s5List)
    # 统计代理
    usedProxy = {}
    [usedProxy.update({v:0}) for v in s5List]
    for user in users:
        if user.saveSocks5:
            s5 = redisHelper.getSocks5Info(user.key)
            if s5:
                user.socks5 = s5
                if usedProxy.get(s5):
                    usedProxy[s5] += 1
    
    def choiseProxy(s5Proxy:dict,minIndex):
        '''选择使用率最低的代理'''
        for key in s5Proxy:
            value = s5Proxy[key]
            if value == minIndex:
                s5Proxy[key] = value + 1
                return key
        return None

    index = 0
    for user in users:
        if not user.socks5 and user.saveSocks5:
            s5 = None
            while s5 is None:
                s5 = choiseProxy(usedProxy,index)
                if not s5:
                    index += 1
                else:
                    user.socks5 = s5
        # user.socks5 = "127.0.0.1:1087"
        runUsers.append(user)
        if len(runUsers) >= maxUser:
            break
    logger.info(f'{descZone}区 开始练号 总量:{len(runUsers)} 线程数:{maxCount}')
    trainingMultithread(runUsers,descZone)

def main(worldId:int,progressNum:int,maxNum:int,tryAllianceId=None):      
    userkeys = redisHelper.allUserKey()
    users = []
    for userkey in userkeys:
        if len(userkey.split("_")) > 1:
            continue
        user = UserInfo(userKey=userkey,saveSocks5=True)
        user.becomeLogSelfOnly()
        users.append(user)
    loadConfig(users,worldId,progressNum,maxNum,tryAllianceId=tryAllianceId)

def main2():
    maxCount = 1
    global threadsLock,waitThreadsLock
    threadsLock = threading.BoundedSemaphore(maxCount)
    waitThreadsLock = threading.BoundedSemaphore(maxCount * 2)
    setQuickModeEnable()
    # u1 = UserInfo(email="google",token="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJfaWQiOiI2MWM1ODc2YWJmMjljNjI1MjY2NjAwM2YiLCJraW5nZG9tSWQiOiI2MWM1ODc2YWJmMjljNjI1MjY2NjAwNDAiLCJ0aW1lIjoxNjQwMzM1MjY5NDM0LCJpYXQiOjE2NDAzMzUyNjksImV4cCI6MTY0MDk0MDA2OSwiaXNzIjoibm9kZ2FtZXMuY29tIiwic3ViIjoidXNlckluZm8ifQ.ZzclyK4ULOM5I8QCi6YMgwQCIbJXC_4peZDdjzx2prU",socks5="jhc:123456@120.236.197.196:33026")
    # u1 = UserInfo(userKey="1e04c80b-a9e0-28a7-de50-b8162506f1dc",socks5="127.0.0.1:1086")
    # u2 = UserInfo(userKey="248b17d5-74e3-ea84-6cbe-ef039dc1aca5",socks5="127.0.0.1:1086")
    # u1 = UserInfo("<EMAIL>","111111",socks5="127.0.0.1:1086")
    # 'b9a8c527-96d5-0e3a-31ed-70a45db1f5c3', '56eac03a-dff9-da5d-8e12-9dfa2053446c', 'd2257154-4c50-a1ed-15b6-a3128ef19f70',
    # 108f0345-c705-3ba0-7bd6-e551ab9fafbd
    # u1 = UserInfo(userKey="9fba0ab6-ba17-e982-7ed1-f2569f08bbac",socks5="a:b@183.232.226.153:33104",saveSocks5=True)#,socks5="jj:jj@183.232.226.153:36060",saveSocks5=True)
    # u1 = UserInfo("<EMAIL>","111111",saveSocks5=True)
    # u1 = UserInfo("<EMAIL>","111111",saveSocks5=True)
    # u1 = UserInfo(userKey="4fe0a558-b4e3-b2af-0122-e0495376232f")
    u1 = UserInfo(userKey="0cdf2c6d-5eb4-14ea-f946-da132fa4c5b9",saveSocks5=True)
    # u2 = UserInfo("g1",socks5="a:b@112.5.37.78:33103",googleToken="eyJhbGciOiJSUzI1NiIsImtpZCI6ImQwMWMxYWJlMjQ5MjY5ZjcyZWY3Y2EyNjEzYTg2YzlmMDVlNTk1NjciLCJ0eXAiOiJKV1QifQ.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QoZuHdHPM_pcixtDXIG4Yh6H0C4u29sAQJFJIOaJ2IqLh3oTzKggkgJfMpa_KjsbDdN44ggq8xWANK6nRtrCnuye68g1n5LPP8bGFRwH-MdOwAaTXxT8kJIorg2w7eEOd2AyH-BlikBmJeJqbf_7vn5u6FcCBR3plNjgK4GodulVfhiI-53iBXdhVTPRyz8FFHwUMRbCpaxeI6vd4GUJJ_cR4pBl-BySjzLRz4YhDRDK3qDS-uYw6dId9onTmnV1szVAj8ByHs5ELrDsHM6zdz7D81nEbHoF6RoFY8Opx8Gp-XjQPu5x2HKd86fyOf6gpKpSmV2CJI3opDrR7JFT8A")
    
    # <EMAIL>|25|9|1.72|3.32|2.43|2.94|518|0
    l = [
"60e5a3a7-2ba7-e062-b75e-cae94276f6d0",
"1f7aee7f-782e-6758-fe28-0fbf5e6c4073",
"879014cb-0680-a102-24af-ef7846001f82",
"0c416e58-76d0-ff70-bdae-28e5a63b4af1",
"8e71bc64-0ca7-da43-50e2-ec4196a25382",


    ]
    users = []
    for u in l:
        users.append(UserInfo(userKey=u,socks5="127.0.0.1:1086"))
        # users.append(UserInfo(u,"111111",socks5="127.0.0.1:1086"))
    
    loadConfig([u1])
    
    # u1.speedup("61b5552db0f48f4687098671", 10103001, 3)

if __name__ == "__main__":
    try:
        if isDebug:
            main(1, 1, 1)
        else:
            fire.Fire(main)
    except KeyboardInterrupt:
        pass