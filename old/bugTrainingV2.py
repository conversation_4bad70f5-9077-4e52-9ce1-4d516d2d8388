# /bin/env python3
# -*- coding: UTF-8 -*- 
'''
功能描述：游客龙bug练级
编写人：darkedge
编写日期：2023年04月01日
   
'''

from concurrent.futures import ThreadPoolExecutor
import json,sys
import time,random,math
from Model.Kingdom import Kingdom
from Model.Account import Account
from Model.UserError import UserError
from Api.UserInfo import UserInfo,buildLevelSleepTimes,buildMainLevelSleepTimes,buildFrameLevelSleepTimes
import threading
from Unit.EthUnit import EthService, changeGasPrice
from Unit.Logger import logger
from Unit.FileTool import writeConfig,loadS5List
from Unit.Redis import redisHelper
from Unit.DeviceInfo import createUserKey,createName
from Unit.WorkThread import WorkThread
import asyncio
import fire
from typing import List

from bugdst import BugDstWork
isDebug = True if sys.gettrace() else False

loggerLock = threading.BoundedSemaphore(1)
loggerNamePrefix = ""
loggerMax = 0
loggerCurrent = 0
quickMode = False
bigMode = False
clearMode = True
changeWorldMode = True
clearIndex = None
allianceId = None
halfNum = 5
runList = {}
usedProxy = {}
s5List = loadS5List()
proxyLock = threading.Lock()
# dragoLock = threading.Lock()
# walletLock = threading.Lock()
bugWork = BugDstWork()

def initUsedProxy():
    [usedProxy.update({v:0}) for v in s5List]
    
def randomUnusedProxy() -> str:
    s5 = None
    proxyLock.acquire()
    while s5 is None:
        s5 = random.choice(s5List)
        if usedProxy[s5] > 0:
            s5 = None
    usedProxy[s5] += 1
    proxyLock.release()
    return s5

def setUserSock(user:UserInfo):
    socks5 = redisHelper.getSocks5Info(user.key)
    if socks5:
        user.socks5 = socks5
        useProxy(socks5)
    else:
        socks5 = randomUnusedProxy()
        user.socks5 = socks5
    
def removeUser(user:UserInfo):
    if runList.get(user.key):
        runList.pop(user.key)
    if runList.get(user.userKey):
        runList.pop(user.userKey)

    unUseProxy(user.socks5)

def useProxy(s5):
    proxyLock.acquire()
    usedProxy[s5] += 1
    proxyLock.release()

def unUseProxy(s5):
    proxyLock.acquire()
    usedProxy[s5] -= 1
    proxyLock.release()

def setQuickModeEnable():
    global quickMode
    quickMode = True
    logger.info("快速模式开启")

def setBigModeEnable():
    global bigMode
    bigMode = True
    logger.info("11级模式开启")

def setClearModeDisEnable():
    global clearMode
    clearMode = False
    logger.info("清除模式关闭")

def setChangeWorldModeDisEnable():
    global changeWorldMode
    changeWorldMode = False
    logger.info("切换世界模式关闭")

def setLoggerName():
    loggerLock.acquire()
    global loggerCurrent
    loggerCurrent += 1
    logger.name = f'{loggerNamePrefix}-{loggerCurrent}/{loggerMax}'
    loggerLock.release()
    pass

def setLoggerCurrentPop():
    loggerLock.acquire()
    global loggerCurrent
    loggerCurrent -= 1
    loggerLock.release()

def setAllianceId(tryAllianceId):
    global allianceId
    allianceId = tryAllianceId
    logger.info(f"设置加入的联盟id为{allianceId}")

def vipshopBuy(u1:UserInfo):
    u1.vipshopBuy(10101044, u1.buyNum)

class WorkdThread(threading.Thread):
    def __init__(self,user:UserInfo,name:str):
        super().__init__(daemon=True)
        self.user:UserInfo = user
        self.name = "%s_%s" %(name, user.email or user.userKey)
        self.needSleep = False
        self.sleepTime = 0
        self.canStop = False
        self.retry = 0
        self.event_loop = None
    
    def run(self):
        super().run()
        loop = asyncio.new_event_loop()
        self.event_loop = loop
        asyncio.set_event_loop(loop)

    def sleep(self,secs):
        self.user.debuglog("休息%d秒" %secs)
        time.sleep(secs)

    def checkSleep(self,canStop=True):
        # canStop = canStop and self.canStop
        if self.needSleep and not self.canStop:
            if self.sleepTime > 10:
                time.sleep(10)
                self.sleepTime -= 10
            else:
                time.sleep(1)
                self.sleepTime -= 1
            if self.sleepTime <= 0:
                self.needSleep = False
                self.sleepTime = 0
            return True
        return False
        
    
class claimQuestThread(WorkdThread):
    def __init__(self,user:UserInfo):
        super().__init__(user,"claim")
        self.harvestCount = 90
        self.harvestRandomCount = random.randint(120, 200)
        self.noQuestCount = 0
    
    def linkDrago(self,user:UserInfo):
        return bugWork.linkDrago(user)

    def dsaToWallet(self,user:UserInfo):
        return bugWork.dsaToWallet(user)

    def overAccount(self,user:UserInfo):
        self.canStop = True
        user.log("请弟弟赴死")
        user.deleteAccount()
        user.invalidStatu = True
        if user.userKey:
            redisHelper.removeUserKeyWithAll(user.userKey)

    def tryClaimDrago(self):
        user = self.user
        user.itemList()
        try:
            if user.dsaAmount():
                user.log("已经有dsa 直接提取")
            else:
                # 没有dsa 判断流程
                user.questDaily()
                user.log(f'当前日常任务进度: {user.dailyPoint}')
                if user.dailyPoint >= 100:
                    if user.dailyLevel5:
                        user.log("日常任务已完成?尝试领取dsa")
                    else:
                        raise UserError("日常任务已完成 没有dsa", 102)
                try:
                    # dragoLock.acquire()
                    # 去链接龙领取
                    self.linkDrago(user)
                    user.itemList()
                except Exception as e:
                    raise e
                # finally:
                #     dragoLock.release()
            
            if user.dsaAmount():
                try:
                    user.log(f"即将载入钱包")
                    # walletLock.acquire()
                    if self.dsaToWallet(user):
                        self.overAccount(user)

                except Exception as e:
                    raise e
                # finally:
                #     walletLock.release()        
            else:
                user.log("没有获得dsa")
                raise UserError("日常任务已完成 没有dsa", 102)
        except UserError as e:
            if e.errorCode > 1000:
                user.errorLog(f"龙异常 请查看 :{e.message}")
                user.useBarkNoti = True
                user.barkNoti(f"龙异常 请查看 :{e.message}",isAdmin=True)
            elif e.errorCode == 102:
                self.overAccount(user)
            else:
                raise e            


    def run(self):
        super().run()
        u1 = self.user
        while not u1.isInvalid:
            self.harvestCount += 1
            if self.harvestCount % self.harvestRandomCount == 0:
                self.sleep(2)
                try:
                    u1.harvest(u1.harvestList[-1])
                except:
                    pass
                self.sleep(3)
                self.sleepTime -= 5

            if self.checkSleep():
                continue
            self.needSleep = True

            try:
                if u1.lastHarvest500 and u1.dailyPoint >= 85:
                    self.tryClaimDrago()
                    
                if self.canStop:
                    u1.log("领取主线结束")
                    break

                hasGet = u1.questClaimAllOnce()
                if u1.dailyPoint < 100 or u1.level != 10:
                    u1.tryClaimDaily(getLevel5=False)
                if u1.allianceId:
                    u1.tryHelpAll()
                if hasGet:
                    self.sleepTime = 30
                else:
                    self.sleepTime = 60
                needUse = False
                for resource in u1.resources:
                    if resource < 1 * 1000 * 1000:
                        needUse = True
                        break
                if needUse:
                    u1.tryUseItems(useTroop=True)
                
            except UserError as e:
                if e.errorCode < 0:
                    u1.errorLog(e,exc_info=True)
                    break
                else:
                    u1.errorLog(e,exc_info=True)


class jungleThread(WorkdThread):
    def __init__(self, user:UserInfo):
        super().__init__(user, "jungle")
        self.attackMonster = user.killMonster
        self.lastT = time.time()

    def run(self):
        super().run()
        u1 = self.user
        while not u1.isInvalid:
            try:
                if self.canStop:
                    u1.log("打野结束")
                    break

                if self.checkSleep(canStop=False):
                    continue
                self.needSleep = True

                if u1.checkWSWithKingdomApp():
                    u1.enter()
                    u1.wsWithKingdomApp(isDebug=isDebug)

                if u1.trainingNum > 0 and len(u1.fieldTasks) < u1.marchLimit:
                    if u1.numTroops > 3000 and self.attackMonster < 13 and u1.actionPoint > 10:
                        if u1.tryAttackMonster(maxLevel=1,maxDistance=200):
                            self.attackMonster += 1
                            # u1.troopRecover()
                    
                    u1.tryGathering(maxDistance=300,maxLevel=5)

                    self.sleepTime = 60
                    # u1.troopRecover()
                else:
                    self.sleepTime = 30
                
            except UserError as e:
                if e.errorCode < 0:
                    break
                else:
                    u1.errorLog(e,exc_info=True)
            except Exception as e:
                u1.errorLog(e,exc_info=True)

class researchThread(WorkdThread):
    def __init__(self,user:UserInfo):
        super().__init__(user,"research")

    def checkResearch(self):
        u1 = self.user
        kingdomTasks = u1.kingdomTasks()
        if kingdomTasks is None:
            self.sleep(5)
            return False

        for task in kingdomTasks:
            code = task.get("code")
            if code == 6:
                status = task.get("status")
                if status == 3:
                    if u1.claim(5,training=True):
                        # u1.log("研究完成")
                        time.sleep(3)
                    
                elif status == 1:
                    # 训练中
                    return False

                break
        return True

    def run(self):
        super().run()
        u1 = self.user
        researches = [30102001,30102002,30102003,30101001,30101002,30101003]
        while not u1.isInvalid:
            if self.canStop:
                u1.log("研究结束")
                break
            if self.retry > 5:
                self.retry = 0
            if self.checkSleep(canStop=False):
                continue

            self.needSleep = True
            try:
                
                if self.checkResearch() is False:
                    self.sleepTime = 30
                    continue
                
                u1.researchList()
                
                # 研究6个1级
                for research in researches:
                    researchInfo = u1.getResearchesInfo(research)
                    if researchInfo is None:
                        if u1.research(research):
                            self.retry = 0
                            # u1.log("开始研究%s" % research)
                            if research < 30102001:
                                self.sleepTime = 70
                            else:
                                self.sleepTime = 5
                            break
                        else:
                            # u1.debuglog("研究失败 延时10s")
                            self.sleepTime = 10
                            self.retry += 1
                        
                if self.sleepTime > 0:
                    continue

                # 研究4个2级
                for research in researches[:4]:
                    researchInfo = u1.getResearchesInfo(research)
                    if researchInfo and researchInfo.get("level") == 1:
                        if u1.research(research):
                            self.retry = 0
                            # u1.log("开始研究%s" % research)
                            if research < 30102001:
                                self.sleepTime = 120
                            else:
                                self.sleepTime = 95
                            break
                        else:
                            # u1.debuglog("研究失败 延时10s")
                            self.sleepTime = 10
                            self.retry += 1
            except UserError as e:
                if e.errorCode < 0:
                    break
                else:
                    u1.errorLog(e,exc_info=True)
            except Exception as e:
                u1.errorLog(e,exc_info=True)
            
            if self.sleepTime > 0:
                continue
            
            u1.log("10级研究完成")
            break

class trainingThread(WorkdThread):
    def __init__(self,user:UserInfo,descZone:int=21,levelMax:int=3):
        super().__init__(user,"training")
        self.buildList = [1,104,105,106,107,108,4,5,9,2,7,6,8,3]
        self.childThreads = []
        self.descZone = descZone
        self.levelMax = levelMax
        self.troopTrainTime = 0
        self.startTime = 0

    def removeUserInfo(self):
        u1 = self.user
        u1.invalidStatu = True
        if u1.userKey:
            redisHelper.removeUserKeyWithAll(u1.userKey)
        elif u1.email:
            redisHelper.removeEmailKeyWithAll(u1.email, u1.pwd)
        
    def run(self):
        super().run()
        setUserSock(self.user)
        self.startWork()
    
    def startWork(self):
        try:
            needEnd = True
            setLoggerName()
            self.startTime = time.time()
            u1 = self.user
            u1.randomSleep(0,10)
            if u1.login():
                u1.wsWithKingdomApp(isDebug=isDebug)
                u1.randomSleep(5,10)
                if u1.sumResources == 1.2 * 1000 * 1000:
                    u1.deleteAccount()
                    u1.log("过期了")
                    self.removeUserInfo()
                    return
                
                self.tryHoldBuild()
                if u1.level > 2:
                    u1.harvest(u1.harvestList[-1])
                    u1.questDaily()
                if u1.dailyPoint >= 85:
                    self.createChildThread()
                elif u1.level <= 5:
                    u1.placeHolderStr = "阶段1"
                    if not bigMode and changeWorldMode:
                        u1.changeWorld(self.descZone,allianceId=allianceId)
                    u1.newAccountEvent()
                    self.build1()
                    u1.randomSleep(2,4)
                    
                    self.level(2)
                    self.createChildThread()
                    u1.tryClaimVip()
                    u1.getGoldFree()
                    
                    u1.tryChangeFace()
                    
                    if u1.trainingNum == 0:
                        self.troopTrain(100)
                    u1.placeHolderStr = "阶段3"
                    self.level(3)
                    
                    self.troopTrain(200)
                    self.build2()
                    u1.tryJoinAlliance()
                    self.troopTrain(300)

                    u1.placeHolderStr = "阶段4"
                    self.level(4)
                    
                    # u1.tryUseItems(useTroop=True)
                    self.user.log(f"砖石:{self.user.crystal} 活跃:{self.user.dailyPoint} 时间:{time.time() - self.startTime}")
                    u1.placeHolderStr = "阶段5"
                    self.level(5)
                    u1.placeHolderStr = "阶段5.1"
                    self.user.log(f"砖石:{self.user.crystal} 活跃:{self.user.dailyPoint} 时间:{time.time() - self.startTime}")
                    self.sleep(5)
                    u1.log("建筑满5")

                u1.tryChangeFace()
                u1.questDaily()

                if u1.dailyPoint >= 100:
                    u1.log("日常满100")
                
                u1.printSelf()
                u1.log(f'当前日活:{u1.dailyPoint}')

                self.endChildThread()
        except UserError as e:
            if e.errorCode == 6 or e.errorCode == 31 or e.errorCode == 32 or e.errorCode == 41:
                u1.errorLog("你号没了")
                self.removeUserInfo()
            elif e.message == UserError.needEmailAuth().message or e.message == UserError.mismatchPassword().message:
                u1.log("需要邮箱验证")
                self.removeUserInfo()
            elif e.errorCode == -5:
                #系统限制
                u1.log("登录触发限制登录 等待6-12分钟重新登录")
                u1.clearSelf()
                u1.randomSleep(360,720)
                setLoggerCurrentPop()
                needEnd = False
                return self.startWork()
            elif e.errorCode == -4:
                u1.errorLog("token失效导致结束 尝试重新登录？")
                u1.clearSelf()
                u1.randomSleep(40,60)
                setLoggerCurrentPop()
                needEnd = False
                return self.startWork()
            elif e.errorCode < 0:
                u1.errorLog("主动异常:%s" % e.message)
            else:
                u1.errorLog("异常导致结束",exc_info=True)
        except Exception as e:
            u1.errorLog("异常导致结束",exc_info=True)
        finally:
            if needEnd:
                u1.log("结束了")
            removeUser(u1)
            logger.info(f"日常完成:{self.user.dailyPoint} 当前消耗时间:{int(time.time() - self.startTime)}")
                

    def tryHoldBuild(self):
        '''检测未完成建筑'''
        u1 = self.user
        t = self.checkBuilding()
        
        while t > 0 and not u1.isInvalid:
            u1.randomSleep(30)
            t = self.checkBuilding()
            t = self.trySpeedUp(t)
            sl = t / 60
            if sl / 5 > 0:
                u1.randomSleep(120,240)
        if not u1.isInvalid:
            u1.resetBuilding()

    def troopTrain(self,maxCount):
        u1 = self.user
        if time.time() - self.troopTrainTime < 0:
            # u1.debuglog("练兵中 跳过")
            return

        kingdomTasks = u1.kingdomTasks()
        if kingdomTasks is None:
            self.sleep(5)
            return
        for task in kingdomTasks:
            code = task.get("code")
            if code == 3:
                status = task.get("status")
                if status == 3:
                    if u1.claim(105,training=True):
                        u1.trainingNum += 100
                        # u1.log("收兵100")
                        time.sleep(5)
                elif status == 1:
                    # 训练中
                    return

                break
        
        
        if u1.trainingNum < maxCount:
            if u1.trainTroop(training=True):
                time.sleep(3)
                t = u1.trySpeedUpUseItem(2,useAny=True)
                self.troopTrainTime = time.time() + t + 2

    def checkBuilding(self):
        '''检测建筑状态'''
        u1 = self.user
        kingdomTasks = u1.kingdomTasks()
        if kingdomTasks is None:
            self.sleep(10)
            kingdomTasks = u1.kingdomTasks()
        if kingdomTasks:
            for task in kingdomTasks:
                code = task.get("code")
                if code and code == 1:
                    # started = task.get("started")
                    expectedEnded = task.get("expectedEnded")
                    t = u1.compareDateTimeWithNow(expectedEnded)
                    return t
            # u1.log("有人支援 完成了")
        return 0
    
    def trySpeedUp(self,t):
        '''尝试加速'''
        u1 = self.user
        if t > 180:
            res = u1.trySpeedUpUseItem(1,useAny=True)
            if res is None:
                u1.log("加速失败")
            elif res == 0:
                # u1.log("加速成功")
                t = 0
            else:
                # u1.log(f"加速成功 剩余时间{res}")
                t = res
        return t

    # 初始建造
    def build1(self):
        self.buildNew(self.buildList[1:6])

    # 建造2
    def build2(self):
        self.buildNew([109,110,111])

     
    def buildNew(self,positionList):
        u1 = self.user
        for position in positionList:
            if u1.getBuildingLevel(position) == 0:
                buildContinue = True
                while buildContinue and not u1.isInvalid:
                    if u1.build(position,u1.buildTypeList(position)):
                        # u1.log("建造1级%s成功" % u1.buildList(position))            
                        u1.randomSleep(4,5)
                        u1.setBuildingLevel(position,1)
                        buildContinue = False
                    else:
                        u1.log(f"建筑1级{u1.buildList(position)}失败")
                        self.sleep(10)


        

    # 升级建筑
    def level(self,level,list1=None,useSpeedup=True):
        u1 = self.user
        if list1 is None:
            list1 = self.buildList
        
        for position in list1:
            u1.getSilverFree()
            if position == 7 and level > 3:
                # u1.log("联盟大厅跳过")
                continue

            if u1.getBuildingLevel(position) == level - 1:
                buildContinue = True
                retry = 0
                mainBuild = [1,2,4,5]
                t = 0
                try:
                    if position in mainBuild:
                        t = buildMainLevelSleepTimes[level - 2]
                    elif position > 100:
                        t = buildFrameLevelSleepTimes[level - 2]
                    else:
                        t = buildLevelSleepTimes[level - 2]
                except:
                    # u1.debuglog("超出等级 直接使用6666")
                    t = 6666
                # first = True
                while buildContinue and not u1.isInvalid:
                    if retry > 5:
                        retry = 0
                        u1.enter()
                        return self.level(level)
                    try:
                        instant = level >= 3 and u1.crystal > 100
                        # if instant:
                        #     first = False
                        if u1.buildingUpgrade(position,level - 1,instant=instant and 1 or 0):
                            u1.debuglog("升级%d级%s成功" % (level,u1.buildList(position)))
                            if useSpeedup:
                                time.sleep(5)
                                t = self.trySpeedUp(t)

                            if t > 30:
                                while t>30 and not u1.isInvalid:
                                    # 3分钟补偿
                                    self.sleep(30)
                                    t -= 30
                                    t2 = self.checkBuilding()
                                    t = min(t,t2)
                                    sl = t / 60
                                    if sl // 5 > 0:
                                        rt = random.randint(120, 240)
                                        rt = min(t, rt)
                                        t -= rt
                                        u1.randomSleep(rt,msg=f'升级建筑长时等待,剩余:{t}')
                                if t > 0 and not u1.isInvalid:
                                    self.sleep(t)        
                            else:
                                self.sleep(t)
                            u1.setBuildingLevel(position,level)
                            buildContinue = False
                            time.sleep(3)
                        else:
                            u1.log(f"升级{level}级{u1.buildList(position)} 异常")
                            self.sleep(int(t / 10) + 2)
                            retry += 1
                    except UserError as e:
                        if e.errorCode == 11:
                            u1.log(f"升级{level}级{u1.buildList(position)} 资源不足 等60s")
                            u1.printSelf()
                            self.sleep(60)
                        elif e.errorCode < 0:
                            raise e
                        else:
                            u1.errorLog(e,exc_info=True)
    
    # 开启子线程
    def createChildThread(self):
        u1 = self.user
        thread1 = claimQuestThread(u1)
        # thread2 = troopBuildThread(u1)
        thread3 = researchThread(u1)
        thread4 = jungleThread(u1)

        self.childThreads.append(thread1)
        # if u1.numTroops < 300:
            # self.childThreads.append(thread2)
        
        if len(u1.researches) < 10:
            self.childThreads.append(thread3)

        self.childThreads.append(thread4)

        for t in self.childThreads:
            # t.setDaemon(True)
            t.start()
        u1.log("开启子线程") 

    def endChildThread(self):
        u1 = self.user
        u1.placeHolderStr = "阶段13"

        u1.kingdomProfileMy()
        
        while not u1.isInvalid and (self.user.dailyPoint < 85 or not u1.lastHarvest500):
            u1.debuglog(f"点数不够 继续蹲 {self.user.dailyPoint} {u1.lastHarvest500}")
            for i in range(60):
                if not u1.isInvalid and (self.user.dailyPoint < 85 or not u1.lastHarvest500):
                    self.sleep(5)
                else:
                    break

        # if u1.checkWSWithKingdomApp():
        #     u1.wsWithKingdomApp(isDebug=isDebug)

        # u1.tryClaimDaily()

        # u1.tryClaimEvents()
        # u1.tryUseItems()                

        u1.log("等待结束子线程")
        for t in self.childThreads:
            t.canStop = True
        [t.join() for t in self.childThreads]
        
        u1.log("结束子线程")

    def checkOverInfo(self):
        '''
        任务完成了
        '''
        self.removeUserInfo()
        if self.user.userKey:
            redisHelper.saveUserKeyWithZone(self.user.userKey, self.user.loc[0],isDaily=True)
        elif self.user.email:
            redisHelper.saveEmailKeyWithZone(self.user.email, self.user.pwd, self.user.loc[0],isDaily=True)
        self.user.invalidStatu = True
        logger.info(f"日常完成:{self.user.dailyPoint} 当前消耗时间:{int(time.time() - self.startTime)}")

def loadUsers():
    userkeys = redisHelper.allUserKey()
    users = []
    for userkey in userkeys:
        if len(userkey.split("_")) > 1:
            continue
        if runList.get(userkey):
            continue
        runList[userkey] = 1
        user = UserInfo(userKey=userkey,saveSocks5=True)
        user.becomeLogSelfOnly()
        users.append(user)
    return users

def startWork(user:UserInfo):
    thread = trainingThread(user)
    thread.setDaemon(True)
    thread.start()
    thread.join()
    
def main(progressNum:int):
    setQuickModeEnable()
    setClearModeDisEnable()
    setChangeWorldModeDisEnable()
    initUsedProxy()

    print(EthService(bugWork.dragoKey).balance())
    for key in bugWork.privateKeys:
        es = EthService(key)
        print(f"{es.address} 钱包余额: {es.balance()}")

    workPool = ThreadPoolExecutor(max_workers=progressNum,thread_name_prefix="workPool")
    while True:
        users = loadUsers()
        if len(users) == 0:
            logger.info("没有新用户")
        else:
            [workPool.submit(startWork,user) for user in users]
            logger.info(f"加入任务池:{len(users)}")
        time.sleep(60 * 3)

if __name__ == "__main__":
    changeGasPrice(400, 300)
    try:
        if isDebug:
            main(1)
        else:
            fire.Fire(main)
    except KeyboardInterrupt:
        pass