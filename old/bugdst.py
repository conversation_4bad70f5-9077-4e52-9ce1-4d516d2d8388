# /bin/env python3
# -*- coding: UTF-8 -*-

'''
功能描述：bugDst模块
编写人：darkedge
编写日期：2023年04月02日

'''

from Api.UserInfo import UserInfo
from Model.Account import Account
from Model.UserError import UserError
from Unit.FileTool import loadConfig, writeConfig, loadS5List
from Unit.Logger import logger
from Unit.EthUnit import EthService, modifyWeb3Socks5, changeGasPrice
from Unit.Redis import redisHelper

import random
import time
import sys
import os
import json
import threading
from tqdm import tqdm
from Api.ProxyApi import allocateIP, changeKeyTo1, changeKeyTo2, createRealSocks5
from Api.Ipinfo import getIP
from concurrent.futures import ThreadPoolExecutor
import fire

isDebug = True if sys.gettrace() else False
onceCount = 3
mintLock = threading.Lock()
failAccountPath = f'./tmpaccount/failMint.txt'
mintedAccountPath = f'./tmpaccount/minted.txt'
succeedCount = 0
failCount = 0
waitList = {}
pool = ThreadPoolExecutor(10, thread_name_prefix="waitEmail")
dragoLock = threading.Lock()
walletLock = threading.Lock()
ethServiceList = {}

def addSucceedCount():
    global succeedCount
    succeedCount += 1


def addFailCount():
    global failCount
    failCount += 1


def writeFailAccount(user: UserInfo):
    addFailCount()
    with open(failAccountPath, 'a+') as f:
        f.write(f'{user.email}|{user.pwd}|{user.token}\n')


def writeMintedAccount(user: UserInfo):
    with open(mintedAccountPath, 'a+') as f:
        f.write(f'{user.email}|{user.pwd}|{user.token}\n')


def returnS5(retry=0, useLocal=False):
    if retry > 5 or useLocal:
        logger.info("使用自己的代理")
        return random.choice(loadS5List())
    s5 = allocateIP()
    logger.info("获取到代理:%s" % s5)
    if s5 is None:
        logger.error("代理异常")
        time.sleep(10)
        return returnS5(retry+1)
    else:
        if getIP(createRealSocks5(s5)) is None:
            logger.error(f"获取代理异常到的: {s5}")
            time.sleep(3)
            return returnS5(retry+1)

    return createRealSocks5(s5)

def getEthService(privateKey):
    es = ethServiceList.get(privateKey)
    if not es:
        es = EthService(privateKey=privateKey)
        ethServiceList[privateKey] = es
    return es

class BugDstWork:
    __dragoKey = None
    __privateKeys = None
    __hashMd5 = None

    def __init__(self,dragoKeyPath=None,privateKeysPath=None) -> None:
        self.dragoKeyPath = dragoKeyPath
        self.privateKeysPath = privateKeysPath
        self.lastWallet = None
        self.pbar = None
        self.WalletThreadPool = ThreadPoolExecutor(1, thread_name_prefix="WalletThreadPool")

    @property
    def privateKeys(self):
        # ['******************************************', 
        #  '******************************************',
        #  '******************************************', 
        #  '******************************************',
        #  '******************************************',
        #  '******************************************', 
        #  '******************************************']
        if self.__privateKeys is None:
            try:
                with open(self.privateKeysPath or "privateKeys.txt", "r") as f:
                    lines = f.readlines()
                    privateKeys = []
                    for line in lines:
                        line = line.strip()
                        if not line:
                            continue
                        privateKeys.append(line)
                    self.__privateKeys = privateKeys
                    print("使用本地私钥key")
            except:
                print("使用内置私钥key")
                self.__privateKeys = ["0xe536e1297aa81b6fdaf798e3167c98303b527dae1ada6494a85c223d4626d804",
                                        "0xee1b65bfdeff1e386d5f86a594063b17e381bdae243ce2126ae44a914f174366",
                                        "0xa0e376e211193b077fb0085a482b5b343194960d9d3871118d700e35c8c0b7b4",
                                        "0xdaf2ed04fefbaa815130939d6a9cd7e6b13bdb617918701a6b7ac2e0238c277d",
                                        "0x8aef9bc0d736591e719e597c5e73255e0603d7b63ca5e5ebaf87acfa9d7cb3a2",
                                        "0xd11ef359ed00e4b5b9ab8008cc854ace28009c0d91d14322892cd5687c3d6a56",
                                        "0xd8a0958144f580bebdf3f977badc5e6e703e617c4c4927252559e00b039ca20c"]
        return self.__privateKeys

    def printPrivateKeys(self):
        for key in self.privateKeys:
            es = EthService(key)
            print(f"{es.address} 钱包余额: {es.balance()}")

    @property
    def dragoKey(self):
        if self.__dragoKey is None:
            try:
                with open(self.dragoKeyPath or "dragoKey.txt", "r") as f:
                    self.__dragoKey = f.read().strip()
                    print("使用本地文件key")
            except:
                print("使用内置key")
                self.__dragoKey = "0x39867c0e809fc775d0ad575569673b6ea4536c41f53efc8db4e97d7b762668fa"
        return self.__dragoKey
        
    def printDragoKey(self):
        es = EthService(self.dragoKey)
        print(f"龙账户 {es.address} 钱包余额: {es.balance()}")

    @property
    def hashMd5(self):
        if self.__hashMd5 is None:
            self.__hashMd5 = EthService(self.dragoKey).address
        return self.__hashMd5
    @property
    def socks5List(self):
        return loadS5List()

    def userKeys(self):
        userKeys = redisHelper.allUserKey()
        if len(userKeys):
            dailyUserKeys = []
            for userKey in userKeys:
                if userKey.find("daily") != -1:
                    dailyUserKeys.append(userKey.split("_")[1])
            return dailyUserKeys
        return []

    def mint(self, user:UserInfo, ethService:EthService, txDataJson):
        try:
            txHash = ethService.mint(txDataJson, delValue=False)
            if txHash:
                logger.info(f"提取dsa成功 {user.key} {ethService.address} {txHash}")
        except Exception as e:
            logger.error(f"提取dsa失败 {user.key} {ethService.address} {e}")

    def chooseEthService(self,user:UserInfo) -> EthService:
        pkey = random.choice(self.privateKeys)
        ethService = getEthService(pkey)
        if ethService.address == self.lastWallet and len(self.privateKeys) > 1:
            return self.chooseEthService(user)
        balance = ethService.balance()
        if balance < 0.5:
            user.useBarkNoti = True
            user.barkNoti(f"{ethService.address}余额告警 {balance}",isAdmin=True)
            if balance < 0.1:
                user.log("余额严重不足 换个私钥")
                return self.chooseEthService(user)
        self.lastWallet = ethService.address
        return ethService
    
    def dragoLockAcquire(self):
        return dragoLock.acquire()

    def dragoLockRelease(self):
        return dragoLock.release()

    def linkDragoWallet(self, user: UserInfo):
        return user.tryLinkWallet(self.dragoKey)
    
    def tryJoinDrago(self, user: UserInfo) -> str:
        dragos = user.nftDragoList()
        if not dragos:
            user.errorLog("没有龙？？？")
            raise UserError("没有龙？？？", 1006)
        dragoId = dragos[0].get("_id")
        user.log("清龙")
        user.dragoLairLeave(dragoId)
        user.randomSleep(2,3,msg="请龙")
        if user.dragoLairJoin(dragoId):
            dragos = user.dragoLairList()
            if len(dragos) == 0:
                user.randomSleep(2,3,msg="放龙失败？再次放龙")
                user.dragoLairJoin(dragoId)
            return dragoId
        else:
            return None

    def linkDrago(self, user: UserInfo):
        self.linkDragoWallet(user)
        dragoId = None
        self.dragoLockAcquire()
        hasRelease = False
        try:
            dragos = user.nftDragoList()
            needLoad = True

            if not dragos:
                user.errorLog("没有龙？？？")
                raise UserError("没有龙？？？", 1006)
            
            if not dragoId:    
                for drago in dragos:
                    lair = drago.get("lair")
                    status = lair.get("status")
                    if status == 0:
                        dragoId = drago.get("_id")
                        break
                    else:
                        dragoId = drago.get("_id")
                        user.dragoLairLeave(dragoId)
                        user.log(f"龙被卡了 主动离开")
                        hasRelease = True
                        dragoLock.release()
                        return self.linkDrago(user)
            if not dragoId:
                dragos = user.dragoLairList()
                if dragos and len(dragos) > 0:
                    dragoId = dragos[0].get("_id")
                    needLoad = False
                else:
                    raise UserError("龙异常", 1002)
                
            if not dragoId:
                raise UserError("没有空闲龙？？？", 1003)

            if needLoad:
                user.dragoInfo(dragoId)
                user.log("即将载入龙")
                dragos = user.dragoLairJoin(dragoId)
                if not dragos:
                    raise UserError("载入龙失败？？", 1004)

            user.tryHarvestAll()
            user.tryClaimDaily(getLevel5=True)
            user.log(f"日常任务进度 : {user.dailyPoint}")

            dragos = user.dragoLairLeave(dragoId)
            if len(dragos) > 0:
                raise UserError("龙没有离开？？", 1005)
            user.log("龙离开成功")
        except Exception as e:
            raise e
        finally:
            if not hasRelease:
                self.dragoLockRelease()

        user.unlinkWallet()
        
    def leaveDragos(self, user: UserInfo):
        dragos = user.dragoLairList()
        if dragos:
            for drago in dragos:
                dragoId = drago.get("_id")
                user.dragoLairLeave(dragoId)
                user.randomSleep(2,3)
            return self.leaveDragos(user)
        else:
            user.log(f"已清理所有龙")

    def dsaToWallet(self, user: UserInfo):
        dsaId = user.dsaId()
        dsaAmount = user.dsaAmount()
        if dsaAmount == 0:
            user.log("没有dsa")
            return False
        walletLock.acquire()
        ethService = self.chooseEthService(user)
        walletLock.release()
        if user.tryLinkWallet(ethService.privateKey):
            if user.dstTakeTry(dsaAmount):
                res = user.dstTake(dsaAmount, dsaId)
                txData = res.get("txData")
                txDataJson = json.loads(str(txData))
                del txDataJson["code"]
                self.WalletThreadPool.submit(self.mint, user, ethService, txDataJson)
                user.unlinkWallet()
                return True
        user.unlinkWallet()
        return False
        # user.unlinkWallet()

    def finishUser(self, user: UserInfo):
        # user.unlinkWallet()
        user.log(f"领取完成 删除")
        user.deleteAccount()
        user.wsWithKingdomClose()
        user.invalidStatu = True
        redisHelper.removeUserKeyWithAll(user.userKey)

    def waitEmail(self, user: UserInfo):
        count = 0
        try:
            waitList[user.userKey] = 1
            while not user.isInvalid and not user.type15MsgGet:
                # if user.checkWSWithKingdomApp():
                #     user.wsWithKingdomApp()
                # 不使用ws?

                if user.checkDsaMail():
                    self.finishUser(user)
                    return True

                for i in range(30):
                    if user.type15MsgGet:
                        self.finishUser(user)
                        return True
                    time.sleep(1)

                count += 1
                if count < 60:
                    user.debuglog(f"等待邮件 {count}")
                else:
                    raise UserError("等待邮件超时", 102)
        except UserError as e:
            if e.errorCode in [-4,41]:
                user.log(f"被ban了")
                redisHelper.removeUserKeyWithAll(user.userKey)
                return False
            if e.errorCode == 102:
                user.errorLog(f"等待邮件超时")
                return False
            user.errorLog(f"等待邮件异常 {e}")
            return False
        except Exception as e:
            user.errorLog(f"等待邮件异常 {e}")
        finally:
            waitList.pop(user.userKey, None)

    def work(self, userKey):
        try:
            startTime = time.time()
            user = UserInfo(userKey=userKey, saveSocks5=True,
                            socks5=random.choice(self.socks5List))
            if user.login():
                user.log("登录完成")
                user.wsWithKingdomApp()
                if user.publicKey:
                    user.unlinkWallet()
                    user.randomSleep(3, msg="解绑钱包")

                if user.dsaAmount():
                    user.log("已经有dsa 直接提取")
                else:
                    user.questDaily()
                    user.log(f'当前日常任务进度: {user.dailyPoint}')
                    if user.dailyPoint < 80:
                        raise UserError("日常任务未完成", 101)
                    if user.dailyPoint >= 100:
                        if user.dailyLevel5:
                            user.log("日常任务已完成?尝试领取dsa")
                        else:
                            raise UserError("日常任务已完成 没有dsa", 102)
                    try:
                        # dragoLock.acquire()
                        self.linkDrago(user)
                    except Exception as e:
                        raise e
                    # finally:
                    #     dragoLock.release()

                user.itemList()
                if user.dsaAmount():
                    try:
                        if self.dsaToWallet(user):
                            pool.submit(self.waitEmail, user)
                    except Exception as e:
                        raise e
                    # finally:
                    #     walletLock.release()        
                else:
                    user.log("没有获得dsa")
                    raise UserError("日常任务已完成 没有dsa", 102)
        except UserError as e:
            if e.errorCode == 41:
                user.errorLog("账号异常 请查看")
                redisHelper.removeUserKeyWithAll(user.userKey)
            if e.errorCode > 1000:
                user.errorLog(f"龙异常 请查看 :{e.message}")
                user.useBarkNoti = True
                user.barkNoti(f"龙异常 请查看 :{e.message}",isAdmin=True)

                exit(0)
            if e.errorCode == 102:
                user.log("查看邮件看看")
                pool.submit(self.waitEmail, user)
            else:
                user.errorLog(e)
        except Exception as e:
            logger.error(e)
        finally:
            user.log(f"finally cost:{time.time() - startTime}")
            self.pbar.update(1)

    def run(self):
        userKeys = self.userKeys()
        if len(userKeys) == 0:
            logger.info("没有用户")
            return
        workPool = ThreadPoolExecutor(max_workers=2, thread_name_prefix="work")
        realKeys = []
        for userKey in userKeys:
            if waitList.get(userKey):
                continue
            realKeys.append(userKey)

        self.pbar = tqdm(total=len(realKeys), desc="任务进度")
        for userKey in realKeys:
            workPool.submit(self.work, userKey)
        workPool.shutdown(wait=True)


def main(autoAgain: bool = False,useSocks=False):
    if useSocks:
        modifyWeb3Socks5(random.choice(loadS5List()))
    print(EthService(BugDstWork().dragoKey).balance())
    if autoAgain:
        logger.info("自动重启")
        while True:
            BugDstWork().run()
            time.sleep(60 * 1)
            logger.info("等待1分钟")
    else:
        BugDstWork().run()
    
    pool.shutdown(wait=True)


if __name__ == '__main__':
    changeGasPrice(400, 300)
    try:
        fire.Fire(main)
    except KeyboardInterrupt:
        pass
    except Exception as e:
        logger.error(e)
