# /bin/env python3
# -*- coding: UTF-8 -*-

'''
功能描述：dst提现模块
编写人：darkedge
编写日期：2023年04月10日

'''


from Api.UserInfo import UserInfo
from Model.Account import Account
from Model.UserError import UserError
from Unit.FileTool import loadConfig, writeConfig, loadS5List
from Unit.Logger import logger
from Unit.EthUnit import EthService, modifyWeb3Socks5, changeGasPrice
from Unit.Redis import redisHelper
from bugdst import BugDstWork

import random
import time
import sys
import os
import json
import threading
from tqdm import tqdm
from Api.ProxyApi import allocateIP, changeKeyTo1, changeKeyTo2, createRealSocks5
from Api.Ipinfo import getIP
from concurrent.futures import ThreadPoolExecutor
import fire

isDebug = True if sys.gettrace() else False

class ExtractWork:
    def __init__(self,privateKeysPath):
        self.work = BugDstWork(privateKeysPath=privateKeysPath)
        self.work.printPrivateKeys()

    def run(self):
        
        while True:
            try:
                inputValue = input(f"请输入token:")
                if inputValue:
                    user = UserInfo(token=inputValue,socks5=random.choice(self.work.socks5List))
                    user.loadEmailWithToken()
                    user.initLog(needTestS5=False)
                    # self.work.leaveDragos(user)
                    user.itemList()
                    if self.work.dsaToWallet(user):
                        user.log("处理完成 等待提现反馈")
                else:
                    continue

            except KeyboardInterrupt:
                break


def main(privateKeysPath):
    realPath = f'./tmpaccount/{privateKeysPath}'
    try:
        with open(realPath, 'r') as f:
            lines = f.readlines()
    except FileNotFoundError as e:
        logger.error("没有找到私钥")
        return

    ExtractWork(realPath).run()

if __name__ == '__main__':
    changeGasPrice(400, 300)
    try:
        if isDebug:
            main("test.txt")
        else:
            fire.Fire(main)
    except KeyboardInterrupt:
        pass
    except Exception as e:
        logger.error(e,exc_info=True)
