# /bin/env python3
# -*- coding: UTF-8 -*- 
'''
功能描述：邮箱注册
编写人：darkedge
编写日期：2021年12月25日
   
'''

from Api.GoogleCaptcha import get_response,create_task
from Api.Ipinfo import getIP
from Api.ProxyApi import allocateIP,whiteAdd,Proxy<PERSON><PERSON>,ProxyPwd
from Api.UserInfo import UserInfo
from Model.Account import Account
from Model.UserError import UserError
from Unit.DeviceInfo import randomEmailPrefix
from Unit.FileTool import loadS5List
from Unit.Redis import redisHelper
from Unit.Logger import logger
from Api.ImapService import imapService
import time
import random,threading

emailList = [
"275936.com",
"267356.com",
"256126.com",
"247892.com",
"246064.com",
"243915.com",
"211935.com",
"209428.com",
"147326.com",
"146449.com",
"140632.com",
"080445.com",
"078041.com",
"075497.com",
"071295.com",
"068134.com",
"067092.com",
"055316.com",
"037042.com",
"024522.com",
"298224.com",
"294589.com",
"293974.com",
"280853.com",
"271336.com",
"252771.com",
"227516.com",
"221832.com",
"217287.com",
"215397.com",
"198643.com",
"172619.com",
"172450.com",
"162406.com",
"154623.com",
"150737.com",
"140435.com",
"131772.com",
"127375.com",
"125764.com",
"110671.com",
"108286.com",
"098693.com",
"094742.com",
"092936.com",
"078084.com",
"077813.com",
"075960.com",
"068492.com",
"061764.com",
"056835.com",
"056329.com",
"054873.com",
"050840.com",
"050680.com",
"049072.com",
"048574.com",
"046780.com",
"038857.com",
"038469.com",
"037815.com",
"032532.com",
"030741.com",
"029610.com",
"025636.com",
"242954.com",

]

emailMaxCount = 6
s5List = loadS5List().copy()
random.shuffle(s5List)

def writeLocal(email,pwd="111111"):
     with open("./emailaccount.txt","a+") as f:
        f.write(f"{email}|{pwd}\n")

def waitActiveEmail(u1:UserInfo):
    logger.info(f"等待邮箱激活 {u1.email}")
    
    while True:
        url = None
        uid = None
        try:
            url,uid = imapService.returnSomeUserInfo(descEmail=u1.email,autoActive=False)
        except Exception as e:
            logger.error(f'获取邮箱信息失败 {e}',exc_info=True)
        if url:
            try:
                s5 = allocateIP()
                u1.socks5 = f"{ProxyKey}:{ProxyPwd}@{s5}"
                u1.initLog()
                res = u1.requestGet(url)
                if res.status_code == 200:
                    imapService.makeSeemAndDelete(uid)
                    if imapService.checkResIsSucceed(res.text):
                        writeLocal(u1.email,u1.pwd)
                        logger.info("激活成功:%s" % u1.email)
                        time.sleep(2)
                        u1.login()
                        u1.wsWithKingdomApp()
                        u1.newAccountEvent()
                        u1.invalidStatu = True
                        # exit(0)
                    else:
                        logger.error(f"激活失败，原因: {res.text}")
                else:
                    logger.error(f"激活失败，状态码：{res.status_code}")
                return
            except Exception as e:
                logger.error(f"发生异常，原因: {e}",exc_info=True)
                return

        time.sleep(10)


def registerWithDynamicProxy():
    logger.info("动态ip注册")
    socksTimeOut = 35

    threads = []
    emailedList = {}
    random.shuffle(emailList)
    recaptchaRes = None
    gt = 0
    imapService.start()
    while True:  
        if recaptchaRes is None:
            taskId = create_task()
            if not taskId:
                continue
            currentTime = time.time()
            recaptchaRes = get_response(taskId)
            gt = time.time()
            logger.info("获得谷歌验证 耗时%d" % (time.time() - currentTime))

        s5 = allocateIP()
        # u1 = UserInfo("<EMAIL>","111111",socks5="08ZJT6QC:B1429F6B5167@%s" % s5)
        # th = threading.Thread(target=waitActiveEmail,args=(u1,),name=f"active_{u1.email}")
        # th.setDaemon(True)
        # th.start()
        # th.join()
        # return
        logger.info("获取到代理:%s" % s5)
        if s5 is None:
            logger.error("代理异常")
            return
        s5Ip = s5.split(":")[0]
        if redisHelper.getIPBan(s5Ip):
            logger.info("ip受限 休息5秒")
            time.sleep(5)
            continue
        t = time.time()
        if s5:
            for i in range(1):
                pwd = randomEmailPrefix(7,8)
                emailSuffix = random.choice(emailList)
                u1 = UserInfo("%s@%s" % (randomEmailPrefix(),emailSuffix),pwd,socks5=f"{ProxyKey}:{ProxyPwd}@{s5}")
                try:
                    if recaptchaRes is None:
                        taskId = create_task()
                        if not taskId:
                            continue
                        currentTime = time.time()
                        recaptchaRes = get_response(taskId)
                        gt = time.time()
                        logger.info("获得谷歌验证 耗时%d" % (time.time() - currentTime))
                    if time.time() - t > (socksTimeOut + i * 5):
                        redisHelper.setIPBan(s5Ip)
                        logger.info("代理超时")
                        break
                    if time.time() - gt > 60:
                        logger.info("谷歌验证超时")
                        recaptchaRes = None
                        continue
                    if u1.authJoin(token=recaptchaRes):
                        u1.log("注册成功:%s|%s" % (u1.email,pwd))
                        # th = threading.Thread(target=waitActiveEmail,args=(u1,),name=f"active_{u1.email}")
                        # th.setDaemon(True)
                        # th.start()
                        # threads.append(th)
                        # th.join()
                        waitActiveEmail(u1)
                        recaptchaRes = None
                        if emailedList.get(emailSuffix):
                            emailedList[emailSuffix] += 1
                            if emailedList[emailSuffix] == emailMaxCount:
                                emailList.remove(emailSuffix)
                        else:
                            emailedList[emailSuffix] = 1
                        
                        if len(emailList) == 0:
                            logger.info("注册完成")
                            exit(0)
                    else:
                        redisHelper.setIPBan(s5Ip)
                        logger.info("代理超时")
                        break
                except UserError as e:
                    if e.errorCode == 7:
                        redisHelper.setIPBan(s5Ip)
                        logger.info("ip受限 换ip")
                        recaptchaRes=None
                        break
                    elif e.errorCode == -3:
                        redisHelper.setIPBan(s5Ip)
                        logger.info("s5异常")
                    elif e.errorCode == 33:
                        logger.info(f"邮箱被封了 {u1.email}")
                        [th.join() for th in threads]
                        exit(0)
                    elif e.errorCode == 32:
                        logger.error("傻逼官方又关注册了")
                        [th.join() for th in threads]
                        exit(0)
                except Exception as e:
                    logger.info("注册失败:%s" % e)
                    
            redisHelper.setIPBan(s5Ip)

        else:
            logger.info("获取ip异常")
            time.sleep(10)
        

def registerWithS5():
    users = []
    socks5List = []
    with open("./socks5.txt") as lines:
        accounts = lines.readlines()
        for account in accounts:
            account = account.strip('\n')
            if len(account):
                user = Account(account)
                socks5List.append(user.s5)
                
                # user.email = "%s@%s" % (randomEmailPrefix(),random.choice(emailList))
                # user.password = "111111"
                # users.append(user)
                # sock5List[user.s5] = user

    for s5 in socks5List:
        
    # for user in users:
        # u1 = user.returnUserInfo()
        for i in range(0,3):
            u1 = UserInfo("%s@%s" % (randomEmailPrefix(),random.choice(emailList)),"111111",socks5=s5)
            try:
                u1.initLog()
                if redisHelper.getIPBan(u1.s5IP):
                    break

                if u1.authJoin():
                    u1.log("注册成功:%s" % u1.email)
                    writeLocal(u1.email)
            except UserError as e:
                if e.errorCode == 7:
                    u1.log("上限了 换ip")
                    redisHelper.setIPBan(u1.s5IP)
                    break
                else:
                    u1.errorLog(e.message)
            except Exception as e:
                u1.log("注册失败:%s" % e)

def localTest():
    for i in range(1,10):
        u1 = UserInfo("%<EMAIL>" % (randomEmailPrefix()),"111111",socks5="jhc:123456@***************:33063")
        if u1.authJoin():
            u1.log("注册成功:%s" % u1.email)
            writeLocal(u1.email)

if __name__ == '__main__':
    # registerWithS5()
    # localTest()
    registerWithDynamicProxy()