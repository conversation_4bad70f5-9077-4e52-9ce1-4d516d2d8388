# /bin/env python3
# -*- coding: UTF-8 -*- 
'''
功能描述：email练级
编写人：darkedge
编写日期：2021年12月23日
   
'''

import json,sys
import time,random,math
from Model.Kingdom import Kingdom
from Model.Account import Account
from Model.UserError import UserError
from Api.UserInfo import UserInfo,buildLevelSleepTimes,buildMainLevelSleepTimes,buildFrameLevelSleepTimes
import threading
from Unit.Logger import logger
from Unit.FileTool import loadConfig,writeConfig,loadS5List
from Unit.Redis import redisHelper
from Unit.DeviceInfo import createUserKey,createName
from Unit.WorkThread import WorkThread
import asyncio
from guestTraining import loadConfig
import fire

def loadLocalAccount(path="emailaccount.txt"):
    with open(path, "r", encoding="utf-8") as f:
        lines = f.readlines()
        # t = int(time.time() - 999999)
        for line in lines:
            # t += random.randint(1, 99)
            s = line.strip()
            v = s.split("|")
            if len(v) == 1:
                redisHelper.saveEmailKey(s, 111111)
            elif len(v) == 2:
                redisHelper.saveEmailKey(*v)
            else:
                logger.error("错误数据")
    
    with open(path,"w+") as f:
        f.write("")
    

def main(worldId:int,progressNum:int,maxNum:int,tryAllianceId=None):
    loadLocalAccount()
    emailKeys = redisHelper.allEmailKey()
    users = []
    for emailKey in emailKeys:
        if len(emailKey.split("_")) > 1:
            continue
        s = emailKey.split("|")
        user = UserInfo(s[0],s[1],saveSocks5=True)
        users.append(user)
    loadConfig(users,worldId,progressNum,maxNum,tryAllianceId)

if __name__ == "__main__":
    fire.Fire(main)