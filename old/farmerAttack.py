#!/usr/bin/python3
# -*- coding: utf-8 -*-
# Path: farmerAttack.py

'''
功能描述：小号刷资源并发行
编写人：darkedge
编写日期：2022年02月08日
   
'''
from Model.UserError import UserError
from Model.Account import Account
from Model.Kingdom import Kingdom
from Unit.Logger import logger
from Api.UserInfo import UserInfo
import threading
import os,sys,random
import asyncio
from typing import List
isDebug = True if sys.gettrace() else False
class Farmer(object):
    def __init__(self,path,useDragon=False,auotoPackage=True,aotuoJoin=False,gathing=False,autoHarassment=False):
        self.path = path
        self.useDragon = useDragon
        self.auotoPackage = auotoPackage
        self.aotuoJoin = aotuoJoin
        self.gathing = gathing
        self.autoHarassment = autoHarassment

        if useDragon:
            logger.info("自动放龙")
        if auotoPackage:
            logger.info("自动打包")
        if aotuoJoin:
            logger.info("自动进团")
        if gathing:
            logger.info("自动挖矿")
        if autoHarassment:
            logger.info("自动侦查")

        self.attackLoc = None
        self.accounts:List[Account] = []
        self.loadPath()

    def raisePathError(self):
        raise UserError("文件异常")

    def loadPath(self):
        with open(f"./tmpaccount/{self.path}","r") as f:
            lines = [line.strip() for line in  f.readlines()]
            if len(lines) < 2:
                self.raisePathError()
            
            locStr = lines[0]
            loc = locStr.split(",")
            if len(loc) != 2:
                raise UserError("异常的坐标 应该为 x,y")
            self.attackLoc = [int(v) for v in loc]
            if self.attackLoc[0] == 0:
                logger.info("不攻击模式")
            
            for line in lines[1:]:
                user = Account(line)
                self.accounts.append(user)
                logger.info(f'workman:{user.email or user.userKey}')
            
    
    def runFarmerAttack(self,u1:UserInfo):
        asyncio.set_event_loop(asyncio.new_event_loop())
        def tryGoback():
            asyncio.set_event_loop(asyncio.new_event_loop())
            timeout = 70
            sleepTime = 20
            if u1.level < 30:
                timeout = 300
                sleepTime = 60
            # elif highDelay:
            #     timeout = 200
            #     sleepTime = 30
            while True:
                u1.gobackOnlyOne(timeout=timeout)
                u1.randomSleep(sleepTime)

        try:
            if u1.login():
                if self.auotoPackage and u1.publicKey is None:
                    u1.errorLog("钱包丢了")
                    return
                u1.wsWithKingdomApp(isDebug=isDebug)
                u1.tryClaimEvents()
                u1.troopRecover()
                u1.tryClaimVip()
                if self.useDragon:
                    u1.tryUseDragon()
                timer = 0
                if self.gathing:
                    th3 = threading.Thread(target=tryGoback,name="gobackThread")
                    th3.setDaemon(daemonic=True)
                    th3.start()

                while True:
                    if u1.checkWSWithKingdomApp():
                        u1.wsWithKingdomApp(isDebug=isDebug)
                    if timer % 30 == 0 and self.auotoPackage:
                        u1.tryUseItems(noPower=True)
                        if u1.myresources():
                            u1.mintMaxResource()

                    if self.aotuoJoin:
                        u1.joinAllianceBattle()
                    if self.autoHarassment:
                        u1.taskAll()
                        if len(u1.fieldTasks) < u1.marchLimit:
                            if u1.startInvestigation(self.attackLoc):
                                u1.randomSleep(3)
                            else:
                                u1.randomSleep(10,msg="自动侦查失败")
                        continue

                    u1.tryHelpAll()

                    timer += 1
                    u1.getSilverFree()
                    u1.getGoldFree()
                    if self.attackLoc[0] != 0:
                        if u1.attackKingdomAlone(Kingdom(f"any{random.randint(1, 9999999)}", self.attackLoc),onlyInfantry=True) is False:
                            u1.log("上限了 多休息一会")
                            u1.randomSleep(120)
                    else:
                        if self.gathing:
                            u1.tryGathering(maxCount=50000,minCount=3000,dsecType=None,minLevel=2,power=3)
                        u1.randomSleep(60)
                        u1.log("一轮循环")
        except UserError as e:
            if e.errorCode == 6 or e.errorCode == 31 or e.errorCode == 32 or e.errorCode == 41:
                u1.errorLog("你号没了")
            elif e.message == UserError.needEmailAuth().message or e.message == UserError.mismatchPassword().message:
                u1.log("需要邮箱验证")
            elif e.errorCode == -5:
                #系统限制
                u1.clearSelf()
                u1.log("登录触发限制登录 等结束")
            elif e.errorCode == -4:
                u1.errorLog("token失效导致结束")
                u1.clearSelf()
            elif e.errorCode < 0:
                u1.errorLog("主动异常:%s" % e.message)
            else:
                u1.errorLog("异常导致结束",exc_info=True)
        except Exception as e:
            u1.errorLog("异常导致结束",exc_info=True)
            
    def run(self):
        threads = []
        for user in self.accounts:
            th = threading.Thread(target=self.runFarmerAttack,args=(user.returnUserInfo(isMain=True,saveSocks5=False),))
            th.setDaemon(True)
            th.start()
            threads.append(th)
            # user.run()

        [th.join() for th in threads]
    

def farmerRun(path,useDragon=False,auotoPackage=True,aotuoJoin=False,gathing=False,autoHarassment=False):
    farmer = Farmer(path,useDragon=useDragon,auotoPackage=auotoPackage,aotuoJoin=aotuoJoin,gathing=gathing,autoHarassment=autoHarassment)
    farmer.run()

if __name__ == "__main__":
    if isDebug:
        farmerRun("a.txt")

    else:
        import fire
        fire.Fire(farmerRun)




