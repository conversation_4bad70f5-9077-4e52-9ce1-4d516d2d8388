# /bin/env python3
# -*- coding: UTF-8 -*- 
'''
功能描述：撤回功能
编写人：darkedge
编写日期：2022年2月14日
   
'''

from Unit.Redis import redisHelper
from Unit.Logger import logger
from Api.UserInfo import UserInfo
import random,time
from Model.UserError import UserError
from Unit.FileTool import loadS5List
import os,sys

def main():
    # sys.argv.append("eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJfaWQiOiI2MWUxNDU5MDIxY2NlZDBmYWFjOWIzMDAiLCJraW5nZG9tSWQiOiI2MWUxNDU5MDIxY2NlZDBmYWFjOWIzMDIiLCJ0aW1lIjoxNjQ2ODA4NTIyNTI5LCJpYXQiOjE2NDY4MDg1MjIsImV4cCI6MTY0NzQxMzMyMiwiaXNzIjoibm9kZ2FtZXMuY29tIiwic3ViIjoidXNlckluZm8ifQ.zyNpumdDuwEMoD2F-o2xYGzQ_gaJgqn8jcRJqVDUPoE")

    isDebug = True if sys.gettrace() else False
    if isDebug:
        from Unit.FileTool import loadSelfToken
        sys.argv.append(loadSelfToken())
        # sys.argv.append("<EMAIL>")
        # sys.argv.append("wxl668899")
    if len(sys.argv) > 1:
        u1 = None
        if len(sys.argv) > 2:
            u1 = UserInfo(sys.argv[1],sys.argv[2],socks5=random.choice(loadS5List()))
            u1.log("邮箱模式")
        else:
            token = sys.argv[1]
            u1 = UserInfo(f'{random.randint(1,1000000)}@somebody.com',socks5=random.choice(loadS5List()),token=token)
            u1.log("token模式")

        u1.isWebDevice = True
        if u1.isLogin:
            u1.initLog()
        else:
            u1.login()
        
        if u1.isLogin:
            u1.wsWithKingdomApp()
            if u1.getTroops():
                crystalMode = False
                inputValue = input("卡水晶(y/n)：")
                if inputValue.lower() == 'y':
                    crystalMode = True

                if crystalMode:
                    u1.taskAll()
                    u1.log("卡水晶模式")
                    u1.gobackOnlyOne(10000)
                    return

                u1.randomSleep(3,5,msg="卡兵撤回")
                for field in u1.troopFields:
                    u1.kingdomId = field.get("kingdomId")
                    moId = field.get("_id")
                    marchType = field.get("marchType")
                    state = field.get("state")
                    endTime = field.get("endTime")
                    t = u1.compareDateTimeWithNow(endTime)

                    if t < 0:
                        if u1.fieldMarchReturn(moId):
                            u1.log("撤回卡死队列成功")
            else:
                u1.log("请求异常")

    else:
        logger.error("输入异常")


if __name__ == "__main__":
    main()
