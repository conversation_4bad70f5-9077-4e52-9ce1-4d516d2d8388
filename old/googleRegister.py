# /bin/env python3
# -*- coding: UTF-8 -*- 
'''
功能描述：注册谷歌账号
编写人：darkedge
编写日期：2022年03月28日
   
'''
import json,os,sys
import time,random
from Model.Kingdom import Kingdom
from Model.Account import Account
from Model.UserError import UserError
from Api.UserInfo import UserInfo
import threading
from Unit.Logger import logger
from Unit.FileTool import loadConfig,writeConfig
from Unit.Redis import redisHelper
from Unit.DeviceInfo import createUserKey
from Api.GoogleCaptcha import get_response,create_task,GoogleCaptcha
from Api.Ipinfo import getIP
from Api.ProxyApi import allocateIP,whiteAdd,changeKeyTo1,createRealSocks5

changeKeyTo1()
fileLock = threading.Lock()
canRun = True
userKeys = []
loadPath = f'./tmpaccount/succeed.txt'
finishPath = f'./tmpaccount/gtoken.txt'
failPath = './tmpaccount/fail.txt'

def setCanRun(value):
    global canRun
    canRun = value


def appendFile(filepath,text):
    fileLock.acquire()
    with open(filepath,'a+') as f:
        f.write(text)
    fileLock.release()

def loadGoogleAccount(onceCount = 20):
    '''读取谷歌账号'''
    if not os.path.exists(loadPath):
        writeConfig([],loadPath)
    logger.info(f"读取 {loadPath} 下的账号")


    accounts = loadConfig(loadPath)
    if len(accounts) == 0:
        return []
    else:
        if len(accounts) > onceCount:
            writeConfig(accounts[onceCount:],loadPath)
            accounts = accounts[:onceCount]
        else:
            writeConfig([],loadPath)
    return accounts

def getOneUserInfo(accounts:[Account]):
    account = accounts.pop()
    return account.returnUserInfo(saveSocks5=False)

def resetUserInfo(accounts:[Account],user:UserInfo):
    accounts.append(Account(f'{user.email}|{user.pwd}|{user.googleToken}'))

# 自定义工作线程
class WorkThread(threading.Thread):
    def __init__(self, userInfo,callBack,threadPool):
        threading.Thread.__init__(self)
        self.userInfo = userInfo
        self.name = userInfo.email or userInfo.userKey
        self.callBack = callBack
        self.threadPool = threadPool
        
    def run(self):
        threadPool = self.threadPool
        if threadPool:
            threadPool.acquire()
        u1 = self.userInfo
        logger.info("开启线程： " + self.name)
        if self.callBack:
            if self.callBack(u1):
                setCanRun(False)
        if threadPool:
                    threadPool.release()
    
    

# 多线程运行
def threadRun(path,workCallBack,endCallBack,isThread=True,threadRunCount=100):
    threadPool = threading.BoundedSemaphore(threadRunCount)

    setCanRun(True)

    sock5List = {}
    with open(path) as lines:
        accounts = lines.readlines()
        for account in accounts:
            account = account.strip('\n')
            if len(account):
                user = Account(account)
                sock5List[user.s5] = user

    newAccounts = loadConfig(path)

    for account in sock5List:
        user = sock5List[account].returnUserInfo(saveSocks5=False)
        user.userKey = createUserKey()
        workCallBack(user)
        if not canRun:
            break
            
    # threads = []
    # try:
    #     account = random.choice(newAccounts)
    #     user = account.returnUserInfo()
    #     user.userKey = createUserKey()
    #     if workCallBack(user):
    #         setCanRun(False)
    # except Exception as e:
    #     print(e)
    #     endCallBack(None)
    # while canRun:
    #     account = random.choice(newAccounts)
    #     user = account.returnUserInfo()
    #     user.userKey = createUserKey()
    #     if isThread:
    #         thread = WorkThread(user,workCallBack,threadPool)
    #         thread.start()
    #         threads.append(thread)
    #     else:
    #         if workCallBack(user):
    #             break

    
    # writeConfig(accounts,path)
    endCallBack([])


def registerWithDynamicProxy(isV2=True):
    logger.info("动态ip注册 %s" % (isV2 and "V2模式" or "V1模式"))
    accounts = []
    g = GoogleCaptcha(accounts,3)
    g.setGlobal()
    succeedCount = 0
    # ip = getIP()
    # logger.info("本地ip %s" % ip)
    # if not ip:
    #     raise Exception("获取IP失败")
    
    # if not whiteAdd(ip):
    #     raise Exception("设置白名单失败")

    recaptchaRes = g.getOneToken()
    gt = time.time()
    accounts += loadGoogleAccount()
    while True:
        if len(accounts) == 0:
            accounts += loadGoogleAccount()
            if len(accounts) == 0:
                logger.info("账号已经用完")
                time.sleep(30)
                continue

        if recaptchaRes is None:
            recaptchaRes = g.getOneToken()

        s5 = allocateIP()
        logger.info("获取到代理:%s" % s5)
        if s5 is None:
            logger.error("代理异常")
            return
        s5Ip = s5.split(":")[0]
        if redisHelper.getIPBan(s5Ip):
            logger.info("ip受限 休息5秒")
            time.sleep(5)
            continue
        t = time.time()
        if s5:
            for i in range(2):
                u1 = None
                if len(accounts) > 0:
                    u1 = getOneUserInfo(accounts)
                    u1.socks5=createRealSocks5(s5)
                else:
                    accounts += loadGoogleAccount()
                    if len(accounts) == 0:
                        logger.info("msg:账号已经用完")
                        break
                    else:
                        u1 = getOneUserInfo(accounts)
                        u1.socks5=createRealSocks5(s5)

                try:
                    if isV2 and recaptchaRes is None:
                        
                        currentTime = time.time()
                        recaptchaRes = g.getOneToken()
                        gt = time.time()
                        # logger.info("获得谷歌验证 耗时%d" % (time.time() - currentTime))
                    if time.time() - t > (90 + i * 5):
                        redisHelper.setIPBan(s5Ip)
                        logger.info("代理超时")
                        resetUserInfo(accounts,u1)
                        break
                    if isV2 and time.time() - gt > 110:
                        logger.info("谷歌验证超时")
                        recaptchaRes = None
                        resetUserInfo(accounts,u1)
                        continue
                    if u1.googleRegister(token = recaptchaRes):
                        recaptchaRes = None
                        succeedCount += 1
                        u1.log(f"注册成功:{u1.email} 累计成功:{succeedCount}次")
                        appendFile(finishPath, f'{u1.email}----{u1.pwd}----{u1.token}\n')
                        try:
                            u1.enter()
                        except Exception as e:
                            pass
                            # u1.newAccountEvent()
                    else:
                        redisHelper.setIPBan(s5Ip)
                        logger.info("代理超时")
                        resetUserInfo(accounts,u1)
                        break
                except UserError as e:
                    if e.errorCode == 7:
                        redisHelper.setIPBan(s5Ip)
                        logger.info("ip受限 换ip")
                        resetUserInfo(accounts,u1)
                        break
                    elif e.errorCode == -3:
                        redisHelper.setIPBan(s5Ip)
                        resetUserInfo(accounts,u1)
                        logger.info("s5异常")
                    elif e.errorCode == 34:
                        logger.info("谷歌token超时")
                        appendFile(failPath, f'{u1.email}|{u1.pwd}|\n')
                    elif e.errorCode == 41:
                        logger.info("被系统删除")

                    elif e.errorCode == 32 or e.errorCode == 6:
                        logger.error("傻逼官方又关注册了")
                        endCallBack(None)
                        exit(0)
                except Exception as e:
                    logger.info("注册失败:%s" % e)
                    
            redisHelper.setIPBan(s5Ip)

        else:
            logger.info("获取ip异常")
            time.sleep(10)

def workCallBack(u1:UserInfo):
    try:
        if u1.login(isRegister=True):
            u1.log("注册成功 %s" % u1.userKey)
            redisHelper.saveUserKey(u1.userKey)
            userKeys.append(u1)
    except UserError as e:
        if e.errorCode == 6:
            # 上限了
            u1.log(e.message)
            setCanRun(False)
        elif e.errorCode == 7:
            u1.log(e.message)
        else:
            u1.errorLog(e,exc_info=True)
    except Exception as e:
        u1.errorLog(e,exc_info=True)

def endCallBack(accounts):
    print("注册结束 本次共注册 %d 个账号" % len(userKeys))
    writeConfig(userKeys,"./guests.txt")


def main():
    threadRun("./socks5.txt", workCallBack, endCallBack,threadRunCount=10,isThread=False)

if __name__ == '__main__':
    logger.info("启动脚本")
    # registerGuest()
    # main()
    import sys
    argv = sys.argv
    registerWithDynamicProxy()