# /bin/env python3
# -*- coding: UTF-8 -*-
'''
功能描述：自动检测驻守兵种
编写人：darkedge
编写日期：2022年04月26日
   
'''


from Api.UserInfo import UserInfo
from Api.DataRecord import dataDisplay
import time
import random
import threading
import json
from Unit.FileTool import loadS5List,loadShrineIds
from Unit.Logger import logger
from Unit.Redis import redisHelper,tzDate
from Unit.WorkThread import WorkThread
import asyncio
import requests
import sys,os
import fire
import datetime,pytz
from Api.FlaskHelper import requestSuperGo
from concurrent.futures import ThreadPoolExecutor

isDebug = True if sys.gettrace() else False

def goback(moId,kingdomId):
    resCount = requestSuperGo('', kingdomId, moId, count=1)
    if resCount > 0:
        logger.debug(f"撤回{kingdomId} {moId} 成功")
    else:
        logger.debug(f"撤回{kingdomId} {moId} 失败")

def printSingle(user:UserInfo,v):
    troopStr = " ".join([s for s in v["troopList"]])
    redStr = user.redString(v["worldId"])
    isHighStr = v["lord"] > 45 and user.blueString("高战") or "普通"
    isRunStr = v["status"] == 1 and user.yellowString("配套") or "行军"
    return f'\t[{redStr}][{isRunStr}]{v["name"]}\t{isHighStr} {troopStr}'


def doSome(user:UserInfo,showAllList:list):
    inputValue = input(f'显示所有数据?(y/n):')
    if inputValue and inputValue.lower() == 'y':
        prints = []
        for i in range(len(showAllList)):
            v = showAllList[i]
            prints.append(f'{i}{printSingle(user, v)}')
        print("\t\n".join(prints))
        
        inputValue = input(f'是否要踢人(输入编号空格 不踢人直接回车):')
        if len(inputValue):
            serialNumbers = inputValue.split(" ")
            if len(serialNumbers) > 0:
                print("即将剔除以下目标:")
                prints = []
                for i in serialNumbers:
                    v = showAllList[int(i)]
                    prints.append(f'{i}{printSingle(user, v)}')
                print("\t\n".join(prints))
                inputValue = input(f'确认(y/n):')
                if inputValue and inputValue.lower() == 'y':
                    print("处理中")
                    gobackPool = ThreadPoolExecutor(max_workers=5,thread_name_prefix="goback")
                    for i in serialNumbers:
                        v = showAllList[int(i)]
                        moId = v["id"]
                        kingdomId = v["kingdomId"]
                        gobackPool.submit(goback, moId, kingdomId)
                    gobackPool.shutdown(wait=True)
                    print("处理完成")

        inputValue = input(f'继续查询?(y/n):')
        if inputValue.lower() == 'n':
            return True

    return False

    # [print(f'{v["name"]} {" ".join([i for i in v["troopList"]])}') for v in showAllList]
    
def printInfo(user:UserInfo,foId,loc):
    if isDebug:
        user.debugToConsole()
    while not user.isInvalid:
        t = time.time()
        value,showAllList = user.monitoringShrineSupportList(foId,showAll=True)
        if value:
            os.system("clear")
            print(f"{loc}\n",value,"\n\n",tzDate().strftime("%m-%d_%H:%M:%S"))
            if doSome(user, showAllList):
                break
        t2 = time.time() - t
        if t2 < 3:
            time.sleep(3 - t2)
        
def main(loc, token):
    '''
    loc: 坐标
    token: token
    '''
    loc = [int(loc[0]), int(loc[1]), int(loc[2])]
    # print(loc[0])
    # loc = [int(v) for v in loc.split(",")]
    user = UserInfo(f'{random.randint(1, 99999999)}@safe.com',
                    token=token, socks5=random.choice(loadS5List()))
    if len(token) == 36:
        user = UserInfo(userKey=token, socks5=random.choice(loadS5List()))

    locKey = f'foId_{loc[0]}_{loc[1]}_{loc[2]}'
    foIds = loadShrineIds()
    foId = redisHelper.get(locKey)
    if not foId:
        if foIds:
            foId = foIds.get(locKey)
            user.log(f'读取本地foIds 当前Id:{foId or "无"}')
        else:
            user.log(f'ws获取')
    else:
        user.log("缓存获取")

    if user.isLogin:
        user.initLog(needTestS5=False)
        user.enter()
    else:
        user.login()
        # user.wsWithKingdomApp(isDebug=True)
    if not user.allianceId:
        user.wsWithKingdomApp(isDebug=True)
        user.tryJoinAllianceBySystem()

    if not foId:
        fields = user.wsGetFields(loc, more=True)
        if fields:
            builds = [20400401,20600101,20600102]
            for key in fields:
                if not foId:
                    if key in builds or key >= 20700101:
                        values = fields[key]
                        for value in values:
                            if loc == value.get("loc"):
                                foId = value.get("_id")
                                redisHelper.set(locKey, foId)
                                break

    if foId:
        threads = []
        for i in range(1):
            t = threading.Thread(target=printInfo, args=(user,foId,loc),daemon=True)
            threads.append(t)
            t.start()
        for t in threads:
            t.join()
            
    else:
        user.log(f'没有找到可以对应建筑的id')


if __name__ == '__main__':
    try:
        if isDebug:
            print("debug")
            token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJfaWQiOiI2MmI0ZmZmMWUxYmEwMTZmMWJjMjA1ZjUiLCJraW5nZG9tSWQiOiI2MmI0ZmZmMWUxYmEwMTZmMWJjMjA1ZjYiLCJ2ZXJzaW9uIjoxNDYyLCJidWlsZCI6MCwicGxhdGZvcm0iOiJpb3MiLCJ0aW1lIjoxNjU2MzA5ODYzNzM2LCJjbGllbnRYb3IiOiIwIiwiaWF0IjoxNjU2MzA5ODYzLCJleHAiOjE2NTY5MTQ2NjMsImlzcyI6Im5vZGdhbWVzLmNvbSIsInN1YiI6InVzZXJJbmZvIn0.Zh-LcApQ8zaWsClbjPDf5ygXsAdNZ33E7oZloQUzCnA"
            loc = [100001,1400,1600]
            main(loc,'0e730cd5-5954-7892-b0eb-7d0a43bf830b')
        else:
            fire.Fire(main)
    except KeyboardInterrupt:
        pass
