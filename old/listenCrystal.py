# /bin/env python3
# -*- coding: UTF-8 -*- 
'''
功能描述：监听水晶总量
编写人：darkedge
编写日期：2022年07月08日
   
'''

import json
import time,random,threading,datetime
from urllib.parse import quote
import functools
from Model.Kingdom import Kingdom
from Model.UserError import UserError

from Api.DataRecord import currentHour
from Api.UserInfo import UserInfo
from Api.User.WebSocket import fieldNames,charmCodes
from Unit.FileTool import loadS5List,loadSelfToken
from Unit.Logger import logger
from Unit.Redis import redisHelper
from Unit.WorkThread import WorkThread
from Unit.LandZoneUnit import returnAdjacentCrystalLands,crystalLands,MaxSixThreadZoneSize

import asyncio
import requests
import sys
import schedule
import fire

from tqdm import tqdm

def search(worldId,users):
    loc = [worldId,1024,1024]
    zones = crystalLands
    crystalList = {}
    def fieldCallBack(object):
        loc = object.get("loc")
        crystalList[f'{loc[1]}_{loc[2]}'] = object

    t1 = time.time()
    for index in range(len(users)):
        user = users[index]
        user.loc = loc
        user.searchCrystalWithZoneSize(loc,zones=zones[index*MaxSixThreadZoneSize:(index+1)*MaxSixThreadZoneSize],callBack=fieldCallBack)
    t2 = time.time()

    crystalLevelList = {5:0,4:0,3:0,2:0,1:0}
    for key in crystalList:
        field = crystalList[key]
        level = field.get("level")
        crystalLevelList[level] += 1
    levelStr = '\n'.join([f'{k}级:{v}' for k,v in crystalLevelList.items()])
    printStr = f'\n{worldId}区域水晶统计耗时:{round(t2-t1,2)} 总数:{len(crystalList)}\n{levelStr}'
    logger.info(printStr)


def main(worldId=20):
    from Api.FlaskHelper import requestToken,requestTokenA
    s5List = loadS5List()
    tasks = UserInfo().runTasks([requestTokenA() for v in range(4)])
    tokens = [task.result() for task in tasks[0]]
    if not tokens:
        print("获取token失败")
        exit(0)
    users = [UserInfo(f"king_{random.randint(1, 9999)}",token=token,socks5=random.choice(s5List)) for token in tokens]
    search(worldId, users)
    schedule.every(5).minutes.do(search,worldId,users)
    while True:
        schedule.run_pending()
        time.sleep(1)
    

if __name__ == '__main__':
    try:
        fire.Fire(main)
    except KeyboardInterrupt:
        pass