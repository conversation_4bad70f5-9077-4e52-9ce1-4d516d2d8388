# /bin/env python3
# -*- coding: UTF-8 -*- 

import json
import time,random,threading
from urllib.parse import quote
import functools
from Model.Kingdom import Kingdom
from Model.UserError import UserError

from Api.UserInfo import UserInfo,Enum
from Api.User.WebSocket import fieldNames
from Unit.FileTool import loadS5List
from Unit.Logger import logger
from Unit.Redis import redisHelper
from Unit.WorkThread import WorkThread
from Unit.EthUnit import EthService,modifyWeb3Socks5,changeGasPrice
from Unit.MarketUnit import requestRefreshMetaData

import asyncio
import requests
import sys

from tqdm import tqdm

from web3 import Account

itemCodes = [
    [********,********,********,Enum.ITEM_CODE_FOOD_50M,Enum.ITEM_CODE_FOOD_100M,],#粮食
    [********,********,********,Enum.ITEM_CODE_LUMBER_50M,Enum.ITEM_CODE_LUMBER_100M,],#木头
    [********,********,********,Enum.ITEM_CODE_STONE_50M,Enum.ITEM_CODE_STONE_100M,],#石头
    [********,********,********,Enum.ITEM_CODE_GOLD_50M,Enum.ITEM_CODE_GOLD_100M,],#黄金
]

payEService:EthService = None
payPk = '0xd274e73b9662a46df76f7f9a149a9c66fd841a7956f3402192c15b17bbd41b1b'

def loadPayEService():
    global payEService
    payEService = EthService(payPk)

def tryWalletWork(u1:UserInfo,nfts:list,privateKey=None):
    unmints = []
    minteds = [[],[],[],[]]
    ethService = None
    for nft in nfts:
        status = nft['status']
        
        if status == 4:
            itemCode = nft['itemCode']
            for i in range(len(itemCodes)):
                if itemCode in itemCodes[i]:
                    minteds[i].append(nft)
                    break
            # minteds.append(nft)
        elif status == 0:
            unmints.append(nft)

    itemNames = ["粮食","木头","石头","黄金"]
    string = ' '.join([f'{itemNames[i]}{len(minteds[i])}个' for i in range(len(itemCodes))])
    u1.log(f"可发行数量:{len(unmints)},可使用数量:{string}")
    if privateKey is None:
        inputValue = input(f"请输入公钥:{u1.publicKey}对应的私钥:")
        if len(inputValue) != 66 and len(inputValue) > 0:
            raise UserError("私钥长度不对")
        privateKey = inputValue
    if len(privateKey) > 0:
        ethService = EthService(privateKey)

    inputValue = input("请选者模式(1发行 2使用 3market刷新 4解绑钱包):")
    if inputValue == '1':
        if not ethService.addressIsEqual(u1.publicKey):
            raise UserError(f"私钥不匹配 {ethService.address}")
        for nft in tqdm(unmints):
            nftItemId = nft.get("_id")
            mintCode = nft.get("mintCode")
            txData = u1.mintNft(nftItemId)
            if txData:
                txHash = ethService.mint(json.loads(str(txData)))
                if txHash:
                    if u1.mintTx(mintCode, txHash):
                        pass
                        # print(f"发行成功:{txHash}")
            else:
                print("发行失败")
    elif inputValue == '2':
        if not ethService.addressIsEqual(u1.publicKey):
            raise UserError(f"私钥不匹配 {ethService.address}")
        useItems = []
        inputValue = input("请输入使用的种类(0粮食1木头2石头3黄金4全部):")
        if inputValue.isdigit():
            index = int(inputValue)
            if index > 3:
                for nfts in minteds:
                    [useItems.append(nft) for nft in nfts]
            else:
                nfts = minteds[index]
                [useItems.append(nft) for nft in nfts]
            
        inputValue = input(f"可用数量:{len(useItems)}请输入使用数量(默认99999):")
        maxCount = 99999
        if inputValue.isdigit():
            maxCount = int(inputValue)

        currentCount = 0
        for nft in tqdm(useItems[:maxCount]):
            currentCount += 1
            if currentCount > maxCount:
                break
            tokenId = nft.get("tokenId")
            txData = u1.useNft(tokenId)
            if txData:
                txHash = ethService.mint(json.loads(str(txData)))
                # if txHash:
                    # print(f"使用成功:{txHash}")
    elif inputValue == '3':
        print("开始刷新所有nft")
        tokens = []
        for nfts in minteds:
            [tokens.append(str(nft["tokenId"])) for nft in nfts]
        s5List = loadS5List()
        tasks = []
        for tokenId in tokens:
            tasks.append(requestRefreshMetaData(tokenId,random.choice(s5List)))
            # if len(tokens) > 10:
            #     u1.runTasks(tasks)
            #     tasks.clear()
        u1.runTasks(tasks)
        # u1.runTasks([requestRefreshMetaData(tokenId,random.choice(s5List)) for tokenId in tokens])
        print("刷新完成")
    elif inputValue == "4":
        if u1.unlinkWallet():
            print("解绑成功")
            if len(privateKey) > 0 and u1.tryLinkWallet(privateKey):
                print("绑定成功")
                u1.walletNetworkChange()
                res = u1.nftItemList()
                if res:
                    u1.publicKey = res.get("publicKey")
                    tryWalletWork(u1, res.get("items"),privateKey=privateKey)
        else:
            print("解绑失败")
    else:
        u1.log("错误选项")

def checkBalance(p):
    if p.balance() < 0.1:
        if payEService.transfer(0.5,p.address,chainId=137):
            for i in range(10):
                if p.balance() < 0.1:
                    print("未查到有效余额，等待重试")
                    time.sleep(5)


def loadPrivates():
    lines = []
    try:
        with open("./tmpaccount/private.txt", 'r') as f:
            lines = f.readlines()
            if len(lines) == 0:
                raise FileExistsError("无效私钥")
    except FileExistsError as e:
        raise FileExistsError("private.txt不能存在")
    return lines

def writeSucceedPrivateKey(k):
    lines = []
    with open("./tmpaccount/private.txt", 'r') as f:
        for line in f.readlines():
            if line.find(k) == -1:
                lines.append(line)
    with open("./tmpaccount/private.txt", 'w') as f:
        f.writelines(lines)

    with open("./tmpaccount/sp.txt", "a+") as f:
        f.write(f'{k.strip()}\n')

    print(f"写入有效私钥:{k}")

def writeFailPrivateKey(k):
    lines = []
    with open("./tmpaccount/private.txt", 'r') as f:
        for line in f.readlines():
            if line.find(k) == -1:
                lines.append(line)
    with open("./tmpaccount/private.txt", 'w') as f:
        f.writelines(lines)

    with open("./tmpaccount/fsp.txt", "a+") as f:
        f.write(f'{k.strip()}\n')
    print(f"写入无效私钥:{k}")

def checkNFT(nfts):
    unmints = []
    minteds = [[],[],[],[]]
    for nft in nfts:
        status = nft['status']
        
        if status == 4:
            itemCode = nft['itemCode']
            for i in range(len(itemCodes)):
                if itemCode in itemCodes[i]:
                    minteds[i].append(nft)
                    break
            # minteds.append(nft)
        elif status == 0:
            unmints.append(nft)

    itemNames = ["粮食","木头","石头","黄金"]
    string = ' '.join([f'{itemNames[i]}{len(minteds[i])}个' for i in range(len(itemCodes))])
    print(f"可发行数量:{len(unmints)},可使用数量:{string}")
    realMinteds = []
    for mints in minteds:
        for mint in mints:
            realMinteds.append(mint)
    return unmints,realMinteds

def work(user:UserInfo,privateKey:str):
    p = EthService(privateKey.strip())
    p.info("")
    user.tryLinkWallet(p.privateKey)
    user.randomSleep(2)
    user.walletNetworkChange()
    user.randomSleep(2)
    res = user.nftItemList()
    if res:
        items = res.get("items")
        unmints,minteds = checkNFT(items)
        if unmints or minteds:
            if unmints:
                checkBalance(p)
                if p.balance() > 0.1:
                    user.log(f"开始发行{len(unmints)}")
                    for nft in tqdm(unmints):
                        nftItemId = nft.get("_id")
                        mintCode = nft.get("mintCode")
                        txData = user.mintNft(nftItemId)
                        if txData:
                            txHash = p.mint(json.loads(str(txData)))
                            if txHash:
                                if user.mintTx(mintCode, txHash):
                                    user.debuglog(f"发行成功:{txHash}")
                        else:
                            print("发行失败")
                            exit(0)
                    user.randomSleep(2,3,msg="准备切换钱包")
                else:
                    print("异常余额")
                    exit(0)
            writeSucceedPrivateKey(privateKey)
        else:
            writeFailPrivateKey(privateKey)

    else:
        print("异常？")
        exit(0)


def main():
    logger.info("自动批量发行脚本")
    token = None
    email = None
    pwd = None
    if len(sys.argv) > 1:
        if len(sys.argv) > 2:
            email = sys.argv[1]
            pwd = sys.argv[2]
        else:
            token = sys.argv[1]
    token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.H7yuob4Dll6YxNBlZxktNsl4uOKpj0EmWDOiMNwKkDs'
    if not token and not email:
        inputValue = input("请输入token:")
        token = inputValue
    if not email:
        email = f'{random.randint(1, 9999999)}@token.cn'

    changeGasPrice(100,120)
    pkeys = loadPrivates()
    u1 = UserInfo(email,pwd,token=token,socks5=random.choice(loadS5List()))
    u1.isWebDevice = True
    if pwd:
        u1.login()
    else:
        u1.initLog()

    u1.wsWithKingdomApp(isDebug=True)
    modifyWeb3Socks5(u1.socks5)
    print(f"私钥{len(pkeys)}")
    loadPayEService()
    for k in tqdm(pkeys):
        work(u1,k)

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        pass
