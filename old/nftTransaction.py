# /bin/env python3
# -*- coding: UTF-8 -*- 

import json
import time,random,threading
from urllib.parse import quote
import functools
from Model.Kingdom import Kingdom
from Model.UserError import UserError

from Api.UserInfo import UserInfo,Enum
from Api.User.WebSocket import fieldNames
from Unit.FileTool import loadS5List
from Unit.Logger import logger
from Unit.Redis import redisHelper
from Unit.WorkThread import WorkThread
from Unit.EthUnit import EthService,modifyWeb3Socks5,changeGasPrice
from Unit.MarketUnit import requestRefreshMetaData
from Unit.Contract.LOKIContract import LOKIContract
import asyncio
import requests
import sys

from tqdm import tqdm

from web3 import Account

itemCodes = [
    [********,********,********,Enum.ITEM_CODE_FOOD_50M,Enum.ITEM_CODE_FOOD_100M,],#粮食
    [********,********,********,Enum.ITEM_CODE_LUMBER_50M,Enum.ITEM_CODE_LUMBER_100M,],#木头
    [********,********,********,Enum.ITEM_CODE_STONE_50M,Enum.ITEM_CODE_STONE_100M,],#石头
    [********,********,********,Enum.ITEM_CODE_GOLD_50M,Enum.ITEM_CODE_GOLD_100M,],#黄金
]

payEService:EthService = None
payPk = '0xd274e73b9662a46df76f7f9a149a9c66fd841a7956f3402192c15b17bbd41b1b'
lokContract = LOKIContract()

sumCount = 0
def addSumCount(count):
    global sumCount
    sumCount += count

def loadPayEService():
    global payEService
    payEService = EthService(payPk)
    print(f"支付公钥:{payEService.address}")

def checkBalance(p):
    if p.balance() < 0.1:
        if payEService.transfer(0.5,p.address,chainId=137):
            for i in range(10):
                if p.balance() < 0.1:
                    print("未查到有效余额，等待重试")
                    time.sleep(5)


def loadPrivates():
    lines = []
    try:
        with open("./tmpaccount/private.txt", 'r') as f:
            lines = f.readlines()
            if len(lines) == 0:
                raise FileExistsError("无效私钥")
    except FileExistsError as e:
        raise FileExistsError("private.txt不能存在")
    return lines

def writeSucceedPrivateKey(k):
    lines = []
    with open("./tmpaccount/private.txt", 'r') as f:
        for line in f.readlines():
            if line.find(k) == -1:
                lines.append(line)
    with open("./tmpaccount/private.txt", 'w') as f:
        f.writelines(lines)

    with open("./tmpaccount/sp.txt", "a+") as f:
        f.write(f'{k.strip()}\n')

    print(f"写入有效私钥:{k}")

def writeFailPrivateKey(k):
    lines = []
    with open("./tmpaccount/private.txt", 'r') as f:
        for line in f.readlines():
            if line.find(k) == -1:
                lines.append(line)
    with open("./tmpaccount/private.txt", 'w') as f:
        f.writelines(lines)

    with open("./tmpaccount/fsp.txt", "a+") as f:
        f.write(f'{k.strip()}\n')
    print(f"写入无效私钥:{k}")

def checkNFT(nfts):
    unmints = []
    minteds = [[],[],[],[]]
    for nft in nfts:
        status = nft['status']
        
        if status == 4:
            itemCode = nft['itemCode']
            for i in range(len(itemCodes)):
                if itemCode in itemCodes[i]:
                    minteds[i].append(nft)
                    break
            # minteds.append(nft)
        elif status == 0:
            unmints.append(nft)

    itemNames = ["粮食","木头","石头","黄金"]
    string = ' '.join([f'{itemNames[i]}{len(minteds[i])}个' for i in range(len(itemCodes))])
    print(f"可发行数量:{len(unmints)},可使用数量:{string}")
    realMinteds = []
    for mints in minteds:
        for mint in mints:
            realMinteds.append(mint)
    return unmints,realMinteds


def work(privateKey:str):
    p = EthService(privateKey.strip())
    count = lokContract.getBalance(p.address)
    if count > 0:
        tokenId = lokContract.tokenOfOwnerByIndex(p.address,0)
        p.info(tokenId)
        data = lokContract.transferFrom(p.address,'******************************************',tokenId)
        # p.info(data)
        tx = p.mint(data)
        p.info(f"tx:{tx}")
    elif p.balance() > 0.1:
        p.transfer(0,'******************************************')

def main():
    logger.info("自动批量发行脚本")

    changeGasPrice(100,120)
    pkeys = loadPrivates()
    # modifyWeb3Socks5('127.0.0.1:1087')
    print(f"私钥{len(pkeys)}")
    loadPayEService()
    for k in tqdm(pkeys):
        try:
            work(k)
        except Exception as e:
            logger.error(e,exc_info=True)
    logger.info(f"总计{sumCount}个")
if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        pass
