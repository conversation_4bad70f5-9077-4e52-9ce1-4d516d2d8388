#!/usr/bin/python3
# -*- coding: utf-8 -*-
from Api.UserInfo import UserInfo
from Model.Kingdom import Kingdom
from Model.Account import Account
from Model.UserError import UserError
from Unit.FileTool import loadConfig,writeConfig,loadS5List
from Unit.Logger import logger
from Unit.WorkThread import WorkThread
from Unit.Redis import redisHelper
import threading,time,random
import sys,os
from concurrent.futures import ThreadPoolExecutor
os.makedirs("tmpaccount",exist_ok=True)
import fire
isDebug = True if sys.gettrace() else False

# loginPool = ThreadPoolExecutor(max)
class ReleaseCenter(object):
    # 操作中心
    def __init__(self):
        self.users:[Account] = None
        self.zone = None
        self.accountPath = None
        self.succeeds = []
        self.fails = []
        self.workPool = ThreadPoolExecutor(max_workers=5,thread_name_prefix='work_')
        self.defPool = ThreadPoolExecutor(max_workers=5,thread_name_prefix='def_')

    def run(self,zone=20,accountPath="release"):
        self.zone = zone
        self.accountPath = accountPath
        self.loadAccounts()
        
    def loadAccounts(self):
        count = 0
        userRaws = []
        loggerNamePrefix = f'{self.accountPath}_{self.zone}'
        with open(f'tmpaccount/{loggerNamePrefix}f.txt', 'r') as f:
            lines = f.readlines()
            if len(lines) < 1:
                raise UserError("文件异常")
            else:
                userRaws = [v.strip() for v in lines]
                count = len(userRaws)

        s5List = loadS5List()
        if len(s5List) < 10:
            logger.info("哥哥你太抠了吧")
            return

        if len(userRaws) < count:
            raise UserError("账号数量不足")

        users = []
        for userKey in userRaws:
            if userKey is None:
                continue
            if len(userKey.split("_")) > 1 and len(userKey.split("|")) != 2:
                continue
            
            if userKey == "None":
                continue

            if not userKey:
                continue
            # logger.info(f'key:{userKey}#')
            s5 = random.choice(s5List)
            user = None
            if len(userKey.split("|")) == 2:
                user = Account(f"{userKey}|{s5}")
            else:
                user = Account('',userKey=userKey,socks5=s5)
            if user:
                users.append(user)
                if len(users) >= count:
                    break
        self.users = users
        
if __name__ == '__main__':
    center = ReleaseCenter()
    if isDebug:
        center.run()
    else:
        fire(center.run)

    