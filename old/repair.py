# /bin/env python3
# -*- coding: UTF-8 -*- 

import json
import time,random
from Model.UserError import UserError
from Api.UserInfo import UserInfo
from Unit.Logger import logger
from Unit.Redis import redisHelper
from Unit.WorkThread import threadRun

channelKey = "repair"
path = "./raccount.txt"
checkList = {}

def repairWork(u1:UserInfo):
    try:
        time.sleep(random.randint(1,50))
        if u1.isLogin is False:
            u1.login()

        if u1.enter():
            if u1.loc[0] == 22:
                redisHelper.removeChannelKey(channelKey, redisHelper.repairs, u1.email)
                u1.errorLog("22区")
                return
            redisHelper.addRepairLoc(u1.loc)
            if not redisHelper.checkMainWithRepair(u1.loc):
                s = "渣渣居然不在主号盘边"
                
                u1.tryUseItems()
                if u1.canTeleport:
                    wallInfo = u1.wallInfo()
                    if wallInfo > 0.95:
                        value = redisHelper.repairs.get(u1.email)
                        redisHelper.removeChannelKey(channelKey, redisHelper.repairs, u1.email)
                        redisHelper.addChannelValue('teleport', redisHelper.teleports, u1.email,value)
                        u1.log("二飞回到小号列表")
                        return
                else:
                    u1.errorLog("渣渣可以丢了")
                    redisHelper.removeChannelKey(channelKey, redisHelper.repairs, u1.email)
                    return

            u1.dismissAllTroops()
            if u1.tryRepairWall():
                redisHelper.removeChannelKey(channelKey, redisHelper.repairs, u1.email)
            else:
                u1.claimMail()
                sum = 0
                for resource in u1.resources:
                    sum += int(resource)
                oldSum = checkList.get(u1.email)
                if oldSum is None:
                    checkList[u1.email] = sum
                else:
                    if oldSum == sum:
                        if oldSum >= 0:
                            selfInfo = u1.info(u1.loc[1:])
                            isShield = u1.checkShield(selfInfo)
                            if isShield:
                                u1.log("需要破盾")
                                if u1.tryBreakShield(u1.loc):
                                    checkList[u1.email] = -1
                            else:
                                checkList[u1.email] = -1
                        else:
                            oldSum -= oldSum
                            checkList[u1.email] = oldSum
                            if oldSum < -3:
                                u1.errorLog("已经3轮没人打我了！")


    except UserError as e:
        if e.errorCode >= 1:
            redisHelper.removeChannelKey(channelKey, redisHelper.repairs, u1.email)
        # u1.tryHarvestAll()

def endWork(accounts):
    t = 60 * 30
    logger.info ("一轮结束 休息%d分钟" % (t / 60))
    time.sleep(t)
    redisHelper.clearAll()

if __name__ == '__main__':
    logger.info("启动脚本")
    threadRun(path,channelKey,redisHelper.repairs,repairWork,endWork)