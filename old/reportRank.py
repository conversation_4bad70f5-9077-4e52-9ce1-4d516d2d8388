# /bin/env python3
# -*- coding: UTF-8 -*- 
'''
功能描述：举报排名
编写人：darkedge
编写日期：2022年05月11日
   
'''

import json
import time,random,threading,datetime
from urllib.parse import quote
import functools
from Model.Kingdom import Kingdom
from Model.UserError import UserError

from Api.DataRecord import currentHour
from Api.UserInfo import UserInfo
from Api.User.WebSocket import fieldNames,charmCodes
from Unit.FileTool import loadS5List,loadSelfToken
from Unit.Logger import logger
from Unit.Redis import redisHelper
from Unit.WorkThread import WorkThread
from Unit.LandZoneUnit import returnAdjacentCrystalLands

import asyncio
import requests
import sys
import schedule

from tqdm import tqdm

def main():
    logger.info("自动举报脚本")
    token = None
    email = None
    pwd = None
    ids = {}
    if len(sys.argv) > 1:
        if len(sys.argv) > 2:
            email = sys.argv[1]
            pwd = sys.argv[2]
        else:
            token = sys.argv[1]
    if not token and not email:
        inputValue = input("请输入token:")
        token = inputValue
    if not email:
        email = f'{random.randint(1, 9999999)}@token.cn'
    u1 = None
    if token and len(token) == 36:
        u1 = UserInfo(userKey=token,socks5=random.choice(loadS5List()))
    else:
        u1 = UserInfo(email,pwd,token=token,socks5=random.choice(loadS5List()))
        if not pwd or len(pwd) == 0:
            u1.noWSUser = True

    if u1.noWSUser:
        u1.initLog()
    else:
        u1.login()

    minLevel = 30
    inputValue = input("请输入最低等级(默认30):")
    if inputValue and len(inputValue) > 0:
        minLevel = int(inputValue)
        
    # print(f"\n 举报等级{minLevel}\n")
    loc = [24,1024,1024]
    isAll = False
    inputValue = input("请输入要举报的目标坐标 默认(24,1024,1024),只输入大陆为全大陆:")
    if inputValue and len(inputValue) > 0:
        loc = inputValue.split(",")
        if len(loc) == 1:
            isAll = True
            loc = [int(loc[0]),1024,1024]
            print(f"举报{loc[0]}全大陆 等级:{minLevel}")

        loc = [int(v) for v in loc]

    u1.kingdomProfileMy()
    continueFlag = True
    locs = {
        f'{loc[1]}_{loc[2]}':1
    }
    
    while not u1.isInvalid and continueFlag:
        resList,resObjects = u1.searchFields([20300101],loc=loc,power=4,show=False)
        if resObjects:
            targetList = []
            for info in resObjects:
                level = info.get("level")
                if level < minLevel:
                    continue
                targetList.append(info)

            u1.log(f"目标数量:{len(targetList)}")
            for info in tqdm(targetList):
                level = info.get("level")
                occupied = info.get("occupied")
                id = occupied.get("id")
                if ids.get(id):
                    continue
                ids[id] = 1
                name = occupied.get("name")
                msgStr = ""
                if u1.kingdomReport(id):
                    msgStr = f"举报成功:"
                else:
                    msgStr = f"举报失败:"
                u1.randomSleep(2,3,msg=f"{msgStr}{name},等级:{level},id:{id}")
        
        continueFlag = isAll
        if isAll:
            loc = [loc[0]] + u1.rightLaw(loc[1:], locs)
            absValue = abs(loc[1] - loc[2])
            continueFlag = absValue < 1200

if __name__ == '__main__':
    main()
