from Unit.UserInfoHelper import searchAll,UserInfo,Enum
from Unit.LandZoneUnit import AllZoneOfPolygon,PolygonWithPoint
import time
import math

def main():
    user = UserInfo(userKey="5b9a6a3d-b71c-53d5-43e0-1a507329cdb0",socks5="jj:jj@156.255.0.22:20202",saveSocks5=True)
    loc = [1400,1400]
    polygon = PolygonWithPoint(*loc)
    zones = AllZoneOfPolygon(polygon)
    index = 0
    maxIndex = math.ceil(len(zones) / 54)

    if user.login():
        while not user.isInvalid:
            if user.checkWSWithKingdomApp():
                with user.requestLock:
                    pass
                user.debuglog("wsKing断开重连")
                user.trySetDeviceInfo()
                user.wsWithKingdomApp()
                time.sleep(5)
            
            currentZones = zones[index*54:(index+1)*54]
            index = (index + 1) % maxIndex
            datas = searchAll([user.token],[100004,*loc],logInfo=user.log,zones=currentZones,codes=Enum.OBJECT_RALLY_MONSTER_CODE_LIST)
            user.log(f'区域数量:{len(datas.get(Enum.OBJECT_CODE_SPARTOI,[]))}')
            user.randomSleep(60)
            
if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        pass