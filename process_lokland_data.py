#!/usr/bin/env python3
# -*- coding: UTF-8 -*-

"""
功能描述：从本地文件lokland_data.csv中读取landId，使用user.landInfo(landId)读取loka数据，最后写回csv
编写人：AI Assistant
编写日期：2025年8月1日
"""

import csv
import json
import time
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from Api.UserInfo import UserInfo
from Unit.Logger import logger


def process_lokland_data(input_file="lokland_data.csv", output_file="lokland_data_with_loka.csv", token=None, socks5=None):
    """
    处理lokland数据文件
    
    Args:
        input_file (str): 输入CSV文件路径
        output_file (str): 输出CSV文件路径
        token (str): 用户token
        socks5 (str): 代理设置
    """
    
    # 如果没有提供token，使用默认的测试token
    if not token:
        token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.vf38q-YMtay-5X0n_8vyaWfJEwAMCw951SIKHDJ-sXQ"
    
    if not socks5:
        socks5 = "127.0.0.1:1087"
    
    # 初始化用户
    logger.info("初始化用户连接...")
    user = UserInfo(token=token, socks5=socks5)
    user.loadEmailWithToken()
    user.initLog(needTestS5=False)
    
    processed_data = []
    success_count = 0
    failed_count = 0
    error_count = 0
    
    try:
        # 检查输入文件是否存在
        if not os.path.exists(input_file):
            logger.error(f"输入文件 {input_file} 不存在")
            return False
        
        # 读取CSV文件
        with open(input_file, 'r', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            headers = reader.fieldnames
            
            if not headers or 'land_id' not in headers:
                logger.error("CSV文件格式错误，缺少 'land_id' 列")
                return False
            
            # 添加新的列用于存储loka数据
            new_headers = list(headers) + ['loka_data', 'loka_amount', 'loka_status', 'process_time']
            
            logger.info(f"开始处理 {input_file} 文件")
            
            for row_num, row in enumerate(reader, 1):
                land_id = row['land_id']
                logger.info(f"处理第 {row_num} 行，landId: {land_id}")
                
                start_time = time.time()
                
                try:
                    # 调用landInfo获取loka数据
                    land_info = user.landInfo(land_id)
                    
                    if land_info:
                        # 提取loka相关信息
                        attributes = land_info.get('attributes', {})
                        storage = attributes.get('storage', {})
                        loka_amount = storage.get('loka', 0)
                        
                        # 存储完整的land_info数据（可选择性存储部分关键信息）
                        simplified_data = {
                            'landId': land_info.get('landId'),
                            'attributes': attributes,
                            'owner': land_info.get('owner'),
                            'sale': land_info.get('sale')
                        }
                        
                        row['loka_data'] = json.dumps(simplified_data, ensure_ascii=False)
                        row['loka_amount'] = loka_amount
                        row['loka_status'] = 'success'
                        
                        success_count += 1
                        logger.info(f"landId {land_id} 获取成功，loka数量: {loka_amount}")
                    else:
                        row['loka_data'] = ''
                        row['loka_amount'] = 0
                        row['loka_status'] = 'failed'
                        
                        failed_count += 1
                        logger.warning(f"landId {land_id} 获取失败")
                        
                except Exception as e:
                    logger.error(f"处理 landId {land_id} 时出错: {str(e)}")
                    row['loka_data'] = ''
                    row['loka_amount'] = 0
                    row['loka_status'] = 'error'
                    
                    error_count += 1
                
                # 记录处理时间
                process_time = time.time() - start_time
                row['process_time'] = f"{process_time:.2f}s"
                
                processed_data.append(row)
                
                # 添加延迟避免请求过快
                time.sleep(0.5)
                
                # 每处理10条记录输出一次进度
                if row_num % 10 == 0:
                    logger.info(f"已处理 {row_num} 条记录，成功: {success_count}, 失败: {failed_count}, 错误: {error_count}")
        
        # 写入新的CSV文件
        logger.info(f"开始写入结果到 {output_file}")
        with open(output_file, 'w', encoding='utf-8', newline='') as outfile:
            writer = csv.DictWriter(outfile, fieldnames=new_headers)
            writer.writeheader()
            writer.writerows(processed_data)
        
        logger.info(f"处理完成！")
        logger.info(f"总计处理: {len(processed_data)} 条记录")
        logger.info(f"成功: {success_count}, 失败: {failed_count}, 错误: {error_count}")
        logger.info(f"结果已保存到 {output_file}")
        
        return True
        
    except FileNotFoundError:
        logger.error(f"文件 {input_file} 不存在")
        return False
    except Exception as e:
        logger.error(f"处理文件时出错: {str(e)}")
        return False


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='处理LOK土地数据，获取loka信息')
    parser.add_argument('--input', '-i', default='lokland_data.csv', help='输入CSV文件路径')
    parser.add_argument('--output', '-o', default='lokland_data_with_loka.csv', help='输出CSV文件路径')
    parser.add_argument('--token', '-t', help='用户token')
    parser.add_argument('--socks5', '-s', help='代理设置，格式: host:port')
    
    args = parser.parse_args()
    
    logger.info("开始处理LOK土地数据...")
    success = process_lokland_data(
        input_file=args.input,
        output_file=args.output,
        token=args.token,
        socks5=args.socks5
    )
    
    if success:
        logger.info("处理完成！")
        sys.exit(0)
    else:
        logger.error("处理失败！")
        sys.exit(1)


if __name__ == "__main__":
    main()
