#!/usr/bin/expect

# 设置超时时间（秒）
set timeout 300

# 设置变量
set local_file "league.zip"
set remote_user "root"
set remote_host "************"
set remote_path "/root/"
set remote_unzip_path "/root/league/"
set remote_port "22"

# 输出开始信息
puts "开始上传文件..."

# 检查 SSH 密钥文件
set ssh_key "~/.ssh/id_ed25519"
if {![file exists $ssh_key]} {
    set ssh_key "~/.ssh/id_rsa"
    puts "使用 RSA 密钥..."
} else {
    puts "使用 ED25519 密钥..."
}

# 上传文件，使用SSH密钥认证
spawn scp -i $ssh_key -P $remote_port $local_file $remote_user@$remote_host:$remote_path$local_file
expect {
    "yes/no" {
        send "yes\r"
        exp_continue
    }
    timeout {
        puts "上传超时！"
        exit 1
    }
    eof {
        wait
    }
}

puts "文件上传完成"

# 等待一下确保文件完全上传
sleep 2

puts "开始解压文件..."

# 解压文件，使用SSH密钥认证
spawn ssh -i $ssh_key $remote_user@$remote_host -p $remote_port "unzip -o $remote_path$local_file -d $remote_unzip_path"
expect {
    "yes/no" {
        send "yes\r"
        exp_continue
    }
    timeout {
        puts "SSH连接超时！"
        exit 1
    }
    eof {
        wait
    }
}

# 获取当前时间并格式化输出
set current_time [clock format [clock seconds] -format "%Y-%m-%d %H:%M:%S"]
puts "部署完成时间: $current_time"
exit 0
