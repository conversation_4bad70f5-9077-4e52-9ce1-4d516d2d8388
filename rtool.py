# /bin/env python3
# -*- coding: UTF-8 -*- 
'''
功能描述：redis操作
编写人：darkedge
编写日期：2022年2月12日
   
'''
import random
from Unit.Redis import redisHelper
from Api.UserInfo import UserInfo,UserError
from Api.FlaskHelper import requestSaveUser
from Unit.FileTool import loadS5List
def dumpGuest(path):
    userkeys = redisHelper.allUserKey()
    users = []
    for userkey in userkeys:
        if len(userkey.split("_")) > 1:
            continue
        users.append(userkey)

    if len(users):
        with open(path,"w+") as f:
            for user in users:
                f.write(user + "\n")
        for v in users:
            redisHelper.removeUserKey(v)
    
    print(f"保存到{len(users)}个游客账号")

def loadGuest(path):
    with open(path) as f:
        lines = f.readlines()
        print(f"读取到{len(lines)}个游客账号")
        for line in lines:
            line = line.strip('\n')
            if len(line) == 36:
                redisHelper.saveUserKey(line)
    
# 发送游客号到洗脚城
def sendGuestToXJC():
    userkeys = redisHelper.allUserKey()
    s5List = loadS5List()
    users = []
    for userkey in userkeys:
        if len(userkey.split("_")) == 1:
            continue
        users.append(userkey.split("_")[0])
    successCount = 0
    for userKey in users:
        user = UserInfo(userKey=userKey,saveSocks5=True,socks5=random.choice(s5List))
        try:
            if user.login():
                if user.level >= 11:
                    if user.tryRandomAppleLink():
                        fakeIP = redisHelper.getFakeIp(userKey,user.kingdomId)
                        if requestSaveUser(user.appleSubId,user.deviceInfo,fakeIP,socks5=user.socks5):
                            redisHelper.removeUserKeyWithAll(userKey)
                            successCount += 1
                else:
                    print("等级:{user.level} 不足11级")
                    redisHelper.removeUserKeyWithAll(userKey)
                    redisHelper.saveUserKey(userKey)
        except UserError as e:
            if e.errorCode == -4:
                user.clearSelf()
            elif e.errorCode == 41:
                print(f"{userKey} 封了")
                redisHelper.removeUserKeyWithAll(userKey)
            else:
                user.errorLog(e,exc_info=True)

    print(f"可用:{len(users)} 成功:{successCount}")

def gusetUpApple():
    needContinue = True
    s5List = loadS5List()

    while needContinue:
        userKey = redisHelper.getOneLevel11UserKey()
        if userKey:
            user = UserInfo(userKey=userKey,saveSocks5=True,socks5=random.choice(s5List))
            try:
                if user.login():
                    if user.level >= 11:
                        if user.tryRandomAppleLink():
                            fakeIP = redisHelper.getFakeIp(userKey,user.kingdomId)
                            if requestSaveUser(user.appleSubId,user.deviceInfo,fakeIP,socks5=user.socks5):
                                redisHelper.removeUserKeyWithAll(userKey)
                    else:
                        print("等级:{user.level} 不足11级")
                        redisHelper.removeUserKeyWithAll(userKey)
                        redisHelper.saveUserKey(userKey)
            except UserError as e:
                if e.errorCode == -4:
                    user.clearSelf()
                elif e.errorCode == 41:
                    print(f"{userKey} 封了")
                    redisHelper.removeUserKeyWithAll(userKey)
                else:
                    user.errorLog(e,exc_info=True)

        else:
            needContinue = False

if __name__ == "__main__":
    path = "./guestaccount.txt"
    print("读写游客工具\n")
    inputValue = input("请输入操作类型：\n1.导入游客\n2.导出游客\n3.发送游客到洗脚城\n4.本地游客升苹果\n")
    if len(inputValue) == 0:
        print("输入异常")
    else:
        value = int(inputValue)
        if value == 1:
            loadGuest(path)
        elif value == 2:
            dumpGuest(path)
        elif value == 3:
            sendGuestToXJC()
        elif value == 4:
            gusetUpApple()
        else:
            print("输入异常")
