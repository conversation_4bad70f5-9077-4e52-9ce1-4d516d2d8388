#!/usr/bin/python3
# -*- coding: utf-8 -*-
'''
功能描述：多进程共享内存测试
编写人：darkedge
编写日期：2022年06月09日
   
'''

import multiprocessing
from multiprocessing import shared_memory
from Unit.Redis import redisMgr
import random,time

import base64,json
import unittest
import time

class MemoryTest(unittest.TestCase):
    def setUp(self):
        pass

    def test_memory(self):
        [multiprocessing.Process(target=progressRun).start() for v in range(5)]
        print("main over")
    
        for i in range(30):
            time.sleep(1)
            print(i)

def initList(manager):
    print('initList')
    shareList = manager.list()

    shareList.append(1)
    shareList.append(2)
    shareList.append(3)

def printList(manager):
    shareList = manager.list()
    with manager.Lock():
        shareList.append(random.randint(4, 1000))
    print(shareList)

def progressRun():
    with redisMgr.lock('test_init20',timeout=30):
        shm = None
        try:
            shm = shared_memory.SharedMemory('test6',size=1024)
            v = int(shm.buf)
            shm.buf = f'{v+1}'
        except Exception as e:
            if not shm:
                shm = shared_memory.SharedMemory('test6',size=1024,create=True)
                shm.buf = '999'
                print("init")

        print(shm.buf)

    # manager = multiprocessing.Manager()
    # with manager.Lock():
    #     print('1')
    #     time.sleep(5)
    #     createBool = manager.Value('i', 0)

    #     if createBool.value == 0:
    #         createBool.value = 1
    #         initList(manager)
    # printList(manager)

if __name__ == '__main__':
    print("main")
    [multiprocessing.Process(target=progressRun).start() for v in range(5)]
    print("main over")
    
    while True:
        time.sleep(1)
