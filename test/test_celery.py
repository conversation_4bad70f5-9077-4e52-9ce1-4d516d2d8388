# /bin/env python3
# -*- coding: UTF-8 -*- 
'''
功能描述：celery分布式任务
编写人：darkedge
编写日期：2022年2月22日
   
'''

import unittest
from Unit.Logger import logger
from Celery.app import app,resultWithTaskId
from Celery.task import *
import platform
import time,random

@app.task(bind=True)
def task(self,name, age):
    print("try task")
    if not self.request.called_directly:
        self.update_state(state="RUN")
    time.sleep(300)
    return f'name is {name}, age is {age}'

class CeleryTest(unittest.TestCase):
    def setUp(self):
        logger.info("celery测试开始")
    
    def test_task(self):
        values = []
        for i in range(2):
            value = task.delay(f"Jay{i}",20+i)
            values.append(value)
        [print(v.get()) for v in values]

    def test_get_task(self):
        result = resultWithTaskId('5a45414a-6859-4592-bad0-9c25d0840c6c')
        print(result)

    def test_get_crystal(self):
        listCrystalTask(20)

    def test_test_a(self):
        print(test_a.delay())