# /bin/env python3
# -*- coding: UTF-8 -*- 
'''
功能描述：邮箱注册
编写人：darkedge
编写日期：2021年12月25日
   
'''

from Api.GoogleCaptcha import get_response,create_task
from Api.Ipinfo import getIP
from Api.ProxyApi import allocateIP,whiteAdd
from Api.UserInfo import UserInfo
from Model.Account import Account
from Model.UserError import UserError
from Unit.DeviceInfo import randomEmailPrefix
from Unit.Redis import redisHelper
from Unit.Logger import logger
import time
import random
emailList = [
"amoyebest.icu",
]
def writeLocal(email):
     with open("./emailaccount.txt","a+") as f:
        f.write("%s\n" % email)

def registerWithDynamicProxy():
    logger.info("动态ip注册")
    index = 0
    recaptchaRes = None
    gt = 0
    while index < len(emailList):  
        if recaptchaRes is None:
            taskId = create_task()
            if not taskId:
                continue
            currentTime = time.time()
            recaptchaRes = get_response(taskId)
            gt = time.time()
            logger.info("获得谷歌验证 耗时%d" % (time.time() - currentTime))

        s5 = allocateIP()
        logger.info("获取到代理:%s" % s5)
        if s5 is None:
            logger.error("代理异常")
            return
        s5Ip = s5.split(":")[0]
        if redisHelper.getIPBan(s5Ip):
            logger.info("ip受限 休息5秒")
            time.sleep(5)
            continue
        t = time.time()
        if s5:
            for i in range(3):

                u1 = UserInfo("%s@%s" % (randomEmailPrefix(),emailList[index].lower()),"111111",socks5="08ZJT6QC:B1429F6B5167@%s" % s5)
                index += 1
                try:
                    if recaptchaRes is None:
                        taskId = create_task()
                        if not taskId:
                            continue
                        currentTime = time.time()
                        recaptchaRes = get_response(taskId)
                        gt = time.time()
                        logger.info("获得谷歌验证 耗时%d" % (time.time() - currentTime))
                    if time.time() - t > (30 + i * 5):
                        redisHelper.setIPBan(s5Ip)
                        logger.info("代理超时")
                        break
                    if time.time() - gt > 60:
                        logger.info("谷歌验证超时")
                        recaptchaRes = None
                        continue
                    if u1.authJoin(token=recaptchaRes):
                        u1.log("注册成功:%s" % u1.email)
                        writeLocal(u1.email)
                        recaptchaRes = None
                    else:
                        redisHelper.setIPBan(s5Ip)
                        logger.info("代理超时")
                        break
                except UserError as e:
                    if e.errorCode == 7:
                        redisHelper.setIPBan(s5Ip)
                        logger.info("ip受限 换ip")
                        recaptchaRes=None
                        break
                    elif e.errorCode == -3:
                        redisHelper.setIPBan(s5Ip)
                        logger.info("s5异常")
                    elif e.errorCode == 33:
                        logger.info(f"邮箱被封了 {u1.email}")
                        recaptchaRes = None
                    elif e.errorCode == 32:
                        logger.error("傻逼官方又关注册了")
                        exit(0)
                except Exception as e:
                    logger.info("注册失败:%s" % e)
                    
            redisHelper.setIPBan(s5Ip)

        else:
            logger.info("获取ip异常")
            time.sleep(10)
        

if __name__ == '__main__':
    # registerWithS5()
    # localTest()
    registerWithDynamicProxy()