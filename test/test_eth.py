# /bin/env python3
# -*- coding: UTF-8 -*- 

import unittest
# import json
import random
from Unit.FileTool import loadS5List
from Unit.Logger import logger

from web3 import Web3, HTT<PERSON>rovider
from Unit.EthUnit import EthService,modifyWeb3Socks5,rpc,use<PERSON>rena<PERSON>
from Unit.Contract.DSTContract import DstContract

address = '******************************************'

class EthTest(unittest.TestCase):
    def setUp(self):
        self.web3 = Web3(HTTPProvider(rpc))
        self.ethService = EthService("0xe536e1297aa81b6fdaf798e3167c98303b527dae1ada6494a85c223d4626d804")

    def test_address(self):
        print(self.ethService.address)

    def test_balance(self):
        print(self.ethService.balance())

    def test_arena_z(self):
        useArenaZ()
        print(self.ethService.balance())

    def test_a2z_tx(self):
        useArenaZ()
        print(dict(self.ethService.getTx("0x5b71e237b1ca50016b12361069861bce8ce80e6bab489f7c8fb52c025f484e98")))

    def test_a2z_transfer(self):
        useArenaZ()
        print(self.ethService.transfer(0.001, to=self.ethService.address, chainId=7897))

    def test_transfer(self):
        res = self.ethService.transfer(0.0001, self.ethService.address)
        print(f"{res and '成功' or '失败'}")

    def test_mint(self):
        from Api.UserInfo import UserInfo
        token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.lxgaTLWpKIZC9e47_qPC1OGXqOxQ34QJaYYeKuSilms'
        u1 = UserInfo(token=token, socks5="127.0.0.1:1087")
        u1.loadEmailWithToken()
        u1.initLog(needTestS5=False)

    def test_sign(self):
        verifyId = "677f45135735c5ddf4d0c32a"
        signedTx = '0x5f38b047e6c67f4dff202d300b02660f7b86d220bdf9dabbb7bfef13b6b534b23975ab3c7620984f526dbd555125129cc158abc0f455a7c377a783e9b131fb5a1b'
        assert self.ethService.signMessage(verifyId) == signedTx


    def test_print(self):
        ps = ["0xe536e1297aa81b6fdaf798e3167c98303b527dae1ada6494a85c223d4626d804",
                "0xee1b65bfdeff1e386d5f86a594063b17e381bdae243ce2126ae44a914f174366",
                "0xa0e376e211193b077fb0085a482b5b343194960d9d3871118d700e35c8c0b7b4",
                "0xdaf2ed04fefbaa815130939d6a9cd7e6b13bdb617918701a6b7ac2e0238c277d",
                "0x8aef9bc0d736591e719e597c5e73255e0603d7b63ca5e5ebaf87acfa9d7cb3a2",
                "0xd11ef359ed00e4b5b9ab8008cc854ace28009c0d91d14322892cd5687c3d6a56",
                "0xd8a0958144f580bebdf3f977badc5e6e703e617c4c4927252559e00b039ca20c"]
        [print(EthService(p).balance()) for p in ps]

    def test_dst_print(self):
        ps = ["0xe536e1297aa81b6fdaf798e3167c98303b527dae1ada6494a85c223d4626d804",
        "0xee1b65bfdeff1e386d5f86a594063b17e381bdae243ce2126ae44a914f174366",
        "0xa0e376e211193b077fb0085a482b5b343194960d9d3871118d700e35c8c0b7b4",
        "0xdaf2ed04fefbaa815130939d6a9cd7e6b13bdb617918701a6b7ac2e0238c277d",
        "0x8aef9bc0d736591e719e597c5e73255e0603d7b63ca5e5ebaf87acfa9d7cb3a2",
        "0xd11ef359ed00e4b5b9ab8008cc854ace28009c0d91d14322892cd5687c3d6a56",
        "0xd8a0958144f580bebdf3f977badc5e6e703e617c4c4927252559e00b039ca20c"]

        contract =  DstContract()
        # contract.getBalance("******************************************")
        print([contract.getBalance(EthService(p).address) for p in ps])

    def test_loki_tokens(self):
        from Unit.Contract.LOKIContract import LOKIContract
        contract = LOKIContract()
        print(contract.tokenOfOwnerByIndex("******************************************",49))
        # print(contract.transferFrom("******************************************","******************************************", ***********))

    def test_socks5(self):
        socks5 = random.choice(loadS5List())
        logger.info(f"使用s5:{socks5}")

        modifyWeb3Socks5(socks5)
        account = self.web3.eth.account.create()
        es = EthService(account.privateKey.hex())
        baseFeePerGas = es.baseFeePerGas()
        logger.info(f'{baseFeePerGas}')

    def test_create_account(self):
        print("生成100钱包")
        for _ in range(500):
            account = self.web3.eth.account.create()
            print(f'{account.address}   {account.privateKey.hex()}')


# if __name__ == '__main__':
#     logger.info("测试脚本")
#     # test()
#     # exit(0)
#     web3 = Web3(HTTPProvider(rpc))
#     web3.middleware_onion.inject(ExtraDataToPOAMiddleware, layer=0)

#     myAddress = web3.toChecksumAddress('******************************************')
#     # web3.eth.account.from_key('0xee1b65bfdeff1e386d5f86a594063b17e381bdae243ce2126ae44a914f174366')
#     # print(web3.eth.accounts)
#     account = Account().from_key('0xee1b65bfdeff1e386d5f86a594063b17e381bdae243ce2126ae44a914f174366')
#     account.sign()
#     max_priority_fee_per_gas = web3.toWei(29.*********, 'gwei')
#     max_fee_per_gas = web3.toWei(29.*********,'gwei')
#     gas = 21576#web3.eth.estimate_gas(transaction={'to': '******************************************'})
#     gas_price = web3.eth.gasPrice
#     nonce = web3.eth.getTransactionCount(account.address)
#     # "to":Web3.toChecksumAddress("******************************************")
#     params = {"to":"******************************************","data":"0xa0712d6881c9afe660fec0188036d85d70991c9515fc3dde847f91e96b163ace21408a85","chainId":137,
#         'gas':gas,'gasPrice':gas_price,'nonce':nonce}
#     print(params)
#     print(web3.eth.estimate_gas( {"to":"******************************************","data":"0xa0712d6881c9afe660fec0188036d85d70991c9515fc3dde847f91e96b163ace21408a85"}))
#     exit(0)
#     signed = account.sign_transaction(params)
#     account.sign_message({})
#     # exit(0)
#     print(signed)
#     print(signed.hash.hex())
#     tx = web3.eth.send_raw_transaction(transaction=signed.rawTransaction)
#     print(tx.hex())
#     # params['from'] = account.address
#     # send_transaction = web3.eth.send_transaction(params)

#     # print(send_transaction)
#     # print(account.address)

#     # print(web3.eth.send_raw_transaction({
#     #     'to': myAddress,
#     #     'from': account.address,
#     #     'value': 1,
#     #     # 'value': web3.toWei(0.1, 'ether'),
#     # }))

#     # account.sign_transaction({'from':account.address,'value':web3.toWei(1,'ether')}).sendTransaction({'to':'******************************************','value':web3.toWei(1,'ether')})
#     # print(web3.eth.accounts().create())
#     # balance = web3.fromWei(web3.eth.getBalance(address), 'ether')
#     # print(web3.toWei(30, 'gwei'))
#     # nonce = web3.eth.getTransactionCount(account.address)
#     # gas = web3.eth.estimate_gas(transaction={'to': myAddress})
#     # params = {'from':account.address,'value':web3.toWei(0.1,'ether'),'gas':web3.toHex(gas),'nonce':web3.toHex(nonce),'gasPrice':web3.eth.gas_price,'chainId':web3.eth.chainId}
#     # print(params)
#     # transaction = account.sign_transaction(params)
#     # print(type(transaction))
#     # web3.eth.send_raw_transaction(transaction.rawTransaction)
#     # print(nonce)
#     # print(Web3.toChecksumAddress('******************************************'))

#     # print(web3.eth.estimate_gas(transaction={'to': myAddress}))
#     # print(balance)

