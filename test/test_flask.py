#!/usr/bin/python3
# -*- coding: utf-8 -*-

"""
功能描述：Flask接口测试
编写人：AI Assistant
编写日期：2024年01月15日
"""

import unittest
from Api.FlaskHelper import requestMakeGlobalCrystalInfo, setEnvUrl


class FlaskTest(unittest.TestCase):
    def setUp(self):
        """测试前设置"""
        # 设置测试环境URL
        setEnvUrl("http://127.0.0.1:9991/")

    def test_make_global_crystal_info(self):
        """测试生成全局水晶信息接口"""
        # 测试用例1: 正常请求 - worldId=20
        result = requestMakeGlobalCrystalInfo(worldId=20)
        self.assertTrue(result)

        # 测试用例2: 正常请求 - worldId=60
        result = requestMakeGlobalCrystalInfo(worldId=17)
        self.assertTrue(result)

        # 测试用例4: worldId 为空
        result = requestMakeGlobalCrystalInfo(worldId=None)
        self.assertFalse(result)

if __name__ == "__main__":
    unittest.main() 