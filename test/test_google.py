import os
import sys
import unittest
from pathlib import Path
print(sys.path)
sys.path.insert(0, os.getcwd())
from Api.UserInfo import UserInfo


class TestGoogleSelenium(unittest.TestCase):
    def setUp(self):
        from Service.Selenium import GoogleSelenium
        print("谷歌登录测试")

    def test_a(self):
        pass

    def test_login(self):
        gs = GoogleSelenium("<EMAIL>","Asd1966./")#,"fjxnuduv:gvwiwc08ayac@*************:6255")
        gtoken = gs.runLoginGoogle()
        if gtoken:
            self.gameLoginWithGToken("<EMAIL>",gtoken)
    
    def test_glogin(self):
        self.gameLoginWithGToken("<EMAIL>","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.jcUr0mOlX9g8kkLAaii3QkyGW0MPZr5JdU9rDB-WxP8")

    def gameLoginWithGToken(self,email,gtoken):
        user = UserInfo(email,googleToken=gtoken)
        if user.login():
            print("登陆成功")
            