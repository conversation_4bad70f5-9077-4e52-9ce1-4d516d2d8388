#!/usr/bin/python3
# -*- coding: utf-8 -*-
'''
功能描述：邮箱测试
编写人：darkedge
编写日期：2022年01月18日
   
'''

import unittest
import re,random
from Api.UserInfo import UserInfo
from Unit.FileTool import loadS5List
from imbox import Imbox
from Api.ImapService import ImapService,imapService

class TestEmail(unittest.TestCase):
    def setUp(self):
        self.imapService:ImapService = imapService

    def test_get_email(self):
        url,uid = self.imapService.returnSomeUserInfo()
        if user:
            user.randomSleep(5,10)
            u1.login()