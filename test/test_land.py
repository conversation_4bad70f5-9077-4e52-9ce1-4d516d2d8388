#!/usr/bin/python3
# -*- coding: utf-8 -*-
"""
功能描述：土地测试
编写人：darkedge
编写日期：2022年05月02日
   
"""

import json
import os
import secrets
import unittest

import requests

from Unit.LandZoneUnit import (
    AllZoneOfPolygon,
    NewPoint,
    NewPolygon,
    isPointOnPolygonEdge,
    loadCrystalLands,
    PolygonWithPoint,
)


class landTest(unittest.TestCase):
    def setUp(self):
        pass

    def test_lands(self):
        print(len(loadCrystalLands()))

    def test_download(self):
        os.makedirs("temple", exist_ok=True)
        temples = {
            "A": range(1, 5),
            "B": range(1, 13),
            "C": range(1, 21),
            "D": range(1, 29),
        }

        api = "https://api-lok-beta.leagueofkingdoms.com/api/land/zone/lands"

        def downloadWithTemple(zone, index):
            res = requests.get(api, params={"zoneId": f"{zone}{index}"})
            if res.status_code == 200:
                resJson = res.json()
                if resJson["result"]:
                    with open(f"temple/{zone}{index}.json", "w") as f:
                        f.write(json.dumps(resJson["lands"]))

        def checkJsonFile(zone, index):
            try:
                with open(f"temple/{zone}{index}.json") as _f:
                    return True
            except Exception:
                return False

        for temple in temples:
            value = temples[temple]
            for index in value:
                if not checkJsonFile(temple, index):
                    downloadWithTemple(temple, index)

    def test_load_land(self):
        files = os.listdir("./temple/")
        lands = []
        for file in files:
            with open(f"./temple/{file}", "r") as f:
                data = json.loads(f.read())
                for landData in data:
                    if landData[6] is not None:
                        lands.append(landData[0] % 100000)

        print("\n")
        print(len(lands))

        mapZone = {}

        for land in lands:
            x = land % 256 * 8
            y = land // 256 * 8
            zone = y // 32 * 64 + x // 32
            mapZone[zone] = 1

        zones = list(mapZone.keys())
        zones.sort()
        print(zones)
        print(len(mapZone))
    def test_polygon_zone(self):
        polygon = PolygonWithPoint(1400,600)
        zones = AllZoneOfPolygon(polygon)
        print(f"zones:{len(zones)} {zones}")

    def test_polygon_zone_A2(self):
        polygon = NewPolygon([(1124,1124),(1124,1180),(1180,1180),(1180,1124)])
        print(isPointOnPolygonEdge([1159,1175], polygon))

    def test_point_inside(self):
        polygon = [(400, 400), (400, 800), (800, 800), (800, 400)]
        x = 300
        y = 700

        isTrue = isPointOnPolygonEdge([x,y], NewPolygon(polygon))
        print(f'{isTrue and "在" or "不在"}')
        envelope = NewPolygon(polygon).envelope
        min_x, min_y, max_x, max_y = envelope.bounds

        print("最小矩形的坐标：")
        print(f"最小x坐标: {min_x}")
        print(f"最小y坐标: {min_y}")
        print(f"最大x坐标: {max_x}")
        print(f"最大y坐标: {max_y}")

        zones = AllZoneOfPolygon(NewPolygon(polygon))
        print(f"zones:{len(zones)} {zones}")

    def test_point_inside_by_shapely(self):
        from shapely.geometry import Point
        from shapely.geometry.polygon import Polygon

        # 定义不规则区域的顶点坐标
        polygon = Polygon([(400, 400), (400, 800), (800, 800), (800, 400), (400, 400)])

        # 要检查的点坐标
        point = Point(400, 401)

        # 检查点是否在区域内
        self.assertTrue(polygon.contains(point), msg="点不在区域内")
        # if polygon.contains(point):
        #     print("点在区域内")
        # else:
        #     print("点不在区域内")

    def test_point(self):
        from shapely.geometry import Point
        from shapely.geometry.polygon import Polygon

        p1 = Point(1, 2)
        p2 = Point(2, 2)
        p3 = Point(2, 1)
        p4 = Point(1, 1)
        po = Polygon([p1, p2, p3, p4])
        print(po)
        print(Polygon([(1, 2), (2, 2), (2, 1), (1, 1)]))

    def test_2(self):
        from shapely.geometry import Point
        from shapely.geometry.polygon import Polygon

        def is_point_on_polygon_edge(point, polygon):
            if polygon.contains(point):
                return "点在多边形内"

            x, y = point.x, point.y
            for i in range(len(polygon.exterior.coords) - 1):
                edge_start = polygon.exterior.coords[i]
                edge_end = polygon.exterior.coords[i + 1]
                x1, y1 = edge_start
                x2, y2 = edge_end
                # 判断点是否在边的整数坐标范围内
                if min(x1, x2) <= x <= max(x1, x2) and min(y1, y2) <= y <= max(y1, y2):
                    return "点在多边形边上"

            return "点不在多边形内"

        # 定义不规则区域的顶点坐标
        polygon = Polygon([(400, 400), (400, 800), (800, 800), (800, 400)])

        # 要检查的点坐标
        for _i in range(100):
            point = Point(secrets.randbelow(1024), secrets.randbelow(1024))
            _s = f"{point} {is_point_on_polygon_edge(point,polygon)}"
            # print(s)

