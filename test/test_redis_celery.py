import unittest
import random
import time
from Celery.redis import redisCeleryHelper

def generate_monster_id():
    """生成符合格式的怪物ID"""
    return f"20_{random.randint(1, 1999)}_{random.randint(1, 1999)}"

class TestRedisCeleryHelper(unittest.TestCase):
    def setUp(self):
        """每个测试方法前的设置"""
        # 生成测试数据
        self.maxCount = 500
        self.rejectCount = 50
        self.test_monsters = [generate_monster_id() for _ in range(self.maxCount)]
        self.rejected_monsters = random.sample(self.test_monsters, self.rejectCount)
        
        # 清理可能存在的测试数据
        for monster in self.test_monsters:
            key = redisCeleryHelper.rejectMonsterKey % monster
            redisCeleryHelper.removeKey(key)

    def tearDown(self):
        """每个测试方法后的清理"""
        for monster in self.test_monsters:
            key = redisCeleryHelper.rejectMonsterKey % monster
            redisCeleryHelper.removeKey(key)

    def test_set_reject_monsters(self):
        """测试设置拒绝列表"""
        # 测试空列表
        redisCeleryHelper.setRejectMonsters([])
        not_rejected = redisCeleryHelper.checkRejectMonsters(self.test_monsters)
        self.assertEqual(set(not_rejected), set(self.test_monsters))

        # 测试正常设置
        redisCeleryHelper.setRejectMonsters(self.rejected_monsters)
        for monster in self.rejected_monsters:
            self.assertTrue(redisCeleryHelper.checkRejectMonster(monster))

    def test_check_reject_monster(self):
        """测试单个怪物检查"""
        # 设置拒绝列表
        redisCeleryHelper.setRejectMonsters(self.rejected_monsters)
        
        # 测试在拒绝列表中的怪物
        for monster in self.rejected_monsters:
            self.assertTrue(redisCeleryHelper.checkRejectMonster(monster))

    def test_check_reject_monsters(self):
        """测试批量检查怪物"""
        # 测试空列表
        self.assertEqual(redisCeleryHelper.checkRejectMonsters([]), [])
        
        # 设置拒绝列表
        redisCeleryHelper.setRejectMonsters(self.rejected_monsters)
        

        # 测试全部怪物
        not_rejected = redisCeleryHelper.checkRejectMonsters(self.test_monsters)
        self.assertEqual(len(not_rejected), self.maxCount - self.rejectCount)  # 应该返回3个未拒绝的怪物


    def test_reject_monster_expiration(self):
        """测试拒绝列表过期"""
        # 设置一个较短的过期时间进行测试
        monster = generate_monster_id()
        key = redisCeleryHelper.rejectMonsterKey % monster
        redisCeleryHelper.set(key, 1, ex=1)  # 1秒后过期
        
        # 检查设置成功
        self.assertTrue(redisCeleryHelper.checkRejectMonster(monster))
        
        # 等待过期
        time.sleep(1.1)
        
        # 检查已过期
        self.assertFalse(redisCeleryHelper.checkRejectMonster(monster))


if __name__ == '__main__':
    unittest.main() 