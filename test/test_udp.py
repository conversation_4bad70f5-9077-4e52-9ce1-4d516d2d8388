import socket
import json
import threading
import unittest

class UDPServer:
    def __init__(self):
        self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_BROADCAST, 1)
        self.server_socket.bind(('localhost', 0))  # Bind to a random available port
        self.server_port = self.server_socket.getsockname()[1]
        self.is_running = False

    def start(self):
        self.is_running = True
        # Start a separate thread to handle incoming messages
        threading.Thread(target=self.receive_messages, daemon=True).start()

    def stop(self):
        self.is_running = False
        self.server_socket.close()

    def receive_messages(self):
        while self.is_running:
            try:
                data, client_address = self.server_socket.recvfrom(1024)
                json_data = json.loads(data.decode('utf-8'))
                print('Received JSON data from {}: {}'.format(client_address, json_data))
            except OSError as e:
                if self.is_running:
                    print('Error receiving data:', e)

# Example of a class to test the UDP server
class TestUDPServer(unittest.TestCase):
    def test_receive_json(self):
        server = UDPServer()
        server.start()

        # Simulate broadcasting JSON data to the server
        client_socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        client_socket.setsockopt(socket.SOL_SOCKET, socket.SO_BROADCAST, 1)
        client_socket.sendto(json.dumps({'key': 'value'}).encode('utf-8'), ('localhost', server.server_port))

        # Allow some time for the server to receive and process the message
        import time
        time.sleep(5)

        server.stop()

if __name__ == '__main__':
    unittest.main()
