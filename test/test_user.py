# trunk-ignore-all(ruff/F401)
import os
import unittest
import random
import json
from concurrent.futures import ThreadPoolExecutor

from Api.UserInfo import Enum, UserInfo, loadBuildJson, freeBuildJson
from Unit.Logger import logger
from Unit.Redis import redisHelper, todayStr, tzDate
from Celery.task import collectCrystalsTask
from Unit.FileTool import loadS5List
from Model.User import User

class TestUser(unittest.TestCase):
    def setUp(self):
        pass

    def setupAccount(self, userKey=None, token=None) -> UserInfo:
        user = None
        if userKey:
            user = UserInfo(
                userKey=userKey, socks5=random.choice(loadS5List()), saveSocks5=True
            )
            user.token = token
        else:
            user = UserInfo(
                token=token, socks5=random.choice(loadS5List()), saveSocks5=True
            )
            user.loadEmailWithToken()
        if not user.token:
            user.token = user.loadRedisToken()
        user.initLog(needTestS5=False)
        return user

    def loadGuestAccount(self):
        accounts = []
        with open("./guestaccount.txt", "r") as f:
            for line in f.readlines():
                if line.strip():
                    accounts.append(line.strip())
        users = [self.setupAccount(v) for v in accounts]
        return users


    def test_dragoInfo(self):
        u1 = UserInfo(userKey="029a0457-8417-c379-0d2a-faa44b2835d7",socks5=random.choice(loadS5List()))
        if u1.login():
            logger.info(json.dumps(u1.dragoInfo("62940939dac142b8bd5a00de"),ensure_ascii=False))

    def test_loadAllDragosLevel(self):
        users = self.loadGuestAccount()
        with ThreadPoolExecutor(max_workers=10) as executor:
            executor.map(lambda u: not u.token and u.login() or u.enter(), users)
            executor.shutdown()
        
        def loadDrago(user:UserInfo,dragoId):
            dragoInfo = user.dragoInfo(dragoId)
            if dragoInfo:
                drago = dragoInfo["drago"]
                level = drago["level"]
                tokenId = drago["tokenId"]
                redisHelper.set(f"dragoLevel:{tokenId}",level, ex=60*60*24 * 30)
                logger.info(f"获取{dragoId}成功,tokenId:{tokenId},level:{level}")
            else:
                logger.error(f"获取{dragoId}失败")
            user.randomSleep(1)

        with ThreadPoolExecutor(max_workers=len(users)) as pool:
            files = os.listdir("./drago/")
            for i,file in enumerate(files):
                # newIds = []
                with open(f"./drago/{file}", "r", encoding="utf-8") as f:
                    # codeMap = {}
                    data = json.load(f)
                    drago = data["drago"]
                    dragoId = drago["_id"]
                    tokenId = drago["tokenId"]
                    if not redisHelper.get(f"dragoLevel:{tokenId}"):
                        pool.submit(loadDrago, users[i % len(users)], dragoId)

            pool.shutdown(wait=True)
    
    def test_printDragosLevel(self):
        keys = redisHelper.keys("dragoLevel:*")
        # 获取所有数据，按id:level,id是dragoLevel:之后的字符串,按tokenId排序，并写入文件
        data = []
        values = redisHelper.mget(keys)
        for k, v in zip(keys, values):
            id = k.decode().split(":")[1]
            data.append((id, v.decode()))
        data = sorted(data, key=lambda x: x[0])
        with open("dragos.txt","w",encoding="utf-8") as f:
            for id,level in data:
                f.write(f"{id}:{level}\n")

    def test_reSortMarchTroops(self):
        """测试部队重排序功能"""
        user = UserInfo()

        # 测试用例1: 不同等级的部队(T1-T3)
        march_troops = [
            {"code": 10101, "amount": 100},  # T1步兵
            {"code": 10102, "amount": 200},  # T2步兵
            {"code": 10103, "amount": 250},  # T3步兵
            {"code": 10201, "amount": 300},  # T1弓兵
            {"code": 10202, "amount": 400},  # T2弓兵
            {"code": 10203, "amount": 450},  # T3弓兵
            {"code": 10301, "amount": 500},  # T1骑兵
            {"code": 10302, "amount": 600},  # T2骑兵
            {"code": 10303, "amount": 650},  # T3骑兵
        ]

        sorted_troops = user.reSortMarchTroops(march_troops)

        # 验证排序结果
        # 1. 检查T3部队是否在最前面
        self.assertEqual(sorted_troops[0]["code"] % 10, 3)
        self.assertEqual(sorted_troops[1]["code"] % 10, 3)
        self.assertEqual(sorted_troops[2]["code"] % 10, 3)

        # 2. 检查T2部队是否在T1部队前面
        self.assertEqual(sorted_troops[3]["code"] % 10, 2)
        self.assertEqual(sorted_troops[4]["code"] % 10, 2)
        self.assertEqual(sorted_troops[5]["code"] % 10, 2)

        # 3. 检查同等级内是否按code升序排列
        t3_troops = sorted_troops[:3]
        t2_troops = sorted_troops[3:6]
        t1_troops = sorted_troops[6:]
        
        self.assertTrue(all(t3_troops[i]["code"] < t3_troops[i+1]["code"] for i in range(len(t3_troops)-1)))
        self.assertTrue(all(t2_troops[i]["code"] < t2_troops[i+1]["code"] for i in range(len(t2_troops)-1)))
        self.assertTrue(all(t1_troops[i]["code"] < t1_troops[i+1]["code"] for i in range(len(t1_troops)-1)))

        # 测试用例2: 空列表
        self.assertEqual(user.reSortMarchTroops([]), [])

        # 测试用例3: 单个部队
        single_troop = [{"code": 10103, "amount": 100}]  # T3步兵
        self.assertEqual(user.reSortMarchTroops(single_troop), single_troop)

        # 测试用例4: 相同等级的部队(T3)
        same_level_troops = [
            {"code": 10303, "amount": 100},  # T3骑兵
            {"code": 10203, "amount": 200},  # T3弓兵
            {"code": 10103, "amount": 300},  # T3步兵
        ]
        sorted_same_level = user.reSortMarchTroops(same_level_troops)
        # 验证是否按code升序排列
        self.assertTrue(all(sorted_same_level[i]["code"] < sorted_same_level[i+1]["code"] for i in range(len(sorted_same_level)-1)))
