#!/usr/bin/python3
# -*- coding: utf-8 -*-
"""
功能描述：yml测试
编写人：darkedge
编写日期：2024年06月25日
   
"""

# import json
# import os
# import secrets
import unittest
import requests
try:
    import yaml
except ModuleNotFoundError:
    pass

class YMLTest(unittest.TestCase):
    def setUp(self):
        pass

    def test_request(self):
        # 获取数据
        url = "https://share.lzf.email/proxy.php?uuid=f7aed90a8f&sid=all&target=clash"
        response = requests.get(url)
        yaml_data = response.text
        self.loadyaml(yaml_data,onlyHK=True)

    def test_local(self):
        with open('clash.yaml', 'r', encoding='utf-8') as file:
            yaml_data = file.read()
            self.loadyaml(yaml_data,onlyHK=True)

    def loadyaml(self, yaml_data,onlyHK=True):
        listeners = []
        def newListener(proxy,port):
            return {
                "name": f'socks-in-{proxy}',
                # "type": "socks",
                "type": "mixed",
                "port": port,
                # trunk-ignore(bandit/B104)
                "listen": "0.0.0.0",
                "udp": True,
                "proxy": proxy
            }

        # 解析YAML数据
        data = yaml.safe_load(yaml_data)
        localData = {}
        validProxy = []
        # 筛选出名称包含“香港”的代理信息
        hong_kong_proxies = data['proxies']
        locations = ['香港',  '台湾','新加坡']

        if onlyHK:
            hong_kong_proxies = [proxy for proxy in data['proxies'] if any(location in proxy['name'] for location in locations)]
        for proxy in hong_kong_proxies:
            key = f'{proxy["server"]}:{proxy["port"]}'
            if localData.get(key):
                print(f"排重:{proxy['name']}")
                continue
            localData[key] = proxy
            validProxy.append(proxy)
            
        # 按顺序命名
        for i, proxy in enumerate(validProxy, start=1):
            if onlyHK:
                 # 找到代理所在的位置
                location = next((location for location in locations if location in proxy['name']), '香港')
                proxy['name'] = f"{i}-{location}"
            else:
                proxy['name'] = f"{i}-{proxy['name']}"
            listeners.append(newListener(proxy['name'],30000 + i))
        print(f"筛选后的总数:{len(validProxy)}")
        # 将筛选后的数据写入本地文件config.yaml
        with open('config.yaml', 'w', encoding='utf-8') as file:
            yaml.dump({'listeners':listeners, 'proxies': validProxy}, file, allow_unicode=True)
        with open('socks5_yml.txt', 'w', encoding='utf-8') as file:
            for i, _ in enumerate(validProxy, start=1):
                file.write(f"||127.0.0.1:{30000+i}\n")

        print("筛选后的数据已写入config.yaml")

    def test_self_ips(self):
        listeners = []
        validProxy = []
        def newListener(proxy,port):
            return {
                "name": f'socks-in-{proxy}',
                # "type": "socks",
                "type": "mixed",
                "port": port,
                # trunk-ignore(bandit/B104)
                "listen": "0.0.0.0",
                "udp": True,
                "proxy": f'{proxy}',
            }
        def newOutProxy(proxy, port):
            return {
                "name": f'{proxy}',
                "type": "socks5",
                "server": "dc.smartproxy.com",
                "port": port,
                "username": "spnofkfj1x",
                "password": "4igBox6oXstGg_4Rp6",
                "skip-cert-verify": True,
            }

        n = 100
        for i in range(n):
            name = f'out-{i}'
            listeners.append(newListener(name,30000 + i))
        for i in range(n):
            name = f'out-{i}'
            validProxy.append(newOutProxy(name,30000 + i))
        # 将筛选后的数据写入本地文件config.yaml
        with open('config.yaml', 'w', encoding='utf-8') as file:
            yaml.dump({'listeners':listeners, 'proxies': validProxy}, file, allow_unicode=True)
        with open('socks5_yml.txt', 'w', encoding='utf-8') as file:
            for i, _ in enumerate(validProxy, start=1):
                file.write(f"||127.0.0.1:{30000+i}\n")

    def test_one(self):
        yaml_data = """
        proxies:
        - {name: 新加坡-1, server: oracle-amd-singapore1.lanmaoyun.icu, port: 30001, type: hysteria2, password: 1381bb4b-32f6-4fd7-8c70-b0d9210d3a52, auth: 1381bb4b-32f6-4fd7-8c70-b0d9210d3a52, sni: main.nodes.lanmaoyun.icu, skip-cert-verify: true, udp: true}
        - {name: 朝鲜-02-1E, server: video.111365.xyz, port: 15544, type: ss, cipher: aes-128-gcm, password: a52d24c9-44a2-4c3a-828c-f3b7da5fdcb4, udp: true}
        - {name: 韩国 02-1D, server: kr-03.worldfirst.eu.org, port: 44300, type: hysteria2, password: 13f82e2d-d62b-4988-96c2-2dea912bcdaf, auth: 13f82e2d-d62b-4988-96c2-2dea912bcdaf, sni: kr-03.worldfirst.eu.org, skip-cert-verify: true, udp: true}
        """

        # 解析YAML数据
        data = yaml.safe_load(yaml_data)

        # 打印解析后的数据
        for proxy in data['proxies']:
            print(f"Name: {proxy['name']}")
            print(f"Server: {proxy['server']}")
            print(f"Port: {proxy['port']}")
            print(f"Type: {proxy['type']}")
            if 'cipher' in proxy:
                print(f"Cipher: {proxy['cipher']}")
            print(f"Password: {proxy['password']}")
            if 'auth' in proxy:
                print(f"Auth: {proxy['auth']}")
            if 'sni' in proxy:
                print(f"SNI: {proxy['sni']}")
            if 'skip-cert-verify' in proxy:
                print(f"Skip Cert Verify: {proxy['skip-cert-verify']}")
            print(f"UDP: {proxy['udp']}")
            print("-" * 20)

    def test_yml2local(self):
        from Unit.Logger import logger
        from Service.ToolUnit import testLocalSocks5Speed
        fails,delays = testLocalSocks5Speed("socks5_yml.txt")
        fastList = []
        maxDelay = 0
        if delays:
            maxDelay = delays[-1][0]
        fastCount = 0
        for k in delays:
            logger.debug(f'||{k[1]}    {k[0]}')
            if k[0] < 0.8:
                fastCount += 1
                fastList.append(k[1])
        logger.info(f"最大延时:{maxDelay} 优选:{fastCount}/{len(delays)} 异常:{len(fails)}")
        if len(fails) > 0:
            logger.error(f"超时代理\n{fails}")

        with open('socks5.txt', 'w', encoding='utf-8') as file:
            for k in fastList:
                file.write(f"||{k}\n")
