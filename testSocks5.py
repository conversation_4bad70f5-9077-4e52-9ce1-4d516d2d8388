#!/usr/bin/python3
# -*- coding: utf-8 -*-
"""
功能描述：代理测试
编写人：darkedge
编写日期：2022年04月04日
   
"""
from Service.ToolUnit import testLocalSocks5,testLocalSocks5Speed
# trunk-ignore(ruff/F401)
from Celery.scheduleTask import autoTestLocalSocks5Speed
from Unit.Logger import logger
import time
import sys
isDebug = True if sys.gettrace() else False

def main():
    fails, loadIPs = testLocalSocks5()
    for k in loadIPs:
        logger.debug(f"||{loadIPs[k]}")
    # allSucceed = '\n'.join(loadIPs.values)
    # logger.info(f"有效代理\n,{allSucceed}")
    failsResult = []
    for fail in fails:
        res = fail.split("@")
        if len(res) > 2:
            failsResult.append(f"{res[1]}:{res[0][2:]}")
        else:
            failsResult.append(fail[2:])
            # logger.error(f"fail:{fail}")
    s = "\n".join(failsResult)
    logger.info(f"无效代理\n{s}")

def delayMain():
    # autoTestLocalSocks5Speed("socks5_yml.txt")
    # return
    fails,delays = testLocalSocks5Speed("socks5_yml.txt")
    maxDelay = 0
    if delays:
        maxDelay = delays[-1][0]
    fastCount = 0
    for k in delays:
        logger.debug(f'||{k[1]}    {k[0]}')
        if k[0] < 0.8:
            fastCount += 1
    logger.info(f"最大延时:{maxDelay} 优选:{fastCount}/{len(delays)} 异常:{len(fails)}")
    if len(fails) > 0:
        logger.error(f"超时代理\n{fails}")

if __name__ == "__main__":
    t = time.time()
    try:
        if isDebug:
            delayMain()
        else:
            main()
    except KeyboardInterrupt:
        pass
    logger.info(f'耗时:{round(time.time()-t,2)}秒')