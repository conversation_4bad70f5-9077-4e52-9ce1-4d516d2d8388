#!/usr/bin/env python3
# -*- coding: UTF-8 -*-

"""
测试脚本：处理lokland数据的简单测试版本
"""

import csv
import json
import time
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from Api.UserInfo import UserInfo
from Unit.Logger import logger


def test_single_land_info():
    """测试单个landId的landInfo调用"""
    
    # 使用测试token
    token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.vf38q-YMtay-5X0n_8vyaWfJEwAMCw951SIKHDJ-sXQ"
    socks5 = "127.0.0.1:1087"
    
    # 初始化用户
    logger.info("初始化用户连接...")
    user = UserInfo(token=token, socks5=socks5)
    user.loadEmailWithToken()
    user.initLog(needTestS5=False)
    
    # 测试第一个landId
    test_land_id = "160208"
    
    logger.info(f"测试获取 landId: {test_land_id} 的信息")
    
    try:
        land_info = user.landInfo(test_land_id)
        
        if land_info:
            logger.info("获取成功！")
            logger.info(f"完整数据: {json.dumps(land_info, ensure_ascii=False, indent=2)}")
            
            # 提取loka信息
            attributes = land_info.get('attributes', {})
            storage = attributes.get('storage', {})
            loka_amount = storage.get('loka', 0)
            
            logger.info(f"loka数量: {loka_amount}")
            
            return True
        else:
            logger.warning("获取失败，返回数据为空")
            return False
            
    except Exception as e:
        logger.error(f"获取landInfo时出错: {str(e)}")
        return False


def process_first_few_records(limit=5):
    """处理前几条记录进行测试"""
    
    input_file = "lokland_data.csv"
    
    # 使用测试token
    token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.vf38q-YMtay-5X0n_8vyaWfJEwAMCw951SIKHDJ-sXQ"
    socks5 = "127.0.0.1:1087"
    
    # 初始化用户
    logger.info("初始化用户连接...")
    user = UserInfo(token=token, socks5=socks5)
    user.loadEmailWithToken()
    user.initLog(needTestS5=False)
    
    try:
        # 检查输入文件是否存在
        if not os.path.exists(input_file):
            logger.error(f"输入文件 {input_file} 不存在")
            return False
        
        # 读取CSV文件
        with open(input_file, 'r', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            
            logger.info(f"开始处理前 {limit} 条记录")
            
            for row_num, row in enumerate(reader, 1):
                if row_num > limit:
                    break
                    
                land_id = row['land_id']
                logger.info(f"处理第 {row_num} 行，landId: {land_id}")
                
                try:
                    # 调用landInfo获取loka数据
                    land_info = user.landInfo(land_id)
                    
                    if land_info:
                        # 提取loka相关信息
                        attributes = land_info.get('attributes', {})
                        storage = attributes.get('storage', {})
                        loka_amount = storage.get('loka', 0)
                        
                        logger.info(f"landId {land_id} 获取成功，loka数量: {loka_amount}")
                        
                        # 显示部分关键信息
                        owner = land_info.get('owner', {})
                        sale = land_info.get('sale', {})
                        
                        logger.info(f"  所有者: {owner.get('name', 'N/A')}")
                        logger.info(f"  销售状态: {sale.get('status', 'N/A')}")
                        logger.info(f"  价格: {sale.get('price', 'N/A')}")
                        
                    else:
                        logger.warning(f"landId {land_id} 获取失败")
                        
                except Exception as e:
                    logger.error(f"处理 landId {land_id} 时出错: {str(e)}")
                
                # 添加延迟避免请求过快
                time.sleep(1)
        
        logger.info("测试处理完成！")
        return True
        
    except Exception as e:
        logger.error(f"处理文件时出错: {str(e)}")
        return False


def main():
    """主函数"""
    logger.info("开始测试LOK土地数据处理...")
    
    # 首先测试单个landId
    logger.info("=== 测试单个landId ===")
    if test_single_land_info():
        logger.info("单个landId测试成功！")
    else:
        logger.error("单个landId测试失败！")
        return
    
    # 然后测试处理前几条记录
    logger.info("\n=== 测试处理前5条记录 ===")
    if process_first_few_records(5):
        logger.info("批量处理测试成功！")
    else:
        logger.error("批量处理测试失败！")


if __name__ == "__main__":
    main()
