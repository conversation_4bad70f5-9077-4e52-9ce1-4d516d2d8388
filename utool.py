# /bin/env python3
# -*- coding: UTF-8 -*- 
'''
功能描述：用户功能
编写人：darkedge
编写日期：2022年2月14日
   
'''

from Unit.Redis import redisHelper
from Unit.Logger import logger
from Api.UserInfo import UserInfo,shopItemNames,epicFragments,vipShopItemNames,skillNames
import random,time
from Model.UserError import UserError
from Unit.FileTool import loadS5List
import os,sys

class UserCenter(object):
    '''用户中心'''
    def __init__(self,token):
        self.user = None
        s5List = loadS5List()
        s5 = random.choice(s5List)
        
        if len(token) == 36:
            self.user = UserInfo(userKey=token,socks5=s5)
        elif len(token) > 0:
            self.user = UserInfo(f'somebody{random.randint(1, 9999)}@any.com',token=token,socks5=s5)
        else:
            raise UserError("输入错误")
        if self.user.isLogin:
            self.user.initLog()
        else:
            if self.user.login():
                self.user.log("登录成功")
            else:
                raise UserError("登录失败")
        
    def tryBuyVipShop(self,code,amount,count=13):
        '''尝试购买vip商品'''
        self.user.bugCount = 0
        tasks = [self.user.vipshopBuyA(code, amount,dl=i) for i in range(count)]
        self.user.runTasks(tasks)
        self.user.log(f"购买vip商城成功{self.user.bugCount}次")
    
    def vipShopList(self):
        '''获取vip商城列表'''
        res = self.user.vipshopList()
        if res:
            vipShop = res.get('vipShop')
            if vipShop:
                items = vipShop.get('items')
                canBuys = []
                for item in items:
                    numRemain = item.get("numRemain")
                    if numRemain and numRemain > 0:
                        canBuys.append(item)
                return canBuys
        self.user.log("无法获取vip商城列表")
        return None

    def askBuySome(self):
        # codes = [10101049,10101051,10103002,10103004,10103005,10103007,10603001,10101009,10104023,10104025,10101007,10101008]
        # names = ["体力10","体力50","加速5分钟","加速30m","加速1h","加速8h","次元门","vip500","传说碎片","紫色宝箱","vip10","vip100"]

        vipList = self.vipShopList()
        if vipList:
            # 10101049
            for item in vipList:
                code = item.get("code")
                
                if code in vipShopItemNames:
                    numRemain = item.get("numRemain")
                    inputValue = input(f"是否购买{vipShopItemNames[code]}？(y/n)")
                    if inputValue == 'y':
                        
                        inputValue = input(f"购买次数？(默认15)")
                        count = 15
                        if inputValue.isdigit():
                            count = int(inputValue)
                        self.tryBuyVipShop(code,numRemain,count=count)
                        return

    def normalShop(self):
        logger.info("开始购买普通商城")
        items = self.user.caravanList()
        if items:
            for i in range(len(items)):
                item = items[i]
                costItemCode = item.get("costItemCode")
                amount = item.get("amount")
                
                if amount == 0:
                    continue

                code = item.get("code")
                cost = item.get("cost")
                saleRate = item.get("saleRate")
                isCrystalItem = costItemCode == 10100005
                itemName = "未知"
                try:
                    itemName = shopItemNames.get(code)
                except:
                    pass

                if itemName is None:
                    if code // 10000 == 1060:
                        ns = ["普通碎片","魔法碎片","史诗碎片","传说碎片"]
                        newCode = int(f'1050{code % 10000}')
                        itemName = epicFragments.get(newCode) or ns[code % 10000 // 1000 - 1]
                    else:
                        itemName = "未知"

                itemTitle = f'({itemName}),{saleRate}%'                
                if saleRate >= 70:
                    itemTitle = self.user.redString(itemTitle)
                elif shopItemNames.get(code) and not isCrystalItem:
                    itemTitle = self.user.blueString(itemTitle)
                elif code // 10000 == 1060:
                    itemTitle = self.user.yellowString(itemTitle)
                
                itemCost = f'{cost}'
                if isCrystalItem:
                    itemCost = self.user.redString(itemCost + f' 砖石')
                else:
                    itemCost += " 资源"
                

                inputValue = input(f"是否购买第{i+1}个物品{itemTitle},{itemCost}商品？(y/n)")
                if inputValue == 'y':
                    inputValue = input(f"购买次数？(默认15)")
                    count = 15
                    if inputValue.isdigit():
                        count = int(inputValue)
                    caravanItemId = item.get("_id")
                    self.user.tryBuyNormalShop(caravanItemId,count=count)
    
    def tryClaimEvent(self):
        '''尝试领取奖励'''
        claimList = []
        u1 = self.user
        res = u1.eventList()
        if res and res.get("events"):
            events = res.get("events")
            for event in events:
                eventId = event.get("_id")
                reddot = event.get("reddot")
                if eventId and reddot > 0:
                    eventInfo = u1.eventInfo(eventId)
                    if eventInfo:
                        eventKingdom = eventInfo.get("eventKingdom")
                        eventKingdoms = eventInfo.get("eventKingdoms")
                        event1 = eventInfo.get("event")
                        if eventKingdom and event1:
                            eventId = eventKingdom.get("eventId")
                            event1s = event1.get("events")
                            eves = eventKingdom.get("events")
                            for e in eves:
                                status = e.get("status")
                                if status == 2:
                                    code = e.get("code")
                                    eventTargetId = u1.findEventTargetIdWithEvents(
                                        code, eventInfo.get("events"))
                                    rewards = e.get("rewards")
                                    needConcurrent = u1.checkRewards(rewards)
                                    if needConcurrent:
                                        claimList.append((eventId, eventTargetId, code))

                            for ek in eventKingdoms:
                                # point = ek.get("point")
                                # if point and point > 0:
                                eves = ek.get("events")
                                eventId = ek.get("eventId")
                                for e in eves:
                                    status = e.get("status")
                                    if status == 2:
                                        code = e.get("code")
                                        eventTargetId = u1.findEventTargetIdWithEvents(
                                            code, eventInfo.get("events"))
                                        rewards = e.get("rewards")
                                        needConcurrent = u1.checkRewards(
                                            rewards)
                                        if needConcurrent:
                                            claimList.append((eventId, eventTargetId, code))
        
        if len(claimList):
            overList = {}
            for eventId, eventTargetId, code in claimList:
                if overList.get(f"{eventId}{eventTargetId}{code}"):
                    continue
                self.user.bugCount = 0
                tasks = [self.user.eventClaimA(eventId,eventTargetId,code) for _ in range(20)]
                self.user.runTasks(tasks)
                self.user.log(f"领取奖励成功{self.user.bugCount}次")
                overList[f"{eventId}{eventTargetId}{code}"] = 1
        
    def equipBuild(self):
        logger.info("装备6连")
        treasureList = self.user.treasureList()
        treasures = treasureList.get("treasures")

        # 10503021 帽子
        # 10504001 dk橙装 
        # 10503001 次元门
        # 10504008 号角        
        researchCap = None
        dkEquipment = None
        hornId = None
        transportGateId = None
        for treasure in treasures:
            id = treasure.get("_id")
            code = treasure.get("code")
            if code == 10503021:
                researchCap = id
            elif code == 10504001:
                dkEquipment = id
            elif code == 10504008:
                hornId = id
            elif code == 10503001:
                transportGateId = id

        def run(id,page):
            while self.user.bugCount < 6:
                self.user.runTasks([self.user.treasurePageA(1)])
                self.user.runTasks([self.user.treasureEquipA(id, True),self.user.treasurePageA(page)])

        inputValue = input("卡号角/dk橙装/帽子/次元门？(1/2/3/4)")
        if inputValue == '1':
            if hornId is None:
                self.user.log("没有号角")
                return
            run(hornId, 2)
        elif inputValue == '2':
            run(dkEquipment, 2)
        elif inputValue == '3':
            run(researchCap, 3)
        elif inputValue == '4':
            if transportGateId is None:
                self.user.log("没有次元门")
                return
            run(transportGateId, 3)
        else:
            self.user.log("输入错误")

        return
        for i in range(20):
            self.user.runTasks([self.user.treasurePageA(2)])

            self.user.runTasks([self.user.treasureEquipA("61d4601b5e42108923632c03", True),self.user.treasurePageA(3)])

    def tryClaimFragments(self):
        self.user.tryFragmentsExchange()
        # tasks = [self.user.eventClaimA('61f5de76685ea669d742e622', '61f5de76685ea669d742e625', 60103102) for _ in range(50)]
        # self.user.runTasks(tasks)
        # self.user.log(f"卡碎片成功{self.user.bugCount}次")
        # tasks = [self.user.treasureEquipA("61d4601b5e42108923632c03", True) for _ in range(20)]
        # self.user.runTasks(tasks)
        # self.user.log(f"装备6矮人成功{self.user.bugCount}次")

    def tryUseSkills(self):
        skillList = self.user.skillList()
        for skill in skillList:
            nextSkillTime = skill.get("nextSkillTime")
            t = -1
            if nextSkillTime:
                t = self.user.compareDateTimeWithNow(nextSkillTime)
            if t < 0:
                code = skill.get("code")
                codeName = skillNames.get(code)
                if codeName is None:
                    continue
                inputValue = input(f"是否使用{codeName}？(y/n)")
                if inputValue == 'y':
                    self.user.tryUseSkill(code)
        
    def tryUpgradeTreasure(self):
        treasureList = self.user.treasureList()
        treasures = treasureList.get("treasures")
        for treasure in treasures:
            code = treasure.get("code")
            piece = treasure.get("piece")
            level = treasure.get("level")
            levelCode = code // 1000 % 10
            if level != levelCode + 1:
                if piece >= 40:
                    newCode = int(f'1050{code % 10000}')
                    name = epicFragments.get(newCode) or newCode
                    skillLevel = treasure.get("skillLevel")
                    inputValue = input(f"是否升级{name} 碎片{piece} 技能槽{skillLevel}？(y/n)")
                    if inputValue == 'y':
                        inputValue = input(f"请输入位置(1-5)")
                        if inputValue.isdigit():
                            idx = int(inputValue) - 1
                            self.user.bugCount = 0
                            tasks = [self.user.treasureUpgradeA(treasure.get("_id"), idx) for _ in range(10)]
                            self.user.runTasks(tasks)
                            self.user.log(f"{name}使用成功{self.user.bugCount}次")

                    
                    # print(f"{newCode} {name} {piece}")

    def tryvipClaim(self):
        inputValue = input(f"领取vip次数？(默认30)")
        count = 30
        if inputValue.isdigit():
            count = int(inputValue)
        self.user.tryClaimVipWithCount(count)



def main():
    # sys.argv.append("eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJfaWQiOiI2MWMwMGE3OTY1YTY1ODE1MDBmNGNhZDkiLCJraW5nZG9tSWQiOiI2MWMwMGE5OTIyYjU2ZDE1NDFlZmU5ZjQiLCJ0aW1lIjoxNjQ2NTAxMzQ5NDk2LCJpYXQiOjE2NDY1MDEzNDksImV4cCI6MTY0NzEwNjE0OSwiaXNzIjoibm9kZ2FtZXMuY29tIiwic3ViIjoidXNlckluZm8ifQ.pEqHafB3v7sHY8BnsgXruPar_xTfnLZfj8_cqBE_xF8")
    # sys.argv.append(3)
    isDebug = True if sys.gettrace() else False
    if isDebug:
        from Unit.FileTool import loadSelfToken
        sys.argv.append(loadSelfToken())
    if len(sys.argv) > 1:
        uc = UserCenter(sys.argv[1])
        # uc.tryClaimEvent()
        # return
        # uc.equipBuild()
        # return
        if len(sys.argv) > 2:
            mode = int(sys.argv[2])
            if mode == 1:
                logger.info("领任务")
                uc.user.tryClaimEvents()
            elif mode == 2:
                logger.info("使用技能")
                uc.tryUseSkills()

                # logger.info("卡碎片")
                # uc.tryClaimFragments()
            elif mode == 3:
                logger.info("升级装备")
                uc.tryUpgradeTreasure()
            elif mode == 4:
                logger.info("领取vip")
                uc.tryvipClaim()
            else:
                logger.info("输入错误")

        else:
            try:
                inputValue = input("vip商店？(y/n)")
                if inputValue == 'y':
                    uc.askBuySome()
                else:
                    uc.normalShop()
            except KeyboardInterrupt:
                pass
            except Exception as e:
                logger.exception(e)

    else:
        logger.error("输入异常")


if __name__ == "__main__":
    main()

