[uwsgi]
master-fifo = /tmp/fifo0
master-fifo = /tmp/fifo1
master-fifo = /var/run/foofifo
master = true
http = :9999
wsgi-file = ./webService.pyi
callable = app

; 进程配置
threads = 16
process = 1
; 启用多线程
enable-threads = true

cache2 = name=default,items=1000
http-timeout  = 300

; 日志配置
; 日志文件位置
logto = ./logs/uwsgi.log
; 支持日志文件重新打开
log-reopen = true
; 日志文件最大大小
log-maxsize = 50000000

; 自动重启
; 触摸此文件即可重启uWSGI
touch-reload = ./reload