#!/usr/bin/python3
# -*- coding: utf-8 -*-

"""
功能描述：web服务端
编写人：darkedge
编写日期：2022年03月09日

"""
import os
import platform

from flask_admin import helpers as admin_helpers

from FlaskApp import app, db, admin
import FlaskApp.Models as Models
from FlaskApp.api import api,normalApi
# trunk-ignore(ruff/F401)
import FlaskApp.views

# trunk-ignore(ruff/F401)
from flask import url_for, redirect, render_template, request, abort,jsonify
from flask_security import Security, SQLAlchemyUserDatastore
from flask_security.utils import hash_password

# Setup Flask-Security
user_datastore = SQLAlchemyUserDatastore(db, Models.User, Models.Role)
security = Security(app, user_datastore)

@app.route('/')
def index():
    return render_template('index.html')

app.register_blueprint(api)
app.register_blueprint(normalApi)
# print(app.url_map)

# define a context processor for merging flask-admin's template context into the
# flask-security views.
@security.context_processor
def security_context_processor():
    return dict(
        admin_base_template=admin.theme.base_template,
        admin_view=admin.index_view,
        h=admin_helpers,
        get_url=url_for
    )


def build_sample_db():
    """
    Populate a small db with some example entries.
    """

    # db.drop_all()

    with app.app_context():
        db.create_all()
        user_role = Models.Role(name='user')
        super_user_role = Models.Role(name='superuser')
        hand_up_user_role = Models.Role(name='handupuser')
        vip_user_role = Models.Role(name='vipuser')
        vip2_user_role = Models.Role(name='vip2user')
        vip3_user_role = Models.Role(name='vip3user')
        vip4_user_role = Models.Role(name='vip4user')
        db.session.add(user_role)
        db.session.add(super_user_role)
        db.session.add(hand_up_user_role)
        db.session.add(vip_user_role)
        db.session.add(vip2_user_role)
        db.session.add(vip3_user_role)
        db.session.add(vip4_user_role)
        db.session.commit()

        _test_user = user_datastore.create_user(
            email='<EMAIL>',
            password=hash_password('qwoidjqiowjdqiowjd1209sd'),
            roles=[user_role, super_user_role,hand_up_user_role,vip_user_role,vip2_user_role,vip3_user_role]
        )


        # for i in range(len(first_names)):
        #     tmp_email = first_names[i].lower() + "." + last_names[i].lower() + "@example.com"
        #     tmp_pass = ''.join(random.choice(string.ascii_lowercase + string.digits) for i in range(10))
        #     user_datastore.create_user(
        #         first_name=first_names[i],
        #         last_name=last_names[i],
        #         email=tmp_email,
        #         password=encrypt_password(tmp_pass),
        #         roles=[user_role, ]
        #     )
        db.session.commit()
    return
# Build a sample db on the fly, if one does not exist yet.
app_dir = app.root_path
database_path = os.path.join(app_dir, app.config['DATABASE_FILE'])
if not os.path.exists(database_path):
    build_sample_db()

if __name__ == '__main__':
    # with app.test_request_context():
    #     print(url_for('work.connect_api'))
    #     print(url_for('work.index_view'))
    # Start app
    import platform
    isDebug = platform.system() != 'Linux'

    port = 9991 if isDebug else 9999
    app.run(debug=isDebug,host="0.0.0.0",port=port)
