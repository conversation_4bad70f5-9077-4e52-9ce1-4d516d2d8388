#!/bin/bash -e
# ./webStart.sh >> run.log 2>&1 &
# echo r > /tmp/fifo0
touch reload

# 定义函数
function stop_processes() {
    # 获取参数
    local process_keyword=$1

    # 查找进程
    local process_ids=$(ps aux | grep "$process_keyword" | grep -v grep | awk '{print $2}')

    # 检查是否找到进程
    if [ -n "$process_ids" ]; then
        echo "找到以下进程匹配关键字 '$process_keyword'："
        echo "$process_ids"

        # 关闭进程
        for process_id in $process_ids; do
            echo "正在关闭进程 $process_id..."
            kill "$process_id"
            echo "进程 $process_id 已关闭"
        done
    else
        echo "未找到匹配的进程"
    fi
}

stop_processes "beat"
stop_processes "celeryTask"
stop_processes "celerySchedule"

celery -A celeryTask multi restart workman -c 50
celery -A celerySchedule multi restart scheduleman -c 3 -Q schedule
celery -A celerySchedule beat --detach
